﻿using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Commands;

public class UpdateHacmpClusterTests : IClassFixture<HacmpClusterFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;
    private readonly Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateHacmpClusterCommandHandler _handler;

    public UpdateHacmpClusterTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.UpdateHacmpClusterRepository(_hacmpClusterFixture.HacmpClusters);

        _handler = new UpdateHacmpClusterCommandHandler(_hacmpClusterFixture.Mapper, _mockHacmpClusterRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidHacmpCluster_UpdateToHacmpClustersRepo()
    {
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId;

        var result = await _handler.Handle(_hacmpClusterFixture.UpdateHacmpClusterCommand, CancellationToken.None);

        var hacmpCluster = await _mockHacmpClusterRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_hacmpClusterFixture.UpdateHacmpClusterCommand.Name, hacmpCluster.Name);
    }

    [Fact]
    public async Task Handle_Return_UpdateHacmpClusterResponse_When_HacmpClusterUpdated()
    {
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId;

        var result = await _handler.Handle(_hacmpClusterFixture.UpdateHacmpClusterCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateHacmpClusterResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_hacmpClusterFixture.UpdateHacmpClusterCommand.Id);

        result.Message.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidHacmpClusterId()
    {
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_hacmpClusterFixture.UpdateHacmpClusterCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var handler = new UpdateHacmpClusterCommandHandler(_hacmpClusterFixture.Mapper, _mockHacmpClusterRepository.Object, _mockPublisher.Object);

        _hacmpClusterFixture.UpdateHacmpClusterCommand.Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId;

        await handler.Handle(_hacmpClusterFixture.UpdateHacmpClusterCommand, CancellationToken.None);

        _mockHacmpClusterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockHacmpClusterRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.HacmpCluster>()), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateHacmpClusterProperties_When_ValidCommand()
    {
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId;
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Name = "Updated HACMP Cluster";
        _hacmpClusterFixture.UpdateHacmpClusterCommand.ServerId = "UpdatedServerId";
        _hacmpClusterFixture.UpdateHacmpClusterCommand.ServerName = "UpdatedServerName";

        var result = await _handler.Handle(_hacmpClusterFixture.UpdateHacmpClusterCommand, CancellationToken.None);

        var hacmpCluster = await _mockHacmpClusterRepository.Object.GetByReferenceIdAsync(result.Id);

        hacmpCluster.Name.ShouldBe("Updated HACMP Cluster");
        hacmpCluster.ServerId.ShouldBe("UpdatedServerId");
        hacmpCluster.ServerName.ShouldBe("UpdatedServerName");
    }

    [Fact]
    public async Task Handle_Return_CorrectMessage_When_HacmpClusterUpdated()
    {
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId;
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Name = "Test HACMP Cluster";

        var result = await _handler.Handle(_hacmpClusterFixture.UpdateHacmpClusterCommand, CancellationToken.None);

        result.Message.ShouldContain("HACMP Cluster");
        result.Message.ShouldContain("Test HACMP Cluster");
        result.Message.ShouldContain("updated successfully");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId;

        await _handler.Handle(_hacmpClusterFixture.UpdateHacmpClusterCommand, CancellationToken.None);

        _mockHacmpClusterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EntityReferenceId_When_HacmpClusterUpdated()
    {
        _hacmpClusterFixture.UpdateHacmpClusterCommand.Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId;

        var result = await _handler.Handle(_hacmpClusterFixture.UpdateHacmpClusterCommand, CancellationToken.None);

        result.Id.ShouldBe(_hacmpClusterFixture.HacmpClusters[0].ReferenceId);
        result.Id.ShouldNotBeNullOrEmpty();
    }
}