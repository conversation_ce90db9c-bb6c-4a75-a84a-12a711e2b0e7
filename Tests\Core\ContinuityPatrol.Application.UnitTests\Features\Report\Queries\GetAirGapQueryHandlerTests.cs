﻿using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries
{
    public class GetAirGapQueryHandlerTests
    {
        private readonly Mock<ICyberAirGapLogRepository> _mockCyberAirGapLogRepository;
        private readonly Mock<ILogger<GetAirGapQueryHandler>> _mockLogger;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<IPublisher> _mockPubilsh;
        private readonly GetAirGapQueryHandler _handler;

        public GetAirGapQueryHandlerTests()
        {
            _mockCyberAirGapLogRepository = new Mock<ICyberAirGapLogRepository>();
            _mockLogger = new Mock<ILogger<GetAirGapQueryHandler>>();
            _mockMapper = new Mock<IMapper>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockPubilsh = new Mock<IPublisher>();

            _handler = new GetAirGapQueryHandler(
                _mockCyberAirGapLogRepository.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockLoggedInUserService.Object, _mockPubilsh.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenRepositoryReturnsNoData()
        {
            var query = new GetAirGapQuery
            {
                StartDate = "DateTime.UtcNow.AddDays(-7)",
                EndDate = "DateTime.UtcNow",
                Id = Guid.NewGuid().ToString()
            };

            _mockCyberAirGapLogRepository
                .Setup(repo => repo.GetCyberAirGapLogByAirGabId(query.StartDate, query.EndDate, query.Id))
                .ReturnsAsync(new List<Domain.Entities.CyberAirGapLog>());

            _mockLoggedInUserService
                .Setup(service => service.LoginName)
                .Returns("TestUser");

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGenerated);
            Assert.Empty(result.CyberAirGapLogLists);

            _mockLogger.Verify(logger => logger.LogError("AirGaplog is Zero"), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedList_WhenRepositoryReturnsData()
        {
            var query = new GetAirGapQuery
            {
                StartDate = "DateTime.UtcNow.AddDays(-7)",
                EndDate = "DateTime.UtcNow",
                Id = Guid.NewGuid().ToString(),
            };

            var airGapLogs = new List<Domain.Entities.CyberAirGapLog>
            {
                new Domain.Entities.CyberAirGapLog { AirGapId = Guid.NewGuid().ToString(), StartTime = DateTime.UtcNow.AddHours(-1), EndTime = DateTime.UtcNow },
                new Domain.Entities.CyberAirGapLog { AirGapId = Guid.NewGuid().ToString(), StartTime = DateTime.UtcNow.AddHours(-2), EndTime = DateTime.UtcNow.AddHours(-1) }
            };

            var expectedMappedLogs = new List<AirGapListReportVm>
            {
               new AirGapListReportVm { AirGapId = Guid.NewGuid().ToString(), StartTime = DateTime.UtcNow.AddHours(-1).ToString(), EndTime = DateTime.UtcNow.ToString(), TotalTime = TimeSpan.FromHours(1).ToString() },
               new AirGapListReportVm { AirGapId = Guid.NewGuid().ToString(), StartTime = DateTime.UtcNow.AddHours(-2).ToString(), EndTime = DateTime.UtcNow.AddHours(-1).ToString(), TotalTime = TimeSpan.FromHours(1).ToString() }
            };

            _mockCyberAirGapLogRepository
                .Setup(repo => repo.GetCyberAirGapLogByAirGabId(query.StartDate, query.EndDate, query.Id))
                .ReturnsAsync(airGapLogs);

            _mockMapper
                .Setup(mapper => mapper.Map<List<AirGapListReportVm>>(It.IsAny<List<Domain.Entities.CyberAirGapLog>>()))
                .Returns(expectedMappedLogs);

            _mockLoggedInUserService
                .Setup(service => service.LoginName)
                .Returns("TestUser");

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGenerated);
            Assert.Equal(expectedMappedLogs.Count, result.CyberAirGapLogLists.Count);
            Assert.Equal(expectedMappedLogs[0].TotalTime, result.CyberAirGapLogLists[0].TotalTime);
            Assert.Equal(expectedMappedLogs[1].TotalTime, result.CyberAirGapLogLists[1].TotalTime);

            _mockMapper.Verify(mapper => mapper.Map<List<AirGapListReportVm>>(It.IsAny<List<Domain.Entities.CyberAirGapLog>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryWithCorrectParameters()
        {
            var query = new GetAirGapQuery
            {
                StartDate = "DateTime.UtcNow.AddDays(-7)",
                EndDate = "DateTime.UtcNow",
                Id = Guid.NewGuid().ToString(),
            };

            _mockCyberAirGapLogRepository
                .Setup(repo => repo.GetCyberAirGapLogByAirGabId(query.StartDate, query.EndDate, query.Id))
                .ReturnsAsync(new List<Domain.Entities.CyberAirGapLog>());

            await _handler.Handle(query, CancellationToken.None);

            _mockCyberAirGapLogRepository.Verify(repo => repo.GetCyberAirGapLogByAirGabId(query.StartDate, query.EndDate, query.Id), Times.Once);
        }
    }
}
