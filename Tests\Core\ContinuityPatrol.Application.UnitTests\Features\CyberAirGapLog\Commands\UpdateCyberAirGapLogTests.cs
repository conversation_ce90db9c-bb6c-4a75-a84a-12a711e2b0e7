using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapLog.Commands;

public class UpdateCyberAirGapLogTests : IClassFixture<CyberAirGapLogFixture>
{
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;
    private readonly Mock<ICyberAirGapLogRepository> _mockCyberAirGapLogRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateCyberAirGapLogCommandHandler _handler;

    public UpdateCyberAirGapLogTests(CyberAirGapLogFixture cyberAirGapLogFixture)
    {
        _cyberAirGapLogFixture = cyberAirGapLogFixture;
        _mockCyberAirGapLogRepository = CyberAirGapLogRepositoryMocks.CreateCyberAirGapLogRepository(_cyberAirGapLogFixture.CyberAirGapLogs);
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        _handler = new UpdateCyberAirGapLogCommandHandler(
            _mockMapper.Object,
            _mockCyberAirGapLogRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapLog_When_ValidCommand()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapId = "updated-airgap-001",
            AirGapName = "Updated Enterprise Air Gap",
            SourceSiteName = "Updated Production Site",
            TargetSiteName = "Updated DR Site",
            Port = 9443,
            Description = "Updated air gap description"
        };

        CyberAirGapLogUpdatedEvent publishedEvent = null;

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapLogCommand), typeof(Domain.Entities.CyberAirGapLog)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapLogCommand)src;
                var entity = (Domain.Entities.CyberAirGapLog)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.SourceSiteName = updateCmd.SourceSiteName;
                entity.TargetSiteName = updateCmd.TargetSiteName;
                entity.Port = updateCmd.Port;
                entity.Description = updateCmd.Description;
            });

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapLogUpdatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapLogUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateCyberAirGapLogResponse>();
        result.Success.ShouldBeTrue();
        result.Id.ShouldBe(existingEntity.ReferenceId);
        result.Message.ShouldContain("updated successfully");

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(command.AirGapName);

        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    /// <summary>
    /// Test: Update cyber air gap log when entity not found
    /// Expected: Throws NotFoundException
    /// </summary>
    [Fact]
    public async Task Handle_UpdateCyberAirGapLog_When_EntityNotFound()
    {
        // Arrange
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = "non-existent-id",
            AirGapName = "Non-existent Air Gap"
        };

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync((Domain.Entities.CyberAirGapLog)null);

        // Act & Assert
        var exception = await Should.ThrowAsync<NotFoundException>(
            async () => await _handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldContain("CyberAirGapLog");
        exception.Message.ShouldContain(command.Id);

        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGapLog>()), Times.Never);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    /// <summary>
    /// Test: Update cyber air gap log with complex properties
    /// Expected: Successfully updates complex JSON configurations
    /// </summary>
    [Fact]
    public async Task Handle_UpdateCyberAirGapLog_When_ComplexProperties()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Updated Complex Air Gap",
            Source = @"{
                ""type"": ""updated-database"",
                ""cluster"": {
                    ""name"": ""UPDATED-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""UPDATED-DB-01"", ""role"": ""Primary""},
                        {""server"": ""UPDATED-DB-02"", ""role"": ""Secondary""},
                        {""server"": ""UPDATED-DB-03"", ""role"": ""Tertiary""}
                    ]
                }
            }",
            Target = @"{
                ""type"": ""updated-database"",
                ""cluster"": {
                    ""name"": ""UPDATED-DR-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""UPDATED-DR-DB-01"", ""role"": ""Primary""},
                        {""server"": ""UPDATED-DR-DB-02"", ""role"": ""Secondary""}
                    ]
                }
            }"
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapLogCommand), typeof(Domain.Entities.CyberAirGapLog)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapLogCommand)src;
                var entity = (Domain.Entities.CyberAirGapLog)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Source = updateCmd.Source;
                entity.Target = updateCmd.Target;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.CyberAirGapLog>(e =>
            e.Source.Contains("UPDATED-CLUSTER-01") &&
            e.Target.Contains("UPDATED-DR-CLUSTER-01"))), Times.Once);
    }

    /// <summary>
    /// Test: Update cyber air gap log with cancellation token
    /// Expected: Respects cancellation and throws OperationCanceledException
    /// </summary>
    [Fact]
    public async Task Handle_UpdateCyberAirGapLog_When_CancellationRequested()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Cancelled Update"
        };
        var cancellationToken = new CancellationToken(true);

    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapLog_When_RepositoryFails()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Failed Update"
        };

        var mockFailingRepository = CyberAirGapLogRepositoryMocks.CreateFailingCyberAirGapLogRepository();
        mockFailingRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(existingEntity);

        var handler = new UpdateCyberAirGapLogCommandHandler(
            _mockMapper.Object,
            mockFailingRepository.Object,
            _mockPublisher.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe("Update operation failed");
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("Active", "System is running normally")]
    [InlineData("Warning", "System has minor issues")]
    [InlineData("Error", "System has critical errors")]
    [InlineData("Maintenance", "System is under maintenance")]
    [InlineData("Disabled", "System is temporarily disabled")]
    public async Task Handle_UpdateCyberAirGapLog_When_DifferentStatusValues(string status, string description)
    {
        
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = $"Status Test - {status}",
            Description = description
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapLogCommand), typeof(Domain.Entities.CyberAirGapLog)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapLogCommand)src;
                var entity = (Domain.Entities.CyberAirGapLog)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Description = updateCmd.Description;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain(command.AirGapName);

        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.CyberAirGapLog>(e =>
            e.AirGapName.Contains(status))), Times.Once);
    }

    /// <summary>
    /// Test: Update cyber air gap log with null properties
    /// Expected: Handles null properties gracefully
    /// </summary>
    [Fact]
    public async Task Handle_UpdateCyberAirGapLog_When_NullProperties()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Null Properties Update",
            Description = null,
            Source = null,
            Target = null,
            SourceComponentName = null,
            TargetComponentName = null
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapLogCommand), typeof(Domain.Entities.CyberAirGapLog)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapLogCommand)src;
                var entity = (Domain.Entities.CyberAirGapLog)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Description = updateCmd.Description;
                entity.Source = updateCmd.Source;
                entity.Target = updateCmd.Target;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGapLog>()), Times.Once);
    }

    /// <summary>
    /// Test: Update cyber air gap log with special characters
    /// Expected: Handles special characters in updated values
    /// </summary>
    [Fact]
    public async Task Handle_UpdateCyberAirGapLog_When_SpecialCharacters()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Updated Special Characters & <script>alert('xss')</script>",
            Description = "Updated description with special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?",
            SourceSiteName = "Updated Source with émojis 🔄💻📊",
            TargetSiteName = "Updated Target with unicode 更新数据"
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapLogCommand), typeof(Domain.Entities.CyberAirGapLog)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapLogCommand)src;
                var entity = (Domain.Entities.CyberAirGapLog)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Description = updateCmd.Description;
                entity.SourceSiteName = updateCmd.SourceSiteName;
                entity.TargetSiteName = updateCmd.TargetSiteName;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain("Updated Special Characters");

        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.CyberAirGapLog>(e =>
            e.AirGapName.Contains("Updated Special Characters") &&
            e.SourceSiteName.Contains("🔄💻📊") &&
            e.TargetSiteName.Contains("更新数据"))), Times.Once);
    }

    [Theory]
    [InlineData(1, "Updated Minimum Port")]
    [InlineData(65535, "Updated Maximum Port")]
    [InlineData(443, "Updated HTTPS Port")]
    [InlineData(80, "Updated HTTP Port")]
    public async Task Handle_UpdateCyberAirGapLog_When_BoundaryPortValues(int port, string description)
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = $"Updated Port Test {port}",
            Description = description,
            Port = port
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapLogCommand), typeof(Domain.Entities.CyberAirGapLog)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapLogCommand)src;
                var entity = (Domain.Entities.CyberAirGapLog)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Port = updateCmd.Port;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.CyberAirGapLog>(e =>
            e.Port == port)), Times.Once);
    }

    /// <summary>
    /// Test: Update cyber air gap log response validation
    /// Expected: Response contains all required properties
    /// </summary>
    [Fact]
    public async Task Handle_UpdateCyberAirGapLog_When_ValidatingResponse()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Response Validation Test"
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapLogCommand), typeof(Domain.Entities.CyberAirGapLog)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapLogCommand)src;
                var entity = (Domain.Entities.CyberAirGapLog)dest;
                entity.AirGapName = updateCmd.AirGapName;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateCyberAirGapLogResponse>();
        result.Success.ShouldBeTrue();
        result.Id.ShouldBe(existingEntity.ReferenceId);
        result.Message.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("CyberAirGapLog");
        result.Message.ShouldContain("updated successfully");
        result.Message.ShouldContain(command.AirGapName);
    }
}
