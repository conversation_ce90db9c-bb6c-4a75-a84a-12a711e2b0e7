﻿using ContinuityPatrol.Application.Features.Incident.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Events;

public class CreateIncidentEventTests : IClassFixture<IncidentFixture>, IClassFixture<UserActivityFixture>
{
    private readonly IncidentFixture _incidentFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly IncidentCreatedEventHandler _handler;

    public CreateIncidentEventTests(IncidentFixture incidentFixture, UserActivityFixture userActivityFixture)
    {
        _incidentFixture = incidentFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");

        var mockIncidentEventLogger = new Mock<ILogger<IncidentCreatedEventHandler>>();

        _mockUserActivityRepository = CompanyRepositoryMocks.CreateCompanyEventRepository(_userActivityFixture.UserActivities);

        _handler = new IncidentCreatedEventHandler(mockIncidentEventLogger.Object, mockLoggedInUserService.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateIncidentEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_incidentFixture.IncidentCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_incidentFixture.IncidentCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}