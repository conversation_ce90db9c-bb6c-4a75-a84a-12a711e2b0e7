using ContinuityPatrol.Application.Features.BackUp.Events.Execute;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUp.Events;


public class BackUpExecutedEventTests : IClassFixture<BackUpFixture>
{
    private readonly BackUpFixture _backUpFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<BackUpExecutedEventHandler>> _mockLogger;
    private readonly BackUpExecutedEventHandler _handler;

    public BackUpExecutedEventTests(BackUpFixture backUpFixture)
    {
        _backUpFixture = backUpFixture;
        _mockUserActivityRepository = BackUpRepositoryMocks.CreateUserActivityRepository(_backUpFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BackUpExecutedEventHandler>>();

        // Setup default user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");
        _mockUserService.Setup(x => x.RequestedUrl).Returns("/api/backup/execute");
        _mockUserService.Setup(x => x.IpAddress).Returns("*************");

        _handler = new BackUpExecutedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    /// <summary>
    /// Test: Event handler processes BackUpExecutedEvent successfully
    /// Expected: User activity is logged for backup execution
    /// </summary>
    [Fact]
    public async Task Handle_ProcessBackUpExecutedEvent_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = "TestDatabase_Executed"
        };

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    /// <summary>
    /// Test: User activity is created with correct execute properties
    /// Expected: UserActivity entity has correct backup execution information
    /// </summary>
    [Fact]
    public async Task Handle_CreateUserActivityWithCorrectExecuteProperties_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = "ProductionDatabase_Executed"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("Execute BackUp");
        createdActivity.ActivityType.ShouldBe("Execute");
        createdActivity.ActivityDetails.ShouldBe("Backup request sent successfully for 'ProductionDatabase_Executed'.");
        createdActivity.UserId.ShouldBe("TestUser123");
        createdActivity.LoginName.ShouldBe("TestUser123");
        createdActivity.RequestUrl.ShouldBe("/api/backup/execute");
        createdActivity.HostAddress.ShouldBe("*************");
        createdActivity.IsActive.ShouldBeTrue();
    }


    [Fact]
    public async Task Handle_ProcessMultipleExecuteEvents_When_ValidEvents()
    {
        // Arrange
        var events = new[]
        {
            new BackUpExecutedEvent { BackUpName = "Database1_Executed" },
            new BackUpExecutedEvent { BackUpName = "Database2_Executed" },
            new BackUpExecutedEvent { BackUpName = "Database3_Executed" }
        };

        // Act
        foreach (var eventItem in events)
        {
            await _handler.Handle(eventItem, CancellationToken.None);
        }

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(3));
    }

    /// <summary>
    /// Test: Event with different backup names
    /// Expected: All backup names are handled correctly in events
    /// </summary>
    [Fact]
    public async Task Handle_HandleDifferentBackUpNames_When_ValidEvents()
    {
        // Arrange
        var backupNames = new[] { "ProductionDB", "StagingDB", "TestDB", "DevDB", "BackupDB" };

        foreach (var backupName in backupNames)
        {
            var backUpEvent = new BackUpExecutedEvent
            {
                BackUpName = $"{backupName}_Executed"
            };

            Domain.Entities.UserActivity createdActivity = null;
            _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
                .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

            // Act
            await _handler.Handle(backUpEvent, CancellationToken.None);

            // Assert
            createdActivity.ShouldNotBeNull();
            createdActivity.ActivityDetails.ShouldContain($"{backupName}_Executed");
            createdActivity.ActivityDetails.ShouldContain("Backup request sent successfully");
        }
    }

    /// <summary>
    /// Test: Event with long backup name
    /// Expected: Long backup names are handled correctly
    /// </summary>
    [Fact]
    public async Task Handle_HandleLongBackUpName_When_ValidEvent()
    {
        // Arrange
        var longBackupName = "VeryLongBackupNameThatExceedsNormalLengthLimitsForTestingPurposesAndShouldBeHandledGracefullyByTheEventHandlerWithoutAnyIssuesOrTruncationProblems_Executed";
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = longBackupName
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain(longBackupName);
        createdActivity.ActivityDetails.ShouldContain("Backup request sent successfully");
    }

    /// <summary>
    /// Test: Event with special characters in backup name
    /// Expected: Special characters are handled correctly in events
    /// </summary>
    [Fact]
    public async Task Handle_HandleSpecialCharsInBackUpName_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = "Special-Backup@123!_Executed & <script>alert('xss')</script>"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain("Special-Backup@123!_Executed");
        createdActivity.ActivityDetails.ShouldContain("Backup request sent successfully");
        createdActivity.UserId.ShouldBe("TestUser123");
    }

    /// <summary>
    /// Test: Event with null or empty backup name
    /// Expected: Null/empty backup names are handled gracefully
    /// </summary>
    [Fact]
    public async Task Handle_HandleNullBackUpName_When_EventWithNullName()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = null
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("Execute BackUp");
        createdActivity.ActivityDetails.ShouldContain("Backup request sent successfully");
        // Name handling should be graceful even when null
    }

    /// <summary>
    /// Test: Event processing performance with rapid succession
    /// Expected: Multiple rapid execute events are processed correctly
    /// </summary>
    [Fact]
    public async Task Handle_ProcessRapidExecuteEvents_When_MultipleEventsInSuccession()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new BackUpExecutedEvent
        {
            BackUpName = $"RapidExecute_Backup_{i:00}"
        }).ToList();

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(10));
    }

    /// <summary>
    /// Test: User service integration
    /// Expected: User service is called to get current user information
    /// </summary>
    [Fact]
    public async Task Handle_UseUserServiceForUserInfo_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = "UserServiceIntegration_Backup_Executed"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.UserId.ShouldBe("TestUser123");
        createdActivity.LoginName.ShouldBe("TestUser123");
        createdActivity.RequestUrl.ShouldBe("/api/backup/execute");
        createdActivity.HostAddress.ShouldBe("*************");
        
        // Verify user service was called
        _mockUserService.Verify(x => x.UserId, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.LoginName, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.RequestedUrl, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.IpAddress, Times.AtLeastOnce);
    }

    /// <summary>
    /// Test: Logger integration
    /// Expected: Logger is used to log event processing
    /// </summary>
    [Fact]
    public async Task Handle_LogEventProcessing_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = "LoggingIntegration_Backup_Executed"
        };

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        
        _mockLogger.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_HandleEmptyBackUpName_When_EventWithEmptyName()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = string.Empty
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("Execute BackUp");
        createdActivity.ActivityDetails.ShouldContain("Backup request sent successfully");
    }

    /// <summary>
    /// Test: Event with whitespace-only backup name
    /// Expected: Whitespace-only backup names are handled gracefully
    /// </summary>
    [Fact]
    public async Task Handle_HandleWhitespaceBackUpName_When_EventWithWhitespaceName()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = "   \t\n\r   "
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("Execute BackUp");
        createdActivity.ActivityDetails.ShouldContain("Backup request sent successfully");
    }

    [Fact]
    public async Task Handle_CaptureExecutionContext_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpExecutedEvent
        {
            BackUpName = "ExecutionContext_Backup"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityType.ShouldBe("Execute");
        createdActivity.Action.ShouldBe("Execute BackUp");
        createdActivity.ActivityDetails.ShouldBe("Backup request sent successfully for 'ExecutionContext_Backup'.");
        createdActivity.Entity.ShouldBe("BackUp");
    }
}
