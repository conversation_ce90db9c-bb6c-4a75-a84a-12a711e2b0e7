using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Events;

public class CyberAirGapStatusDeletedEventTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<CyberAirGapStatusDeletedEventHandler>> _mockLogger;
    private readonly CyberAirGapStatusDeletedEventHandler _handler;

    private readonly Mock<ILoggedInUserService> _userService;
    private readonly Mock<ILogger<CyberAirGapStatusDeletedEventHandler>> _logger;
    private readonly Mock<IUserActivityRepository> _userActivityRepository;
    public CyberAirGapStatusDeletedEventTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockUserActivityRepository = CyberAirGapStatusRepositoryMocks.CreateUserActivityRepository();
        _mockUserService = CyberAirGapStatusRepositoryMocks.CreateUserService();
        _mockLogger = new Mock<ILogger<CyberAirGapStatusDeletedEventHandler>>();

        _userService = new Mock<ILoggedInUserService>();
        _logger = new Mock<ILogger<CyberAirGapStatusDeletedEventHandler>>();
        _userActivityRepository = new Mock<IUserActivityRepository>();
        _handler = new CyberAirGapStatusDeletedEventHandler(
            _userService.Object,
            _logger.Object,
            _userActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_ValidData()
    {
        // Arrange
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = "Deleted Enterprise Air Gap Status System"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_NullName()
    {
        // Arrange
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = null
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_EmptyName()
    {
        // Arrange
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = string.Empty
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();
        
    }
    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_SpecialCharacters()
    {
        // Arrange
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = "Deleted Special Characters & <script>alert('xss')</script> 🗑️💻📊 删除数据"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_CancellationRequested()
    {
        // Arrange
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = "Cancelled Delete Event Test"
        };
        var cancellationToken = new CancellationToken(true);

    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_RepositoryFails()
    {
        // Arrange
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = "Repository Failure Delete Test"
        };

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act & Assert
       
    }

    
    [Theory]
    [InlineData("DeleteUser001", "<EMAIL>", "*************", "/api/v1/cyberairgapstatus/delete")]
    [InlineData("DeleteUser002", "<EMAIL>", "*********", "/api/v2/cyberairgapstatus/delete")]
    [InlineData("DeleteAdmin", "<EMAIL>", "***********", "/admin/cyberairgapstatus/delete")]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_DifferentUserContexts(
        string userId, string loginName, string ipAddress, string requestUrl)
    {
        // Arrange
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns(userId);
        mockUserService.Setup(x => x.LoginName).Returns(loginName);
        mockUserService.Setup(x => x.IpAddress).Returns(ipAddress);
        mockUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);

        var handler = new CyberAirGapStatusDeletedEventHandler(
           _userService.Object,
            _logger.Object,
            _userActivityRepository.Object);

        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = "User Context Delete Test"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_NullUserServiceValues()
    {
        // Arrange
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns((string)null);
        mockUserService.Setup(x => x.LoginName).Returns((string)null);
        mockUserService.Setup(x => x.IpAddress).Returns((string)null);
        mockUserService.Setup(x => x.RequestedUrl).Returns((string)null);

        var handler = new CyberAirGapStatusDeletedEventHandler(
           _userService.Object,
            _logger.Object,
            _userActivityRepository.Object);

        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = "Null User Service Delete Test"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_MultipleEvents()
    {
        // Arrange
        var events = new[]
        {
            new CyberAirGapStatusDeletedEvent { Name = "Deleted Air Gap Status 1" },
            new CyberAirGapStatusDeletedEvent { Name = "Deleted Air Gap Status 2" },
            new CyberAirGapStatusDeletedEvent { Name = "Deleted Air Gap Status 3" }
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        foreach (var evt in events)
        {
            await _handler.Handle(evt, CancellationToken.None);
        }


       
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_ValidatingActivityDetails()
    {
        // Arrange
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = "Validation Delete Test Air Gap Status"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();

    }
    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_LongName()
    {
        // Arrange
        var longName = new string('D', 500) + " Deleted Air Gap Status System";
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = longName
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();
        
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_ConcurrentOperations()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new CyberAirGapStatusDeletedEvent
        {
            Name = $"Concurrent Delete Test {i}"
        }).ToArray();

        var capturedActivities = new List<Domain.Entities.UserActivity>();
        var lockObject = new object();

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity =>
            {
                lock (lockObject)
                {
                    capturedActivities.Add(activity);
                }
            })
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

       
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusDeletedEvent_When_SoftDeleteConfirmation()
    {
        // Arrange
        var deletedEvent = new CyberAirGapStatusDeletedEvent
        {
            Name = "Soft Delete Confirmation Test"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldNotBeNull();
    }
}
