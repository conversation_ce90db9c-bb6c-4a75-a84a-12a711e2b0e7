﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactCategory.Events
{
    public class FiaImpactCategoryCreatedEventHandlerTests : IClassFixture<FiaImpactCategoryFixture>
    {
        private readonly FiaImpactCategoryFixture _fixture;
        private readonly Mock<ILogger<FiaImpactCategoryCreatedEventHandler>> _mockLogger;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly FiaImpactCategoryCreatedEventHandler _handler;
        private readonly List<Domain.Entities.UserActivity> _userActivities;

        public FiaImpactCategoryCreatedEventHandlerTests(FiaImpactCategoryFixture fixture)
        {
            _fixture = fixture;

            _mockLogger = new Mock<ILogger<FiaImpactCategoryCreatedEventHandler>>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _userActivities = new List<Domain.Entities.UserActivity>();

            _mockUserActivityRepository =
                FiaImpactCategoryRepositoryMocks.CreateFiaImpactCategoryEventRepository(_userActivities);

            _mockUserService.Setup(u => u.UserId).Returns("test-user-id");
            _mockUserService.Setup(u => u.LoginName).Returns("testuser");
            _mockUserService.Setup(u => u.RequestedUrl).Returns("/api/fiaimpactcategory/create");
            _mockUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

            _handler = new FiaImpactCategoryCreatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object);
        }

        [Fact(DisplayName = "Handle_Should_Log_And_Add_UserActivity_When_Event_Handled")]
        public async Task Handle_Should_Log_And_Add_UserActivity_When_Event_Handled()
        {
            // Arrange
            var createdEvent = _fixture.FiaImpactCategoryCreatedEvent;

            // Act
            await _handler.Handle(createdEvent, CancellationToken.None);

            // Assert
            Assert.Single(_userActivities);

            var activity = _userActivities.First();

            Assert.Equal("test-user-id", activity.UserId);
            Assert.Equal("testuser", activity.LoginName);
            Assert.Equal("/api/fiaimpactcategory/create", activity.RequestUrl);
            Assert.Equal("127.0.0.1", activity.HostAddress);
            Assert.Equal("Create FiaImpactCategory", activity.Action);
            Assert.Equal("FiaImpactCategory", activity.Entity);
            Assert.Equal("Create", activity.ActivityType);
            Assert.Contains(createdEvent.Name, activity.ActivityDetails);
            Assert.Equal("test-user-id", activity.CreatedBy);
            Assert.Equal("test-user-id", activity.LastModifiedBy);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _mockLogger.Verify(log => log.Log( LogLevel.Information, It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString() == $"FiaImpactCategory '{createdEvent.Name}' created successfully."),
                It.IsAny<Exception>(), It.IsAny<Func<It.IsAnyType, Exception, string>>()!), Times.Once);
        }

        [Fact(DisplayName = "Handle_Should_Use_Generated_Guid_When_UserId_Is_Empty")]
        public async Task Handle_Should_Use_Generated_Guid_When_UserId_Is_Empty()
        {
            // Arrange
            _mockUserService.Setup(u => u.UserId).Returns(string.Empty);

            var createdEvent = _fixture.FiaImpactCategoryCreatedEvent;

            // Act
            await _handler.Handle(createdEvent, CancellationToken.None);

            // Assert
            var activity = _userActivities.Last();

            Assert.False(string.IsNullOrEmpty(activity.CreatedBy));
            Assert.False(string.IsNullOrEmpty(activity.LastModifiedBy));
            Assert.True(Guid.TryParse(activity.CreatedBy, out _));
            Assert.True(Guid.TryParse(activity.LastModifiedBy, out _));
        }
    }
}
