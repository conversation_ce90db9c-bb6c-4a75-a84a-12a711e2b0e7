﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactCategory.Queries
{
    public class GetFiaImpactCategoryNameUniqueQueryHandlerTests
    {
        private readonly Mock<IFiaImpactCategoryRepository> _mockRepository;
        private readonly GetFiaImpactCategoryNameUniqueQueryHandler _handler;
        private readonly List<Domain.Entities.FiaImpactCategory> _fiaImpactCategories;
        private readonly Fixture _fixture;

        public GetFiaImpactCategoryNameUniqueQueryHandlerTests()
        {
            _fixture = new Fixture();

            // Create sample impact categories
            _fiaImpactCategories = new List<Domain.Entities.FiaImpactCategory>
            {
                new Domain.Entities.FiaImpactCategory
                {
                    Id = 1,
                    Name = "RiskCategory1",
                    ReferenceId = "abc-123"
                },
                new Domain.Entities.FiaImpactCategory
                {
                    Id = 2,
                    Name = "RiskCategory2",
                    ReferenceId = "xyz-789"
                }
            };

            _mockRepository = FiaImpactCategoryRepositoryMocks
                .GetFiaImpactCategoryNameUniqueRepository(_fiaImpactCategories);

            _handler = new GetFiaImpactCategoryNameUniqueQueryHandler(_mockRepository.Object);
        }

        [Fact(DisplayName = "Handle_Should_Return_True_When_Name_Exists_For_Same_ReferenceId")]
        public async Task Handle_Should_Return_True_When_Name_Exists_For_Same_ReferenceId()
        {
            // Arrange
            var query = new GetFiaImpactCategoryNameUniqueQuery
            {
                Name = "RiskCategory1",
                Id = "abc-123"
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result);
        }

        [Fact(DisplayName = "Handle_Should_Return_False_When_Name_Not_Exists_For_Given_ReferenceId")]
        public async Task Handle_Should_Return_False_When_Name_Not_Exists_For_Given_ReferenceId()
        {
            // Arrange
            var query = new GetFiaImpactCategoryNameUniqueQuery
            {
                Name = "UnknownCategory",
                Id = "non-existent-id"
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result);
        }
    }
}
