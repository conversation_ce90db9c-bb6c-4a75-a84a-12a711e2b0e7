﻿using ContinuityPatrol.Application.Features.HacmpCluster.Events.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Events;

public class PaginatedViewHacmpClusterEventTests : IClassFixture<HacmpClusterFixture>, IClassFixture<UserActivityFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly HacmpClusterPaginatedViewEventHandler _handler;

    public PaginatedViewHacmpClusterEventTests(HacmpClusterFixture hacmpClusterFixture, UserActivityFixture userActivityFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Admin");

        var mockHacmpClusterEventLogger = new Mock<ILogger<HacmpClusterPaginatedViewEventHandler>>();

        _mockUserActivityRepository = HacmpClusterRepositoryMocks.CreateHacmpClusterEventRepository(_userActivityFixture.UserActivities);

        _handler = new HacmpClusterPaginatedViewEventHandler(mockHacmpClusterEventLogger.Object, _mockUserActivityRepository.Object, mockLoggedInUserService.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_ViewHacmpClusterEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Admin";

        var result = _handler.Handle(_hacmpClusterFixture.HacmpClusterPaginatedViewEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_hacmpClusterFixture.HacmpClusterPaginatedViewEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}