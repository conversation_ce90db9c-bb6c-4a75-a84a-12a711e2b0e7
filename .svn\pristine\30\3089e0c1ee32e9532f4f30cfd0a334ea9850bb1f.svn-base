﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowProfileInfoService
{
    Task<List<GetWorkflowProfileInfoByProfileIdVm>> GetWorkflowProfileInfoByProfileId(string profileId);
    Task<List<WorkflowProfileInfoNameVm>> GetWorkflowProfileInfoByInfraObjectId(string infraId);
    Task<GetWorkflowProfileInfoByWorkflowIdVm> WorkflowProfileInfoByWorkflowIdExist(string workflowId);
    Task<WorkflowProfileInfoDetailVm> GetByReferenceId(string workflowProfileId);
    Task<BaseResponse> DeleteAsync(string workflowProfileInfoId);
    Task<BaseResponse> UpdateAsync(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfo);
    Task<BaseResponse> CreateAsync(CreateWorkflowProfileInfoCommand createWorkflowProfileInfo);
    Task<bool> IsWorkflowProfileInfoNameExist(string name, string id);
    Task<PaginatedResult<WorkflowProfileInfoListVm>> GetPaginatedWorkflowProfileInfos(GetWorkflowProfileInfoPaginatedListQuery query);
   // Task<PaginatedResult<WorkflowProfileInfoListVm>> GetManagedWorkflowProfileInfos(GetManagedWorkflowProfileInfosQuery query);
   // Task<BaseResponse> UpdateIsFourEye(string id, string isFourEye);
    Task<List<WorkflowProfileInfoNameVm>> WorkflowProfileInfoNames();
}