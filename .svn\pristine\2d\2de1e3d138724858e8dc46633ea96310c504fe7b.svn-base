﻿using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Update;
using ContinuityPatrol.Application.Features.AccessManager.Events.PaginatedView;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class AccessManagerController : BaseController
{
    private readonly ILogger<AccessManagerController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;
    public AccessManagerController(ILogger<AccessManagerController> logger, IMapper mapper, IPublisher publisher, IDataProvider dataProvider)
    {
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _publisher = publisher;
    }
    [EventCode(EventCodes.AccessManager.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in AccessManager");

        try
        {
            await _publisher.Publish(new AccessManagerPaginatedEvent());

            var userRole = await _dataProvider.UserRole.GetUserRoleNames();

            _logger.LogDebug($"Retrieved {userRole.Count} user roles in AccessManager.");

            var userRoles = userRole.Select(x => new SelectListItem
            {
                Value = x.Id,
                Text = x.Role
            }).ToList();

            _logger.LogDebug("Preparing AccessManager view.");

            var result = new AccessManagerViewModel
            {
                UserRoles = userRoles
            };

            _logger.LogDebug("List method completed successfully in AccessManager, returning view.");

            return View(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on access manager page while processing the list request.", ex);

            return View();
        }
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.AccessManager.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(AccessManagerViewModel accessManager)
    {
        var accessId = Request.Form["Id"].ToString();

        _logger.LogDebug("Entering CreateOrUpdate method in AccessManager");

        try
        {
            
            if (accessId.IsNullOrWhiteSpace())
            {
                var accessManagerCommand = _mapper.Map<CreateAccessManagerCommand>(accessManager);

                _logger.LogDebug($"Creating AccessManager for role '{accessManagerCommand.RoleName}'.");
                var result = await _dataProvider.AccessManager.CreateAsync(accessManagerCommand);
                _logger.LogDebug("CreateOrUpdate operation completed successfully in accessManager, returning view.");
                return Json(new { Success = true, data = result.Message });

            }
            else
            {
                var accessManagerCommand = _mapper.Map<UpdateAccessManagerCommand>(accessManager);

                _logger.LogDebug($"Updating AccessManager '{accessManager.UserName}'");

               var result = await _dataProvider.AccessManager.UpdateAsync(accessManagerCommand);
                _logger.LogDebug("CreateOrUpdate operation completed successfully in accessManager, returning view.");
                return Json(new { Success = true, data = result.Message });
              
            }
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation exception on access manager page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on access manager page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.Message);

            return RedirectToAction("List");
        }
    }

    [HttpGet]
    [EventCode(EventCodes.AccessManager.GetUserByRole)]
    public async Task<List<UsersByUserRoleVm>> GetUserByRole(string data)
    {
        _logger.LogDebug("Entering GetUserByRole method in AccessManager");
        try
        {
            var userRole = await _dataProvider.User.GetUserByRole(data);

            _logger.LogDebug($"Successfully retrieved user roles '{data}' in AccessManager");

            return userRole;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on access manager page while getting user by role '{data}'", ex);

            return new List<UsersByUserRoleVm>();
        }
    }

    //public async Task<IActionResult> GetDetails(string userId)
    //{
    //    var accessManager = await _accessManagerService.GetByUserId(userId);
    //    return Json(accessManager);
    //}

    [EventCode(EventCodes.AccessManager.GetRoleDetails)]
    public async Task<IActionResult> GetRoleDetails(string role)
    {
        _logger.LogDebug("Entering GetRoleDetails method in AccessManager");

        try
        {
            var accessManager = await _dataProvider.AccessManager.GetByUserRole(role);

            _logger.LogDebug($"Successfully retrieved role details for '{role}' in AccessManager");

            return Json(accessManager);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on access manager page while getting role details : {role}", ex);

            return Json("");
        }
    }

    [HttpGet]
    [EventCode(EventCodes.AccessManager.GetUserInfraByUser)]
    public async Task<GetUserInfraObjectByUserIdVm> GetUserInfraByUser(string data)
    {
        _logger.LogDebug("Entering GetUserInfraByUser method in AccessManager");
        try
        {
            var userInfra = await _dataProvider.User.GetUserInfraObject(data);
            _logger.LogDebug($"Successfully retrieved infraObject list for user '{userInfra.UserId}' in AccessManager.");
            return userInfra;
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on access manager page wile retrieving infraObject list by users",ex);
            return new GetUserInfraObjectByUserIdVm();
        }
    }
}