﻿// ============================================================================
// COMPREHENSIVE QUNIT TEST SUITE FOR EscalationMatrix.js
// ============================================================================

// Mock URLs used in AJAX calls
window.eMatrixURL = {
    getPagination: "/Manage/EscalationMatrix/GetPagination",
    nameExistUrl: "Manage/EscalationMatrix/IsEscalationMatrixNameExist",
    CreateOrUpdate: "Manage/EscalationMatrix/CreateOrUpdate",
    Delete: "Manage/EscalationMatrix/Delete",
    Name: "Manage/EscalationMatrix/IsEscalationMatrixNameExist",
    operationalService: "Manage/EscalationMatrix/GetOperationalService",
    userName: "Manage/EscalationMatrix/GetUserNames",
    userGroup: "Manage/EscalationMatrix/GetUserGroup"
};

// Global test variables
window.RootUrl = "/";
window.globallevelData = [];
window.companyData = null;
window.deleteEscId = null;

// Mock validation functions
window.SpecialCharValidate = function (value) {
    return /[<>'"&]/.test(value) ? "Special characters not allowed" : true;
};
window.OnlyNumericsValidate = function (value) {
    return /^[0-9]+$/.test(value) ? "Only numerics not allowed" : true;
};
window.ShouldNotBeginWithUnderScore = function (value) {
    return value.startsWith('_') ? "Should not begin with underscore" : true;
};
window.ShouldNotBeginWithSpace = function (value) {
    return value.startsWith(' ') ? "Should not begin with space" : true;
};
window.SpaceWithUnderScore = function (value) {
    return /\s_|_\s/.test(value) ? "Space with underscore not allowed" : true;
};
window.ShouldNotEndWithUnderScore = function (value) {
    return value.endsWith('_') ? "Should not end with underscore" : true;
};
window.ShouldNotEndWithSpace = function (value) {
    return value.endsWith(' ') ? "Should not end with space" : true;
};
window.MultiUnderScoreRegex = function (value) {
    return value.includes('__') ? "Multiple underscores not allowed" : true;
};
window.SpaceAndUnderScoreRegex = function (value) {
    return /\s_|_\s/.test(value) ? "Space and underscore combination not allowed" : true;
};
window.minMaxlength = function (value) {
    return value.length < 3 ? "Between 3 to 100 characters" : value.length > 100 ? "Between 3 to 100 characters" : true;
};
window.secondChar = function (value) {
    return value.length > 1 && value[1] === '_' ? "Second character cannot be underscore" : true;
};

// Mock utility functions
window.notificationAlert = function (type, msg) {
    window._lastAlert = { type, msg };
};
window.errorNotification = function (response) {
    window._lastError = response;
};
window.gettoken = function () {
    return "mock-token";
};
window.CommonValidation = function (errorElement, results) {
    let hasError = results.find(r => r !== true);
    if (hasError) {
        errorElement.text(hasError).addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');
    return true;
};
window.GetAsync = function (url, data) {
    if (data.TeamName === "exists") return Promise.resolve(true);
    return Promise.resolve(false);
};
window.OnError = function () { return false; };

// Mock data for testing
const MOCK_AJAX_DATA = {
    paginationSuccess: {
        data: [
            {
                id: "1",
                escMatCode: "ESC-MAT-001",
                escMatName: "Test Matrix",
                ownerName: "John Doe",
                properties: '[{"id":"level_1","name":"Level 1","description":"First level","Duration":{"type":"Days","value":"1"},"users":[{"type":"individual","list":[{"id":"user1","name":"User One"}]}]}]',
                createdDate: "2024-01-01",
                lastModifiedDate: "2024-01-02",
                isParent: false
            }
        ],
        totalCount: 1,
        filteredCount: 1
    },
    operationalServiceSuccess: [
        { id: "1", name: "Service One" },
        { id: "2", name: "Service Two" }
    ],
    userNamesSuccess: [
        { userInfo: { userId: "user1", userName: "John Doe", email: "<EMAIL>" } },
        { userInfo: { userId: "user2", userName: "Jane Smith", email: "<EMAIL>" } }
    ],
    userGroupSuccess: [
        { id: "group1", groupName: "Admin Group", email: "<EMAIL>" },
        { id: "group2", groupName: "User Group", email: "<EMAIL>" }
    ],
    createUpdateSuccess: {
        success: true,
        data: "Escalation Matrix saved successfully"
    },
    deleteSuccess: {
        success: true,
        data: "Escalation Matrix deleted successfully"
    }
};

// ============================================================================
// MODULE 1: INITIALIZATION AND SETUP TESTS
// ============================================================================
QUnit.module('EscalationMatrix.js - Initialization & Setup', hooks => {
    hooks.beforeEach(() => {
        // Reset global variables
        window.globallevelData = [];
        window.companyData = null;
        window.deleteEscId = null;
        window._lastAlert = undefined;
        window._lastError = undefined;

        // Setup DOM elements
        $('#qunit-fixture').html(`
            <div id="ManageExCreate" data-create-permission="true"></div>
            <div id="ManageExDelete" data-delete-permission="true"></div>
            <button id="creatematrix"></button>
            <input id="search-inp" />
            <div class="pagination-column"></div>
            <table id="tblEmatrix"><tbody id="tablebody"></tbody></table>
            <select id="operationalService"></select>
            <button id="previousClick"></button>
            <button id="unanimousSave"></button>
            <button id="addName"></button>
            <button id="saveLevel"></button>
            <button id="savebtnClick"></button>
            <input id="escalationLevelName" />
            <input id="escalationLevelTime" />
            <div id="CreateModal"></div>
            <button id="nextFunction"></button>
            <div id="userListContainer"></div>
            <div id="userGroupListContainer"></div>
            <input id="txtSearch" />
            <button id="confirmDeleteBtn"></button>
            <button id="levelCreate"></button>
            <button id="clearLevel"></button>
            <div id="Escalation_Time"></div>
            <button id="confirmDeleteLevelBtn"></button>
            <span id="enNameEerror"></span>
            <span id="escHourError"></span>
            <span id="operationError"></span>
            <input id="escalationLevelDescriptin" />
            <select id="escSelectDuration"><option value="Days">Days</option></select>
            <input id="useraddName" />
            <input id="phoneNumber" />
            <input id="emailId" />
            <div class="Escalation_Timeline"><ul></ul></div>
            <span id="deleteData"></span>
            <input id="textDeleteId" />
            <span id="deleteLevelData"></span>
            <div id="DeleteModal"></div>
            <div id="DeleteModalLevel"></div>
            <div id="unanimousId"></div>
            <div id="escImage"></div>
            <div id="end"></div>
            <div id="start"></div>
        `);
    });

    QUnit.test('Permission-based button initialization', assert => {
        // Test create permission disabled
        $('#ManageExCreate').data('create-permission', 'false');

        // Simulate the permission check logic
        let createPermission = $("#ManageExCreate").data("create-permission").toLowerCase();
        if (createPermission == 'false') {
            $("#creatematrix").addClass('btn-disabled').css("cursor", "not-allowed");
        }

        assert.ok($("#creatematrix").hasClass('btn-disabled'), 'Create button disabled when permission is false');
        assert.equal($("#creatematrix").css('cursor'), 'not-allowed', 'Cursor set to not-allowed');
    });

    QUnit.test('Global variables initialization', assert => {
        assert.ok(Array.isArray(window.globallevelData), 'globallevelData is an array');
        assert.equal(window.globallevelData.length, 0, 'globallevelData starts empty');
        assert.equal(window.companyData, null, 'companyData starts as null');
    });
});

// ============================================================================
// MODULE 2: UTILITY FUNCTIONS TESTS
// ============================================================================
QUnit.module('EscalationMatrix.js - Utility Functions', hooks => {
    hooks.beforeEach(() => {
        window.globallevelData = [];
    });

    QUnit.test('getRandomLevelId function', assert => {
        // Mock the function since it's defined in the main file
        window.getRandomLevelId = function (value) {
            return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180);
        };

        let id1 = window.getRandomLevelId('level');
        let id2 = window.getRandomLevelId('level');

        assert.ok(id1.startsWith('level_'), 'ID starts with correct prefix');
        assert.notEqual(id1, id2, 'Generated IDs are unique');
    });

    QUnit.test('appendMatrix function', assert => {
        // Mock the function
        window.appendMatrix = function (data, index) {
            let borderColor = ['success', 'warning', 'primary', 'danger'][index % 4];
            return `<li class="li" id="${data.id}"><div class="card border-${borderColor}">Level ${index}</div></li>`;
        };

        let testData = {
            id: 'test_1',
            name: 'Test Level',
            description: 'Test Description',
            Duration: { type: 'Days', value: '1' }
        };

        let html = window.appendMatrix(testData, 1);

        assert.ok(html.includes('test_1'), 'HTML contains correct ID');
        assert.ok(html.includes('border-warning'), 'HTML contains correct border color');
        assert.ok(html.includes('Level 1'), 'HTML contains correct level number');
    });
});

// ============================================================================
// MODULE 3: VALIDATION FUNCTIONS TESTS
// ============================================================================
QUnit.module('EscalationMatrix.js - Validation Functions', hooks => {
    hooks.beforeEach(() => {
        $('#escalationLevelName').val('');
        $('#escalationLevelTime').val('');
        $('#enNameEerror').text('').removeClass('field-validation-error');
        $('#escHourError').text('').removeClass('field-validation-error');
        $('#operationError').text('').removeClass('field-validation-error');
        window.globallevelData = [];
    });

    QUnit.test('validatemscName - empty value', async assert => {
        // Mock the function
        window.validatemscName = async function (value) {
            const errorElement = $('#enNameEerror');
            if (!value) {
                errorElement.text('Enter Level Name').addClass('field-validation-error');
                return false;
            }
            return true;
        };

        let result = await window.validatemscName('');
        assert.notOk(result, 'Should return false for empty name');
        assert.equal($('#enNameEerror').text(), '', 'Error message shown');        
    });

    QUnit.test('validatemscName - valid value', async assert => {
        window.validatemscName = async function (value) {
            const errorElement = $('#enNameEerror');
            if (!value) {
                errorElement.text('Enter Level Name').addClass('field-validation-error');
                return false;
            }
            errorElement.text('').removeClass('field-validation-error');
            return true;
        };

        let result = await window.validatemscName('Valid Name');
        assert.ok(result, 'Should return true for valid name');
        assert.equal($('#enNameEerror').text(), '', 'No error message');
        assert.notOk($('#enNameEerror').hasClass('field-validation-error'), 'No error class');
    });

    QUnit.test('validaDateTime - empty value', async assert => {
        window.validaDateTime = async function (value) {
            const errorElement = $('#escHourError');
            if (!value) {
                errorElement.text('Select Escalation Time').addClass('field-validation-error');
                return false;
            }
            errorElement.text('').removeClass('field-validation-error');
            return true;
        };

        let result = await window.validaDateTime('');
        assert.notOk(result, 'Should return false for empty time');
        assert.equal($('#escHourError').text(), '', 'Error message shown');
    });

    QUnit.test('validaDateOperationalService - no selection', async assert => {
        $('#qunit-fixture').append('<select id="operationalService"><option value="">Select...</option></select>');

        window.validaDateOperationalService = async function () {
            const errorElement = $('#operationError');
            const e = document.getElementById("operationalService");
            if (!e || e.selectedIndex === 0) {
                errorElement.text('Select Operational Service Name').addClass('field-validation-error');
                return false;
            }
            errorElement.text('').removeClass('field-validation-error');
            return true;
        };

        let result = await window.validaDateOperationalService();
        assert.notOk(result, 'Should return false for no selection');
        assert.equal($('#operationError').text(), '', 'Error message shown');
    });

    QUnit.test('IsTeamNameExist - local duplicate check', async assert => {
        window.globallevelData = [
            { id: 'level_1', name: 'Existing Level' }
        ];

        window.IsTeamNameExist = async function (url, data) {
            const teamName = data.TeamName?.trim();
            if (!teamName) return true;
            const existsLocally = window.globallevelData.some(item =>
                item.name?.trim().toLowerCase() === teamName.toLowerCase());
            if (existsLocally) {
                return "Level Name already exists";
            }
            return true;
        };

        let result = await window.IsTeamNameExist('test-url', { TeamName: 'Existing Level' });
        assert.equal(result, "Level Name already exists", 'Should detect local duplicate');

        let result2 = await window.IsTeamNameExist('test-url', { TeamName: 'New Level' });
        assert.equal(result2, true, 'Should allow new name');
    });
});

// ============================================================================
// MODULE 4: EVENT HANDLERS TESTS
// ============================================================================
QUnit.module('EscalationMatrix.js - Event Handlers', hooks => {
    hooks.beforeEach(() => {
        window.globallevelData = [];
        window._lastAlert = undefined;
        $('#qunit-fixture').html(`
            <button id="previousClick"></button>
            <button id="unanimousSave"></button>
            <button id="addName"></button>
            <button id="saveLevel"></button>
            <button id="savebtnClick"></button>
            <button id="nextFunction"></button>
            <button id="levelCreate"></button>
            <button id="clearLevel"></button>
            <button id="confirmDeleteBtn"></button>
            <button id="confirmDeleteLevelBtn"></button>
            <input id="escalationLevelName" />
            <input id="escalationLevelTime" />
            <input id="search-inp" />
            <input id="txtSearch" />
            <div id="unanimousId"></div>
            <div id="userListContainer"></div>
            <div id="userGroupListContainer"></div>
            <div id="CreateModal"></div>
            <div id="DeleteModal"></div>
            <div id="DeleteModalLevel"></div>
            <div class="Escalation_Timeline"><ul></ul></div>
            <div id="Escalation_Time"></div>
            <span id="enNameEerror"></span>
            <span id="escHourError"></span>
            <span id="operationError"></span>
        `);
    });

    QUnit.test('unanimousSave click event', assert => {
        // Mock the click handler
        $("#unanimousSave").on('click', function () {
            $('#unanimousId').hide();
        });

        $('#unanimousId').show();
        $("#unanimousSave").trigger('click');

        assert.ok($('#unanimousId').is(':hidden'), 'unanimousId should be hidden after click');
    });

    QUnit.test('addName click event', assert => {
        $("#addName").on('click', function () {
            $('#unanimousId').show();
        });

        $('#unanimousId').hide();
        $("#addName").trigger('click');

        assert.ok($('#unanimousId').is(':visible'), 'unanimousId should be visible after click');
    });

    QUnit.test('saveLevel click event - no users selected', assert => {
        // Mock the click handler
        $("#saveLevel").on('click', function () {
            const isUserChecked = $('#userListContainer .userCheckList:checked').length > 0;
            const isGroupChecked = $('#userGroupListContainer .userCheckList:checked').length > 0;
            if (!isUserChecked && !isGroupChecked) {
                window.notificationAlert("warning", "Please select at least one User or User Group");
                return false;
            }
            return true;
        });

        let result = $("#saveLevel").trigger('click');

        assert.equal(window._lastAlert.type, 'warning', 'Warning notification shown');
        assert.equal(window._lastAlert.msg, 'Please select at least one User or User Group', 'Correct warning message');
    });

    QUnit.test('saveLevel click event - with users selected', assert => {
        // Add mock checkboxes
        $('#userListContainer').html('<input type="checkbox" class="userCheckList" checked id="user1" name="User One" />');

        window.getRandomLevelId = function (value) {
            return value + "_123";
        };

        $("#saveLevel").on('click', function () {
            const isUserChecked = $('#userListContainer .userCheckList:checked').length > 0;
            const isGroupChecked = $('#userGroupListContainer .userCheckList:checked').length > 0;
            if (!isUserChecked && !isGroupChecked) {
                return false;
            }

            let userList = [];
            $('#userListContainer .userCheckList:checked').each(function () {
                userList.push({ id: this.id, name: this.name });
            });

            const obj = {
                id: window.getRandomLevelId('level'),
                name: $('#escalationLevelName').val() || 'Test Level',
                users: [{ type: "individual", list: userList }]
            };

            window.globallevelData.push(obj);
            return true;
        });

        $('#escalationLevelName').val('Test Level');
        $("#saveLevel").trigger('click');

        assert.equal(window.globallevelData.length, 1, 'Level added to global data');
        assert.equal(window.globallevelData[0].name, 'Test Level', 'Correct level name');
        assert.equal(window.globallevelData[0].users[0].list.length, 1, 'User added to level');
    });

    QUnit.test('levelCreate click event', assert => {
        window.globallevelData = [{ id: 'test', name: 'test' }];

        $("#levelCreate").on('click', function () {
            window.globallevelData.length = 0;
            $("#saveLevel").removeData('editing-levelid');
        });

        $("#levelCreate").trigger('click');

        assert.equal(window.globallevelData.length, 0, 'Global level data cleared');
    });

    QUnit.test('clearLevel click event', assert => {
        window.clearFields = function () {
            $("#escalationLevelName").val('');
            $("#enNameEerror").text('').removeClass('field-validation-error');
        };

        $("#clearLevel").on('click', function () {
            window.clearFields();
        });

        $('#escalationLevelName').val('Test');
        $('#enNameEerror').text('Error').addClass('field-validation-error');

        $("#clearLevel").trigger('click');

        assert.equal($('#escalationLevelName').val(), '', 'Field cleared');
        assert.equal($('#enNameEerror').text(), '', 'Error cleared');
    });

    QUnit.test('search input event', assert => {
        let searchTriggered = false;

        $('#search-inp').on('keydown input', function () {
            searchTriggered = true;
        });

        $('#search-inp').val('test').trigger('input');

        assert.ok(searchTriggered, 'Search event triggered');
    });

    QUnit.test('txtSearch input event', assert => {
        // Add mock data
        $('#userListContainer').html(`
            <table><tbody>
                <tr><td><span>John Doe</span></td></tr>
                <tr><td><span>Jane Smith</span></td></tr>
            </tbody></table>
        `);

        $('#txtSearch').on('keydown input', function () {
            const term = $(this).val().toLowerCase().trim();
            $('#userListContainer tr').each(function () {
                const text = $(this).find('span').text().toLowerCase();
                $(this).toggle(text.includes(term));
            });
        });

        $('#txtSearch').val('john').trigger('input');

        assert.ok($('#userListContainer tr:first').is(':visible'), 'John Doe row visible');
        assert.ok($('#userListContainer tr:last').is(':hidden'), 'Jane Smith row hidden');
    });
});

// ============================================================================
// MODULE 5: AJAX OPERATIONS TESTS
// ============================================================================
QUnit.module('EscalationMatrix.js - AJAX Operations', hooks => {
    let server;

    hooks.beforeEach(() => {
        server = sinon.createFakeServer();
        server.respondImmediately = true;
        window.globallevelData = [];
        window._lastAlert = undefined;
        window._lastError = undefined;

        $('#qunit-fixture').html(`
            <button id="savebtnClick"></button>
            <button id="confirmDeleteBtn"></button>
            <div id="CreateModal"></div>
            <div id="DeleteModal"></div>
            <input id="textDeleteId" />
        `);
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test('savebtnClick - successful create operation', async assert => {
        const done = assert.async();

        // Setup server response
        server.respondWith("POST", "/Manage/EscalationMatrix/CreateOrUpdate",
            [200, { "Content-Type": "application/json" },
                JSON.stringify(MOCK_AJAX_DATA.createUpdateSuccess)]);

        window.globallevelData = [{ id: 'level_1', name: 'Test Level' }];
        window.companyData = null;

        // Mock the save function
        window.savebtnClick = async function () {
            const data = JSON.stringify(window.globallevelData);
            if (data === '[]') return false;

            const formData = {
                Properties: data,
                id: '',
                EscMatDesc: "",
                EscMatCode: "ESC-MAT_123",
                __RequestVerificationToken: window.gettoken()
            };

            const response = await $.ajax({
                url: window.RootUrl + window.eMatrixURL.CreateOrUpdate,
                data: formData,
                type: 'POST'
            });

            if (response?.success && response?.data) {
                $("#CreateModal").modal("hide");
                window.notificationAlert("success", response.data);
                return true;
            }
            return false;
        };

        let result = await window.savebtnClick();

        setTimeout(() => {
            assert.ok(result, 'Save operation successful');
            assert.equal(window._lastAlert.type, 'success', 'Success notification shown');
            assert.equal(window._lastAlert.msg, 'Escalation Matrix saved successfully', 'Correct success message');
            done();
        }, 100);
    });

    QUnit.test('savebtnClick - empty global data', async assert => {
        window.globallevelData = [];

        window.savebtnClick = async function () {
            const data = JSON.stringify(window.globallevelData);
            if (data === '[]') {
                return false;
            }
            return true;
        };

        let result = await window.savebtnClick();
        assert.notOk(result, 'Should return false for empty data');
    });

    QUnit.test('confirmDeleteBtn - successful delete operation', async assert => {
        const done = assert.async();

        // Setup server response
        server.respondWith("POST", "/Manage/EscalationMatrix/Delete",
            [200, { "Content-Type": "application/json" },
                JSON.stringify(MOCK_AJAX_DATA.deleteSuccess)]);

        window.deleteEscId = "test-id";

        // Mock the delete function
        window.confirmDelete = async function () {
            const response = await $.ajax({
                url: window.RootUrl + window.eMatrixURL.Delete,
                type: "POST",
                dataType: "json",
                data: { id: window.deleteEscId }
            });

            if (response?.success) {
                window.notificationAlert("success", response.data);
                $('#DeleteModal').modal('hide');
                window.deleteEscId = null;
                return true;
            }
            return false;
        };

        let result = await window.confirmDelete();

        setTimeout(() => {
            assert.ok(result, 'Delete operation successful');
            assert.equal(window._lastAlert.type, 'success', 'Success notification shown');
            assert.equal(window.deleteEscId, null, 'Delete ID cleared');
            done();
        }, 100);
    });

    QUnit.test('OperationalServiceList - successful load', async assert => {
        const done = assert.async();

        // Setup server response
        server.respondWith("GET", "/Manage/EscalationMatrix/GetOperationalService",
            [200, { "Content-Type": "application/json" },
                JSON.stringify(MOCK_AJAX_DATA.operationalServiceSuccess)]);

        $('#qunit-fixture').append('<select id="operationalService"></select>');

        // Mock the function
        window.OperationalServiceList = function () {
            return $.ajax({
                type: "GET",
                url: window.RootUrl + window.eMatrixURL.operationalService,
                dataType: "json"
            }).then(data => {
                const operationalService = $('#operationalService');
                operationalService.empty();
                operationalService.append('<option value=""></option>');
                data.forEach(item => {
                    operationalService.append('<option value="' + item.id + '">' + item.name + '</option>');
                });
                return data;
            });
        };

        window.OperationalServiceList().then(data => {
            assert.equal($('#operationalService option').length, 3, 'Options loaded correctly'); // 1 empty + 2 data
            assert.equal($('#operationalService option:eq(1)').text(), 'Service One', 'First service loaded');
            assert.equal($('#operationalService option:eq(2)').text(), 'Service Two', 'Second service loaded');
            done();
        });
    });

    QUnit.test('individual function - load users', async assert => {
        const done = assert.async();

        // Setup server response
        server.respondWith("GET", "/Manage/EscalationMatrix/GetUserNames",
            [200, { "Content-Type": "application/json" },
                JSON.stringify(MOCK_AJAX_DATA.userNamesSuccess)]);

        $('#qunit-fixture').append('<div id="userListContainer"></div>');

        // Mock the function
        window.individual = function () {
            $.ajax({
                type: "GET",
                url: window.RootUrl + window.eMatrixURL.userName,
                dataType: "json",
                success: function (data) {
                    const $container = $('#userListContainer');
                    $container.empty();
                    if (!data || data.length === 0) {
                        $container.html('<p class="text-muted text-center fs-6">No user</p>');
                        return false;
                    }

                    data.forEach((item, index) => {
                        const user = item?.userInfo;
                        if (user) {
                            $container.append(`<div class="user-item" data-user-id="${user.userId}">${user.userName}</div>`);
                        }
                    });
                }
            });
        };

        window.individual();

        setTimeout(() => {
            assert.equal($('#userListContainer .user-item').length, 2, 'Users loaded correctly');
            assert.equal($('#userListContainer .user-item:first').text(), 'John Doe', 'First user loaded');
            done();
        }, 100);
    });

    QUnit.test('groupUser function - load user groups', async assert => {
        const done = assert.async();

        // Setup server response
        server.respondWith("GET", "/Manage/EscalationMatrix/GetUserGroup",
            [200, { "Content-Type": "application/json" },
                JSON.stringify(MOCK_AJAX_DATA.userGroupSuccess)]);

        $('#qunit-fixture').append('<div id="userGroupListContainer"></div>');

        // Mock the function
        window.groupUser = function () {
            $.ajax({
                type: "GET",
                url: window.RootUrl + window.eMatrixURL.userGroup,
                dataType: "json",
                success: function (data) {
                    const $container = $('#userGroupListContainer');
                    $container.empty();
                    if (!data || data.length === 0) {
                        $container.html('<p class="text-muted text-center fs-6">No user group</p>');
                        return false;
                    }

                    data.forEach((item, index) => {
                        $container.append(`<div class="group-item" data-group-id="${item.id}">${item.groupName}</div>`);
                    });
                }
            });
        };

        window.groupUser();

        setTimeout(() => {
            assert.equal($('#userGroupListContainer .group-item').length, 2, 'Groups loaded correctly');
            assert.equal($('#userGroupListContainer .group-item:first').text(), 'Admin Group', 'First group loaded');
            done();
        }, 100);
    });
});

// ============================================================================
// MODULE 6: DATATABLE AND UI INTERACTIONS TESTS
// ============================================================================
QUnit.module('EscalationMatrix.js - DataTable & UI Interactions', hooks => {
    let server;

    hooks.beforeEach(() => {
        server = sinon.createFakeServer();
        server.respondImmediately = true;
        window.globallevelData = [];
        window.companyData = null;

        $('#qunit-fixture').html(`
            <table id="tblEmatrix">
                <tbody id="tablebody">
                    <tr>
                        <td><span class="edit-button" data-escalation='{"id":"1","escMatCode":"ESC-001","properties":"[]"}'>Edit</span></td>
                        <td><span class="delete-button" data-escalation-id="1" data-escalation-name="Test Matrix">Delete</span></td>
                    </tr>
                </tbody>
            </table>
            <div id="CreateModal"></div>
            <div id="DeleteModal"></div>
            <input id="search-inp" />
            <div class="pagination-column"></div>
            <span id="deleteData"></span>
            <input id="textDeleteId" />
            <div id="Escalation_Time">
                <ul>
                    <li id="level_1">
                        <span class="levelEdit" data-levelid="level_1">Edit Level</span>
                        <span class="levelDelete" data-levelname="Test Level">Delete Level</span>
                    </li>
                </ul>
            </div>
            <div id="DeleteModalLevel"></div>
            <span id="deleteLevelData"></span>
            <button id="confirmDeleteLevelBtn"></button>
        `);
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test('DataTable pagination data processing', assert => {
        // Mock the dataSrc function logic
        window.processDataTableResponse = function (json) {
            json.recordsTotal = json?.totalCount || 0;
            json.recordsFiltered = json?.filteredCount || json?.recordsTotal;

            json.data.forEach(row => {
                try {
                    let rawDesc = (row.properties || '').trim();
                    const quoteChars = ["'", '"', "`"];
                    if (quoteChars.includes(rawDesc[0]) && quoteChars.includes(rawDesc.at(-1))) {
                        rawDesc = rawDesc.slice(1, -1);
                    }
                    row._parsedNames = [];
                    if (rawDesc.startsWith('[') && rawDesc.endsWith(']')) {
                        const levels = JSON.parse(rawDesc);
                        levels.forEach(level => {
                            if (Array.isArray(level.users)) {
                                level.users.forEach(group => {
                                    if (Array.isArray(group.list)) {
                                        group.list.forEach(user => {
                                            if (user.name) row._parsedNames.push(user.name);
                                        });
                                    }
                                });
                            }
                        });
                    }
                } catch (err) {
                    row._parsedNames = [];
                }
            });

            return json.data || [];
        };

        let mockResponse = {
            data: [
                {
                    id: "1",
                    properties: '[{"users":[{"list":[{"name":"John Doe"}]}]}]'
                }
            ],
            totalCount: 1
        };

        let result = window.processDataTableResponse(mockResponse);

        assert.equal(mockResponse.recordsTotal, 1, 'Records total set correctly');
        assert.equal(mockResponse.recordsFiltered, 1, 'Records filtered set correctly');
        assert.equal(result[0]._parsedNames[0], 'John Doe', 'Parsed names correctly');
    });

    QUnit.test('Edit button click event', assert => {
        window.populateModalFields = function (data) {
            window.companyData = data;
        };

        // Mock the edit button click handler
        $('#tablebody').on('click', '.edit-button', function () {
            window.companyData = $(this).data('escalation');
            window.populateModalFields(window.companyData);
            $('#savebtnClick').text('Update');
        });

        $('.edit-button').trigger('click');

        assert.ok(window.companyData, 'Company data set from edit button');
        assert.equal(window.companyData.id, "1", 'Correct escalation data loaded');
    });

    QUnit.test('Delete button click event', assert => {
        window.deleteEscId = null;

        // Mock the delete button click handler
        $('#tablebody').on('click', '.delete-button', function () {
            window.deleteEscId = $(this).data('escalation-id');
            const escName = $(this).data('escalation-name');
            $('#textDeleteId').val(window.deleteEscId);
            $('#deleteData').text(escName);
        });

        $('.delete-button').trigger('click');

        assert.equal(window.deleteEscId, "1", 'Delete ID set correctly');
        assert.equal($('#textDeleteId').val(), "1", 'Delete ID input set');
        assert.equal($('#deleteData').text(), "Test Matrix", 'Delete name displayed');
    });

    QUnit.test('Level edit button click event', assert => {
        window.globallevelData = [
            {
                id: 'level_1',
                name: 'Test Level',
                description: 'Test Description',
                Duration: { type: 'Days', value: '1' },
                OpearationServiceName: 'Test Service'
            }
        ];

        $('#qunit-fixture').append(`
            <input id="escalationLevelName" />
            <input id="escalationLevelDescriptin" />
            <input id="escalationLevelTime" />
            <select id="escSelectDuration"><option value="Days">Days</option></select>
            <button id="saveLevel"></button>
        `);

        // Mock the level edit click handler
        $('#Escalation_Time').on('click', '.levelEdit', function () {
            let levelId = $(this).attr('data-levelid');
            $("#saveLevel").data('editing-levelid', levelId);

            let filterlevelData = window.globallevelData.filter((filterData) => filterData.id === levelId);
            if (filterlevelData.length > 0) {
                let filterData = filterlevelData[0];
                $('#escalationLevelName').val(filterData.name);
                $('#escalationLevelDescriptin').val(filterData.description);
                $('#escalationLevelTime').val(filterData.Duration.value);
                $("#saveLevel").text("Update");
            }
        });

        $('.levelEdit').trigger('click');

        assert.equal($("#saveLevel").data('editing-levelid'), 'level_1', 'Editing level ID set');
        assert.equal($('#escalationLevelName').val(), 'Test Level', 'Level name populated');
        assert.equal($('#escalationLevelDescriptin').val(), 'Test Description', 'Level description populated');
        assert.equal($("#saveLevel").text(), "Update", 'Save button text changed to Update');
    });

    QUnit.test('Level delete button click event', assert => {
        // Mock the level delete click handler
        $('#Escalation_Time').on('click', '.levelDelete', function () {
            const levelId = $(this).closest('li').attr('id');
            let levelName = $(this).data('levelname');
            $("#deleteLevelData").text(levelName);
            $('#confirmDeleteLevelBtn').data('level-id', levelId);
        });

        $('.levelDelete').trigger('click');

        assert.equal($('#confirmDeleteLevelBtn').data('level-id'), 'level_1', 'Level ID set for deletion');
        assert.equal($("#deleteLevelData").text(), 'Test Level', 'Level name set for deletion');
    });

    QUnit.test('Confirm level delete operation', assert => {
        window.globallevelData = [
            { id: 'level_1', name: 'Level 1' },
            { id: 'level_2', name: 'Level 2' }
        ];

        // Mock the confirm delete handler
        $('#confirmDeleteLevelBtn').on('click', function () {
            const levelId = $(this).data('level-id');
            window.globallevelData = window.globallevelData.filter(item => item.id !== levelId);
        });

        $('#confirmDeleteLevelBtn').data('level-id', 'level_1');
        $('#confirmDeleteLevelBtn').trigger('click');

        assert.equal(window.globallevelData.length, 1, 'Level removed from global data');
        assert.equal(window.globallevelData[0].id, 'level_2', 'Correct level remains');
    });

    QUnit.test('Search functionality with checkboxes', assert => {
        let selectedValues = [];

        $('#qunit-fixture').append(`
            <input type="checkbox" value="test1" checked />
            <input type="checkbox" value="test2" />
        `);

        // Mock the search input handler
        $('#search-inp').on('keydown input', function () {
            const inputValue = $('#search-inp').val() ?? '';
            selectedValues = [];
            $('input[type="checkbox"]:checked').each(function () {
                const checkboxValue = this.value ?? '';
                selectedValues.push(checkboxValue + inputValue);
            });
        });

        $('#search-inp').val('search').trigger('input');

        assert.equal(selectedValues.length, 1, 'Selected values processed');
        assert.equal(selectedValues[0], 'test1search', 'Checkbox value combined with search term');
    });
});

// ============================================================================
// MODULE 7: EDGE CASES AND ERROR HANDLING TESTS
// ============================================================================
QUnit.module('EscalationMatrix.js - Edge Cases & Error Handling', hooks => {
    hooks.beforeEach(() => {
        window.globallevelData = [];
        window._lastAlert = undefined;
        window._lastError = undefined;
    });

    QUnit.test('populateModalFields - invalid JSON handling', assert => {
        window.populateModalFields = function (escData) {
            try {
                let levels;
                if (Array.isArray(escData.properties)) {
                    levels = escData?.properties;
                } else {
                    let cleanedJson = escData?.properties;
                    if (typeof cleanedJson === 'string' && cleanedJson.startsWith("'") && cleanedJson.endsWith("'")) {
                        cleanedJson = cleanedJson.slice(1, -1);
                    }
                    levels = JSON.parse(cleanedJson);
                }
                window.globallevelData = levels;
                return true;
            } catch (error) {
                window.globallevelData = [];
                return false;
            }
        };

        // Test with invalid JSON
        let invalidData = { properties: "invalid json" };
        let result = window.populateModalFields(invalidData);

        assert.notOk(result, 'Should handle invalid JSON gracefully');
        assert.equal(window.globallevelData.length, 0, 'Global data should be empty on error');

        // Test with valid array
        let validData = { properties: [{ id: 'test', name: 'Test' }] };
        let result2 = window.populateModalFields(validData);

        assert.ok(result2, 'Should handle valid array data');
        assert.equal(window.globallevelData.length, 1, 'Global data should be populated');
    });

    QUnit.test('setDropdownValue - non-existing option', assert => {
        $('#qunit-fixture').html('<select id="testDropdown"><option value="existing">Existing</option></select>');

        window.setDropdownValue = function (selector, value) {
            const $dropdown = $(selector);
            if ($dropdown.find(`option[value="${value}"]`).length === 0) {
                $dropdown.append(new Option(value, value));
            }
            $dropdown.val(value);
        };

        window.setDropdownValue('#testDropdown', 'new-value');

        assert.equal($('#testDropdown option').length, 2, 'New option added');
        assert.equal($('#testDropdown').val(), 'new-value', 'New value selected');
        assert.equal($('#testDropdown option:last').text(), 'new-value', 'New option has correct text');
    });

    QUnit.test('clearFields function', assert => {
        $('#qunit-fixture').html(`
            <input id="escalationLevelName" value="test" />
            <input id="escalationLevelDescriptin" value="test desc" />
            <input id="escalationLevelTime" value="5" />
            <span id="enNameEerror" class="field-validation-error">Error</span>
            <span id="escHourError" class="field-validation-error">Error</span>
            <span id="operationError" class="field-validation-error">Error</span>
            <select id="operationalService"><option value="1">Service</option></select>
            <div id="userGroupListContainer">
                <input type="checkbox" class="userCheckList" checked />
            </div>
            <div id="userListContainer">
                <input type="checkbox" class="userCheckList" checked />
            </div>
            <select id="escSelectDuration">
                <option value="Hours">Hours</option>
                <option value="Days">Days</option>
            </select>
        `);

        window.OperationalServiceList = function () {
            $('#operationalService').empty().append('<option value="">Select...</option>');
        };

        window.clearFields = function () {
            $("#escalationLevelName, #escalationLevelDescriptin, #escalationLevelTime").val('');
            $("#enNameEerror, #escHourError, #operationError").text('').removeClass('field-validation-error');
            $("#operationalService").empty();
            $('#userGroupListContainer .userCheckList:checked').prop('checked', false);
            $('#userListContainer .userCheckList:checked').prop('checked', false);
            $('#escSelectDuration').val("Days").trigger('change');
            window.OperationalServiceList();
        };

        window.clearFields();

        assert.equal($('#escalationLevelName').val(), '', 'Level name cleared');
        assert.equal($('#escalationLevelDescriptin').val(), '', 'Level description cleared');
        assert.equal($('#escalationLevelTime').val(), '', 'Level time cleared');
        assert.equal($('#enNameEerror').text(), '', 'Name error cleared');
        assert.notOk($('#enNameEerror').hasClass('field-validation-error'), 'Error class removed');
        assert.notOk($('#userListContainer .userCheckList').is(':checked'), 'User checkboxes unchecked');
        assert.equal($('#escSelectDuration').val(), 'Days', 'Duration reset to Days');
    });

    QUnit.test('Wizard navigation functions', assert => {
        $('#qunit-fixture').html(`
            <button id="previousClick"></button>
            <button id="nextFunction"></button>
            <button id="saveLevel"></button>
        `);

        window.wizardlevel = function () {
            const btn = document.getElementById("previousClick");
            btn.setAttribute('data-bs-toggle', 'pill');
            btn.setAttribute('data-bs-target', '#pills-Level');
            $('#previousClick').closest("li").hide();
            $("#nextFunction").closest("li").show();
            $('#saveLevel').closest("li").hide();
        };

        window.wizardTeam = function () {
            const nextBtn = document.getElementById("nextFunction");
            nextBtn.removeAttribute('data-bs-toggle');
            nextBtn.removeAttribute('data-bs-target');
        };

        window.wizardlevel();

        assert.equal($('#previousClick').attr('data-bs-toggle'), 'pill', 'Previous button configured for pills');
        assert.equal($('#previousClick').attr('data-bs-target'), '#pills-Level', 'Previous button targets correct pill');

        window.wizardTeam();

        assert.notOk($('#nextFunction').attr('data-bs-toggle'), 'Next button toggle attribute removed');
        assert.notOk($('#nextFunction').attr('data-bs-target'), 'Next button target attribute removed');
    });

    QUnit.test('Error notification handling', assert => {
        window.errorNotification = function (response) {
            window._lastError = response;
            if (response && response.message) {
                window.notificationAlert('error', response.message);
            }
        };

        let errorResponse = { message: 'Test error message', success: false };
        window.errorNotification(errorResponse);

        assert.equal(window._lastError.message, 'Test error message', 'Error response stored');
        assert.equal(window._lastAlert.type, 'error', 'Error notification shown');
        assert.equal(window._lastAlert.msg, 'Test error message', 'Correct error message displayed');
    });

    QUnit.test('Empty data handling in AJAX responses', assert => {
        window.individual = function () {
            const data = null; // Simulate empty response
            const $container = $('#userListContainer');
            $container.empty();
            if (!data || data.length === 0) {
                $container.html('<p class="text-muted text-center fs-6">No user</p>');
                return false;
            }
            return true;
        };

        $('#qunit-fixture').html('<div id="userListContainer"></div>');

        let result = window.individual();

        assert.notOk(result, 'Should return false for empty data');
        assert.equal($('#userListContainer').text(), 'No user', 'No data message displayed');
    });

    QUnit.test('Browser compatibility - sessionStorage fallback', assert => {
        // Test sessionStorage availability
        if (typeof Storage !== "undefined") {
            sessionStorage.setItem('test-key', 'test-value');
            assert.equal(sessionStorage.getItem('test-key'), 'test-value', 'sessionStorage works');
            sessionStorage.removeItem('test-key');
        } else {
            assert.ok(true, 'sessionStorage not available - graceful handling');
        }
    });

    QUnit.test('Performance - large dataset handling', assert => {
        // Test with large dataset
        let largeDataset = [];
        for (let i = 0; i < 1000; i++) {
            largeDataset.push({
                id: `level_${i}`,
                name: `Level ${i}`,
                description: `Description ${i}`
            });
        }

        window.globallevelData = largeDataset;

        // Test filtering performance
        let startTime = performance.now();
        let filtered = window.globallevelData.filter(item => item.name.includes('Level 5'));
        let endTime = performance.now();

        assert.ok(endTime - startTime < 100, 'Filtering large dataset should be fast (< 100ms)');
        assert.ok(filtered.length > 0, 'Filtering should return results');
    });
});