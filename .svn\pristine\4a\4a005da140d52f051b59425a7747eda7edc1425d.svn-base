﻿using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Application.Features.DataSet.Event.PaginatedView;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;


namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class DataSetController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<DataSetController> _logger;
    private readonly IConfiguration _config;

    public DataSetController(IPublisher publisher, ILogger<DataSetController> logger, IDataProvider dataProvider, IMapper mapper, IConfiguration config)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
        _mapper = mapper;
        _config = config;
    }

    [EventCode(EventCodes.DataSet.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in DataSet");

        await _publisher.Publish(new DataSetPaginatedEvent());

        var schemaNames = await _dataProvider.TableAccess.GetSchemaNames();

        var schema = schemaNames.Select(x => new SelectListItem
        {
            Value = x.Id,
            Text = x.SchemaName

        }).ToList();

        var dataSetModel = new DataSetModel
        {
            //PaginatedDataSet = dataSetView,
            SchemaList = schema
        };

        return View(dataSetModel);
    }

    [HttpPost]
    [AntiXss]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    [ValidateAntiForgeryToken]
    [EventCode(EventCodes.DataSet.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(DataSetModel dataSetModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in DataSet");

        var id = dataSetModel.Id;
        try
        {
            if (id.IsNullOrWhiteSpace())
            {
                var createCommand = _mapper.Map<CreateDataSetCommand>(dataSetModel);
                var result = await _dataProvider.DataSet.CreateAsync(createCommand);
                _logger.LogDebug($"Creating DataSet '{createCommand.DataSetName}'.");
                _logger.LogDebug("CreateOrUpdate operation completed successfully in DataSet, returning view.");
                return Json(new { Success = true, data = result.Message });
            }
            else
            {
                var updateCommand = _mapper.Map<UpdateDataSetCommand>(dataSetModel);
                var result = await _dataProvider.DataSet.UpdateAsync(updateCommand);
                _logger.LogDebug($"Updating DataSet '{updateCommand.DataSetName}'.");
                _logger.LogDebug("CreateOrUpdate operation completed successfully in DataSet, returning view.");
                return Json(new { Success = true, data = result.Message });
            }       

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on dataSet page while processing the request for create or update.", ex);

            return ex.GetJsonException();
        }
    }

    [Authorize(Policy = Permissions.Admin.Delete)]
    [EventCode(EventCodes.DataSet.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in DataSet");

        try
        {
            var result = await _dataProvider.DataSet.DeleteAsync(id);
            _logger.LogDebug($"Successfully deleted record in DataSet");
            return Json(new { Success = true, data = result.Message });
           
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on dataSet.", ex);
             return Json(new { Success = false, data = ex.GetMessage() });
        }
    }

    
    [HttpGet]
    [EventCode(EventCodes.DataSet.RunQuery)]
    public async Task<JsonResult> RunQuery(string runQuery)
    {
        _logger.LogDebug("Entering RunQuery method in DataSet");

        if (runQuery.IsNullOrWhiteSpace())
        {
            return Json("");
        }
        try
        {
            //var dbProvider = _config.GetSection("ConnectionStrings").GetSection("DBProvider").Value;

            //var config = _config.GetConnectionString("Default");

            //var sqlQuery = runQuery;

            var result = await _dataProvider.DataSet.RunQuery(runQuery);

            _logger.LogDebug($"SQL query '{runQuery}' executed successfully, returning result on DataSet.");
          
            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on dataSet page while executing the SQL query.", ex);
            return ex.GetJsonException();
        }


    }

    [HttpGet]
    [EventCode(EventCodes.DataSet.GetPagination)]
    public async Task<JsonResult> GetPagination(GetDataSetPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in DataSet");
        try
        {
            _logger.LogDebug("Successfully retrieved dataSet pagination in DataSet");
            return Json(await _dataProvider.DataSet.GetDataSetPaginatedList(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on dataSet page while processing the pagination request", ex);

            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    [HttpGet]
    [EventCode(EventCodes.DataSet.DataSetNameExist)]
    public async Task<IActionResult> DataSetNameExist(string dataSetName, string id)
    {
        _logger.LogDebug($"Entering DataSetNameExist method in DataSet with dataSetName: {dataSetName}, id: {id}");

        try
        {
            var nameExist = await _dataProvider.DataSet.IsDataSetNameExist(dataSetName, id);
            _logger.LogDebug("Returning result for DataSetNameExist on DataSet");
            return Json(new { Success = true, data = nameExist });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on dataSet page while processing the request DataSetNameExist.", ex);
            return Json(new { Success = false, data = ex.GetMessage() });
        }
    }

    [HttpGet]
    [EventCode(EventCodes.DataSet.GetDbDetail)]
    public IActionResult GetDbDetail()
    {
        _logger.LogDebug("Entering GetDbDetail method in DataSet");

        try
        {
            var url = _config.GetValue<string>("ConnectionStrings:DBProvider");
            _logger.LogDebug("Retrieved encrypted DBProvider configuration in DataSet page.");

            var dbProvider = CryptographyHelper.Decrypt(url);
            _logger.LogDebug("Decrypted DBProvider configuration successfully in DataSet page.");

            _logger.LogDebug("Returning DBProvider details successfully from GetDbDetail on DataSet page.");
            return Json(new { Success = true, data = dbProvider });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on dataSet page while retrieving database details.", ex);
            return ex.GetJsonException();
        }
    }

    [AntiXss]
    [EventCode(EventCodes.DataSet.GetColumnNamesBySchemaNameAndTableName)]
    public async Task<IActionResult> GetColumnNamesBySchemaNameAndTableName(string schemaName,string tableName)
    {
        _logger.LogDebug("Entering GetColumnNamesBySchemaNameAndTableName method in DataSet");

        try
        {
            var columns = await _dataProvider.DataSetColumns.GetColumnNamesBySchemaNameAndTableName(schemaName, tableName);
            _logger.LogDebug($"Successfully deleted record in DataSet");
            return Json(new { Success = true, data = columns });

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on dataSet page while processing the request GetColumnNamesBySchemaNameAndTableName.", ex);
            return Json(new { Success = false, data = ex.GetMessage() });
        }
    }


}