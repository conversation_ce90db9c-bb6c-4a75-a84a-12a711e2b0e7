using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class IncidentRepositoryMocks
{
    public static Mock<IIncidentRepository> CreateIncidentRepository(List<Incident> incidents)
    {
        var incidentRepository = new Mock<IIncidentRepository>();

        incidentRepository.Setup(repo => repo.AddAsync(It.IsAny<Incident>())).ReturnsAsync((Incident incident) =>
        {
            incident.ReferenceId = Guid.NewGuid().ToString();
            incidents.Add(incident);
            return incident;
        });

        incidentRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(incidents);

        incidentRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string id) =>
        {
            return incidents.FirstOrDefault(x => x.ReferenceId == id);
        });

        incidentRepository.Setup(repo => repo.IsIncidentNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string name, string id) =>
        {
            return id == "0" ? incidents.Exists(x => x.IncidentName == name) : incidents.Exists(x => x.IncidentName == name && x.ReferenceId != id);
        });

        incidentRepository.Setup(repo => repo.IsIncidentNameUnique(It.IsAny<string>())).ReturnsAsync((string name) =>
        {
            return !incidents.Exists(x => x.IncidentName == name);
        });

        incidentRepository.Setup(repo => repo.GetAllIncidentNames()).ReturnsAsync(() =>
        {
            return incidents.Where(x => x.IsActive).ToList();
        });

        return incidentRepository;
    }

    public static Mock<IIncidentRepository> DeleteIncidentRepository(List<Incident> incidents)
    {
        var incidentRepository = new Mock<IIncidentRepository>();

        incidentRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string id) =>
        {
            return incidents.FirstOrDefault(x => x.ReferenceId == id);
        });

        incidentRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Incident>())).ReturnsAsync((Incident incident) =>
        {
            return incident;
        });

        return incidentRepository;
    }
}
