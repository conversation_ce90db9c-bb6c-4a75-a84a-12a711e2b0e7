using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class IncidentRepositoryMocks
{
    public static Mock<IIncidentRepository> CreateIncidentRepository(List<Incident> incidents)
    {
        var incidentRepository = new Mock<IIncidentRepository>();

        incidentRepository.Setup(repo => repo.AddAsync(It.IsAny<Incident>())).ReturnsAsync((Incident incident) =>
        {
            incident.ReferenceId = Guid.NewGuid().ToString();
            incidents.Add(incident);
            return incident;
        });

        incidentRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(incidents);

        incidentRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string id) =>
        {
            return incidents.FirstOrDefault(x => x.ReferenceId == id);
        });

        incidentRepository.Setup(repo => repo.IsIncidentNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string name, string id) =>
        {
            return id == "0" ? incidents.Exists(x => x.IncidentName == name) : incidents.Exists(x => x.IncidentName == name && x.ReferenceId != id);
        });

        incidentRepository.Setup(repo => repo.IsIncidentNameUnique(It.IsAny<string>())).ReturnsAsync((string name) =>
        {
            return !incidents.Exists(x => x.IncidentName == name);
        });

        incidentRepository.Setup(repo => repo.GetAllIncidentNames()).ReturnsAsync(() =>
        {
            return incidents.Where(x => x.IsActive).ToList();
        });

        return incidentRepository;
    }

    public static Mock<IIncidentRepository> DeleteIncidentRepository(List<Incident> incidents)
    {
        var incidentRepository = new Mock<IIncidentRepository>();

        incidentRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string id) =>
        {
            return incidents.FirstOrDefault(x => x.ReferenceId == id);
        });

        incidentRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Incident>())).ReturnsAsync((Incident incident) =>
        {
            return incident;
        });

        return incidentRepository;
    }

    public static Mock<IIncidentRepository> UpdateIncidentRepository(List<Incident> incidents)
    {
        var incidentRepository = new Mock<IIncidentRepository>();

        incidentRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string id) =>
        {
            return incidents.FirstOrDefault(x => x.ReferenceId == id);
        });

        incidentRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Incident>())).ReturnsAsync((Incident incident) =>
        {
            return incident;
        });

        return incidentRepository;
    }

    public static Mock<IIncidentRepository> GetIncidentRepository(List<Incident> incidents)
    {
        var incidentRepository = new Mock<IIncidentRepository>();

        incidentRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string id) =>
        {
            return incidents.FirstOrDefault(x => x.ReferenceId == id);
        });

        incidentRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(incidents);

        return incidentRepository;
    }

    public static Mock<IIncidentRepository> GetIncidentEmptyRepository()
    {
        var incidentRepository = new Mock<IIncidentRepository>();

        incidentRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Incident>());

        return incidentRepository;
    }

    public static Mock<IIncidentRepository> GetPaginatedIncidentRepository(List<Incident> incidents)
    {
        var incidentRepository = new Mock<IIncidentRepository>();

        incidentRepository.Setup(repo => repo.PaginatedListAllAsync(It.IsAny<int>(), It.IsAny<int>(),
            It.IsAny<IncidentFilterSpecification>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, IncidentFilterSpecification spec, string sortColumn, string sortOrder) =>
            {
                var filteredIncidents = incidents.Where(x => x.IsActive).ToList();

                // Since IncidentFilterSpecification doesn't expose SearchString property,
                // we'll apply the specification's criteria if it exists
                if (spec?.Criteria != null)
                {
                    var compiledCriteria = spec.Criteria.Compile();
                    filteredIncidents = filteredIncidents.Where(compiledCriteria).ToList();
                }

                var totalCount = filteredIncidents.Count;
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var pagedIncidents = filteredIncidents
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new PaginatedResult<Incident>
                {
                    Data = pagedIncidents,
                    TotalCount = totalCount,
                    TotalPages = totalPages,
                    CurrentPage = pageNumber,
                    PageSize = pageSize,
                    HasNextPage = pageNumber < totalPages,
                    HasPreviousPage = pageNumber > 1
                };
            });

        return incidentRepository;
    }
}
