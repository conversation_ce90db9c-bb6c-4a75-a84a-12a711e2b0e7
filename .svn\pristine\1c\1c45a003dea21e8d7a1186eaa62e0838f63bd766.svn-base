using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Application.Features.Form.Commands.Delete;
using ContinuityPatrol.Application.Features.Form.Commands.Import;
using ContinuityPatrol.Application.Features.Form.Commands.Lock;
using ContinuityPatrol.Application.Features.Form.Commands.Publish;
using ContinuityPatrol.Application.Features.Form.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Application.Features.Form.ImportFormPlugins;
using ContinuityPatrol.Application.Features.Form.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Form.Queries.GetList;
using ContinuityPatrol.Application.Features.Form.Queries.GetNames;
using ContinuityPatrol.Application.Features.Form.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Form.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Form.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.FormModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using FluentAssertions;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class FormsControllerTests : IClassFixture<FormsFixture>
{
    private readonly FormsController _controller;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly FormsFixture _formsFixture;

    public FormsControllerTests(FormsFixture formsFixture)
    {
        _formsFixture = formsFixture;
        var testBuilder = new ControllerTestBuilder<FormsController>();
        _controller = testBuilder.CreateController(
            _ => new FormsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task CreateForm_WithValidRequest_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formsFixture.CreateFormCommand;
        var response = _formsFixture.CreateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateForm_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.UpdateFormCommand;
        var response = _formsFixture.UpdateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task DeleteForm_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var response = _formsFixture.DeleteFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<DeleteFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.DeleteForm(id);

        // Assert
        result.Should().BeOfType<ActionResult<DeleteFormResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<DeleteFormCommand>(c => c.Id == id), default), Times.Once);
    }

    [Fact]
    public async Task GetFormDetail_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var response = _formsFixture.FormDetailVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormDetailQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormById(id);

        // Assert
        result.Should().BeOfType<ActionResult<FormDetailVm>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormDetailQuery>(q => q.Id == id), default), Times.Once);
    }

    [Fact]
    public async Task GetFormList_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var response = _formsFixture.FormListVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormListQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetForms();

        // Assert
        result.Should().BeOfType<ActionResult<List<FormListVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetFormListQuery>(), default), Times.Once);
    }

    [Fact]
    public async Task GetFormNames_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var response = _formsFixture.FormNameVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormNameQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormNames();

        // Assert
        result.Should().BeOfType<ActionResult<List<FormNameVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetFormNameQuery>(), default), Times.Once);
    }

    [Fact]
    public async Task IsFormNameExist_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var query = _formsFixture.GetFormNameUniqueQuery;
        var response = true;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormNameUniqueQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.IsFormNameExist(query.FormName, query.FormId);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var actionResult = result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormNameUniqueQuery>(q => 
            q.FormName == query.FormName && q.FormId == query.FormId), default), Times.Once);
    }

    [Fact]
    public async Task GetPaginatedForms_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var query = _formsFixture.GetFormPaginatedListQuery;
        var response = _formsFixture.FormPaginatedResult;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormPaginatedListQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetPaginatedForms(query);

        // Assert
        result.Should().BeOfType<ActionResult<PaginatedResult<FormListVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(query, default), Times.Once);
    }

    [Fact]
    public async Task CreateForm_WithComplexProperties_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formsFixture.CreateFormCommand;
        command.Properties = "{\"sections\":[{\"title\":\"Enterprise Risk Assessment\",\"fields\":[{\"name\":\"riskLevel\",\"type\":\"select\",\"options\":[\"Low\",\"Medium\",\"High\",\"Critical\"]},{\"name\":\"department\",\"type\":\"text\",\"required\":true}]}]}";
        var response = _formsFixture.CreateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<CreateFormCommand>(c => 
            c.Properties.Contains("Enterprise Risk Assessment")), default), Times.Once);
    }

    [Fact]
    public async Task UpdateForm_WithPublishFlag_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.UpdateFormCommand;
        command.IsPublish = true;
        command.Version = "2.0";
        var response = _formsFixture.UpdateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<UpdateFormCommand>(c => 
            c.IsPublish == true && c.Version == "2.0"), default), Times.Once);
    }

    [Fact]
    public async Task CreateForm_WithLockFlag_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formsFixture.CreateFormCommand;
        command.IsLock = true;
        command.Name = "Locked Enterprise Form";
        var response = _formsFixture.CreateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<CreateFormCommand>(c => 
            c.IsLock == true && c.Name == "Locked Enterprise Form"), default), Times.Once);
    }

    [Fact]
    public async Task UpdateForm_WithRestoreFlag_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.UpdateFormCommand;
        command.IsRestore = true;
        command.Version = "1.0";
        var response = _formsFixture.UpdateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<UpdateFormCommand>(c => 
            c.IsRestore == true && c.Version == "1.0"), default), Times.Once);
    }

    [Fact]
    public async Task IsFormNameExist_WithDuplicateName_ReturnsTrue()
    {
        // Arrange
        var name = "Enterprise Risk Assessment Form";
        var id = Guid.NewGuid().ToString();
        var response = true;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormNameUniqueQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.IsFormNameExist(name, id);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var actionResult = result as OkObjectResult;
        actionResult?.Value.Should().Be(true);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormNameUniqueQuery>(q => 
            q.FormName == name && q.FormId == id), default), Times.Once);
    }

    [Fact]
    public async Task GetPaginatedForms_WithSpecificType_ReturnsOkResult()
    {
        // Arrange
        var query = _formsFixture.GetFormPaginatedListQuery;
        query.Type = "Enterprise Risk Management";
        query.PageSize = 25;
        var response = _formsFixture.FormPaginatedResult;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormPaginatedListQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetPaginatedForms(query);

        // Assert
        result.Should().BeOfType<ActionResult<PaginatedResult<FormListVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormPaginatedListQuery>(q => 
            q.Type == "Enterprise Risk Management" && q.PageSize == 25), default), Times.Once);
    }

    [Fact]
    public async Task GetFormList_WithEmptyResult_ReturnsEmptyList()
    {
        // Arrange
        var emptyResponse = new List<FormListVm>();
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormListQuery>(), default))
                   .ReturnsAsync(emptyResponse);

        // Act
        var result = await _controller.GetForms();

        // Assert
        result.Should().BeOfType<ActionResult<List<FormListVm>>>();
        var actionResult = result.Result as OkObjectResult;
        var resultValue = actionResult?.Value as List<FormListVm>;
        resultValue.Should().NotBeNull();
        resultValue.Should().BeEmpty();
    }

    [Fact]
    public async Task CreateForm_WithCompanyId_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formsFixture.CreateFormCommand;
        command.CompanyId = Guid.NewGuid().ToString();
        command.Type = "Enterprise Compliance";
        var response = _formsFixture.CreateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<CreateFormCommand>(c => 
            !string.IsNullOrEmpty(c.CompanyId) && c.Type == "Enterprise Compliance"), default), Times.Once);
    }

    [Fact]
    public async Task UpdateForm_WithTypeChange_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.UpdateFormCommand;
        command.Type = "Enterprise Security Assessment";
        command.Properties = "{\"sections\":[{\"title\":\"Security Analysis\",\"fields\":[{\"name\":\"securityLevel\",\"type\":\"select\",\"options\":[\"Low\",\"Medium\",\"High\"]}]}]}";
        var response = _formsFixture.UpdateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<UpdateFormCommand>(c => 
            c.Type == "Enterprise Security Assessment"), default), Times.Once);
    }

    [Fact]
    public async Task GetFormNames_WithPublishedFormsOnly_ReturnsFilteredNames()
    {
        // Arrange
        var response = _formsFixture.FormNameVm;
        response.ForEach(x => x.IsPublish = true);
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormNameQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormNames();

        // Assert
        result.Should().BeOfType<ActionResult<List<FormNameVm>>>();
        var actionResult = result.Result as OkObjectResult;
        var resultValue = actionResult?.Value as List<FormNameVm>;
        resultValue.Should().NotBeNull();
        resultValue.Should().OnlyContain(x => x.IsPublish == true);
    }

    [Fact]
    public async Task IsFormNameExist_WithUniqueName_ReturnsFalse()
    {
        // Arrange
        var name = "Unique Enterprise Form";
        var id = Guid.NewGuid().ToString();
        var response = false;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormNameUniqueQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.IsFormNameExist(name, id);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var actionResult = result as OkObjectResult;
        actionResult?.Value.Should().Be(false);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormNameUniqueQuery>(q =>
            q.FormName == name && q.FormId == id), default), Times.Once);
    }

    // Null validation tests
    [Fact]
    public async Task GetFormsByType_WithNullType_ThrowsInvalidArgumentException()
    {
        // Arrange
        string nullType = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _controller.GetFormsByType(nullType));
    }

    [Fact]
    public async Task GetFormById_WithNullId_ThrowsInvalidArgumentException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _controller.GetFormById(nullId));
    }

    [Fact]
    public async Task DeleteForm_WithNullId_ThrowsInvalidArgumentException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _controller.DeleteForm(nullId));
    }

    [Fact]
    public async Task IsFormNameExist_WithNullFormName_ThrowsInvalidArgumentException()
    {
        // Arrange
        string nullFormName = null;
        string formId = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _controller.IsFormNameExist(nullFormName, formId));
    }

    [Fact]
    public async Task CreateForm_WithNullCommand_ReturnsSuccessfully()
    {
        // Arrange
        CreateFormCommand command = null;
        var response = _formsFixture.CreateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateForm_WithNullCommand_ReturnsSuccessfully()
    {
        // Arrange
        UpdateFormCommand command = null;
        var response = _formsFixture.UpdateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    #region GetFormsByType Tests

    [Fact]
    public async Task GetFormsByType_WithValidType_ReturnsOkResult()
    {
        // Arrange
        var type = "Risk Management";
        var response = _formsFixture.FormTypeVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormsByType(type);

        // Assert
        result.Should().BeOfType<ActionResult<List<FormTypeVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeQuery>(q => q.Type == type), default), Times.Once);
    }

    [Fact]
    public async Task GetFormsByType_WithNullType_ThrowsArgumentException()
    {
        // Arrange
        string type = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _controller.GetFormsByType(type));
    }

    [Fact]
    public async Task GetFormsByType_WithEmptyType_ThrowsArgumentException()
    {
        // Arrange
        var type = "";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(
            () => _controller.GetFormsByType(type));
    }

    #endregion

    #region SaveAsForm Tests

    [Fact]
    public async Task SaveAsForm_WithValidCommand_ReturnsCreatedAtActionResult()
    {
        // Arrange
        var command = _formsFixture.SaveAsFormCommand;
        var response = _formsFixture.SaveAsFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<SaveAsFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.SaveAsForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<SaveAsFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task SaveAsForm_WithNullCommand_ThrowsNullReferenceException()
    {
        // Arrange
        SaveAsFormCommand command = null;

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => _controller.SaveAsForm(command));
    }

    [Fact]
    public async Task SaveAsForm_WithInvalidFormId_HandlesGracefully()
    {
        // Arrange
        var command = _formsFixture.SaveAsFormCommand;
        command.FormId = "invalid-guid";
        var response = _formsFixture.SaveAsFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<SaveAsFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.SaveAsForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<SaveAsFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    #endregion

    #region ImportFormPlugins Tests

    [Fact]
    public async Task ImportFormPlugins_WithValidCommand_ReturnsCreatedAtActionResult()
    {
        // Arrange
        var command = _formsFixture.ImportPluginListCommand;
        var response = _formsFixture.ImportFormPluginResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<ImportPluginListCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.ImportFormPlugins(command);

        // Assert
        result.Should().BeOfType<ActionResult<ImportFormPluginResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task ImportFormPlugins_WithNullCommand_ReturnsSuccessfully()
    {
        // Arrange
        ImportPluginListCommand command = null;
        var response = _formsFixture.ImportFormPluginResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<ImportPluginListCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.ImportFormPlugins(command);

        // Assert
        result.Should().BeOfType<ActionResult<ImportFormPluginResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task ImportFormPlugins_WithEmptyPlugins_HandlesGracefully()
    {
        // Arrange
        var command = _formsFixture.ImportPluginListCommand;
        command.ImportPlugins = "";
        var response = _formsFixture.ImportFormPluginResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<ImportPluginListCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.ImportFormPlugins(command);

        // Assert
        result.Should().BeOfType<ActionResult<ImportFormPluginResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    #endregion

    #region ImportForms Tests

    [Fact]
    public async Task ImportForms_WithValidCommand_ReturnsCreatedAtActionResult()
    {
        // Arrange
        var command = _formsFixture.ImportFormCommand;
        var response = _formsFixture.ImportFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<ImportFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.ImportForms(command);

        // Assert
        result.Should().BeOfType<ActionResult<ImportFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task ImportForms_WithNullCommand_ReturnsSuccessfully()
    {
        // Arrange
        ImportFormCommand command = null;
        var response = _formsFixture.ImportFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<ImportFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.ImportForms(command);

        // Assert
        result.Should().BeOfType<ActionResult<ImportFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task ImportForms_WithEmptyFormsList_HandlesGracefully()
    {
        // Arrange
        var command = _formsFixture.ImportFormCommand;
        command.Forms = new List<FormListCommand>();
        var response = _formsFixture.ImportFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<ImportFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.ImportForms(command);

        // Assert
        result.Should().BeOfType<ActionResult<ImportFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    #endregion

    #region Publish Tests

    [Fact]
    public async Task Publish_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.UpdateFormPublishCommand;
        var response = _formsFixture.UpdateFormPublishResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormPublishCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.Publish(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormPublishResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task Publish_WithNullCommand_ReturnsSuccessfully()
    {
        // Arrange
        UpdateFormPublishCommand command = null;
        var response = _formsFixture.UpdateFormPublishResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormPublishCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.Publish(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormPublishResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task Publish_WithInvalidFormId_HandlesGracefully()
    {
        // Arrange
        var command = _formsFixture.UpdateFormPublishCommand;
        command.Id = "invalid-guid";
        var response = _formsFixture.UpdateFormPublishResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormPublishCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.Publish(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormPublishResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    #endregion

    #region UpdateWorkflowLock Tests

    [Fact]
    public async Task UpdateWorkflowLock_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.UpdateFormLockCommand;
        var response = _formsFixture.UpdateFormLockResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormLockCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateWorkflowLock(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormLockResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateWorkflowLock_WithNullCommand_ReturnsSuccessfully()
    {
        // Arrange
        UpdateFormLockCommand command = null;
        var response = _formsFixture.UpdateFormLockResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormLockCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateWorkflowLock(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormLockResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateWorkflowLock_WithInvalidFormId_HandlesGracefully()
    {
        // Arrange
        var command = _formsFixture.UpdateFormLockCommand;
        command.Id = "invalid-guid";
        var response = _formsFixture.UpdateFormLockResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormLockCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateWorkflowLock(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormLockResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

   

    [Fact]
    public async Task GetFormsByType_WithWhitespaceType_ThrowsArgumentException()
    {
        // Arrange
        string whitespaceType = "   ";

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.GetFormsByType(whitespaceType));
    }

    [Fact]
    public async Task GetFormsByType_WithSpecialCharacters_ReturnsOkResult()
    {
        // Arrange
        string specialType = "enterprise-risk_assessment.v2";
        var response = _formsFixture.FormTypeVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormsByType(specialType);

        // Assert
        result.Should().BeOfType<ActionResult<List<FormTypeVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeQuery>(q => q.Type == specialType), default), Times.Once);
    }

    [Fact]
    public async Task GetForms_WithEmptyResult_ReturnsEmptyList()
    {
        // Arrange
        var emptyResponse = new List<FormListVm>();
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormListQuery>(), default))
                   .ReturnsAsync(emptyResponse);

        // Act
        var result = await _controller.GetForms();

        // Assert
        result.Should().BeOfType<ActionResult<List<FormListVm>>>();
        var actionResult = result.Result as OkObjectResult;
        var resultValue = actionResult?.Value as List<FormListVm>;
        resultValue.Should().NotBeNull();
        resultValue.Should().BeEmpty();
    }

    [Fact]
    public async Task GetFormNames_WithMultipleForms_ReturnsAllNames()
    {
        // Arrange
        var response = _formsFixture.FormNameVm;
        response.AddRange(new[]
        {
            new FormNameVm { Id = Guid.NewGuid().ToString(), Name = "Enterprise Security Assessment" },
            new FormNameVm { Id = Guid.NewGuid().ToString(), Name = "Enterprise Compliance Review" }
        });
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormNameQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormNames();

        // Assert
        result.Should().BeOfType<ActionResult<List<FormNameVm>>>();
        var actionResult = result.Result as OkObjectResult;
        var resultValue = actionResult?.Value as List<FormNameVm>;
        resultValue.Should().NotBeNull();
        resultValue.Should().HaveCountGreaterThan(2);
        resultValue.Should().Contain(x => x.Name == "Enterprise Security Assessment");
        resultValue.Should().Contain(x => x.Name == "Enterprise Compliance Review");
    }

    [Fact]
    public async Task CreateForm_WithComplexFormData_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formsFixture.CreateFormCommand;
        command.Name = "Enterprise Risk Management Assessment Form";
        command.Properties = "{\"sections\":[{\"title\":\"Compliance Analysis\",\"fields\":[{\"name\":\"complianceLevel\",\"type\":\"select\"}]}]}";
        command.Version = "2.1.0";
        var response = _formsFixture.CreateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<CreateFormCommand>(c =>
            c.Name == "Enterprise Risk Management Assessment Form" &&
            c.Version == "2.1.0"), default), Times.Once);
    }

    [Fact]
    public async Task UpdateForm_WithVersionIncrement_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.UpdateFormCommand;
        command.Version = "3.0.0";
        command.IsPublish = true;
        var response = _formsFixture.UpdateFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<UpdateFormCommand>(c =>
            c.Version == "3.0.0" && c.IsPublish == true), default), Times.Once);
    }

    [Fact]
    public async Task SaveAsForm_WithNewFormName_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formsFixture.SaveAsFormCommand;
        command.Name = "Enterprise Risk Assessment Form - Copy";
        command.Version = "1.0.0";
        var response = _formsFixture.SaveAsFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<SaveAsFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.SaveAsForm(command);

        // Assert
        result.Should().BeOfType<ActionResult<SaveAsFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<SaveAsFormCommand>(c =>
            c.Name == "Enterprise Risk Assessment Form - Copy" &&
            c.Version == "1.0.0"), default), Times.Once);
    }

    [Fact]
    public async Task ImportFormPlugins_WithMultiplePlugins_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.ImportPluginListCommand;
        command.ImportPlugins = "{\"properties\":{\"server\":true,\"serverType\":\"Enterprise Security Server\",\"serverdata\":\"{\\\"version\\\":\\\"2.0.0\\\"}\"}}";
        var response = _formsFixture.ImportFormPluginResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<ImportPluginListCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.ImportFormPlugins(command);

        // Assert
        result.Should().BeOfType<ActionResult<ImportFormPluginResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<ImportPluginListCommand>(c =>
            c.ImportPlugins.Contains("Enterprise Security Server")), default), Times.Once);
    }

    [Fact]
    public async Task ImportForms_WithBulkImport_ReturnsOkResult()
    {
        // Arrange
        var command = _formsFixture.ImportFormCommand;
        command.Forms.Add(new FormListCommand
        {
            Name = "Enterprise Compliance Form",
            Type = "Compliance",
            Properties = "{\"sections\":[{\"title\":\"Compliance Analysis\",\"fields\":[{\"name\":\"complianceLevel\",\"type\":\"select\"}]}]}",
            Version = "1.0",
            IsPublish = false,
            IsLock = false,
            IsRestore = false
        });
        var response = _formsFixture.ImportFormResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<ImportFormCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.ImportForms(command);

        // Assert
        result.Should().BeOfType<ActionResult<ImportFormResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<ImportFormCommand>(c =>
            c.Forms.Any(f => f.Name == "Enterprise Compliance Form")), default), Times.Once);
    }

    #endregion
}
