﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Mocks
{
    public static class CGExecutionReportRepositoryMocks
    {
        public static Mock<IRpForVmCgEnableDisableStatusRepository> CreateCGExecutionReportRepository(List<RpForVmCgEnableDisableStatus> executionReports)
        {
            var mockCGExecutionReportRepository = new Mock<IRpForVmCgEnableDisableStatusRepository>();

            mockCGExecutionReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(executionReports);

            mockCGExecutionReportRepository.Setup(repo => repo.AddAsync(It.IsAny<RpForVmCgEnableDisableStatus>())).ReturnsAsync(
            (RpForVmCgEnableDisableStatus executionReport) =>
            {
                executionReport.Id = new Fixture().Create<int>();

                executionReport.ReferenceId = new Fixture().Create<Guid>().ToString();

                executionReports.Add(executionReport);

                return executionReport;
            });

            return mockCGExecutionReportRepository;
        }

        public static Mock<IRpForVmCgEnableDisableStatusRepository> UpdateCGExecutionReportRepository(List<RpForVmCgEnableDisableStatus> executionReports)
        {
            var mockCGExecutionReportRepository = new Mock<IRpForVmCgEnableDisableStatusRepository>();
            mockCGExecutionReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(executionReports);

            mockCGExecutionReportRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => executionReports.SingleOrDefault(x => x.ReferenceId == i));

            mockCGExecutionReportRepository.Setup(repo => repo.UpdateAsync(It.IsAny<RpForVmCgEnableDisableStatus>())).ReturnsAsync((RpForVmCgEnableDisableStatus executionReport) =>
            {
                var index = executionReports.FindIndex(item => item.Id == executionReport.Id);

                executionReports[index] = executionReport;

                return executionReport;
            });

            return mockCGExecutionReportRepository;
        }

        public static Mock<IRpForVmCgEnableDisableStatusRepository> DeleteCGExecutionReportRepository(List<RpForVmCgEnableDisableStatus> executionReports)
        {
            var mockCGExecutionReportRepository = new Mock<IRpForVmCgEnableDisableStatusRepository>();

            mockCGExecutionReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(executionReports);

            mockCGExecutionReportRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => executionReports.SingleOrDefault(x => x.ReferenceId == i));

            mockCGExecutionReportRepository.Setup(repo => repo.UpdateAsync(It.IsAny<RpForVmCgEnableDisableStatus>())).ReturnsAsync((RpForVmCgEnableDisableStatus executionReport) =>
            {
                var index = executionReports.FindIndex(item => item.Id == executionReport.Id);

                executionReport.IsActive = false;

                executionReports[index] = executionReport;

                return executionReport;
            });

            return mockCGExecutionReportRepository;
        }

        public static Mock<IResiliencyReadyWorkflowScheduleLogRepository> CreateResiliencyWorkflowScheduleLogRepository(List<ResiliencyReadyWorkflowScheduleLog> scheduleLogs)
        {
            var mockCGExecutionReportRepository = new Mock<IResiliencyReadyWorkflowScheduleLogRepository>();

            scheduleLogs ??= new List<ResiliencyReadyWorkflowScheduleLog>();

            mockCGExecutionReportRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync((string workflowOperationId) => scheduleLogs.FirstOrDefault(x => x.ReferenceId == workflowOperationId));

            mockCGExecutionReportRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ResiliencyReadyWorkflowScheduleLog>()))
                .ReturnsAsync((ResiliencyReadyWorkflowScheduleLog log) =>
                {
                    var index = scheduleLogs.FindIndex(x => x.ReferenceId == log.ReferenceId);
                    if (index >= 0)
                    {
                        scheduleLogs[index] = log;
                    }
                    return log;
                });
            mockCGExecutionReportRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(scheduleLogs);

            return mockCGExecutionReportRepository;
        }

        public static Mock<IRpForVmCgEnableDisableStatusRepository> CreateCgEnableDisableStatusRepositoryMock(List<CgExecutionPaginatedListVm> mockData)
        {
            var mockRepo = new Mock<IRpForVmCgEnableDisableStatusRepository>();

            mockRepo.Setup(repo => repo.GetCgExecutionPaginatedList(
                    It.IsAny<string>(),
                    It.IsAny<string>(),  // StartDate as string
                    It.IsAny<string>(),  // EndDate as string
                    It.IsAny<int>(),
                    It.IsAny<int>()))
                .ReturnsAsync((string type, string startDate, string endDate, int pageNumber, int pageSize) =>
                {
                    var pagedData = mockData.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();

                    var result = new PaginatedResult<CgExecutionPaginatedListVm>
                    {
                        Data = pagedData,
                        TotalCount = mockData.Count,
                        PageSize = pageSize,
                        Succeeded = true
                    };

                    return result;
                });

            return mockRepo;
        }

        public static Mock<IResiliencyReadyWorkflowScheduleLogRepository> CreateWorkflowScheduleLogRepositoryMock()
        {
            var mockRepo = new Mock<IResiliencyReadyWorkflowScheduleLogRepository>();

            mockRepo.Setup(repo => repo.GetResiliencyReadyWorkflowScheduleLogById(It.IsAny<string>()))
                .Returns((string id) =>
                    Task.FromResult<(int, string)>((123, "CurrentActionName-Test")));

            return mockRepo;
        }

        //Events
        public static Mock<IUserActivityRepository> CreateCGExecutionReportEventRepository(List<UserActivity> userActivities)
        {
            var mockCGExecutionReportEventRepository = new Mock<IUserActivityRepository>();
            userActivities ??= new List<UserActivity>();

            mockCGExecutionReportEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
                .ReturnsAsync((UserActivity activity) =>
                {
                    activity.Id = userActivities.Count + 1;
                    userActivities.Add(activity);
                    return activity;
                });

            mockCGExecutionReportEventRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(userActivities);

            return mockCGExecutionReportEventRepository;
        }
    }
}
