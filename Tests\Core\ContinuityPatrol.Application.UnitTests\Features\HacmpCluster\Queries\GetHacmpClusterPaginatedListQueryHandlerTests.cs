﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Queries;

public class GetHacmpClusterPaginatedListQueryHandlerTests : IClassFixture<HacmpClusterFixture>
{
    private readonly GetHacmpClusterPaginatedListQueryHandler _handler;
    private readonly Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;
    private readonly HacmpClusterFixture _hacmpClusterNewFixture;

    public GetHacmpClusterPaginatedListQueryHandlerTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterNewFixture = hacmpClusterFixture;

        _hacmpClusterNewFixture.HacmpClusters[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _hacmpClusterNewFixture.HacmpClusters[0].Name = "TestHacmpCluster";
        _hacmpClusterNewFixture.HacmpClusters[0].ServerName = "TestServer";
        _hacmpClusterNewFixture.HacmpClusters[0].ServerId = "TestServerId";

        _hacmpClusterNewFixture.HacmpClusters[1].Name = "TestCluster123";
        _hacmpClusterNewFixture.HacmpClusters[1].ServerName = "TestServer2";
        _hacmpClusterNewFixture.HacmpClusters[1].ServerId = "TestServerId2";

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.GetPaginatedHacmpClusterRepository(_hacmpClusterNewFixture.HacmpClusters);

        _handler = new GetHacmpClusterPaginatedListQueryHandler(_hacmpClusterNewFixture.Mapper, _mockHacmpClusterRepository.Object);
    }

    [Fact]
    public void Should_Create_Query_With_Valid_Parameters()
    {
        // Act
        var query = new GetHacmpClusterPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        // Assert
        Assert.NotNull(query);
        Assert.Equal(1, query.PageNumber);
        Assert.Equal(10, query.PageSize);
        Assert.Equal("test", query.SearchString);
        Assert.Equal("Name", query.SortColumn);
        Assert.Equal("asc", query.SortOrder);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetHacmpClusterPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<HacmpClusterListVm>>();

        result.TotalCount.ShouldBe(5);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_PaginatedHacmpClusters_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetHacmpClusterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<HacmpClusterListVm>>();

        result.TotalCount.ShouldBeGreaterThan(0);

        result.Data[0].ShouldBeOfType<HacmpClusterListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldNotBeEmpty();

        result.Data[0].ServerName.ShouldNotBeEmpty();

        result.Data[0].ServerId.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetHacmpClusterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<HacmpClusterListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetHacmpClusterPaginatedListQuery(), CancellationToken.None);

        _mockHacmpClusterRepository.Verify(x => x.PaginatedListAllAsync(It.IsAny<int>(),
            It.IsAny<int>(), It.IsAny<HacmpClusterFilterSpecification>(),
            It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_HacmpClusters_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetHacmpClusterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=TestHacmpCluster;servername=TestServer;serverid=TestServerId" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<HacmpClusterListVm>>();

        result.TotalCount.ShouldBeGreaterThan(0);

        if (result.Data.Any())
        {
            result.Data[0].Name.ShouldNotBeEmpty();
            result.Data[0].ServerName.ShouldNotBeEmpty();
            result.Data[0].ServerId.ShouldNotBeEmpty();
        }
    }

    [Fact]
    public async Task Handle_Return_ValidHacmpClusterDetails_When_Found()
    {
        var result = await _handler.Handle(new GetHacmpClusterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "TestHacmpCluster" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<HacmpClusterListVm>>();

        if (result.Data.Any())
        {
            var firstCluster = result.Data.First();
            firstCluster.Id.ShouldNotBeNullOrEmpty();
            firstCluster.Name.ShouldNotBeNullOrEmpty();
            firstCluster.ServerId.ShouldNotBeNullOrEmpty();
            firstCluster.ServerName.ShouldNotBeNullOrEmpty();
        }
    }

    [Fact]
    public async Task Handle_Return_CorrectPageSize_When_Specified()
    {
        var result = await _handler.Handle(new GetHacmpClusterPaginatedListQuery { PageNumber = 1, PageSize = 2 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<HacmpClusterListVm>>();

        result.PageSize.ShouldBe(2);
        result.Data.Count.ShouldBeLessThanOrEqualTo(2);
    }
}