﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormTypeCategory.Queries;

public class GetFormTypeCategoryNameUniqueQueryHandlerTests : IClassFixture<FormTypeCategoryFixture>
{
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;
    private readonly GetFormTypeCategoryNameUniqueQueryHandler _handler;

    public GetFormTypeCategoryNameUniqueQueryHandlerTests(FormTypeCategoryFixture formTypeCategoryFixture)
    {
        _formTypeCategoryFixture = formTypeCategoryFixture;

        _formTypeCategoryFixture.FormTypeCategories[0].ReferenceId = Guid.NewGuid().ToString();
        _formTypeCategoryFixture.FormTypeCategories[0].FormTypeName = "Test FormTypeName";
        _formTypeCategoryFixture.FormTypeCategories[0].Name = "Test FormTypeCategory";
        _formTypeCategoryFixture.FormTypeCategories[0].IsActive = true;

        var mockFormTypeCategoryRepository = FormTypeCategoryRepositoryMocks.GetFormTypeCategoryNameUniqueRepository(_formTypeCategoryFixture.FormTypeCategories);
        _handler = new GetFormTypeCategoryNameUniqueQueryHandler(mockFormTypeCategoryRepository.Object);
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_True_When_Name_Already_Exists(GetFormTypeCategoryNameUniqueQuery query)
    {
        // Arrange
        var existingFormTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        query.Name = existingFormTypeCategory.FormTypeName;
        query.Id = existingFormTypeCategory.ReferenceId;

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_False_When_Name_Does_Not_Exist(GetFormTypeCategoryNameUniqueQuery query)
    {
        // Arrange
        query.Name = "NonExistentFormTypeCategoryName";
        query.Id = Guid.NewGuid().ToString();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_Success_Response_When_Name_Exists(GetFormTypeCategoryNameUniqueQuery query)
    {
        // Arrange
        var existingFormTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        query.Name = existingFormTypeCategory.FormTypeName;
        query.Id = existingFormTypeCategory.ReferenceId;

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_Success_Response_When_Name_Does_Not_Exist(GetFormTypeCategoryNameUniqueQuery query)
    {
        // Arrange
        query.Name = "NonExistentFormTypeCategoryName";
        query.Id = Guid.NewGuid().ToString();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Be_Case_Sensitive_For_Name_Comparison(GetFormTypeCategoryNameUniqueQuery query)
    {
        // Arrange
        var existingFormTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        query.Name = existingFormTypeCategory.FormTypeName.ToUpper();
        query.Id = existingFormTypeCategory.ReferenceId;

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse(); // Should be false because case is different
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_False_When_Name_Is_Null(GetFormTypeCategoryNameUniqueQuery query)
    {
        // Arrange
        query.Name = null;
        query.Id = Guid.NewGuid().ToString();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_False_When_Name_Is_Empty(GetFormTypeCategoryNameUniqueQuery query)
    {
        // Arrange
        query.Name = string.Empty;
        query.Id = Guid.NewGuid().ToString();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }
}