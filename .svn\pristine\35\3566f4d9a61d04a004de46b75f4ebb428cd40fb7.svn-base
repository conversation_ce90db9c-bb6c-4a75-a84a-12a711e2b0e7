using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberSnapsRepository : BaseRepository<CyberSnaps>, ICyberSnapsRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    private static readonly string[] TimestampFormats = {
        "MMM d HH:mm:ss yyyy",
        "MMM dd HH:mm:ss yyyy"
    };

    public CyberSnapsRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public async Task<bool> IsNameExist(string name, string id)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            throw new ArgumentException("Name must be provided", nameof(name));
        }

        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(entity => entity.Name == name);
        }

        var matchingEntities = await Entities
            .Where(entity => entity.Name == name)
            .ToListAsync();

        return matchingEntities.Unique(id);
    }
    public async Task<List<CyberSnaps>> GetCyberSnapsBySnapTagName(string cyberSnapTagName, string startDate, string endDate)
    {
        ValidateDateParameters(startDate, endDate);

        if (string.IsNullOrEmpty(cyberSnapTagName))
        {
            throw new ArgumentException("Tag name cannot be null or empty", nameof(cyberSnapTagName));
        }

        var startDateTime = startDate.ToDateTime();
        var endDateTime = endDate.ToDateTime();
        var isAllTags = string.Equals(cyberSnapTagName, "all", StringComparison.OrdinalIgnoreCase);

        return await _dbContext.CyberSnaps
            .Active()
            .Where(snap => snap.CreatedDate.Date >= startDateTime.Date &&
                           snap.CreatedDate.Date <= endDateTime.Date &&
                           (isAllTags || snap.Tag == cyberSnapTagName))
            .ToListAsync();
    }
    public async Task<List<CyberSnaps>> GetCyberSnapsListByDate(string startDate, string endDate)
    {
        ValidateDateParameters(startDate, endDate);

        var startDateTime = startDate.ToDateTime();
        var endDateTime = endDate.ToDateTime();

        return await _dbContext.CyberSnaps
            .Active()
            .Where(snap => snap.CreatedDate.Date >= startDateTime.Date &&
                           snap.CreatedDate.Date <= endDateTime.Date)
            .ToListAsync();
    }
    public async Task<List<CyberSnaps>> GetCyberSnapsByStorageGroupNameAndLinkedStatus(Expression<Func<CyberSnaps, bool>> expression)
    {
        if (expression == null)
        {
            throw new ArgumentNullException(nameof(expression), "Filter expression cannot be null");
        }

        return await _dbContext.CyberSnaps
            .Active()
            .Where(expression)
            .ToListAsync();
    }

    public async Task<PaginatedResult<CyberSnapsListVm>> GetCyberSnapsByDateTime(int pageNumber, int pageSize, Specification<CyberSnaps> specification, string sortColumn, string sortOrder, DateTime startDate, DateTime endDate)
    {

        if (specification == null)
        {
            throw new ArgumentNullException(nameof(specification), "Specification cannot be null");
        }

        // Get base query results
        var baseQuery = await _dbContext.CyberSnaps
            .AsNoTracking()
            .Specify(specification)
            .ToListAsync();

        // Filter by timestamp
        var query = baseQuery
            .AsEnumerable()
            .Where(snap => IsTimestampInRange(snap.TimeStamp, startDate, endDate))
            .ToList();

        // Map to view model
        var filtered = query
            .Select(snap => new CyberSnapsListVm
            {
                Id = snap.ReferenceId,
                Name = snap.Name,
                Gen = snap.Gen,
                TimeStamp = snap.TimeStamp,
                StorageGroupName = snap.StorageGroupName,
                LinkedStatus = snap.LinkedStatus,
                LinkedSGTime = snap.LinkedSGTime,
                LinkSG = snap.LinkSG
            })
            .AsQueryable();

        // Get total count
        var totalCount = filtered.Count();

        // Normalize pagination parameters
        pageNumber = Math.Max(1, pageNumber);
        pageSize = pageSize <= 0 ? totalCount : pageSize;

        // Apply pagination
        var pagedData = filtered
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        // Return paginated result
        return new PaginatedResult<CyberSnapsListVm>(
            succeeded: true,
            data: pagedData,
            count: totalCount,
            page: pageNumber,
            pageSize: pageSize
        );
    }

    private static bool IsTimestampInRange(string timestamp, DateTime startDate, DateTime endDate)
    {
        if (string.IsNullOrEmpty(timestamp) || timestamp.Length <= 4)
        {
            return false;
        }

        // Extract and normalize the timestamp part
        var timestampPart = timestamp.Substring(4).Trim();
        timestampPart = Regex.Replace(timestampPart, @"\s{2,}", " ");

        // Try to parse the timestamp
        if (DateTime.TryParseExact(
                timestampPart,
                TimestampFormats,
                CultureInfo.InvariantCulture,
                DateTimeStyles.None,
                out DateTime parsedDate))
        {
            // Check if the parsed date is within range (inclusive of end date)
            return parsedDate >= startDate && parsedDate <= endDate.AddDays(1).AddSeconds(-1);
        }

        return false;
    }

    private static void ValidateDateParameters(string startDate, string endDate)
    {
        if (string.IsNullOrEmpty(startDate))
        {
            throw new ArgumentException("Start date cannot be null or empty", nameof(startDate));
        }

        if (string.IsNullOrEmpty(endDate))
        {
            throw new ArgumentException("End date cannot be null or empty", nameof(endDate));
        }
    }
}