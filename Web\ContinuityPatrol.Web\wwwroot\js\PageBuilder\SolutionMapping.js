//GetSolutionMappingList()
//GetPageBuilderList()

let btn_save=document.querySelector('.btn_save')

$(".createbutton").on('click', function () {
    GetPageBuilderList()
    clearSolutionMapping()
    btn_save.textContent = 'Save'

})

let selectedValues = [];
$(document).ready(function () {
    let dataTable = $('#tblSolutionMapping').DataTable(
        {

            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            draw: 1,
            deferRender: true,
            scroller: true,
            processing: true,
            serverSide: true,
            filter: true,
            Sortable: true,
            order: [],
            "ajax": {
                "type": "GET",
                "url": RootUrl + "Admin/SolutionMapping/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    if (json.success) {

                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")

                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }

                        return json.data.data;
                    }
                    else {
                        return ""
                    }
                },
            },
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        return meta.row + 1;
                        return data;
                    },
                    "orderable": false
                },
                {
                    data: "pageBuilderName",
                    name: 'Page Builder',
                    autoWidth: true,
                    render: function (data, type, row) {

                        return data;

                    }
                },
                {
                    data: "",
                    name: 'solution matched Image',
                    autoWidth: true,
                    Sortable: false,
                    className: "text-center",
                    render: function (data, type, row) {

                        return '<img src="/img/isomatric/solution_matched.svg" loading="lazy" />';

                    }
                },

                {
                    "data": "name", "name": "Solution Mapping", "autoWidth": true,
                    "render": function (data, type, row) {


                        return data;
                    }
                },

                {
                    "render": function (data, type, row) {
                        return `<div class="d-flex align-items-center  gap-2">                                       
                                <span role="button" title="Edit"  class="edit-button" data-solution-id="${row.id}" data-solution='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                               
                                    <span role="button" title="Delete" class="delete-button" data-solution-id="${row.id}" data-solution-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>
                            </div>
                     `
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },

        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('.form-select-sm').select2({
        minimumResultsForSearch: Infinity
    });





$('#search-inp').on('keydown input', function (e) {


    if (e.key === '=' || e.key === 'Enter') {
        e.preventDefault();
        return false;
    } else {
        const inputValue = $('#search-inp').val();
        selectedValues.push(inputValue);
        dataTable.ajax.reload(function (json) {
            
            if (json.data.data.length === 0) {
                $('.dataTables_empty').append(`<tr> <td style="text-align: center;"  >No matching records Found</td><td style="text-align: center;"  ></td></tr>`);
            }
        })
    }


})



$('#tblSolutionMapping').on('click', '.edit-button',async function () {
    
    clearSolutionMapping()
   
    let solutionData = $(this).data('solution');

    GetPageBuilderList(solutionData.pageBuilderId)
    //populateModalFields(solutionData);
    btn_save.textContent = 'Update'  
    $("#solutionName").val(solutionData.name)
   
    $("#Activetype option[value=" + solutionData.type + "]").attr('selected', 'selected');
    let activeType = solutionData.typeName
    if (solutionData.type == "2") {
        $(".databaseType").removeClass("d-none")
        GetDatabaseLisList(solutionData.subTypeId)
    }
    else {
        $(".databaseType").addClass("d-none")
    }

    

    await GetReplicationList(solutionData.typeName, solutionData.replicationCategoryTypeId)
    setTimeout(() => {
         GetReplicationType(solutionData.replicationCategoryTypeId, activeType, solutionData.replicationTypeId)
    },200)

    btn_save.setAttribute('solutionId', solutionData.id)
    btn_save.setAttribute('title', 'Update')

    $('#CreateModal').modal('show');
});

//delete
$('#tblSolutionMapping').on('click', '.delete-button', function () {
    
    let solutionId = $(this).data('solution-id');
    let solutionName = $(this).data('solution-name');
    $('#textDeleteId').val(solutionId);
    $('#deleteData').text(solutionName);
});


$('#solutionName').on('keydown keyup', async function () {
    
    const value = $(this).val();
    let sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateName(value);
});



$("#pageBuilderDropdown").on("change", () => {
    $("#pageBuilderName-error").removeClass("field-validation-error")
    $("#pageBuilderName-error").text("")
})
$("#Activetype").on("change", () => {
    
    $("#activityName-error").removeClass("field-validation-error")
    $("#activityName-error").text("")
    let selectedActivity = $("#Activetype option:selected").val()
    if (selectedActivity == "2") {
        $(".databaseType").removeClass("d-none")
        GetDatabaseLisList()
    }
    else {
        $(".databaseType").addClass("d-none")
    }
    GetReplicationList($("#Activetype option:selected").text())

})


$("#activityDatabaseType").on("change", () => {

    $("#activityDatabaseName-error").removeClass("field-validation-error")
    $("#activityDatabaseName-error").text("")
   

})




$("#replicationNmae").on("change", () => {
    
    $("#replicationName-error").removeClass("field-validation-error")
    $("#replicationName-error").text("")

    let selectedreplication = $("#replicationNmae option:selected").val()
    let activeType = $("#Activetype option:selected").text()
    GetReplicationType(selectedreplication, activeType)

})
$("#replicationType").on("change", () => {
    $("#replicationType-error").removeClass("field-validation-error")
    $("#replicationType-error").text("")
})

$(".btn_save").on('click', async function (e) {
    
    let name = $("#solutionName").val();
    let pageBuilderId = $("#pageBuilderDropdown").val(); 
    let pageBuilderName = $("#pageBuilderDropdown option:selected").text(); 
    let ActivetypeId = $("#Activetype option:selected").val(); 
    let ActivetypeName = $("#Activetype option:selected").text(); 
    let ActivetypeDatabaseId = $("#activityDatabaseType option:selected").val();
    let ActivetypeDatabaseName = $("#activityDatabaseType option:selected").text(); 
    
    let replicationId = $("#replicationNmae option:selected").val(); 
    let replicationNmae = $("#replicationNmae option:selected").text(); 
    let replicationTypeId = $("#replicationType option:selected").val(); 
    let replicationTypeName = $("#replicationType option:selected").text(); 
    const pageBuilderNameerror = $("#pageBuilderName-error")
    const activityNameerror = $("#activityName-error")
    const activityDatabaseNameerror = $("#activityDatabaseName-error")
    let isName = await validateName(name);
    if (pageBuilderName == "") {
        pageBuilderNameerror.addClass("field-validation-error")
        pageBuilderNameerror.text("Enter page builder name")
        
    }
    else {
        pageBuilderNameerror.removeClass("field-validation-error")
        pageBuilderNameerror.text("")
    }
    if (ActivetypeName == "") {
        activityNameerror.addClass("field-validation-error")
        activityNameerror.text("Enter activity type")
       
    }
    else {
        activityNameerror.removeClass("field-validation-error")
        activityNameerror.text("")
    }
    if (ActivetypeDatabaseName == "") {
        activityDatabaseNameerror.addClass("field-validation-error")
        activityDatabaseNameerror.text("Enter Database Type")

    }
    else {
        activityDatabaseNameerror.removeClass("field-validation-error")
        activityDatabaseNameerror.text("")
    }

    if (replicationNmae == "") {
        $("#replicationName-error").addClass("field-validation-error")
        $("#replicationName-error").text("Enter replication name")
        
    }
    else {
        $("#replicationName-error").removeClass("field-validation-error")
        $("#replicationName-error").text("")
    }
    
    if (replicationTypeName == "") {
        $("#replicationType-error").addClass("field-validation-error")
        $("#replicationType-error").text("Enter replication type")

    }
    else {
        $("#replicationType-error").removeClass("field-validation-error")
        $("#replicationType-error").text("")
    }
    //let randomNum = Math.floor(Math.random() * 90000) + 10000;
    if (ActivetypeName == "DB") {
        if (name == "" || pageBuilderName == "" || ActivetypeName == "" || replicationNmae == "" || replicationTypeName == "" || ActivetypeDatabaseName == "") {
            return false
        }
    }
    else {
        if (name == "" || pageBuilderName == "" || ActivetypeName == "" || replicationNmae == "" || replicationTypeName == "") {
            return false
        }
    }
    let pageHtml = $('#widgetCreationCard').children()[0];
    let data = {}
   

    if (e.target.textContent == 'Update') {
        data.id = e.target.getAttribute('solutionId')
    }
   
    data.Name = name
    data.PageBuilderId = pageBuilderId
    data.PageBuilderName = pageBuilderName
    data.Type = ActivetypeId
    data.TypeName = ActivetypeName
    data.SubTypeId = ActivetypeDatabaseId
    data.SubType = ActivetypeDatabaseName
    data.ReplicationTypeId = replicationTypeId
    data.ReplicationTypeName = replicationTypeName
    data.ReplicationCategoryTypeId = replicationId
    data.ReplicationCategoryType = replicationNmae
    data.__RequestVerificationToken= gettoken()
    //data.Properties = JSON.stringify({
    //    type: e.target.getAttribute('type'),
    //    widgetDetails: pageHtml.outerHTML
    //})



    if (isName) {
        $.ajax({
            type: "POST",
            url: RootUrl + 'Admin/SolutionMapping/CreateOrUpdate',
            data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {

                if (result.success) {
                    $("#CreateModal").modal("hide")
                    $("#solutionName").val("")
                    $("#pageBuilderDropdown option:selected").val("")
                    $("#Activetype option:selected").val("")
                    $("#replicationNmae option:selected").val("")
                    $('#alertClass').removeClass("info-toast")
                    $('#alertClass').addClass("success-toast")
                    $('#notificationAlertmessage').text(result.message)
                    $('#mytoastrdata').toast({ delay: 3000 });
                    $('#mytoastrdata').toast('show');
                    $(".iconClass").removeClass("cp-exclamation")
                    $(".iconClass").addClass("cp-check")

                    dataTable.ajax.reload()
                    /*$("#tblSolutionMapping").dataTable().fnReloadAjax()*/
                }

            }
        })
    }
})
})




    function clearSolutionMapping() {

        $('#Name-error').removeClass("field-validation-error")
        $('#Name-error').text("")
        $('#pageBuilderName-error').removeClass("field-validation-error")
        $('#pageBuilderName-error').text("")
        $('#activityName-error').removeClass("field-validation-error")
        $('#activityName-error').text("")
        $('#activityDatabaseName-error').removeClass("field-validation-error")
        $('#activityDatabaseName-error').text("")
        $('#replicationName-error').removeClass("field-validation-error")
        $('#replicationName-error').text("")
        $('#replicationType-error').removeClass("field-validation-error")
        $('#replicationType-error').text("")
    }



async function validateName(value) {
    
    const errorElement = $('#Name-error');

    if (!value) {
        errorElement.text('Enter solution name')
            .addClass('field-validation-error');
        return false;
    }


    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxCompanylength(value),
        await secondChar(value),
    ];

    return await CommonValidation(errorElement, validationResults);
}




function GetSolutionMappingList() {
    
    $.ajax({
        url: RootUrl+"Admin/SolutionMapping/GetPageWidgetList",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            
            
        },
        error: function (error) {
            console.log(error);
        }
    });

}

function GetDatabaseLisList(databaseId) {
    
    $.ajax({
        url: RootUrl + "Configuration/InfraObject/GetDatabaseListByName",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (result) {
            $("#activityDatabaseType").empty()
            if (result.success) {
                $("#activityDatabaseType").append('<option value=""></option>')
                if (result?.data && Array.isArray(result.data) && result.data.length) {
                    result.data.forEach((s) => {
                        let optionValues = JSON.parse(s.properties)
                        $('#activityDatabaseType').append('<option value="' + s.id + '">' + optionValues['name'] + '</option>');
                        $("#activityDatabaseType option[value=" + databaseId + "]").attr('selected', 'selected');
                    })

                }

            }
        },
        error: function (error) {
            console.log(error);
        }
    });

}

function GetReplicationList(type,replicationId) {
    
    var data = {};
    data.infraMasterName = type == 'Database' ? 'DB' : type;
    $.ajax({
        url: RootUrl + "Configuration/InfraObject/GetReplicationMasterByInfraMasterName",
        dataType: "json",
        traditional: true,
        type: 'GET',
        data: data,
        success: function (response) {
            $("#replicationNmae").empty()
            if (response.success) {
                $("#replicationNmae").append('<option value=""></option>')
                response.data.forEach((data) => {

                    $("#replicationNmae").append("<option value='" + data.id + "'>" + data.name + "</option>")
                    $("#replicationNmae option[value=" + replicationId + "]").attr('selected', 'selected');
                })
              
                        
                   
            }
          
        },
        error: function (error) {
            console.log(error);
        }

    });

}


function GetReplicationType(replicationType, activeType,categorytype) {
    
    var data = {};
    data.databaseid = activeType === "DB" ? $('#activityDatabaseType option:selected').val() : ''
    data.replicationmasterid = replicationType
   
    data.type =  activeType == 'DB' ? 'Database' : activeType 

    $.ajax({
        url: RootUrl + "Admin/SolutionMapping/GetTypeByDatabaseIdAndReplicationMasterId",
        dataType: "json",
        traditional: true,
        type: 'GET',
        data: data,
        success: function (result) {
            
            $("#replicationType").empty()
            if (result.success) {

                if (result?.data && Array.isArray(result.data) && result.data.length) {
                    let uniqueIds = new Set();
                    $('#replicationType').append('<option value=""></option>');

                    for (let index = 0; index < result.data.length; index++) {
                        let properties = JSON.parse(result.data[index].properties)
                        if (properties.length > 0) {
                            for (let j = 0; j < properties.length; j++) {

                                if (!uniqueIds.has(properties[j].id)) {
                                    $('#replicationType').append('<option value="' + properties[j].id + '">' + properties[j].label + '</option>');
                                    $("#replicationType option[value=" + categorytype + "]").attr('selected', 'selected');
                                }

                            }
                        }
                    }
                   
                }

              

            }
        },
        error: function (error) {
            console.log(error);
        }
    });

}

function GetPageBuilderList(id) {
    
    $.ajax({
        url: RootUrl + "Admin/SolutionMapping/GetPageBuilderList",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            $("#pageBuilderDropdown").empty()
            if (response.success) {
                $("#pageBuilderDropdown").append('<option value=""></option>')
                response.message.forEach((data) => {
                    
                    $("#pageBuilderDropdown").append("<option value='" + data.id + "'>" + data.name +"</option>")
                })

                if (id) {
                    $("#pageBuilderDropdown option[value=" + id + "]").attr('selected', 'selected');
                }
            }
           
        },
        error: function (error) {
            console.log(error);
        }
    });

}


