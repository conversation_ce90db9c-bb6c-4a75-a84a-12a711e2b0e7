using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class CyberRepositoryMocks
{
    public static Mock<ICyberAirGapLogRepository> CreateCyberAirGapLogRepository(List<CyberAirGapLog> cyberAirGapLogs)
    {
        var mockRepository = new Mock<ICyberAirGapLogRepository>();

        // ListAllAsync - Returns only active entities
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberAirGapLogs.Where(x => x.IsActive).ToList());

        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberAirGapLogs.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        //mockRepository.Setup(repo => repo.GetByAirGapIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string airGapId) => cyberAirGapLogs.Where(x => x.AirGapId == airGapId && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetByLogLevelAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string logLevel) => cyberAirGapLogs.Where(x => x.LogLevel == logLevel && x.IsActive).ToList());

       
        //mockRepository.Setup(repo => repo.GetLogsByDateRangeAsync(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
        //    .ReturnsAsync((DateTime startDate, DateTime endDate) => 
        //        cyberAirGapLogs.Where(x => x.Timestamp >= startDate && x.Timestamp <= endDate && x.IsActive).ToList());

        // AddAsync - Adds new entity
        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAirGapLog>()))
            .ReturnsAsync((CyberAirGapLog log) =>
            {
                log.ReferenceId = Guid.NewGuid().ToString();
                log.Id = cyberAirGapLogs.Count + 1;
                log.IsActive = true;
              //  log.Timestamp = DateTime.UtcNow;
                cyberAirGapLogs.Add(log);
                return log;
            });

        // UpdateAsync - Updates existing entity
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAirGapLog>()))
            .Returns<CyberAirGapLog>(async (CyberAirGapLog log) =>
            {
                var existingLog = cyberAirGapLogs.FirstOrDefault(x => x.ReferenceId == log.ReferenceId);
                if (existingLog != null)
                {
                    existingLog.AirGapName = log.AirGapName;
                    existingLog.Description = log.Description;
                    //existingLog.LogLevel = log.LogLevel;
                    //existingLog.LogMessage = log.LogMessage;
                    //existingLog.LogData = log.LogData;
                    return existingLog;
                }
                return log;
            });

        // DeleteAsync - Soft delete
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberAirGapLog>()))
            .Returns<CyberAirGapLog>(async (CyberAirGapLog log) =>
            {
                var existingLog = cyberAirGapLogs.FirstOrDefault(x => x.ReferenceId == log.ReferenceId);
                if (existingLog != null)
                {
                    existingLog.IsActive = false;
                }
                return log;
            });

        return mockRepository;
    }

    public static Mock<ICyberAirGapStatusRepository> CreateCyberAirGapStatusRepository(List<CyberAirGapStatus> cyberAirGapStatuses)
    {
        var mockRepository = new Mock<ICyberAirGapStatusRepository>();

        // ListAllAsync - Returns only active entities
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberAirGapStatuses.Where(x => x.IsActive).ToList());

        // GetByReferenceIdAsync - Returns active entity by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberAirGapStatuses.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

      
        //mockRepository.Setup(repo => repo.GetByAirGapIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string airGapId) => cyberAirGapStatuses.Where(x => x.AirGapId == airGapId && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetByStatusAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string status) => cyberAirGapStatuses.Where(x => x.Status == status && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetCurrentStatusAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string airGapId) => 
        //        cyberAirGapStatuses.Where(x => x.AirGapId == airGapId && x.IsActive)
        //            .OrderByDescending(x => x.LastChecked).FirstOrDefault());

        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAirGapStatus>()))
            .ReturnsAsync((CyberAirGapStatus status) =>
            {
                status.ReferenceId = Guid.NewGuid().ToString();
                status.Id = cyberAirGapStatuses.Count + 1;
                status.IsActive = true;
               // status.LastChecked = DateTime.UtcNow;
                cyberAirGapStatuses.Add(status);
                return status;
            });

        // UpdateAsync - Updates existing entity
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAirGapStatus>()))
            .Returns<CyberAirGapStatus>(async (CyberAirGapStatus status) =>
            {
                var existingStatus = cyberAirGapStatuses.FirstOrDefault(x => x.ReferenceId == status.ReferenceId);
                if (existingStatus != null)
                {
                    existingStatus.Status = status.Status;
                    //existingStatus.StatusCode = status.StatusCode;
                    //existingStatus.StatusMessage = status.StatusMessage;
                    //existingStatus.StatusData = status.StatusData;
                    //existingStatus.LastChecked = DateTime.UtcNow;
                    return existingStatus;
                }
                return status;
            });

        // DeleteAsync - Soft delete
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberAirGapStatus>()))
            .Returns<CyberAirGapStatus>(async (CyberAirGapStatus status) =>
            {
                var existingStatus = cyberAirGapStatuses.FirstOrDefault(x => x.ReferenceId == status.ReferenceId);
                if (existingStatus != null)
                {
                    existingStatus.IsActive = false;
                }
                return status;
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock ICyberAlertRepository with comprehensive CRUD operations
    /// Purpose: CyberAlert manages security alerts and notifications for cyber threats and incidents
    /// </summary>
    public static Mock<ICyberAlertRepository> CreateCyberAlertRepository(List<CyberAlert> cyberAlerts)
    {
        var mockRepository = new Mock<ICyberAlertRepository>();

        // ListAllAsync - Returns only active entities
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberAlerts.Where(x => x.IsActive).ToList());

        // GetByReferenceIdAsync - Returns active entity by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberAlerts.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        //mockRepository.Setup(repo => repo.GetByAlertIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string alertId) => cyberAlerts.FirstOrDefault(x => x.AlertId == alertId && x.IsActive));

        //mockRepository.Setup(repo => repo.GetBySeverityAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string severity) => cyberAlerts.Where(x => x.Severity == severity && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetByStatusAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string status) => cyberAlerts.Where(x => x.Status == status && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetOpenAlertsAsync())
        //    .ReturnsAsync(cyberAlerts.Where(x => x.Status == "Open" && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetAlertsByDateRangeAsync(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
        //    .ReturnsAsync((DateTime startDate, DateTime endDate) => 
        //        cyberAlerts.Where(x => x.DetectedAt >= startDate && x.DetectedAt <= endDate && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAlert>()))
        //    .ReturnsAsync((CyberAlert alert) =>
        //    {
        //        alert.ReferenceId = Guid.NewGuid().ToString();
        //        alert.Id = cyberAlerts.Count + 1;
        //        alert.IsActive = true;
        //      //  alert.DetectedAt = DateTime.UtcNow;
        //        cyberAlerts.Add(alert);
        //        return alert;
        //    });

        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAlert>()))
            .ReturnsAsync((CyberAlert alert) =>
            {
                if (alert == null)
                    throw new ArgumentNullException(nameof(alert), "CyberAlert passed to AddAsync is null. Check your handler mapping.");
                alert.ReferenceId = Guid.NewGuid().ToString();
                alert.Id = cyberAlerts.Count + 1;
                alert.IsActive = true;
                cyberAlerts.Add(alert);
                return alert;
            });
        // UpdateAsync - Updates existing entity
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAlert>()))
            .Returns<CyberAlert>(async (CyberAlert alert) =>
            {
                var existingAlert = cyberAlerts.FirstOrDefault(x => x.ReferenceId == alert.ReferenceId);
                if (existingAlert != null)
                {
                    //existingAlert.Title = alert.Title;
                    //existingAlert.Description = alert.Description;
                    //existingAlert.Severity = alert.Severity;
                    //existingAlert.Priority = alert.Priority;
                    //existingAlert.Status = alert.Status;
                    //existingAlert.AcknowledgedAt = alert.AcknowledgedAt;
                    //existingAlert.AcknowledgedBy = alert.AcknowledgedBy;
                    //existingAlert.ResolvedAt = alert.ResolvedAt;
                    //existingAlert.ResolvedBy = alert.ResolvedBy;
                    //existingAlert.ResolutionNotes = alert.ResolutionNotes;
                    return existingAlert;
                }
                return alert;
            });

        // DeleteAsync - Soft delete
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberAlert>()))
            .Returns<CyberAlert>(async (CyberAlert alert) =>
            {
                var existingAlert = cyberAlerts.FirstOrDefault(x => x.ReferenceId == alert.ReferenceId);
                if (existingAlert != null)
                {
                    existingAlert.IsActive = false;
                }
                return alert;
            });

        return mockRepository;
    }

    public static Mock<ICyberComponentRepository> CreateCyberComponentRepository(List<CyberComponent> cyberComponents)
    {
        var mockRepository = new Mock<ICyberComponentRepository>();

        // ListAllAsync - Returns only active entities
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberComponents.Where(x => x.IsActive).ToList());

        // GetByReferenceIdAsync - Returns active entity by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberComponents.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        //mockRepository.Setup(repo => repo.GetByComponentIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string componentId) => cyberComponents.FirstOrDefault(x => x.ComponentId == componentId && x.IsActive));

        //mockRepository.Setup(repo => repo.GetByTypeAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string type) => cyberComponents.Where(x => x.Type == type && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetByGroupIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string groupId) => cyberComponents.Where(x => x.GroupId == groupId && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetBySiteIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string siteId) => cyberComponents.Where(x => x.SiteId == siteId && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetByStatusAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string status) => cyberComponents.Where(x => x.Status == status && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetActiveComponentsAsync())
        //    .ReturnsAsync(cyberComponents.Where(x => x.Status == "Active" && x.IsActive).ToList());

        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberComponent>()))
            .ReturnsAsync((CyberComponent component) =>
            {
                component.ReferenceId = Guid.NewGuid().ToString();
                component.Id = cyberComponents.Count + 1;
                component.IsActive = true;
               // component.LastHeartbeat = DateTime.UtcNow;
                cyberComponents.Add(component);
                return component;
            });

        // UpdateAsync - Updates existing entity
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberComponent>()))
            .Returns<CyberComponent>(async (CyberComponent component) =>
            {
                var existingComponent = cyberComponents.FirstOrDefault(x => x.ReferenceId == component.ReferenceId);
                if (existingComponent != null)
                {
                    existingComponent.Name = component.Name;
                    existingComponent.Description = component.Description;
                   // existingComponent.Version = component.Version;
                    existingComponent.Status = component.Status;
                    //existingComponent.Health = component.Health;
                    //existingComponent.Configuration = component.Configuration;
                    //existingComponent.Metadata = component.Metadata;
                    //existingComponent.LastHeartbeat = DateTime.UtcNow;
                    return existingComponent;
                }
                return component;
            });

        // DeleteAsync - Soft delete
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberComponent>()))
            .Returns<CyberComponent>(async (CyberComponent component) =>
            {
                var existingComponent = cyberComponents.FirstOrDefault(x => x.ReferenceId == component.ReferenceId);
                if (existingComponent != null)
                {
                    existingComponent.IsActive = false;
                }
                return component;
            });

        return mockRepository;
    }
    public static Mock<ICyberComponentGroupRepository> CreateCyberComponentGroupRepository(List<CyberComponentGroup> cyberComponentGroups)
    {
        var mockRepository = new Mock<ICyberComponentGroupRepository>();

        // ListAllAsync - Returns only active entities
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberComponentGroups.Where(x => x.IsActive).ToList());

        // GetByReferenceIdAsync - Returns active entity by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberComponentGroups.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        // IsNameExist - Checks if name exists for different entity
        mockRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) =>
                cyberComponentGroups.Any(x => x.GroupName == name && x.ReferenceId != id && x.IsActive));

        // GetCyberComponentGroupsBySiteId - Returns groups by site ID
        mockRepository.Setup(repo => repo.GetCyberComponentGroupsBySiteId(It.IsAny<string>()))
            .ReturnsAsync((string siteId) => cyberComponentGroups.Where(x => x.SiteId == siteId && x.IsActive).ToList());

        // GetComponentGroupsByComponentId - Returns groups containing specific component
        mockRepository.Setup(repo => repo.GetComponentGroupsByComponentId(It.IsAny<string>()))
            .ReturnsAsync((string componentId) => cyberComponentGroups.Where(x =>
                x.ComponentProperties != null && x.ComponentProperties.Contains(componentId) && x.IsActive).ToList());

        // PaginatedListAllAsync - Returns paginated results
        //mockRepository.Setup(repo => repo.PaginatedListAllAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<Specification<CyberComponentGroup>>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, Specification<CyberComponentGroup> spec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredGroups = cyberComponentGroups.Where(x => x.IsActive).ToList();
        //        var totalCount = filteredGroups.Count;
        //        var pagedGroups = filteredGroups.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();

        //        return new PaginatedResult<CyberComponentGroup>(pagedGroups, totalCount, pageNumber, pageSize);
        //    });

        // GetPaginatedQuery - Returns queryable for pagination
        mockRepository.Setup(repo => repo.GetPaginatedQuery())
            .Returns(cyberComponentGroups.Where(x => x.IsActive).AsQueryable());

        // AddAsync - Adds new entity
        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberComponentGroup>()))
            .ReturnsAsync((CyberComponentGroup group) =>
            {
                group.ReferenceId = Guid.NewGuid().ToString();
                group.Id = cyberComponentGroups.Count + 1;
                group.IsActive = true;
                cyberComponentGroups.Add(group);
                return group;
            });

        // UpdateAsync - Updates existing entity
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberComponentGroup>()))
            .Returns<CyberComponentGroup>(async (CyberComponentGroup group) =>
            {
                var existingGroup = cyberComponentGroups.FirstOrDefault(x => x.ReferenceId == group.ReferenceId);
                if (existingGroup != null)
                {
                    existingGroup.GroupName = group.GroupName;
                    existingGroup.ComponentProperties = group.ComponentProperties;
                    existingGroup.SiteId = group.SiteId;
                    existingGroup.SiteName = group.SiteName;
                    existingGroup.IsActive = group.IsActive;
                    return existingGroup;
                }
                return group;
            });

        // DeleteAsync - Soft delete
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberComponentGroup>()))
            .Returns<CyberComponentGroup>(async (CyberComponentGroup group) =>
            {
                var existingGroup = cyberComponentGroups.FirstOrDefault(x => x.ReferenceId == group.ReferenceId);
                if (existingGroup != null)
                {
                    existingGroup.IsActive = false;
                }
                return group;
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for empty CyberComponentGroup list scenarios
    /// </summary>
    public static Mock<ICyberComponentGroupRepository> GetCyberComponentGroupEmptyRepository()
    {
        return CreateCyberComponentGroupRepository(new List<CyberComponentGroup>());
    }

    /// <summary>
    /// Creates a mock repository for delete scenarios with validation
    /// </summary>
    public static Mock<ICyberComponentGroupRepository> DeleteCyberComponentGroupRepository(List<CyberComponentGroup> cyberComponentGroups)
    {
        var mockRepository = CreateCyberComponentGroupRepository(cyberComponentGroups);

        // Override UpdateAsync for delete scenarios
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberComponentGroup>()))
            .Returns<CyberComponentGroup>(async (CyberComponentGroup group) =>
            {
                var existingGroup = cyberComponentGroups.FirstOrDefault(x => x.ReferenceId == group.ReferenceId);
                if (existingGroup != null)
                {
                    existingGroup.IsActive = group.IsActive;
                    return existingGroup;
                }
                return group;
            });

        return mockRepository;
    }

    public static Mock<ICyberComponentMappingRepository> CreateCyberComponentMappingRepository(List<CyberComponentMapping> cyberComponentMappings)
    {
        var mockRepository = new Mock<ICyberComponentMappingRepository>();

        // ListAllAsync - Returns only active entities
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberComponentMappings.Where(x => x.IsActive).ToList());

        // GetByReferenceIdAsync - Returns active entity by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberComponentMappings.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        //mockRepository.Setup(repo => repo.GetByMappingIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string mappingId) => cyberComponentMappings.FirstOrDefault(x => x.MappingId == mappingId && x.IsActive));

        //mockRepository.Setup(repo => repo.GetBySourceComponentIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string sourceComponentId) => cyberComponentMappings.Where(x => x.SourceComponentId == sourceComponentId && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetByTargetComponentIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string targetComponentId) => cyberComponentMappings.Where(x => x.TargetComponentId == targetComponentId && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetByMappingTypeAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string mappingType) => cyberComponentMappings.Where(x => x.MappingType == mappingType && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetComponentRelationshipsAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string componentId) => cyberComponentMappings.Where(x =>
        //        (x.SourceComponentId == componentId || x.TargetComponentId == componentId) && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetMappingBetweenComponentsAsync(It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((string sourceId, string targetId) => cyberComponentMappings.FirstOrDefault(x =>
        //        x.SourceComponentId == sourceId && x.TargetComponentId == targetId && x.IsActive));

        //mockRepository.Setup(repo => repo.GetActiveMappingsAsync())
        //    .ReturnsAsync(cyberComponentMappings.Where(x => x.Status == "Active" && x.IsActive).ToList());

        // AddAsync - Adds new entity
        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberComponentMapping>()))
            .ReturnsAsync((CyberComponentMapping mapping) =>
            {
                mapping.ReferenceId = Guid.NewGuid().ToString();
                mapping.Id = cyberComponentMappings.Count + 1;
                mapping.IsActive = true;
               // mapping.LastValidated = DateTime.UtcNow;
                cyberComponentMappings.Add(mapping);
                return mapping;
            });

        // UpdateAsync - Updates existing entity
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberComponentMapping>()))
            .Returns<CyberComponentMapping>(async (CyberComponentMapping mapping) =>
            {
                var existingMapping = cyberComponentMappings.FirstOrDefault(x => x.ReferenceId == mapping.ReferenceId);
                if (existingMapping != null)
                {
                    existingMapping.Name = mapping.Name;
                    //existingMapping.Description = mapping.Description;
                    //existingMapping.Priority = mapping.Priority;
                    //existingMapping.Status = mapping.Status;
                    //existingMapping.Configuration = mapping.Configuration;
                    //existingMapping.Metadata = mapping.Metadata;
                    //existingMapping.Rules = mapping.Rules;
                    //existingMapping.Conditions = mapping.Conditions;
                    //existingMapping.LastValidated = DateTime.UtcNow;
                    return existingMapping;
                }
                return mapping;
            });

        // DeleteAsync - Soft delete
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberComponentMapping>()))
            .Returns<CyberComponentMapping>(async (CyberComponentMapping mapping) =>
            {
                var existingMapping = cyberComponentMappings.FirstOrDefault(x => x.ReferenceId == mapping.ReferenceId);
                if (existingMapping != null)
                {
                    existingMapping.IsActive = false;
                }
                return mapping;
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock IUserActivityRepository for all cyber modules
    /// Provides user activity logging functionality for audit trails
    /// </summary>
    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockRepository = new Mock<IUserActivityRepository>();

        // ListAllAsync - Returns only active entities
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities.Where(x => x.IsActive).ToList());

        // GetByReferenceIdAsync - Returns active entity by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => userActivities.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        //mockRepository.Setup(repo => repo.GetByUserIdAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string userId) => userActivities.Where(x => x.UserId == userId && x.IsActive).ToList());

        //mockRepository.Setup(repo => repo.GetByEntityAsync(It.IsAny<string>()))
        //    .ReturnsAsync((string entity) => userActivities.Where(x => x.Entity == entity && x.IsActive).ToList());

        // AddAsync - Adds new entity
        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity activity) =>
            {
                activity.ReferenceId = Guid.NewGuid().ToString();
                activity.Id = userActivities.Count + 1;
                activity.IsActive = true;
                activity.CreatedDate = DateTime.UtcNow;
                userActivities.Add(activity);
                return activity;
            });

        return mockRepository;
    }
}
