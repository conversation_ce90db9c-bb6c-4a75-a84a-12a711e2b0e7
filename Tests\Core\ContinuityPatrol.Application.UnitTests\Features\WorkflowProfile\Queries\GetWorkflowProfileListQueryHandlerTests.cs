﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Queries;

public class GetWorkflowProfileListQueryHandlerTests : IClassFixture<WorkflowProfileFixture>, IClassFixture<WorkflowProfileInfoFixture>, IClassFixture<WorkflowFixture>, IClassFixture<WorkflowProfileInfoViewFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;
    private readonly WorkflowProfileInfoViewFixture _workflowProfileInfoViewFixture;
    private Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;
    private readonly Mock<IWorkflowViewRepository> _mockWorkflowViewRepository;
    private readonly Mock<IWorkflowProfileInfoViewRepository> _mockWorkflowProfileInfoViewRepository;
    private readonly GetWorkflowProfileListQueryHandler _handler;

    public GetWorkflowProfileListQueryHandlerTests(WorkflowProfileFixture workflowProfileFixture, WorkflowProfileInfoFixture workflowProfileInfoFixture, WorkflowProfileInfoViewFixture workflowProfileInfoViewFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;
        _workflowProfileInfoFixture = workflowProfileInfoFixture;
        _workflowProfileInfoViewFixture = workflowProfileInfoViewFixture;


        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetWorkflowProfileRepository(_workflowProfileFixture.WorkflowProfiles);

        _mockWorkflowProfileInfoViewRepository = WorkflowProfileInfoViewRepositoryMocks.GetRunningProfileByProfileIds(_workflowProfileInfoViewFixture.WorkflowProfileInfoViews);

        var profileIds = _workflowProfileFixture.WorkflowProfiles
            .Select(x => x.ReferenceId).ToList();

        _mockWorkflowViewRepository = WorkflowViewRepositoryMocks.GetWorkflowByProfileIds(profileIds);

        _handler = new GetWorkflowProfileListQueryHandler(_workflowProfileFixture.Mapper, _mockWorkflowProfileRepository.Object, mockLoggedInUserService.Object, _mockWorkflowViewRepository.Object, _mockWorkflowProfileInfoViewRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowProfileCount()
    {
        var result = await _handler.Handle(new GetWorkflowProfileListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowProfileListQuery(), CancellationToken.None);

        _mockWorkflowProfileRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_Valid_WorkflowProfileList()
    {
        var result = await _handler.Handle(new GetWorkflowProfileListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileListVm>>();

        result[0].Id.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].ReferenceId);
        result[0].Name.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Name);
        result[0].Status.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Status);
        result[0].Password.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Password);
    }

    [Fact]
    public async Task Handle_Return_Valid_IsAllInfra_WorkflowProfileList()
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetWorkflowProfileRepository(_workflowProfileFixture.WorkflowProfiles);

        var mockWorkflowProfileInfoViewRepository = WorkflowProfileInfoViewRepositoryMocks.GetRunningProfileByProfileIds(_workflowProfileInfoViewFixture.WorkflowProfileInfoViews);

        var profileIds = _workflowProfileFixture.WorkflowProfiles
            .Select(x => x.ReferenceId).ToList();

        var mockWorkflowViewRepository = WorkflowViewRepositoryMocks.GetWorkflowByProfileIds(profileIds);

        var handler = new GetWorkflowProfileListQueryHandler
            (_workflowProfileFixture.Mapper,
                mockWorkflowProfileRepository.Object, 
                mockLoggedInUserService.Object,
                mockWorkflowViewRepository.Object,
                mockWorkflowProfileInfoViewRepository.Object);

        var result = await handler.Handle(new GetWorkflowProfileListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileListVm>>();

        result[0].Id.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].ReferenceId);
        result[0].Name.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Name);
        result[0].Status.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Status);
        result[0].Password.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Password);
    }


    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetWorkflowProfileEmptyRepository();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var handler = new GetWorkflowProfileListQueryHandler(_workflowProfileFixture.Mapper, _mockWorkflowProfileRepository.Object, mockLoggedInUserService.Object, _mockWorkflowViewRepository.Object, _mockWorkflowProfileInfoViewRepository.Object);

        var result = await handler.Handle(new GetWorkflowProfileListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileListVm>>();

        result.Count.ShouldBe(0);
    }
}