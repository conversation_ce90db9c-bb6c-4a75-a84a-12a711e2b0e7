using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetCyberAlertCount;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Queries;

public class GetCyberAlertsByseverityTests : IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
    private readonly GetCyberAlertPaginatedListQueryHandler _handler;
    private readonly Mock<IMapper> _mapper;

    public GetCyberAlertsByseverityTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);
        //_mockLogger = new Mock<ILogger<GetCyberAlertCountQueryHandler>>();

        _mapper = new Mock<IMapper>();
        _handler = new GetCyberAlertPaginatedListQueryHandler(_mapper.Object,
            _mockCyberAlertRepository.Object
            );
    }

    [Fact]
    public async Task Handle_GetCyberAlertsByseverity_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "High"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnOnlyAlertsWithSpecifiedseverity_When_ValidQuery()
    {
        // Arrange
        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(severityLevels.Length * 2).ToList();

        // Set different severities for testing
        for (int i = 0; i < alerts.Count; i++)
        {
            alerts[i].Severity = severityLevels[i % severityLevels.Length];
        }

        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "Critical"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoAlertsMatchseverity()
    {
        // Arrange
        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "NonExistentseverity"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "High"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

       
    }

    [Fact]
    public async Task Handle_ProcessAllseverityLevels_When_ValidQueries()
    {
        // Arrange
        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(severityLevels.Length * 3).ToList();

        // Set different severities for testing
        for (int i = 0; i < alerts.Count; i++)
        {
           // alerts[i].severity = severityLevels[i % severityLevels.Length];
        }

        foreach (var severity in severityLevels)
        {
            var query = new GetCyberAlertPaginatedListQuery
            {
                severity = severity
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            query.ShouldNotBeNull();
        }
    }

    /// <summary>
    /// Test: Query handler handles case sensitivity correctly
    /// Expected: severity matching is case-sensitive
    /// </summary>
    [Fact]
    public async Task Handle_HandleCaseSensitivity_When_DifferentCasing()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlerts.Take(3).ToList();
        alerts[0].Severity = "High";
        alerts[1].Severity = "HIGH";
        alerts[2].Severity = "high";

        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "High"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
         query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnAlertsWithCompleteInformation_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "Critical"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();

    }

   
    [Fact]
    public async Task Handle_FilterByseverityAcrossStatuses_When_ValidQuery()
    {
        // Arrange
        var statuses = new[] { "Open", "Acknowledged", "In Progress", "Resolved" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(statuses.Length).ToList();

        for (int i = 0; i < alerts.Count; i++)
        {
            alerts[i].Severity = "High";
           // alerts[i].Status = statuses[i];
        }

        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "High"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_FilterByseverityAcrossTypes_When_ValidQuery()
    {
        // Arrange
        var alertTypes = new[] { "Intrusion", "Malware", "DDoS", "Phishing" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(alertTypes.Length).ToList();

        for (int i = 0; i < alerts.Count; i++)
        {
            alerts[i].Severity = "Critical";
            alerts[i].Type = alertTypes[i];
        }

        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "Critical"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
        
        
    }

  
    [Fact]
    public async Task Handle_HandleInvalidseverity_When_NullOrEmptyseverity()
    {
        // Arrange
        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = null
        };

       
    }

    
    [Fact]
    public async Task Handle_ReturnAlertsOrderedByDetectionDate_When_ValidQuery()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlerts.Take(5).ToList();
        for (int i = 0; i < alerts.Count; i++)
        {
            alerts[i].Severity = "High";
           // alerts[i].DetectedAt = DateTime.UtcNow.AddHours(-i);
        }

        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "High"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();

        
    }

    [Fact]
    public async Task Handle_HandleLargeDatasetEfficiently_When_ManyAlerts()
    {
        // Arrange
        var additionalAlerts = _cyberAlertFixture.AutoCyberAlertFixture.CreateMany<Domain.Entities.CyberAlert>(100).ToList();
        foreach (var alert in additionalAlerts)
        {
            alert.Severity = "Critical";
        }
        _cyberAlertFixture.CyberAlerts.AddRange(additionalAlerts);

        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "Critical"
        };

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await _handler.Handle(query, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ProcessRapidseverityQueries_When_MultipleQueries()
    {
        // Arrange
        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };
        var queries = severityLevels.Select(severity => new GetCyberAlertPaginatedListQuery
        {
            severity = severity
        }).ToList();

        // Act
        var tasks = queries.Select(query => _handler.Handle(query, CancellationToken.None));
        var results = await Task.WhenAll(tasks);

        // Assert
        results.Length.ShouldBe(4);
    }

    [Fact]
    public async Task Handle_IncludeWorkflowInformation_When_ValidQuery()
    {
        // Arrange
        var acknowledgedAlert = _cyberAlertFixture.CyberAlerts.First();

        var query = new GetCyberAlertPaginatedListQuery
        {
            severity = "High"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }
}
