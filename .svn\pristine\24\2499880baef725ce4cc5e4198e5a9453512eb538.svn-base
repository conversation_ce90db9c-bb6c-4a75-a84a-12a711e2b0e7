﻿using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator.Helper;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;

public class BulkImportValidatorCommand
{
    public bool IsServer { get; set; }
    public bool IsDatabase { get; set; }
    public bool IsReplication { get; set; }
    public bool IsInfraObject { get; set; }

    public List<CreateBulkImportComponentServerCommand> ServerList { get; set; }
    public List<CreateBulkImportComponentDataBaseCommand> DatabaseList { get; set; }
    public List<CreateBulkImportComponentReplicationCommand> ReplicationList { get; set; }
    public CreateBulkImportComponentInfraObjectCommand InfraObject { get; set; }
}