﻿const errorElements = ['#NameError', '#DescriptionError', '#SelectActiveTypeError', '#DRSelectServerNamesError', "#SelectSubTypeError",
    "#BusinessServiceError", "#SelectReplicationTypeError", "#SelectPriorityError", '#SelectAssociateError', '#SelectReplicationNameError',
    '#BusinessFunctionError', '#SelectPairInfraError', '#SelectReplicationError', '#SelectServerNameError', '#PRSelectDatabaseError',
    '#DRSelectDatabaseNamesError', "#DatbaseTypeError", '#prSrmServerError', '#drSrmServerError', '#siteTypeError'];

const dynamicErrorElements = ['#DatbaseTypeError, #SelectServerNameError', '#DRSelectServerNamesError', '#PRSelectDatabaseError',
    '#DRSelectDatabaseNamesError', '#SelectReplicationNameError', '#DRSelectReplicationNamesError', '#NearReplicationNameError',
    '#prSrmServerError', '#drSrmServerError'];

const dynamicElements = ['#SelectServerName', '#PRSelectDatabase', '#PRMultipleServer', '#PRMultipleDatabase', '#DRMultipleServer',
    '#DRMultipleDatabase', '#PRSelectReplicationName', '#DRSelectServerNames', '#DRSelectDatabase', '#prSrmServer',
    '#drSrmServer', '#SelectReplicationNames'];

let isEdit = false;
let currentSrNo = 1;

let NodeValuesLength, activeType = '', databaseType = '', replicationType = '';
let replication = '', Activitydata = '', prServer = '', drServer = '';
let ReplicationtypeName = '', ReplicationCategory = '', database = '';

let siteProperties = [], selectedNodeValues = [], PRdata = [], associateProperties = [], serverList = [], DRdata = [];
let prToDrMapping = {}, infraData = {}, serverProperties = {}, databaseProperties = {}, replicationProperties = {};

let multipleServerDetails = { multiplePRServer: [], multipleDRServer: [] };
let multipleDatabaseDetails = { multiplePRDatabase: [], multipleDRDatabase: [] };
let serverDataMap = new Map();

const clusterReplicationData = ['hyper-v', 'zerto', 'robocopy', 'rsync', 'rpforvmreplication', 'srmvmware']
const exceptThisSymbols = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];

const infraObjectURL = {
    nameExistUrl: "Configuration/InfraObject/IsInfraObjectNameExist",    
    pagination: "/Configuration/InfraObject/GetPagination",
    GetServerRole: "Configuration/Server/GetServerRole",
    GetServerType: "Configuration/Server/GetServerType",
    GetVeritasClusters: "Configuration/InfraObject/GetVeritasClusters",
    GetHACMPClusters: "Configuration/InfraObject/GetHACMPClusters",
    GetSiteTypeDetails: "Configuration/InfraObject/GetSiteTypeDetails",
    GetBusinessFunctions: "Configuration/InfraObject/GetBusinessFunctions",
    GetServerRoleTypeAndServerType: "Configuration/InfraObject/GetServerRoleTypeAndServerType",
    GetDatabase: "Configuration/InfraObject/GetDatabase",
    GetDatabaseListByName: "Configuration/InfraObject/GetDatabaseListByName",
    GetReplicationMasterByInfraMasterName: "Configuration/InfraObject/GetReplicationMasterByInfraMasterName",
    GetTypeByDatabaseIdAndReplicationMasterId: "Configuration/InfraObject/GetTypeByDatabaseIdAndReplicationMasterId",
}

//----pagination--->
$(function () {
 
    const infraObjectPermission = {
        createPermission: $("#configurationCreate").data("create-permission").toLowerCase(),
        deletePermission: $("#configurationDelete").data("delete-permission").toLowerCase()
    }

    if (infraObjectPermission?.createPermission == 'false') {
        $("#infraObject-createbutton").removeClass('#infraObject-createbutton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }

    $("#pairinfra, #associate").hide();
    btnCrudEnable('confirmDeleteButton')

    let selectedValues = [];

    let dataTable = $('#InfraObjectList').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": infraObjectURL?.pagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = (d?.order && d?.order[0]) ? d?.order[0]?.column : ''; 
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "businessServiceName" : sortIndex === 3 ? "businessFunctionName" :
                        sortIndex === 4 ? "typeName" : sortIndex === 5 ? "replicationCategoryType" : sortIndex === 6 ? "state" : sortIndex === 7 ? "status" : "";
                    let orderValue = (d?.order && d?.order[0]) ? d?.order[0]?.dir : 'asc';
                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#infraObjectSearch')?.val() : selectedValues?.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;

                    if (json?.data?.length === 0) $(".pagination-column").addClass("disabled")
                    else $(".pagination-column").removeClass("disabled")

                    return json?.data;
                },
                "error": function (xhr, status, error) {
                    if (error?.status === 401) {
                        window.location.assign('/Account/Logout')
                    }
                },
            },
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },                  
                },

                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "businessServiceName", "name": "Business Service", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "businessFunctionName", "name": "Business Function", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "typeName", "name": "Active type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "replicationCategoryType", "name": "Replication Category", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "state", "name": "State", "autoWidth": true,
                    "render": function (data, type, row) {
                        data = data || 'Maintenance'

                        if (type === 'display') {

                            return `<span title="${data}">
                                     <i class="${data?.toLowerCase() === 'locked' ? 'cp-lock text-warning' : data?.toLowerCase() === 'active' ? 'cp-active-inactive text-success' : data?.toLowerCase() === 'locked' ? 'cp-lock text-warning' : data?.toLowerCase() === 'maintenance' ? 'cp-maintenance text-primary' : '-'}"></i>
                               </span>`;
                        }
                        return data;
                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {

                        const isEditAllowed = infraObjectPermission?.createPermission === "true";
                        const isDeleteAllowed = infraObjectPermission?.deletePermission === "true";
                        const stateClass = row?.state?.toLowerCase() == "active" ? "icon-disabled" : "";
                        const rowData = JSON.stringify(row);

                        const editBtn = isEditAllowed
                            ? `<span role="button" title="Edit" class="edit-button ${stateClass}" data-infra='${rowData}' data-infra-state="${row?.state}">
                                <i class="cp-edit"></i></span>`
                            : `<span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span>`;

                        const deleteBtn = isDeleteAllowed
                            ? `<span role="button" title="Delete" class="delete-button ${stateClass}" data-infra-id="${row?.id}" data-infra-name="${row?.name}" data-bs-toggle="modal">
                               <i class="cp-Delete"></i></span>`
                            : `<span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span>`;

                        return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}</div>`;
                    },
                }
            ],
            "columnDefs": [
                {
                    "targets": [1, 2, 3, 5],
                    "className": "truncate"
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this?.api();
                let startIndex = api?.context[0]?._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#infraObjectSearch').on('keyup input', commonDebounce(function (e) {
        if (e?.key === '=' || e?.key === 'Enter') {
            e.preventDefault();
            return false;
        }

        const inputValue = $('#infraObjectSearch').val();
        selectedValues = [];

        $('.searchCheckbox:checked').each(function () {
            selectedValues.push($(this).val() + inputValue);
        });

        dataTable.ajax.reload(json => {
            if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        });
    }, 500));

    $('#infraObjectSearch').attr('autocomplete', 'off');

    $('.form-select-sm').select2({
        language: {
            noResults: () => "No Results Found"
        }
    });

    // --- Form Submit ---> 
    $("#SaveFunction").on('click', commonDebounce(function () {

        let form = $("#CreateForm");
        form.trigger('submit');
        btnCrudDiasable('SaveFunction');

    }, 800));

})

$('#confirmDeleteButton').on('click', function () {
    btnCrudDiasable('confirmDeleteButton');
})

const getServerRole = async () => {

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + infraObjectURL?.GetServerRole,
        dataType: "json",
        data: {},
        success: function (result) {
            if (result?.success) {
                if (result?.data && Array.isArray(result?.data)) serverList = result?.data
            } else {
                errorNotification(result);
            }
        },
    });
}

const getServerOsType = (id) => {

    let data = []

    if (id) {
        $.ajax({
            type: "GET",
            async: false,
            url: RootUrl + infraObjectURL?.GetServerType,
            dataType: "json",
            data: { id: id },
            success: function (result) {
                if (result?.success) {
                    if (result?.data && Array.isArray(result?.data)) data = result?.data
                } else {
                    errorNotification(result);
                }
            },
        });
    }

    return data
}

const getVeritasHacmpList = async (mode = '') => {

    $('#clusterPR, #clusterDR').empty();

    try {
        const [veritasResult, hacmpResult] = await Promise.all([
            (mode?.toLowerCase() == "veritas") && $.get(RootUrl + infraObjectURL?.GetVeritasClusters),
            (mode?.toLowerCase() == "hacmp") && $.get(RootUrl + infraObjectURL?.GetHACMPClusters)
        ]);

        if (mode?.toLowerCase() == 'veritas') {

            if (veritasResult?.success) {
                $('#clusterPR').append(`<option value=""></option>`);

                if (veritasResult?.data && Array.isArray(veritasResult?.data) && veritasResult?.data.length) {
                    for (let cluster of veritasResult?.data) {
                        $('#clusterPR').append(`<option clusterType="PR" value="${cluster?.id}">${cluster?.clusterName}</option>`);
                    }
                }
            } else {
                errorNotification(veritasResult);
            }
        }

        if (mode?.toLowerCase() == 'hacmp') {
            if (hacmpResult?.success) {
                $('#clusterPR, #clusterDR').append(`<option value=""></option>`);

                if (hacmpResult?.data && Array.isArray(hacmpResult?.data) && hacmpResult?.data.length) {
                    for (let cluster of hacmpResult?.data) {
                        $('#clusterPR').append(`<option clusterType="PR" value="${cluster?.id}">${cluster?.name}</option>`);
                        $('#clusterDR').append(`<option clusterType="DR" value="${cluster?.id}">${cluster?.name}</option>`);
                    }
                }
            } else {
                errorNotification(hacmpResult);
            }
        }

        if (isEdit) {
            serverProperties?.clusters && serverProperties['clusters'].length && serverProperties['clusters'].forEach((server) => {
                if (server?.type == 'PR') $('#clusterPR').val(server?.id).trigger('change')
                else if (server?.type == 'DR') $('#clusterDR').val(server?.id).trigger('change')
            })
        }

    } catch (error) { // console.error(error); 
    }
};

// site type list

const getSiteDetailsByOperationService = async (getSiteProperties) => {

    $('#siteType').empty();

    if (getSiteProperties) {

        let siteData = getSiteProperties ? JSON.parse(getSiteProperties) : {}
        let sitePropertiesKeys = Object.keys(siteData)

        for (let i = 0; i < sitePropertiesKeys?.length; i++) {
            let getSiteCategory = siteData[sitePropertiesKeys[i]]?.category || await getSiteBySiteId(siteData[sitePropertiesKeys[i]]?.Id);

            if (siteData[sitePropertiesKeys[i]]?.Id) {
                $('#siteType').append(`<option data-category='${getSiteCategory}' data-sitetypename='${sitePropertiesKeys[i]}' value='${siteData[sitePropertiesKeys[i]]?.Id}'>
              ${sitePropertiesKeys[i]} (${siteData[sitePropertiesKeys[i]]?.Name})
               </option >`);
            }
        }

        if (isEdit) {
            let getSiteIds = siteProperties?.length && siteProperties.map(data => data?.id)
            if (getSiteIds?.length) $('#siteType').val(getSiteIds)
            else if (infraData?.drReady) $('#siteType option[data-category="DR"]').prop('selected', true).trigger('change')
        }

        if (!$('#siteType')?.val()?.length) $('#siteType option[data-category="Primary"]').prop('selected', true).trigger('change').prop('disabled', true)
    }
}

const getSiteBySiteId = async (id) => {

    let category = '';

    if (id) {
        await $.ajax({
            type: "GET",
            url: RootUrl + infraObjectURL?.GetSiteTypeDetails,
            data: { siteId: id },
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result?.success) {
                    if (result?.data) category = result?.data?.category

                } else {
                    errorNotification(result);
                }

            }
        })
    }

    return category;
}

//---Bussiness Function--->
async function SetBusinessFunction(id) {

    $('#ddlbusinessFunctionId').empty();

    if (id) {
        await $.ajax({
            type: "GET",
            async: false,
            url: RootUrl + infraObjectURL?.GetBusinessFunctions,
            dataType: "json",
            data: { id: id },
            success: function (result) {
                if (result?.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data?.length) {
                        let length = result?.data?.length
                        $('#ddlbusinessFunctionId').prepend('<option value="">Select operational function</option>')
                        
                        for (let i = 0; i < length; i++) {
                            $('#ddlbusinessFunctionId').append('<option  value="' + result?.data[i]?.id + '">' + result?.data[i]?.name + '</option>');
                        }

                        if (length === 1) $('#ddlbusinessFunctionId').val(result?.data[0]?.id).trigger('change')
                    }

                    if (Object.keys(infraData)?.length) {
                        $('#ddlbusinessFunctionId').val(infraData?.businessFunctionId);
                        $('#BusinessFunctionId').val(infraData?.businessFunctionId);
                        $('#BusinessFunctionVal').val(infraData?.businessFunctionName);
                    }

                } else {
                    errorNotification(result);
                }
            },
        });
    }
}

//--- PR & DR Server --->
async function getPRAndDRServerType(roleType, type, serverType = 'PRDBServer', oracleFound = false, srm = false) {
    const role = srm ? 'virtualization' : roleType == 'DB' ?
        'database' : roleType == 'Virtual' ? 'virtualization' : roleType.toLowerCase();

    const filteredServer = serverList?.length && serverList.find(data => data?.name?.toLowerCase() === role);
    const getServerType = (roleType == 'DB' || srm) && getServerOsType(filteredServer?.id);
    const getServerSubType = Array.isArray(getServerType) && getServerType?.length && getServerType.find(data => data?.name?.toLowerCase() === serverType?.toLowerCase());
    const selectorPrefix = (type === 'production') ? 'PR' : 'DR';

    if (filteredServer) {

        let data = {
            roleType: roleType === 'Virtual' ? '' : filteredServer?.id,
            serverType: (roleType === 'DB' || srm) ? getServerSubType?.id : ''
        };

        $(`.${selectorPrefix}ServerName, .${selectorPrefix}DatabaseName`).empty();

        await $.ajax({
            type: "GET",
            async: false,
            url: RootUrl + infraObjectURL?.GetServerRoleTypeAndServerType,
            dataType: "json",
            data: data,
            success: function (result) {
                if (result?.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data?.length) {
                      
                        let length = result?.data?.length

                        $(`.${selectorPrefix}ServerName`).not('[multiple]').append('<option value="" selected hidden disabled>' + "Select Server" + '</option>')

                        for (let index = 0; index < length; index++) {
                            const idAttr = (type === 'production') ? 'prSId' : 'drSId';
                            $(`.${selectorPrefix}ServerName`).append(`<option ${idAttr}=${result?.data[index]?.id} value="${result?.data[index]?.name}">${result?.data[index]?.name}</option>`);
                        }

                        if (Object?.keys(infraData)?.length) {
                            const parsed = infraData?.serverProperties ? JSON.parse(infraData?.serverProperties || '{}') : {};
                            const serverInfo = parsed?.[selectorPrefix];

                            if (oracleFound) {
                                $(`#${selectorPrefix}MultipleServer`).val(serverInfo?.name?.split(',')).trigger('change');
                            } else {
                                $(`#${(type === 'production') ? 'SelectServerName' : 'DRSelectServerNames'}`).val(serverInfo?.name).trigger('change');
                                if ($('#Activetype')?.val() === '2') SetServerID(serverInfo?.id, type, '', false) 
                            }

                            $(`#${(type === 'production') ? 'SelectServerName' : 'DRSelectServerNames'}Error`)
                                .text('')
                                .removeClass('field-validation-error');
                        }
                    }
                } else {
                    errorNotification(result);
                }
            },
        });
    }
}

// srm server
async function SetSrmServerType(roleType, serverType = '', mode = '') {

    let role = roleType?.toLowerCase();
    let filteredServer = serverList?.length && serverList.find(data => data?.name?.toLowerCase() === role)
    let getServerType = getServerOsType(filteredServer?.id);
    let getServerSubType = Array.isArray(getServerType) && getServerType?.length && getServerType.find(data => data?.name?.toLowerCase() === serverType?.toLowerCase());
    const isProduction = mode === 'production';

    if (filteredServer) {

        let data = {
            roleType: filteredServer?.id,
            serverType: getServerSubType?.id
        };

        if (isProduction) $('#prSrmServer').empty();
        else $('#drSrmServer').empty();

        await $.ajax({
            type: "GET",
            async: false,
            url: RootUrl + infraObjectURL?.GetServerRoleTypeAndServerType,
            dataType: "json",
            data: data,
            success: function (result) {
                if (result?.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data?.length) {

                        $('#prSrmServer, #drSrmServer').append('<option value="">Select Server</option>');

                        for (let index = 0; index < result?.data?.length; index++) {

                            $(`#${isProduction ? 'pr' : 'dr'}SrmServer`).append(`<option srmType="${isProduction ? 'PR' : 'DR'}" SrmName="${result?.data[index]?.name}" value="${result?.data[index]?.id}">${result?.data[index]?.name}</option>`);
                        }

                        if (Object.keys(infraData)?.length) {
                            serverProperties?.SRMServer && serverProperties['SRMServer']?.length && serverProperties['SRMServer'].forEach((server) => {
                                if (server?.type == 'PR' && isProduction) {
                                    $('#prSrmServer').val(server?.id);
                                    $('#tablesrm, #prSrm').show();
                                } else if (server?.type == 'DR') {
                                    $('#drSrmServer').val(server?.id);
                                    $('#drSrm').show();
                                }

                            })
                        }
                    }

                } else {
                    errorNotification(result);
                }
            },
        });

    }
}

//---PR & DR DataBase--->
async function SetServerID(id, type = 'production', selectType = '') {
    
    const selectorPrefix = (type === 'production') ? 'PR' : 'DR';
    const $dbName = $(`.${selectorPrefix}DatabaseName`);
    const $multipleSelect = $(`#${selectorPrefix}MultipleDatabase`);

    if (id) {

        if (!selectType?.includes('multiple')) $dbName.empty();

        await $.ajax({
            type: "GET",
            async: false,
            url: RootUrl + infraObjectURL?.GetDatabase,
            dataType: "json",
            data: { id: id },
            success: function (result) {
                if (result?.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data?.length) {

                        const dbIdAttr = (type === 'production') ? 'prDId' : 'drDId';
                        const srvIdAttr = (type === 'production') ? 'prServerId' : 'drServerId';
                        $dbName.not('[multiple]').append('<option value="" selected hidden disabled>' + "Select Database" + '</option>')

                        for (let index = 0; index < result?.data?.length; index++) {

                            $dbName.append(`
                               <option data-type="${result?.data[index]?.type}" ${srvIdAttr}="${result?.data[index]?.serverId}" ${dbIdAttr}="${result?.data[index]?.id}" value="${result?.data[index]?.name?.trim()}">
                               ${result?.data[index]?.name}</option>`);
                        }

                        if (result?.data?.length === 1 && !isEdit) {
                            if ($(`#${selectorPrefix}SelectDatabase`).is(':visible')) {
                                $(`#${selectorPrefix}SelectDatabase`).val(result?.data[0]?.name).trigger('change');
                            }
                        }

                        if (Object?.keys(infraData)?.length) {
                            const oracleFound = getOracleFound()
                            const parsed = infraData?.databaseProperties ? JSON.parse(infraData?.databaseProperties || '{}') : {};
                            const dbData = parsed?.[selectorPrefix]; 

                            const isMssql = (type === 'production') ? false : getMssqlFound(); 
                            const shouldUseMultiple = oracleFound || isMssql;

                            if (shouldUseMultiple) {
                                $multipleSelect.val(dbData?.name?.split(',')).trigger('change');
                            } else {
                                $(`#${selectorPrefix}SelectDatabase`).val(dbData?.name?.trim()).trigger('change');
                            }
                        }
                    }
                } else {
                    errorNotification(result);
                }
            },
        });
    }
}

//--- Replication Category --->
async function SetReplicationMaster(type) {
    let data = {
        infraMasterName: type == 'Database' ? 'DB' : type
    };

    $('#SelectReplicationType, #ddlReplicationTypeNameId').empty();

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + infraObjectURL?.GetReplicationMasterByInfraMasterName,
        dataType: "json",
        data: data,
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data?.length) {
                    $('#SelectReplicationType').append('<option value="">Select replication category</option>')

                    for (let i = 0; i < result?.data?.length; i++) {
                        $('#SelectReplicationType').append('<option  value=' + result?.data[i]?.id + '>' + result?.data[i]?.name + '</option>');
                    }
                }

                if (Object?.keys(infraData)?.length) {
                    $('#SelectReplicationType').val(infraData?.replicationCategoryTypeId)
                    $('#infraReplicationTypeId').val(infraData?.replicationCategoryTypeId);
                    $('#ReplicationCategorytext').val(infraData?.replicationCategoryType);
                    $('#SelectReplicationTypeError').text('').removeClass('field-validation-error')

                    replicationType = infraData?.replicationCategoryTypeId
                    SetReplicationMapping()

                    if ((infraData?.replicationCategoryType !== "Application - No Replication") || (infraData?.replicationCategoryType !== "Database - No Replication")) {
                        $("#DRReplication, #PRReplication,#tablereplication").show();
                        $('#SelectReplicationNameError, #DRSelectReplicationNamesError').text('').removeClass('field-validation-error');
                    }
                }
            } else {
                errorNotification(result);
            }
        },
    });
}

//--- Replication Type --->
async function SetReplicationMapping() {
    $('#ddlReplicationTypeNameId').empty();

    let data = {
        databaseid: activeType === "DB" ? databaseType : '',
        replicationmasterid: replicationType,
        type: activeType ? activeType == 'DB' ? 'Database' : activeType : infraData?.typeName
    };

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + infraObjectURL?.GetTypeByDatabaseIdAndReplicationMasterId,
        dataType: "json",
        data: data,
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data?.length) {
                    let uniqueIds = new Set();
                    $('#ddlReplicationTypeNameId').append('<option value=""> Select replication type </option>');

                    for (let index = 0; index < result?.data?.length; index++) {
                        let properties = JSON.parse(result?.data[index]?.properties || '[]')
                        if (properties.length) {
                            for (let j = 0; j < properties?.length; j++) {

                                if (!uniqueIds.has(properties[j]?.id)) {
                                    $('#ddlReplicationTypeNameId').append('<option value="' + properties[j]?.id + '">' + properties[j]?.label + '</option>');
                                    uniqueIds.add(properties[j]?.id)
                                }
                            }
                        }
                    }
                }

                if (Object?.keys(infraData)?.length) {
                    $('#ddlReplicationTypeNameId').val(infraData?.replicationTypeId);
                    $('#ReplicationTypeName').val(infraData?.replicationTypeName);
                    $('#ReplicationTypeId').val(infraData?.replicationTypeId);
                    $('#SelectReplicationError').text('').removeClass('field-validation-error');
                }
            } else {
                errorNotification(result);
            }
        },
    });
}

//--- Database Name --->
async function GetdabaseNames() {

    $('#SelectDatabaseType').empty()

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + infraObjectURL?.GetDatabaseListByName,
        dataType: "json",
        data: {},
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data?.length) {

                    $('#SelectDatabaseType').append('<option value="" hidden selected>Select database</option>');

                    result?.data.forEach((s) => {
                        let optionValues = s?.properties ? JSON.parse(s?.properties) : {}
                        $('#SelectDatabaseType').append('<option value="' + s?.id + '">' + optionValues['name'] + '</option>');
                    })

                    if (Object?.keys(infraData)?.length) {
                        $('#SelectDatabaseType').val(infraData?.subTypeId)
                        $('#DatabaseId').val(infraData?.subTypeId);
                        $('#databaseText').val(infraData?.subType);
                    }
                }
            } else {
                errorNotification(result);
            }
        },
    });
}

// load dynamic servers, database, replication

async function SetDynamicServerType(urls, type, serverType, selectType = 'server', value = '', sType = '', srm = false) {

    let html = ''
    let getActiveType = $('#Activetype option:selected').text();

    if (type === 'dynamic') {

        let data = {};

        const role = (srm && sType != 'dresxiappserver') ? 'virtualization' : getActiveType == 'DB' ?
            'database' : getActiveType == 'Virtual' ? 'virtualization' : getActiveType?.toLowerCase();

        const filteredServer = serverList?.length && serverList.find(data => data?.name?.toLowerCase() === role)
        const getServerType = (getActiveType == 'DB' || srm) && getServerOsType(filteredServer?.id);
        const getServerSubType = Array.isArray(getServerType) && getServerType?.length && getServerType.find(data => data?.name?.toLowerCase() === sType?.toLowerCase())

        switch (selectType) {
            case 'server':
                if (srm && sType === 'dresxiappserver') {
                    data.roleType = filteredServer?.id;
                    data.serverType = getServerSubType?.id;
                    $(`#${serverType}SrmServer`).empty();
                } else {
                    data.roleType = getActiveType === 'Virtual' ? '' : filteredServer?.id;
                    data.serverType = (getActiveType === 'DB' || srm) ? getServerSubType?.id : '';
                    $(`#${serverType}ServerName, #${serverType}DatabaseName`).empty();
                }
                break;

            case 'multipleserver':
                data.roleType = getActiveType === 'Virtual' ? '' : filteredServer?.id;
                data.serverType = getActiveType === 'DB' ? getServerSubType?.id : '';
                $(`#${serverType}MultipleServer`).empty();
                break;

            case 'database':
                data.id = value;
                $(`#${serverType}DatabaseName`).empty();
                break;

            default: 
                data.roleType = '';
                data.serverType = '';
                $(`#${serverType}ReplicationName`).empty();
                break;
        }

        if (filteredServer) {

            await $.ajax({
                type: "GET",
                async: false,
                url: RootUrl + urls,
                dataType: "json",
                data: data,
                success: function (result) {
                    if (result?.success) {

                            if (result?.data && Array.isArray(result?.data) && result?.data?.length) {

                                let length = result?.data?.length

                                html += `<option value="" hidden selected>Select ${selectType}</option>`

                                for (let index = 0; index < length; index++) {
                                    selectType == 'server' ? html += '<option  value="' + result?.data[index]?.id + '">' + result?.data[index]?.name + '</option>'
                                        : html += '<option value="' + result?.data[index]?.id + '">' + result?.data[index]?.name + '</option>'
                                }
                            }

                    } else {
                        errorNotification(result);
                    }
                },
            });
        }
    }

    return html
}

async function SetDynamicDatabaseType(urls, type, serverType = 'PRDBServer', selectType = 'database', value = '') {

    let oracleFound = getOracleFound() || getMssqlFound();

    if (type === 'dynamic' && value) {

        let data = { id: value };

        if (selectType === 'database') {
            serverDataMap = new Map();
            $(`#${serverType}DatabaseName`).val('').empty();
        }

        await $.ajax({
            type: "GET",
            async: false,
            url: RootUrl + urls,
            dataType: "json",
            data: data,
            success: function (result) {
                if (result?.success) {

                        let $select = selectType !== 'database' ? $(`#${serverType}MultipleDatabase`) : $(`#${serverType}DatabaseName`);

                        if (result?.data && Array.isArray(result?.data) && result?.data?.length) {

                            let length = result?.data?.length
                            let selectize = $select && $select[0]?.selectize;

                            if (selectize) {

                                selectize.disable();
                                if (selectType === 'database') {

                                    selectize.options = {};
                                    selectize.clearOptions();
                                    selectize.clear();
                                }

                                for (let index = 0; index < length; index++) {

                                    let serverId = result?.data[index]?.serverId;
                                    selectize.addOption({
                                        value: result?.data[index]?.id,
                                        text: result?.data[index]?.name,
                                    });

                                    serverDataMap.set(result?.data[index]?.id, { serverId });
                                };

                                if (selectType === 'database') selectize.refreshOptions();
                                selectize.enable();
                                selectize.close();
                            }
                        }
                } else {
                    errorNotification(result);
                }
            },
        });
    }

    if (isEdit) {

        Object.keys(databaseProperties)?.length && Object.keys(databaseProperties).forEach((database) => {
            if (databaseProperties[database] && $(`#${databaseProperties[database]?.siteId}DatabaseName`)?.length && databaseProperties[database]?.type == 'customdatabase') {
                if (oracleFound) {

                    multipleDatabaseDetails[database] = databaseProperties[database]?.id?.split(',') || []
                    $(`#${databaseProperties[database]?.siteId}MultipleDatabase`)[0]?.selectize.setValue(multipleDatabaseDetails[database]);

                } else {
                    $(`#${databaseProperties[database]?.siteId}DatabaseName`)[0]?.selectize.setValue(databaseProperties[database]?.id);
                }
            }
        })
    }
}

// Replication List
const getReplicationList = async (urls, selectType) => {
    let html = ''

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + urls,
        dataType: "json",
        data: {},
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data?.length) {
                    let length = result?.data?.length;
                    html += `<option value="" hidden selected>Select ${selectType}</option>`

                    for (let index = 0; index < length; index++) {
                        html += '<option  value="' + result?.data[index]?.id + '">' + result?.data[index]?.name + '</option>'
                    }
                }
            } else {
                errorNotification(result);
            }
        },
    });
    return html
}

const getOracleFound = () => {

    let oracleFound = ((activeType?.replace(/\s+/g, '')?.toLowerCase() === 'db' || activeType == '2') && ReplicationCategory?.replace(/\s+/g, '')?.toLowerCase() === 'nativereplication' &&
        (ReplicationtypeName?.replace(/\s+/g, '')?.toLowerCase() === 'nativereplication-oracle-rac' || ReplicationtypeName?.replace(/\s+/g, '')?.toLowerCase() === 'native-replication-oracle-rac') &&
        (database?.replace(/\s+/g, '')?.toLowerCase() === 'oracle-rac' || database?.replace(/\s+/g, '')?.toLowerCase() === 'oracle')) || ((activeType?.replace(/\s+/g, '')?.toLowerCase() === 'db' || activeType == '2') && ReplicationCategory?.replace(/\s+/g, '')?.toLowerCase() === 'nativereplication' &&
            ReplicationtypeName?.replace(/\s+/g, '')?.toLowerCase().includes('redis') &&
            database?.replace(/\s+/g, '')?.toLowerCase().includes('redis'))

    return oracleFound;
}

const getMssqlFound = () => {

    let mssqlFound = ((activeType?.replace(/\s+/g, '')?.toLowerCase() === 'db' || activeType == '2') && ReplicationCategory?.replace(/\s+/g, '')?.toLowerCase() === 'database-inbuildreplication' &&
        (ReplicationtypeName?.replace(/\s+/g, '')?.toLowerCase() === 'mssql-alwayson-availabilitygroup'))

    return mssqlFound;
}

   // --- Update --->
    $('#InfraObjectList').on('click', '.edit-button', function () {
        btnCrudEnable('SaveFunction');
        $("#steps-uid-0-t-0, #infraObjectWizard-t-0").trigger("click");
        isEdit = true
        infraData = $(this)?.data('infra');

        clearInputInfraFields();
        getServerRole();
        GetdabaseNames()
        populateModalFields(infraData);

        $('#SaveFunction').text('Update')
        $("#ModelSave").text('Update')
        
        if (infraData?.state !== "Active") $('#CreateModal').modal('show')
    });

    // --- delete --->
    $('#InfraObjectList').on('click', '.delete-button', function () {
        let infraDetails = $(this)?.data('infra');

        if (infraDetails) {
            $('#deleteData').text(infraDetails?.name).attr('title', infraDetails?.name);
            $('#textDeleteId').val(infraDetails?.id);

            if (infraDetails?.state !== "Active") {
                $('#DeleteModal').modal('show')
            } else {
                if (!$(this).hasClass('icon-disabled')) $(this).addClass('icon-disabled');
            }
        }
    });

    // --- Create --->
    $('#infraObject-createbutton').on('click', function () {
        btnCrudEnable('SaveFunction');
        $("#steps-uid-0-t-0, #infraObjectWizard-t-0").trigger("click");
        $("#DataTypeCol, #pairinfra, #associate").hide();
        isEdit = false;
        infraData = {}
        siteProperties = []

        clearInputInfraFields();
        getServerRole();
        $("#ModelSave").text('Save')
        $("#Activetype").val('');       
        $(`#SelectPairInfra option, #SelectAssociate option`).prop('disabled', false);
        $('#CreateModal').modal('show')
    });

    function populateSummary() {
        const summaryMap = {
            '#infraObjectTableName': $('#textName').val(),
            '#DescriptionSum': $('#Description').val(),
            '#BusinessServiceSum': $('#infraBusinessServiceId').val(),
            '#TypeSum': $('#Activetype option:selected').text(),
            '#BusinessFunSum': $('#BusinessFunctionVal').val(),
            '#IsPairSum': $('#PairId').prop('checked') ? $("#SelectPairInfra").val() : 'NA',
            '#ReplicationNameSum': $('#ddlReplicationTypeNameId option:selected').text(),
            '#replication_name': $('#SelectReplicationType option:selected').text(),
            '#database_type': $('#SelectDatabaseType').val() ? $('#SelectDatabaseType option:selected').text() : 'NA',
            '#IsAssociateSum': $('#InfraId').prop('checked') ? $("#SelectAssociate").val() : 'NA'
        };

        Object.entries(summaryMap).forEach(([id, val]) => {
            $(id).text(val || 'NA').attr('title', val || 'NA');
        });

        $('#site_category').text(siteProperties.map(site => site.name).join(',') || 'NA');
    }

function approverLists(prData, drData) {
        
        $('#addUserApproval').empty();
        
        const maxLength = Math.max(prData?.length, drData?.length);

        let nodeProperties = infraData?.nodeProperties && JSON.parse(infraData?.nodeProperties);
        let PRNodeData = [];
        let DRNodeData = [];
    if (nodeProperties && typeof nodeProperties === 'object' && Array.isArray(nodeProperties.database)) {
        selectedNodeValues = nodeProperties.database;

        PRNodeData = nodeProperties.database.map(db => db?.prDB || { id: '', value: '' });
        DRNodeData = nodeProperties.database.map(db => db?.drDB || { id: '', value: '' });
    }

        for (let i = 0; i < maxLength; i++) {
            
            const prItem = prData[i % prData.length];
            const drItem = drData[i % drData.length];

            const html = `
        <tr>
            <!-- Primary column with value from prData -->
            <td>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="cp-city-name"></i>
                        </span>
                        <select class="form-select-modal dataVal dataPr" data-placeholder="Select database" data-pr="${prItem}" value="${prData?.length ? prData[0] : ''}">
                            <option value="" disabled selected>Select database</option>
                            ${prData?.map(option => `
                                <option value="${option.value}" data-id="${option.id}"  ${option === getSelectedValueForItem(prItem) ? 'selected' : ''}>${option.value}</option>
                            `).join('')}
                        </select>
                    </div>
                    <span i" class="error-message"></span>
                </div>
            </td>

            <!-- DR column with a select dropdown (populated with drData) -->
            <td>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="cp-city-name"></i>
                        </span>
                        <select class="form-select-modal dataVal dataDr" data-placeholder="Select database" data-pr="${prItem}" data-dr="${drItem}">
                            <option value="" disabled selected>Select database</option>
                            ${drData?.map(option => `
                                <option value="${option.value}" data-id="${option.id}" ${option === getSelectedValueForItem(drItem) ? 'selected' : ''}>${option.value}</option>
                            `).join('')}
                        </select>
                    </div>
                    <span  class="error-message"></span>
                </div>
            </td>
        </tr>`;

            $('#addUserApproval').append(html);
        }

        NodeValuesLength = $("#addUserApproval")?.children()?.length;

        $('#addUserApproval .dataPr').each(function (index, element) {
            $(element).val(PRNodeData[index]?.value);
        });

        $('#addUserApproval .dataDr').each(function (index, element) {
            $(element).val(DRNodeData[index]?.value);
        });
    }

    function getSelectedValueForItem(prItem) {
        return prToDrMapping[prItem] || "";
    }
 
    function populateModalFields(infraData) {

        serverProperties = infraData?.serverProperties ? JSON.parse(infraData?.serverProperties) : {};
        databaseProperties = infraData?.databaseProperties ? JSON.parse(infraData?.databaseProperties) : {};
        replicationProperties = infraData?.replicationProperties ? JSON.parse(infraData?.replicationProperties) : {};
        siteProperties = infraData?.siteProperties ? JSON.parse(infraData?.siteProperties) : []

        databaseType = infraData?.subTypeId
        database = infraData?.subType
        replicationType = infraData?.replicationCategoryTypeId
        activeType = infraData?.typeName
        ReplicationtypeName = infraData?.replicationTypeName
        ReplicationCategory = infraData?.replicationCategoryType

        const clusterFound = clusterReplicationData && clusterReplicationData?.some((rep) => (ReplicationtypeName?.toLowerCase()?.replace(/ /g, "")?.includes(rep)))
        const oracleFound = getOracleFound();
        const mssqlFound = getMssqlFound();

        $('#textName').val(infraData?.name);
        $('#infraNameId').val(infraData?.id);
        $('#Description').val(infraData?.description);
        $('#infraBusinessServiceId').val(infraData?.businessServiceName).trigger('change');
        $('#BusinessServiceId').val(infraData?.businessServiceId)
        $('#Activetype').val(infraData?.type)
        $('#ActiveTypeName').val(infraData?.typeName)
        $('#StateValue').val(infraData?.state);
        $('#SelectPairInfra option, #SelectAssociate option').prop('disabled', false);
        $(`#SelectPairInfra option[infraid="${infraData?.id}"], #SelectAssociate option[Acid="${infraData?.id}"]`).prop('disabled', true);

        $('#drift').prop('checked', !!infraData?.isDrift);
        $('#driftValue').val(!!infraData?.isDrift);

        SetReplicationMapping()
        SetReplicationMaster(infraData?.typeName);

        if (ReplicationtypeName && ReplicationtypeName?.toLowerCase()?.replace(/ /g, "")?.includes('srmvmware')) {

            getPRAndDRServerType(activeType, 'production', 'presxiserver', false, true)
            getPRAndDRServerType(activeType, 'DR', 'dresxiserver', false, true)
            SetSrmServerType(activeType, 'presxiappserver', 'production')
        } else {
            getPRAndDRServerType(infraData?.typeName, 'production', activeType?.toLowerCase() == 'db' ? 'PRDBServer' : '', oracleFound)
            getPRAndDRServerType(infraData?.typeName, 'DR', activeType?.toLowerCase() == 'db' ? 'DRDBServer' : '', oracleFound || mssqlFound)
        }

        if (clusterFound) {
            $('#clusterCol').show();
            if (serverProperties?.isCluster) {
                $('#cluster').show();
                $('#cluster').prop('checked', true).trigger('change')
                $('#clusterType').val(serverProperties?.clusterType);
                if (serverProperties?.clusterType === 'veritas') {
                    $('#veritas-child').show()
                    $('#HACMP-child').hide()
                } else {
                    $('#veritas-child, #HACMP-child').show();
                }

                getVeritasHacmpList(serverProperties?.clusterType);
            } else {           
                $('#clusterProperties, #selectedClusterCol').hide()
            }
        }

        $('#DataTypeCol').toggle(infraData?.typeName === "DB");
        function toggleReplicationDisplay(replicationName, elementId) {
            if (replicationName) $(elementId).show();
            else $(elementId).hide();
        }

        toggleReplicationDisplay(infraData?.prReplicationName, '#PRReplication');
        toggleReplicationDisplay(infraData?.drReplicationName, '#DRReplication');

        const parsedRep = infraData?.replicationProperties && JSON.parse(infraData?.replicationProperties);
        $('#PRSelectReplicationName').val(parsedRep?.PR?.name || "").trigger('change');
        $('#SelectReplicationNames').val(parsedRep?.DR?.name || "").trigger('change');

        if ((infraData?.replicationCategoryType === "Application - No Replication") || (infraData?.replicationCategoryType === "Database - No Replication")
            || infraData?.replicationTypeName?.toLowerCase().replace(/ /g, "").includes('redis')) {
            $("#DRReplication, #PRReplication, #tablereplication").hide();

        } else {
            $("#DRReplication, #PRReplication, #tablereplication").show();
        }

        if (infraData?.typeName === "Application" || infraData?.typeName === "Virtual") $("#prdatabase, #drdatabase, #tabledatabase").hide()
        else $("#prdatabase, #drdatabase, #tabledatabase").show()

        $("#PrioritySum").text(infraData?.priority);
        $('input[name="Priority"]').filter(`[value="${infraData?.priority}"]`).prop('checked', true);

        if (infraData?.isPair) {
            $('#pairinfra').show();
            $('#SelectPairInfra').val(infraData?.pairInfraObjectName);
            $('#PairInfraId').val(infraData?.pairInfraObjectId);
            $('#PairId').prop('checked', infraData?.isPair);
            $('#pairValue').val(infraData?.isPair);
        }else {
            $('#pairinfra').hide();
        }

        if (infraData?.isAssociate) {
            $('#associate').show();
            $('#SelectAssociate').val(infraData?.isAssociateInfraObjectName);
            $('#AssociateId').val(infraData?.isAssociateInfraObjectId);
            $('#InfraId').prop('checked', infraData?.isAssociate);
            $('#AssociateValue').val(infraData?.isAssociate);
        }else {
            $('#associate').hide()
        }

        errorElements.forEach(element => $(element).text('').removeClass('field-validation-error'));
    }

    const clearInputInfraFields = () => {

        multipleServerDetails = { multiplePRServer: [], multipleDRServer: [] };
        multipleDatabaseDetails = { multiplePRDatabase: [], multipleDRDatabase: [] };
        $("#textName,#NearDRServerName,#SelectPairInfra,#SelectServerName,#nodeProperties,#databaseText,#DatabaseId,#DRSelectServerNames,#DRSelectDatabase,#SelectReplicationType,#ddlReplicationNameId, #ddlReplicationTypeNameId,#PRSelectDatabase, #infraNameId").val('');
        $("#SelectDatabaseType,#SelectAssociate,#PairInfraId,#infraReplicationNameId,#infraBusinessServiceId,#NearDRDatabaseNames,#NearDRServerName,#Description,#SelectAssociate, #pairValue, #AssociateValue, #prSrmServer, #drSrmServer, #nearSrmServer, #siteType").val("");
        $("#PRMultipleServer,#DRMultipleServer,#PRMultipleDatabase,#DRMultipleDatabase,#SelectReplicationNames").val("");
        $("#infrServerNameId, #PRServerName, #PRDatabaseName, #PRSelectDatabaseId, #PRSelectReplicationName, #drServerName, #DRSelectServerNamesId, #drDatabaseName, #infraDatabaseId").val('');
        $('#driftValue').val(true)
        $('#drift').prop('checked', true);
        $('.btn-check').prop('checked', false);
        $('#PairId, #InfraId, #cluster').prop('checked', false);
        $('#clusterType, #clusterPR, #clusterDR').val('').trigger('change');
        $('#PairInfra, #Associate, #DRCol').hide();
        $('#ddlbusinessFunctionId, #SelectReplicationType, #ddlReplicationTypeNameId, #tbody1, #siteType').empty();
        $('#SaveFunction').text('Save');
        $('#btnradio1').prop('checked', true).trigger('change');

        errorElements.forEach(element =>  $(element).text('').removeClass('field-validation-error'));
    }

