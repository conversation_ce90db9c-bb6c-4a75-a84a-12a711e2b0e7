using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Commands;

public class DeleteCyberAirGapStatusTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<ICyberAirGapStatusRepository> _mockCyberAirGapStatusRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteCyberAirGapStatusCommandHandler _handler;

    public DeleteCyberAirGapStatusTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockCyberAirGapStatusRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(_cyberAirGapStatusFixture.CyberAirGapStatuses);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new DeleteCyberAirGapStatusCommandHandler(
            _mockCyberAirGapStatusRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_ValidCommand()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };

        CyberAirGapStatusDeletedEvent publishedEvent = null;

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapStatusDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteCyberAirGapStatusResponse>();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("deleted successfully");

        existingEntity.IsActive.ShouldBeFalse();

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(existingEntity.AirGapName);

        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }


    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_CancellationRequested()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };
        var cancellationToken = new CancellationToken(true);

        
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_RepositoryFails()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };

        var mockFailingRepository = CyberAirGapStatusRepositoryMocks.CreateFailingCyberAirGapStatusRepository();
        mockFailingRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(existingEntity);

        var handler = new DeleteCyberAirGapStatusCommandHandler(
            mockFailingRepository.Object,
            _mockPublisher.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe("Update operation failed");
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

   
    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_MultipleDeletes()
    {
        var entities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Take(2).ToList();
        var commands = entities.Select(e => new DeleteCyberAirGapStatusCommand { Id = e.ReferenceId }).ToList();

        // Setup the mock for each entity
        foreach (var entity in entities)
        {
            _mockCyberAirGapStatusRepository
                .Setup(x => x.GetByReferenceIdAsync(entity.ReferenceId))
                .ReturnsAsync(entity);
        }

        // Act
        var results = new List<DeleteCyberAirGapStatusResponse>();
        foreach (var command in commands)
        {
            var result = await _handler.Handle(command, CancellationToken.None);
            results.Add(result);
        }

        // Assert
        results.ShouldAllBe(r => r.Success);
        results.ShouldAllBe(r => !r.IsActive);
        entities.ShouldAllBe(e => !e.IsActive);

        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Exactly(2));
        // _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAirGapStatus>()), Times.Exactly(2));
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_AlreadyInactive()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        existingEntity.IsActive = false; // Set as already inactive

        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };

        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
        .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();

        existingEntity.IsActive.ShouldBeFalse();

        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    /// <summary>
    /// Test: Delete cyber air gap status with special characters in name
    /// Expected: Handles special characters in air gap name correctly
    /// </summary>
    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_SpecialCharactersInName()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        existingEntity.AirGapName = "Special Characters & <script>alert('xss')</script> 🚀💻📊 测试数据";

        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };

        CyberAirGapStatusDeletedEvent publishedEvent = null;

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapStatusDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
        .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain("Special Characters");

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldContain("Special Characters");
        publishedEvent.Name.ShouldContain("🚀💻📊");
        publishedEvent.Name.ShouldContain("测试数据");
    }

    /// <summary>
    /// Test: Delete cyber air gap status with null air gap name
    /// Expected: Handles null air gap name gracefully
    /// </summary>
    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_NullAirGapName()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        existingEntity.AirGapName = null;

        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };

        CyberAirGapStatusDeletedEvent publishedEvent = null;

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapStatusDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
        .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBeNull();

        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
    }

    /// <summary>
    /// Test: Delete cyber air gap status with empty air gap name
    /// Expected: Handles empty air gap name gracefully
    /// </summary>
    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_EmptyAirGapName()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        existingEntity.AirGapName = string.Empty;

        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };

        CyberAirGapStatusDeletedEvent publishedEvent = null;

        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
        .ReturnsAsync(existingEntity);

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapStatusDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(string.Empty);

        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
    }

    /// <summary>
    /// Test: Delete cyber air gap status response validation
    /// Expected: Response contains all required properties
    /// </summary>
    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_ValidatingResponse()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };

        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
        .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteCyberAirGapStatusResponse>();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("CyberAirGapStatus");
        result.Message.ShouldContain("deleted successfully");
        result.Message.ShouldContain(existingEntity.AirGapName);
    }

    /// <summary>
    /// Test: Delete cyber air gap status with concurrent operations
    /// Expected: Handles concurrent delete operations correctly
    /// </summary>
    [Fact]
    public async Task Handle_DeleteCyberAirGapStatus_When_ConcurrentOperations()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new DeleteCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId
        };

        // Act - Simulate concurrent delete operations
        var tasks = Enumerable.Range(1, 3).Select(async i =>
        {
            try
            {
                return await _handler.Handle(command, CancellationToken.None);
            }
            catch (Exception)
            {
                return null; // Some operations might fail due to concurrency
            }
        });

        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
        .ReturnsAsync(existingEntity);

        var results = await Task.WhenAll(tasks);

        // Assert
        var successfulResults = results.Where(r => r != null).ToList();
        successfulResults.ShouldNotBeEmpty();
        successfulResults.ShouldAllBe(r => r.Success);
        successfulResults.ShouldAllBe(r => !r.IsActive);

        existingEntity.IsActive.ShouldBeFalse();

        // Verify that repository operations were called
        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.AtLeastOnce);
        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.AtLeastOnce);
    }
}
