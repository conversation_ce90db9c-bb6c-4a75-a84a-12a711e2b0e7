﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Queries;

public class GetHacmpClusterDetailQueryHandlerTests : IClassFixture<HacmpClusterFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;
    private readonly Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;
    private readonly GetHacmpClusterDetailsQueryHandler _handler;

    public GetHacmpClusterDetailQueryHandlerTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.GetHacmpClusterRepository(_hacmpClusterFixture.HacmpClusters);

        _handler = new GetHacmpClusterDetailsQueryHandler(_hacmpClusterFixture.Mapper, _mockHacmpClusterRepository.Object);

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
    }

    [Fact]
    public async Task Handle_Return_HacmpClusterDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetHacmpClusterDetailQuery { Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<HacmpClusterDetailVm>();
        result.Id.ShouldBeGreaterThan(0.ToString());
        result.Name.ShouldNotBeEmpty();
        result.ServerId.ShouldNotBeEmpty();
        result.ServerName.ShouldNotBeEmpty();
        result.LSSRCPath.ShouldNotBeEmpty();
        result.CLRGInfoPath.ShouldNotBeEmpty();
        result.ResourceGroupName.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidHacmpClusterId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetHacmpClusterDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("HacmpCluster");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        await _handler.Handle(new GetHacmpClusterDetailQuery { Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId }, CancellationToken.None);

        _mockHacmpClusterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_CorrectHacmpClusterProperties_When_ValidId()
    {
        var expectedHacmpCluster = _hacmpClusterFixture.HacmpClusters[0];
        expectedHacmpCluster.Name = "Test HACMP Cluster";
        expectedHacmpCluster.ServerId = "TestServerId";
        expectedHacmpCluster.ServerName = "TestServerName";

        var result = await _handler.Handle(new GetHacmpClusterDetailQuery { Id = expectedHacmpCluster.ReferenceId }, CancellationToken.None);

        result.Name.ShouldBe("Test HACMP Cluster");
        result.ServerId.ShouldBe("TestServerId");
        result.ServerName.ShouldBe("TestServerName");
        result.Id.ShouldBe(expectedHacmpCluster.ReferenceId);
    }

    [Fact]
    public async Task Handle_Return_MappedProperties_When_ValidHacmpCluster()
    {
        var result = await _handler.Handle(new GetHacmpClusterDetailQuery { Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<HacmpClusterDetailVm>();
        result.Id.ShouldNotBeNullOrEmpty();
        result.Name.ShouldNotBeNullOrEmpty();
        result.ServerId.ShouldNotBeNullOrEmpty();
        result.ServerName.ShouldNotBeNullOrEmpty();
    }
}