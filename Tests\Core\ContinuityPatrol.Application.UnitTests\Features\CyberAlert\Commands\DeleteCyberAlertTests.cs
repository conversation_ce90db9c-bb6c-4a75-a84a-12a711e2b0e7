using ContinuityPatrol.Application.Features.CyberAlert.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Commands;

public class DeleteCyberAlertTests : IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<DeleteCyberAlertCommandHandler>> _mockLogger;
    private readonly DeleteCyberAlertCommandHandler _handler;

    private readonly Mock<IPublisher> _publisher;
    public DeleteCyberAlertTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);
        _mockUserActivityRepository = CyberRepositoryMocks.CreateUserActivityRepository(_cyberAlertFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<DeleteCyberAlertCommandHandler>>();

        // Setup default user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");

        _publisher = new Mock<IPublisher>();
        _handler = new DeleteCyberAlertCommandHandler(
            _mockCyberAlertRepository.Object, _publisher.Object);
    }

    
    [Fact]
    public async Task Handle_DeleteCyberAlert_When_ValidCommand()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var command = new DeleteCyberAlertCommand
        {
            Id = existingAlert.ReferenceId
        };

        _mockCyberAlertRepository
       .Setup(x => x.GetByReferenceIdAsync(existingAlert.ReferenceId))
       .ReturnsAsync(existingAlert);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain("deleted successfully");
        _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync(existingAlert.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_PerformSoftDelete_When_ValidCommand()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var command = new DeleteCyberAlertCommand
        {
            Id = existingAlert.ReferenceId
        };

        _mockCyberAlertRepository
       .Setup(x => x.GetByReferenceIdAsync(existingAlert.ReferenceId))
       .ReturnsAsync(existingAlert);

        Domain.Entities.CyberAlert deletedEntity = null;
        _mockCyberAlertRepository.Setup(x => x.DeleteAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity => deletedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        command.ShouldNotBeNull();
    }

    //[Fact]
    //public async Task Handle_ThrowNotFoundException_When_AlertNotFound()
    //{
    //    // Arrange
    //    var command = new DeleteCyberAlertCommand
    //    {
    //        Id = "non-existent-alert-id"
    //    };

    //    // Act & Assert
    //    await Should.ThrowAsync<NotFoundException>(async () =>
    //        await _handler.Handle(command, CancellationToken.None));

    //    _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync("non-existent-alert-id"), Times.Once);
    //    _mockCyberAlertRepository.Verify(x => x.DeleteAsync(It.IsAny<Domain.Entities.CyberAlert>()), Times.Never);
    //}

    //[Fact]
    //public async Task Handle_ThrowNotFoundException_When_AlertIsInactive()
    //{
    //    // Arrange
    //    var inactiveAlert = new CyberAlert
    //    {
    //        ReferenceId = "inactive-alert-id",
    //        Title = "Inactive Alert",
    //        IsActive = false
    //    };
    //    _cyberAlertFixture.CyberAlerts.Add(inactiveAlert);

    //    var command = new DeleteCyberAlertCommand
    //    {
    //        Id = "inactive-alert-id"
    //    };

    //    // Act & Assert
    //    await Should.ThrowAsync<NotFoundException>(async () =>
    //        await _handler.Handle(command, CancellationToken.None));
    //}

    
    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var command = new DeleteCyberAlertCommand
        {
            Id = existingAlert.ReferenceId
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        
    }
    [Fact]
    public async Task Handle_ProcessMultipleDeleteCommands_When_ValidCommands()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlerts.Take(3).ToList();
        var commands = alerts.Select(alert => new DeleteCyberAlertCommand
        {
            Id = alert.ReferenceId
        }).ToArray();

        _mockCyberAlertRepository.Reset(); // Clear previous setups

        foreach (var alert in alerts)
        {
            alert.IsActive = true; // Ensure the alert is active
            _mockCyberAlertRepository
                .Setup(x => x.GetByReferenceIdAsync(alert.ReferenceId))
                .ReturnsAsync(alert);
        }

        // Act
        foreach (var command in commands)
        {
            var result = await _handler.Handle(command, CancellationToken.None);
            result.Success.ShouldBeTrue();
        }

        


    }

    [Fact]
    public async Task Handle_DeleteAlertsWithDifferentStatuses_When_ValidCommands()
    {
        // Arrange
        var alertStatuses = new[] { "Open", "Acknowledged", "In Progress", "Resolved", "Closed" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(alertStatuses.Length).ToList();

        foreach (var alert in alerts)
        {
            alert.IsActive = true; // Ensure the alert is active
            _mockCyberAlertRepository
                .Setup(x => x.GetByReferenceIdAsync(alert.ReferenceId))
                .ReturnsAsync(alert);
        }

        for (int i = 0; i < alerts.Count && i < alertStatuses.Length; i++)
        {
            // alerts[i].Name = alertStatuses[i]; // If you want to set a status property, do it here
            var command = new DeleteCyberAlertCommand
            {
                Id = alerts[i].ReferenceId
            };
            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Success.ShouldBeTrue();
            result.Message.ShouldContain("deleted successfully");
        }

    }

    [Fact]
    public async Task Handle_DeleteAlertsWithDifferentSeverities_When_ValidCommands()
    {
        // Arrange
        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(severityLevels.Length).ToList();

        for (int i = 0; i < alerts.Count && i < severityLevels.Length; i++)
        {
            alerts[i].Severity = severityLevels[i];
            var command = new DeleteCyberAlertCommand
            {
                Id = alerts[i].ReferenceId
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Success.ShouldBeTrue();
        }

    }
    [Fact]
    public async Task Handle_DeleteRecentlyCreatedAlert_When_ValidCommand()
    {
        // Arrange
        var recentAlert = new Domain.Entities.CyberAlert
        {
            ReferenceId = "recent-alert-id",
            Severity = "Medium",
            IsActive = true
        };
        _cyberAlertFixture.CyberAlerts.Add(recentAlert);

        var command = new DeleteCyberAlertCommand
        {
            Id = "recent-alert-id"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain("deleted successfully");
        
    }

    [Fact]
    public async Task Handle_DeleteOldAlert_When_ValidCommand()
    {
        // Arrange
        var oldAlert = new Domain.Entities.CyberAlert
        {
            ReferenceId = "old-alert-id",
            Severity = "Low",
            IsActive = true
        };
        _cyberAlertFixture.CyberAlerts.Add(oldAlert);

        var command = new DeleteCyberAlertCommand
        {
            Id = "old-alert-id"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain("deleted successfully");
       
    }

    [Fact]
    public async Task Handle_LogUserActivity_When_ValidDelete()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var command = new DeleteCyberAlertCommand
        {
            Id = existingAlert.ReferenceId
        };

        _mockCyberAlertRepository
      .Setup(x => x.GetByReferenceIdAsync(existingAlert.ReferenceId))
      .ReturnsAsync(existingAlert);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.ShouldBeTrue();
        
    }
    [Fact]
    public async Task Handle_ProcessRapidDeleteOperations_When_MultipleCommands()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlerts.Take(10).ToList();
        var commands = alerts.Select(alert => new DeleteCyberAlertCommand
        {
            Id = alert.ReferenceId
        }).ToList();

        // Setup the mock for each alert
        foreach (var alert in alerts)
        {
            _mockCyberAlertRepository
                .Setup(x => x.GetByReferenceIdAsync(alert.ReferenceId))
                .ReturnsAsync(alert);
        }

        // Act
        var tasks = commands.Select(cmd => _handler.Handle(cmd, CancellationToken.None));
        var results = await Task.WhenAll(tasks);

        // Assert
        results.ShouldAllBe(result => result.Success);
       

    }
    [Fact]
    public async Task Handle_HandleInvalidId_When_NullOrEmptyId()
    {
        // Arrange
        var command = new DeleteCyberAlertCommand
        {
            Id = null
        };

        // Act & Assert
       
    }
}
