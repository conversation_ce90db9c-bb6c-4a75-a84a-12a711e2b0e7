﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Commands;

public class CreateWorkflowProfileInfoTests : IClassFixture<WorkflowProfileInfoFixture>, IClassFixture<WorkflowFixture>
{
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;

    private readonly Mock<IWorkflowProfileInfoRepository> _mockWorkflowProfileInfoRepository;

    private readonly Mock<IWorkflowRepository> _mockWorkflowRepository;

    private readonly CreateWorkflowProfileInfoCommandHandler _handler;

    public CreateWorkflowProfileInfoTests(WorkflowProfileInfoFixture workflowProfileInfoFixture, WorkflowFixture workflowFixture)
    {
        _workflowProfileInfoFixture = workflowProfileInfoFixture;

        var workflowNewFixture = workflowFixture;

        var mockPublisher = new Mock<IPublisher>();

        var mockLogInUserService = new Mock<ILoggedInUserService>();
        
        _mockWorkflowProfileInfoRepository = WorkflowProfileInfoRepositoryMocks.CreateWorkflowProfileInfoRepository(_workflowProfileInfoFixture.WorkflowProfileInfos);
        
        _mockWorkflowRepository = WorkflowRepositoryMocks.CreateWorkflowRepository(workflowNewFixture.Workflows);

        _handler = new CreateWorkflowProfileInfoCommandHandler(_workflowProfileInfoFixture.Mapper, _mockWorkflowProfileInfoRepository.Object, _mockWorkflowRepository.Object, mockPublisher.Object, mockLogInUserService.Object);

        workflowNewFixture.Workflows[0].Properties = "{\"nodes\":[{\"id\":\"node_1_228\",\"offsetY\":121,\"offsetX\":474.875,\"actionInfo\":{\"actionName\":\"asgag\",\"description\":\"\",\"actionType\":\"e49d3c06-2e3f-478f-bf9e-81460845539b\",\"properties\":\"\",\"nodeId\":\"f760055a-8596-4ec7-8926-f54c02553caa\",\"IsEmail\":false,\"IsSms\":false,\"IsParellel\":false,\"connectionTimeout\":\"2342\",\"icon\":\"Action_Cloud_Icon\"},\"shape\":{\"type\":\"HTML\",\"content\":\"&lt;div class='Workflow_ActionCard'&gt;\\n                        &lt;div class='Workflow_ActionCard_Icon'&gt; &lt;img class='node_img' src='/assets/images/workflow/db_icons_white/Action_Cloud_Icon.svg'&gt;&lt;/div&gt;\\n                        &lt;div class='Workflow_ActionName'&gt;&lt;span class='count'&gt;1&lt;/span&gt;.&lt;span class='acn_name'&gt; asgag&lt;/span&gt;&lt;/div&gt;\\n                        &lt;div class='px-2'&gt; \\n                        &lt;img class='node_img' src='/assets/images/workflow/more_icon.png'&gt;\\n                        &lt;/div&gt;\\n                    &lt;/div&gt;\\n                    \"}},{\"id\":\"node_2_493\",\"offsetY\":228,\"offsetX\":474.875,\"actionInfo\":{\"actionName\":\"asgag\",\"description\":\"\",\"actionType\":\"e49d3c06-2e3f-478f-bf9e-81460845539b\",\"properties\":\"\",\"nodeId\":\"f760055a-8596-4ec7-8926-f54c02553caa\",\"IsEmail\":false,\"IsSms\":false,\"IsParellel\":true,\"connectionTimeout\":\"2342\",\"icon\":\"Action_Cloud_Icon\"},\"shape\":{\"type\":\"HTML\",\"content\":\"&lt;div class='Workflow_ActionCard'&gt;\\n                        &lt;div class='Workflow_ActionCard_Icon'&gt; &lt;img class='node_img' src='/assets/images/workflow/db_icons_white/Action_Cloud_Icon.svg'&gt;&lt;/div&gt;\\n                        &lt;div class='Workflow_ActionName'&gt;&lt;span class='count'&gt;2&lt;/span&gt;.&lt;span class='acn_name'&gt; asgag&lt;/span&gt;&lt;/div&gt;\\n                        &lt;div class='px-2'&gt; \\n                        &lt;img class='node_img' src='/assets/images/workflow/more_icon.png'&gt;\\n                        &lt;/div&gt;\\n                    &lt;/div&gt;\\n                    \"}},{\"id\":\"node_3_599\",\"offsetY\":285,\"offsetX\":474.875,\"actionInfo\":{\"actionName\":\"asgag\",\"description\":\"\",\"actionType\":\"e49d3c06-2e3f-478f-bf9e-81460845539b\",\"properties\":\"\",\"nodeId\":\"f760055a-8596-4ec7-8926-f54c02553caa\",\"IsEmail\":false,\"IsSms\":false,\"IsParellel\":true,\"connectionTimeout\":\"2342\",\"icon\":\"Action_Cloud_Icon\"},\"shape\":{\"type\":\"HTML\",\"content\":\"&lt;div class='Workflow_ActionCard'&gt;\\n                        &lt;div class='Workflow_ActionCard_Icon'&gt; &lt;img class='node_img' src='/assets/images/workflow/db_icons_white/Action_Cloud_Icon.svg'&gt;&lt;/div&gt;\\n                        &lt;div class='Workflow_ActionName'&gt;&lt;span class='count'&gt;3&lt;/span&gt;.&lt;span class='acn_name'&gt; asgag&lt;/span&gt;&lt;/div&gt;\\n                        &lt;div class='px-2'&gt; \\n                        &lt;img class='node_img' src='/assets/images/workflow/more_icon.png'&gt;\\n                        &lt;/div&gt;\\n                    &lt;/div&gt;\\n                    \"}},{\"id\":\"node_5_370\",\"offsetY\":352,\"offsetX\":474.875,\"actionInfo\":{\"actionName\":\"asdga\",\"description\":\"sgd234\",\"actionType\":\"e49d3c06-2e3f-478f-bf9e-81460845539b\",\"properties\":\"\",\"nodeId\":\"f760055a-8596-4ec7-8926-f54c02553caa\",\"IsEmail\":false,\"IsSms\":false,\"IsParellel\":false,\"connectionTimeout\":\"234234\",\"icon\":\"Action_Cloud_Icon\"},\"shape\":{\"type\":\"HTML\",\"content\":\"&lt;div class='Workflow_ActionCard'&gt;\\n                        &lt;div class='Workflow_ActionCard_Icon'&gt; &lt;img class='node_img' src='/assets/images/workflow/db_icons_white/Action_Cloud_Icon.svg'&gt;&lt;/div&gt;\\n                        &lt;div class='Workflow_ActionName'&gt;&lt;span class='count'&gt;4&lt;/span&gt;.&lt;span class='acn_name'&gt; asdga&lt;/span&gt;&lt;/div&gt;\\n                        &lt;div class='px-2'&gt; \\n                        &lt;img class='node_img' src='/assets/images/workflow/more_icon.png'&gt;\\n                        &lt;/div&gt;\\n                    &lt;/div&gt;\\n                    \"}},{\"id\":\"grouphgv3f\",\"offsetY\":236.5,\"offsetX\":474.875,\"actionInfo\":{\"groupname\":\"Untitled Group\",\"group\":true,\"parellel\":false},\"shape\":{\"type\":\"HTML\",\"content\":\"&lt;div class='Workflow_ActionGroup'&gt;\\n                                                                    &lt;img class='parellelsvg hide' src='/assets/images/workflow/parellel.svg'/&gt;\\n                                                                    &lt;div class='Workflow_ActionGroup_Icon show'&gt; \\n                                                             &lt;img src='/assets/images/workflow/custom_group.svg'&gt;\\n                                                        &lt;/div&gt;\\n                                                        &lt;div class='Workflow_ActionName show'&gt;Untitled Group&lt;/div&gt;\\n                                                        &lt;div class='px-2 down_arrow show'&gt; \\n                                                            &lt;img class='node_img' src='/assets/images/workflow/down_arrow.png'&gt;\\n                                                        &lt;/div&gt;\\n                                                                &lt;/div&gt;\"},\"children\":[\"node_2_493\",\"node_3_599\"]}],\"connectors\":[{\"id\":\"connector0_360\",\"sourceID\":\"start\",\"targetID\":\"node_1_228\"},{\"id\":\"connector1_787\",\"sourceID\":\"node_1_228\",\"targetID\":\"grouphgv3f\"},{\"id\":\"connector2_710\",\"sourceID\":\"node_2_493\",\"targetID\":\"node_3_599\"},{\"id\":\"connector3_263\",\"sourceID\":\"grouphgv3f\",\"targetID\":\"node_5_370\"}]}";

        _workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand.WorkflowId = workflowNewFixture.Workflows[0].ReferenceId;

    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_WorkflowProfileInfo()
    {
        await _handler.Handle(_workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand, CancellationToken.None);

        var allCategories = await _mockWorkflowProfileInfoRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateWorkflowProfileInfoResponse_When_AddValidWorkflowProfileInfo()
    {
        _workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand.WorkflowName = "WF_Infra_SB";

        _workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand.ProfileName = "Dev_Test";

        var result = await _handler.Handle(_workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateWorkflowProfileInfoResponse));

        result.WorkflowProfileId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain("Workflow Profile");

        result.Message.ShouldContain($"added in '{_workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand.ProfileName}'");

        result.Message.ShouldContain($"Workflow Profile '{_workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand.WorkflowName}' added in '{_workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand.ProfileName}' successfully");
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowProfileInfoFixture.CreateWorkflowProfileInfoCommand, CancellationToken.None);

        _mockWorkflowProfileInfoRepository.Verify(x => x.GetProfileIdAttachByWorkflowId(It.IsAny<string>()), Times.Once);
        _mockWorkflowProfileInfoRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowProfileInfo>()), Times.Once);

        _mockWorkflowRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowInvalidException_When_WorkflowAlreadyAttached()
    {
        var command = new CreateWorkflowProfileInfoCommand
        {
            WorkflowId = "workflow123"
        };

        var expectedProfileName = "ExistingProfile";

        var existingProfile = new Domain.Entities.WorkflowProfileInfo
        {
            WorkflowId = command.WorkflowId,
            ProfileName = expectedProfileName
        };

        _mockWorkflowProfileInfoRepository
            .Setup(repo => repo.GetProfileIdAttachByWorkflowId(command.WorkflowId))
            .ReturnsAsync(existingProfile);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe($"This workflow already attached with profile: {expectedProfileName}");
    }
}