using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

/// <summary>
/// Mock repository implementations for CyberAirGapStatus unit testing
/// Provides comprehensive mock setups for all repository operations
/// </summary>
public static class CyberAirGapStatusRepositoryMocks
{
    /// <summary>
    /// Creates a mock CyberAirGapStatus repository with standard CRUD operations
    /// </summary>
    /// <param name="cyberAirGapStatuses">Test data for the repository</param>
    /// <returns>Configured mock repository</returns>
    public static Mock<ICyberAirGapStatusRepository> CreateCyberAirGapStatusRepository(List<CyberAirGapStatus> cyberAirGapStatuses)
    {
        var mockRepository = new Mock<ICyberAirGapStatusRepository>();

        // Setup ListAllAsync - returns all active cyber air gap statuses
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberAirGapStatuses.Where(x => x.IsActive).ToList());

        // Setup GetByReferenceIdAsync - returns cyber air gap status by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberAirGapStatuses.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        // Setup AddAsync - adds new cyber air gap status and returns it
        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAirGapStatus>()))
            .ReturnsAsync((CyberAirGapStatus cyberAirGapStatus) =>
            {
                cyberAirGapStatus.ReferenceId = Guid.NewGuid().ToString();
                cyberAirGapStatus.CreatedDate = DateTime.Now;
                cyberAirGapStatus.IsActive = true;
                cyberAirGapStatuses.Add(cyberAirGapStatus);
                return cyberAirGapStatus;
            });

        // Setup UpdateAsync - updates existing cyber air gap status
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAirGapStatus>()))
            .ReturnsAsync((CyberAirGapStatus cyberAirGapStatus) =>
            {
                var existingStatus = cyberAirGapStatuses.FirstOrDefault(x => x.ReferenceId == cyberAirGapStatus.ReferenceId);
                if (existingStatus != null)
                {
                    existingStatus.AirGapName = cyberAirGapStatus.AirGapName;
                    existingStatus.Description = cyberAirGapStatus.Description;
                    existingStatus.SourceSiteName = cyberAirGapStatus.SourceSiteName;
                    existingStatus.TargetSiteName = cyberAirGapStatus.TargetSiteName;
                    existingStatus.Port = cyberAirGapStatus.Port;
                    existingStatus.Status = cyberAirGapStatus.Status;
                    existingStatus.LastModifiedDate = DateTime.Now;
                    existingStatus.IsActive = cyberAirGapStatus.IsActive;
                    return existingStatus;
                }
                return cyberAirGapStatus;
            });

        // Setup DeleteAsync - soft delete (sets IsActive to false)
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberAirGapStatus>()))
            .ReturnsAsync((CyberAirGapStatus cyberAirGapStatus) =>
            {
                var existingStatus = cyberAirGapStatuses.FirstOrDefault(x => x.ReferenceId == cyberAirGapStatus.ReferenceId);
                if (existingStatus != null)
                {
                    existingStatus.IsActive = false;
                    existingStatus.LastModifiedDate = DateTime.Now;
                }
                return existingStatus ?? cyberAirGapStatus;
            });

        // Setup IsNameExist - checks if air gap name already exists
        mockRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) =>
            {
                if (string.IsNullOrEmpty(id))
                {
                    return cyberAirGapStatuses.Any(x => x.AirGapName.Equals(name, StringComparison.OrdinalIgnoreCase) && x.IsActive);
                }
                return cyberAirGapStatuses.Any(x => x.AirGapName.Equals(name, StringComparison.OrdinalIgnoreCase) && 
                                              x.ReferenceId != id && x.IsActive);
            });

        // Setup GetCyberAirGapStatusByAirGabId - gets statuses by air gap ID within date range
        //mockRepository.Setup(repo => repo.GetCyberAirGapStatusByAirGabId(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((string startDate, string endDate, string airGapId) =>
        //    {
        //        var start = DateTime.TryParse(startDate, out var startDateTime) ? startDateTime : DateTime.MinValue;
        //        var end = DateTime.TryParse(endDate, out var endDateTime) ? endDateTime : DateTime.MaxValue;
                
        //        return cyberAirGapStatuses.Where(x => x.AirGapId == airGapId && 
        //                                         x.IsActive &&
        //                                         x.CreatedDate >= start && 
        //                                         x.CreatedDate <= end).ToList();
        //    });

        // Setup GetAirGaplist - gets all statuses within date range
        //mockRepository.Setup(repo => repo.GetAirGaplist(It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((string startDate, string endDate) =>
        //    {
        //        var start = DateTime.TryParse(startDate, out var startDateTime) ? startDateTime : DateTime.MinValue;
        //        var end = DateTime.TryParse(endDate, out var endDateTime) ? endDateTime : DateTime.MaxValue;
                
        //        return cyberAirGapStatuses.Where(x => x.IsActive &&
        //                                         x.CreatedDate >= start && 
        //                                         x.CreatedDate <= end).ToList();
        //    });

        // Setup PaginatedListAllAsync - returns paginated results
        //mockRepository.Setup(repo => repo.PaginatedListAllAsync(
        //        It.IsAny<int>(), It.IsAny<int>(), It.IsAny<object>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, object filterSpec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredStatuses = cyberAirGapStatuses.Where(x => x.IsActive).ToList();
                
        //        // Apply sorting
        //        if (!string.IsNullOrEmpty(sortColumn))
        //        {
        //            switch (sortColumn.ToLower())
        //            {
        //                case "airgapname":
        //                    filteredStatuses = sortOrder?.ToLower() == "desc" 
        //                        ? filteredStatuses.OrderByDescending(x => x.AirGapName).ToList()
        //                        : filteredStatuses.OrderBy(x => x.AirGapName).ToList();
        //                    break;
        //                case "status":
        //                    filteredStatuses = sortOrder?.ToLower() == "desc" 
        //                        ? filteredStatuses.OrderByDescending(x => x.Status).ToList()
        //                        : filteredStatuses.OrderBy(x => x.Status).ToList();
        //                    break;
        //                case "createddate":
        //                    filteredStatuses = sortOrder?.ToLower() == "desc" 
        //                        ? filteredStatuses.OrderByDescending(x => x.CreatedDate).ToList()
        //                        : filteredStatuses.OrderBy(x => x.CreatedDate).ToList();
        //                    break;
        //                default:
        //                    filteredStatuses = filteredStatuses.OrderBy(x => x.AirGapName).ToList();
        //                    break;
        //            }
        //        }

        //        // Apply pagination
        //        var totalCount = filteredStatuses.Count;
        //        var pagedStatuses = filteredStatuses.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();

        //        return new PaginatedResult<CyberAirGapStatus>
        //        {
        //            Data = pagedStatuses,
        //            PageSize = pageSize,
        //            TotalCount = totalCount,
        //            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        //        };
        //    });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository that throws exceptions for testing error scenarios
    /// </summary>
    /// <returns>Mock repository configured to throw exceptions</returns>
    public static Mock<ICyberAirGapStatusRepository> CreateFailingCyberAirGapStatusRepository()
    {
        var mockRepository = new Mock<ICyberAirGapStatusRepository>();

        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAirGapStatus>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAirGapStatus>()))
            .ThrowsAsync(new InvalidOperationException("Update operation failed"));

        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ThrowsAsync(new InvalidOperationException("Query operation failed"));

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ThrowsAsync(new InvalidOperationException("List operation failed"));

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock user service for testing
    /// </summary>
    /// <returns>Configured mock user service</returns>
    public static Mock<ILoggedInUserService> CreateUserService()
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        
        mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        mockUserService.Setup(x => x.LoginName).Returns("TestUser123");
        mockUserService.Setup(x => x.CompanyId).Returns("TestCompany123");
        mockUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");
        mockUserService.Setup(x => x.RequestedUrl).Returns("/api/test");

        return mockUserService;
    }

    /// <summary>
    /// Creates a mock user activity repository for testing
    /// </summary>
    /// <returns>Configured mock user activity repository</returns>
    public static Mock<IUserActivityRepository> CreateUserActivityRepository()
    {
        var mockRepository = new Mock<IUserActivityRepository>();

        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity activity) =>
            {
                activity.ReferenceId = Guid.NewGuid().ToString();
                activity.CreatedDate = DateTime.Now;
                activity.IsActive = true;
                return activity;
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock job scheduler for testing
    /// </summary>
    /// <returns>Configured mock job scheduler</returns>
    public static Mock<IJobScheduler> CreateJobScheduler()
    {
        var mockJobScheduler = new Mock<IJobScheduler>();

        //mockJobScheduler.Setup(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .Returns(Task.CompletedTask);

        //mockJobScheduler.Setup(x => x.DeleteJob(It.IsAny<string>()))
        //    .Returns(Task.CompletedTask);

        return mockJobScheduler;
    }

    /// <summary>
    /// Creates an empty mock repository for testing scenarios with no data
    /// </summary>
    /// <returns>Mock repository with empty data</returns>
    public static Mock<ICyberAirGapStatusRepository> CreateEmptyCyberAirGapStatusRepository()
    {
        return CreateCyberAirGapStatusRepository(new List<CyberAirGapStatus>());
    }

    /// <summary>
    /// Creates a mock repository with large dataset for performance testing
    /// </summary>
    /// <param name="count">Number of test records to create</param>
    /// <returns>Mock repository with large dataset</returns>
    public static Mock<ICyberAirGapStatusRepository> CreateLargeCyberAirGapStatusRepository(int count = 1000)
    {
        var cyberAirGapStatuses = new List<CyberAirGapStatus>();
        
        for (int i = 1; i <= count; i++)
        {
            cyberAirGapStatuses.Add(new CyberAirGapStatus
            {
                ReferenceId = $"cyberairgapstatus-{i:0000}",
                AirGapId = $"airgap-{i:0000}",
                AirGapName = $"Performance Test Air Gap Status {i:0000}",
                SourceSiteName = $"Source Site {i:000}",
                TargetSiteName = $"Target Site {i:000}",
                Port = 8000 + (i % 1000),
                Status = i % 3 == 0 ? "Active" : i % 3 == 1 ? "Warning" : "Error",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-i % 30),
                CreatedBy = "PerformanceTestUser"
            });
        }

        return CreateCyberAirGapStatusRepository(cyberAirGapStatuses);
    }
}
