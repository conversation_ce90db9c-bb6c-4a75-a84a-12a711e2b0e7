﻿using System.Text.Json.Nodes;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Authentication;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Import;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Lock;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.SaveAs;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionFieldMasterModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionModel;
using ContinuityPatrol.Language.Parser;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class WorkflowActionController : BaseController
{
    private readonly IWorkflowActionFieldMasterService _workflowActionService;
    //private readonly IWorkflowHistoryService _workflowHistoryService;
    private readonly ILogger<WorkflowActionController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    public WorkflowActionController(IWorkflowActionFieldMasterService workflowActionService, ILogger<WorkflowActionController> logger, IMapper mapper, IDataProvider dataProvider)
    {

        _workflowActionService = workflowActionService;
        //_workflowHistoryService = workflowHistoryService;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    public IActionResult List()
    {
        return View();
    }

    public async Task<IActionResult> WorkflowActionList(string nodeId)
    {

        var workflowActionList = await _dataProvider.WorkflowAction.GetWorkflowActionByNodeId(nodeId);

        return Json(workflowActionList);
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(WorkflowActionViewModel workflowAction)
    {
        try
        {
            var workflowCategoryId = Request.Form["id"];

            if (string.IsNullOrEmpty(workflowCategoryId))
            {
                var workflowCategoryModel = _mapper.Map<CreateWorkflowActionCommand>(workflowAction);
                var result = await _dataProvider.WorkflowAction.CreateAsync(workflowCategoryModel);

                return Json(result);
            }
            else
            {
                var workflowCategoryModel = _mapper.Map<UpdateWorkflowActionCommand>(workflowAction);
                var result = await _dataProvider.WorkflowAction.UpdateAsync(workflowCategoryModel);
                return Json(result);
            }
        }
        catch (ValidationException ex)
        {


            return Json(ex.ValidationErrors.FirstOrDefault());
        }
        catch (Exception ex)
        {


            return Json(ex.Message);
        }

    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> SaveAsCreateOrUpdate(SaveAsWorkflowActionCommand workflowActionCommand)
    {
        try
        {
            var result = await _dataProvider.WorkflowAction.SaveAsWorkflowAction(workflowActionCommand);
            return Json(result);
        }
        catch (ValidationException ex)
        {
            return Json(ex.ValidationErrors.FirstOrDefault());
        }
        catch (Exception ex)
        {
            return Json(ex.Message);
        }

    }

    public JsonResult CryptoEncryptPassword(string password)
    {
        var result = CryptographyHelper.Encrypt(password);

        return Json(result);
    }

    public JsonResult CryptoDecryptPassword(string password)
    {
        var result = CryptographyHelper.Decrypt(password);

        return Json(result);
    }

    public async Task<IActionResult> LockCreateOrUpdate(AuthenticationCommand workflowComment)
    {
        try
        {

            //var AuthenticationCommand = _mapper.Map<AuthenticationCommand>(workflowComment);
            var result = await _dataProvider.GlobalSettings.Authentication(workflowComment);

            return Json(result);

        }
        catch (ValidationException ex)
        {


            return Json(ex.ValidationErrors.FirstOrDefault());
        }
        catch (Exception ex)
        {


            return Json(ex.Message);
        }

    }



    public async Task<IActionResult> LockStatusUpdate(UpdateWorkflowActionLockCommand lockStatusComment)
    {
        try
        {

            //var AuthenticationCommand = _mapper.Map<AuthenticationCommand>(workflowComment);
            var result = await _dataProvider.WorkflowAction.UpdateWorkflowActionLock(lockStatusComment);

            return Json(result);

        }
        catch (ValidationException ex)
        {


            return Json(ex.ValidationErrors.FirstOrDefault());
        }
        catch (Exception ex)
        {


            return Json(ex.Message);
        }
    }

    [HttpPut]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> ImportWorkflowAction(ImportWorkflowActionCommand importWorkflowActionCommand)
    {

        try
        {
            var importAction = await _dataProvider.WorkflowAction.ImportWorkflowActionAsync(importWorkflowActionCommand);
            return Json(new { Success = true, Message = importAction });
        }
        catch (ValidationException ex)
        {
            return Json(new
            {
                Success = false,
                Message = ex.ValidationErrors.FirstOrDefault()
            });
        }
        catch (Exception ex)
        {
            return Json(new { Success = false, Message = ex.GetMessage() });
        }
    }


    [HttpGet]
    public async Task<IActionResult> Delete(string id)
    {

        try
        {
            var workflowAction = await _dataProvider.WorkflowAction.DeleteAsync(id);

            return Json(new { Success = true, Message = workflowAction });
            //return RouteToPostView(workflowAction);
        }
        catch (Exception ex)
        {
            return Json(new { Success = false, Message = ex.GetMessage() });
        }
    }

    public async Task<IActionResult> WorkflowActionCompareJson(string nodeId)
    {
        var workflowCurrentList = await _dataProvider.WorkflowAction.GetByReferenceId(nodeId);
        var workflowPreviousList = await _dataProvider.SolutionHistory.GetSolutionHistoryByActionId(nodeId);

        var currentData = JsonConvert.SerializeObject(workflowCurrentList);
        var previousData = JsonConvert.SerializeObject(workflowPreviousList);
        var result = new JsonObject
        {
            { "current", currentData },
            { "previous", previousData}
        };

        return Json(result);
    }

    //private IActionResult RouteToPostView(BaseResponse result)
    //{
    //    TempData.Set(result.Success
    //        ? new NotificationMessage(NotificationType.Success, result.Message)
    //        : new NotificationMessage(NotificationType.Error, result.Message));

    //    return RedirectToAction("List", "ActionBuilder", new { area = "Admin" });
    //}

    public async Task<IActionResult> WorkflowActionDataList()
    {
        try
        {
            var workflowActionList = await _dataProvider.WorkflowActionFieldMaster.GetWorkflowActionFieldMasterList();
            var workflowActionListOrderBy = workflowActionList.OrderBy(item => item.Name).ToList();
            return Json(workflowActionListOrderBy);

        }
        catch (ValidationException ex)
        {
            return Json(ex.ValidationErrors.FirstOrDefault());
        }
        catch (Exception ex)
        {
            return Json(ex.Message);
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> ActionCreateOrUpdate(WorkflowActionFieldMasterViewModel workflowAction)
    {
        var workflowActionId = Request.Form["id"];
        BaseResponse result;

        if (string.IsNullOrEmpty(workflowActionId))
        {
            var workflowActionModel = _mapper.Map<CreateWorkflowActionFieldMasterCommand>(workflowAction);
            result = await _workflowActionService.CreateAsync(workflowActionModel);
        }
        else
        {
            var workflowActionModel = _mapper.Map<UpdateWorkflowActionFieldMasterCommand>(workflowAction);
            result = await _workflowActionService.UpdateAsync(workflowActionModel);
        }

        return Json(result);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> ActionDelete(string id)
    {
        var workflowAction = await _workflowActionService.DeleteAsync(id);

        return Json(workflowAction);
    }

    [HttpGet]
    public async Task<bool> WorkflowActionNameExist(string actionName, string actionId)
    {
        return await _dataProvider.WorkflowAction.IsWorkflowActionNameExist(actionName, actionId);
    }

    [HttpGet]
   
    public string ValidateCpActionScript(string script)
    {
        _logger.LogDebug("Entering ValidateCpslScript method in Workflow Configuration with script : '{script}'", script);

        try
        {
            if (script.IsNullOrWhiteSpace())
            {
                _logger.LogWarning("Script validation failed: Input script is null or empty.");
                return string.Empty;
            }
            string filename = "";
            Interpreter interpreter = new();
            string result = interpreter.ProcessAsyncValidate(script, filename);
            return $"OK: {result}";

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred during script validation.", ex);
            return "ERROR: An unexpected error occurred during validation.";
        }
    }
}
