﻿const approverPreventSpecialKeys = (selector) => {
    $(selector).on('keypress', (e) => {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
        }
    });
};

const baseDataTableConfig = {
    language: {
        decimal: ",",
        paginate: {
            next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
            previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
        },
        infoFiltered: ""
    },
    dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
    scrollY: true,
    deferRender: true,
    scroller: true,
    processing: true,
    serverSide: true,
    filter: true,
    Sortable: true,
    order: [],
    fixedColumns: { left: 1, right: 1 },
    "rowCallback": function (row, data, index) {
        let api = this.api();
        let startIndex = api.context[0]._iDisplayStart;
        let counter = startIndex + index + 1;
        $('td:eq(0)', row).html(counter);
    },

    initComplete: function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    },
    "drawCallback": function () {
        const namelist = document.querySelectorAll("#userLogo");
        namelist.forEach((name) => {
            name.style.backgroundColor = randomColor();
        });
    }
};

function commonAjax(url, type) {
    return {
        type: "GET",
        url,
        dataType: "json",
        "data": function (d) {
            let sortIndex = d?.order?.[0]?.column ?? '';
            let orderValue = d?.order?.[0]?.dir ?? 'asc';
            const sortColumnMap = {
                "Individual": {
                    1: "userName",
                    2: "userType",
                    3: "Description"
                },
                "Group": {
                    1: "userName",
                    2: "userGroupProperties"
                }
            };

            let sortValue = sortColumnMap[type]?.[sortIndex] || "";

         
            const searchKey = type === "Individual" ? 'Individual' : 'Group';
            const filters = selectedValues[searchKey];
            const searchBoxId = type === "Individual" ? '#userSearch' : '#groupSearch';

            d.PageNumber = Math.ceil(d.start / d.length) + 1;
            d.pageSize = d.length;
            d.searchString = filters.length === 0 ? $(searchBoxId).val().trim() : filters.join(';');
            d.sortColumn = sortValue;
            d.SortOrder = orderValue;
            d.Type = type;
        },
        "dataSrc": function (json) {
            json.recordsTotal = json?.totalPages;
            json.recordsFiltered = json?.totalCount;
            $(".pagination-column").toggleClass("disabled", json?.data?.length === 0);
            return json?.data;
        }
    };
}
function userLists() {
    
    userApproverDataTable = $('#ApproverTableUser').DataTable({
        ...baseDataTableConfig,
        ajax: commonAjax(approverURL.getPaginatedlist,"Individual"),
        "columnDefs": [
                    {
                        "targets": [0,1, 2, 3,4],
                        "className": "truncate"
                    }
                ],          
        "columns": [
            {
                "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return meta.row + 1;
                    }
                    return data;
                },
                "orderable": false
            },
            {
                "data": "userName", "name": "User Name", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        let userName = data || 'NA';
                        let nameSplit = userName?.split(/[ _]+/);
                        let initials = nameSplit?.length > 1 ? nameSplit[0]?.trim().substring(0, 1) + nameSplit[1]?.trim().substring(0, 1).toUpperCase() : nameSplit[0]?.trim().substring(0, 1).toUpperCase();
                        return `<span class="Avatar_Logo"><span id="userLogo" class="Icon" title="${data || 'NA'}">${initials || 'NA'}</span></span>  <span title=''>  ${data || "NA"} </span>`;
                    }
                    return data;
                }
            },
            {
                "data": "userType", "name": "Type", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<span>  ${data || "NA"} </span>`;
                    }
                    return data;
                }
            },
            {
                "data": "Description", "name": "Deligates to", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<span>  ${data || "-"} </span>`;
                    }
                    return data;
                }
            },
            {
                "orderable": false,
                "render": (data, type, row) => {
                    const isEditAllowed = createPermission === "true";
                    const isDeleteAllowed = deletePermission === "true";
                    const isCpUser = row.userType?.toLowerCase() === "cp-user";

                    const editBtn = isEditAllowed
                        ? `<span role="button" title="${isCpUser ? '' : 'Edit'}" class="${isCpUser ? 'icon-disabled' : 'edit-button'}" data-site='${JSON.stringify(row)}'>
                    <i class="cp-edit"></i>
               </span>`
                        : `<span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span>`;

                    const deleteBtn = isDeleteAllowed
                        ? `<span role="button" title="Delete" class="delete-button" data-site-id="${row.id}" data-site-name="${row.userName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                    <i class="cp-Delete"></i>
               </span>`
                        : `<span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span>`;

                    const deligateBtn = `<span role="button" title="Deligate to" class="deligate-button" data-site-id="${row.id}" data-site-name="${row.userName}">
                                <i class="cp-deligate"></i>
                             </span>`;

                    return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}${deligateBtn}</div>`;
                }
            }

        ],
    });
 
}
function userGroupLists() {

    userGroupApproverDataTable = $('#ApproverTableGroupUser').DataTable({
        ...baseDataTableConfig,
        ajax: commonAjax(approverURL.getPaginatedlist, "Group"),
        "columnDefs": [
            {
                "targets": [1, 2],
                "className": "truncate"
            }       
        ],
        "columns": [
            {
                "data": null, "name": "Sr. No.", "orderable": false,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return meta.row + 1;
                    }
                    return data;
                },
                "orderable": false
            },
            {
                "data": "userName", "name": "GroupName", 
                "render": function (data, type, row) {
                    if (type === 'display') {
                        let userName = data || 'NA';
                        let nameSplit = userName?.split(/[ _]+/);
                        let initials = nameSplit?.length > 1 ? nameSplit[0]?.trim().substring(0, 1) + nameSplit[1]?.trim().substring(0, 1).toUpperCase() : nameSplit[0]?.trim().substring(0, 1).toUpperCase();
                        return `<span class="Avatar_Logo"><span id="userLogo" class="Icon" title="${data || 'NA'}">${initials || 'NA'}</span></span>  <span title=''>  ${data || "NA"} </span>`;
                    }
                    return data;
                }
            },            
            {
                "data": "userGroupProperties", "name": "Approvers", 
                "render": function (data, type, row) {
                    if (type === 'display') {
                        try {
                            const parsed = typeof data === 'string' ? JSON.parse(data) : data;
                            const names = parsed?.userGroupProperties?.map(u => u.Name).join(', ') || "-";
                            return `<span title="${names}">${names}</span>`;
                        } catch (e) {
                            return `<span>-</span>`;
                        }
                    }
                    return data;
                }
            },
            {
              
                "orderable": false,
                "render": (data, type, row) => {
                    const isEditAllowed = createPermission === "true";
                    const isDeleteAllowed = deletePermission === "true";

                    const editBtn = isEditAllowed
                        ? `<span role="button" title="Edit" class="userGroupEdit" data-usergroup='${JSON.stringify(row)}'>
                    <i class="cp-edit"></i>
               </span>`
                        : `<span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span>`;

                    const deleteBtn = isDeleteAllowed
                        ? `<span role="button" title="Delete" class="userGroupDelete" data-usergroupid="${row.id}" data-usergroup="${row.userName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                    <i class="cp-Delete"></i>
               </span>`
                        : `<span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span>`;
                 
                    return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}</div>`;
                }
            }

        ],
    });
}


const randomColor = () => {
    return `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase()}`
}

$.getJSON('/json/CountryDailCode.json', function (response) {

    setTimeout(() => {
        response.countrycode.forEach(function (value) {
            $('#mobilepre').append('<option value="' + value.dial_code + '">' + value.dial_code + ' (' + value.name + ')</option>');
        });
    }, 500);
});

const validEmail = (value) => {
    return (!(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/).test(value)) ? "Invalid email" : true;
}

async function getApproversData() {
    await $.ajax({
        type: "GET",
        url: RootUrl + approverURL.getApproverList,
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result && result?.data.length) {
                let options = [];
                let approverList = $('#approverList');
                approverList.empty();
             
                result?.data.forEach(function (item) {           
                options.push($('<option>').val(item.userId).text(item.userName));                  
                });
                approverList.append(options)
            }
            else {
                errorNotification(result);
            }
        }
    })
}

function addCPUser() {
    $.ajax({
        type: "GET",
        url: RootUrl + approverURL.getUsersList,
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                newApproverData = result?.data;
                approvarlists(newApproverData);
            } else {
                errorNotification(result);
            }
        }
    });
}

function searchedApproverData() {
    let searchedData = $("#approverSearchInput").val();
    const $addserApproval = $("#addserApproval");

    if (searchedData) {
        const searchedApproverData = newApproverData.filter(user => user.loginName.toLowerCase().includes(searchedData.toLowerCase()));
        let filteredData = searchedApproverData.filter(a => !addedApproverData.some(b => b.id === a.id))
        approvarlists(filteredData);

        if (filteredData?.length === 0) {
            $('#addUserApproval').append(`<div class='usersList'>${noDataFount}</div>`);
        } else {
            $(".usersList").remove();
        }

        if (addedApproverData?.length) {
            const searchedNewApproverData = addedApproverData.filter(user => user.loginName.toLowerCase().includes(searchedData.toLowerCase()));

            if (searchedNewApproverData?.length === 0) {
                $addserApproval.append(`<div class='addedUsersList'>${noDataFount}</div>`);
            } else {
                $(".addedUsersList").remove();
            }
            searchedNewApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }

    } else {
        let filteredData = newApproverData.filter(a => !addedApproverData.some(b => b.id === a.id))
        approvarlists(filteredData);

        if (filteredData?.length === 0) {
            $('#addUserApproval').append(`<div class='usersList'>${noDataFount}</div>`);
        } else {
            $(".usersList").remove();
        }

        if ($addserApproval.children().length !== 0) {
            if (addedApproverData?.length === 0) {
                $addserApproval.append(`<div class='addedUsersList'>${noDataFount}</div>`);

            }
        } else {
            $(".addedUsersList").remove();

            addedApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }

    }


    //if ($addserApproval.children().length === 0) {

    //    if ($('#addserApprovalTag').children('.NoDataContainer').length === 0) {
    //        $('#addserApproval').append(`<div class='addedUsersList'>
    //                                        ${noDataFount}
    //                                     </div>`);
    //    }
    //} else {
    //    $('#addserApprovalTag').children('.NoDataContainer').remove();
    //}

    //if ($("#addUserApproval").children().length === 0) {

    //    if ($('#unAddedTag').children('.NoDataContainer').length === 0) {
    //        $('#addUserApproval').append(`<div class="usersList">
    //                                        ${noDataFount}
    //                                      </div>`);
    //    }
    //} else {
    //    $('#unAddedTag').children('.NoDataContainer').remove();
    //}
}

function approvarlists(newApproverData) {
    const $addUserApproval = $("#addUserApproval");
    $addUserApproval.empty();

    const $addserApproval = $("#addserApproval");
    $addserApproval.empty();


    // Check if newApproverData is empty before running the loop
    //if (newApproverData.length === 0) {
    //    $('#addUserApproval').append(`${noDataFount}`);
    //} else {
    newApproverData.forEach(function (data, index) {
        let userName = data?.loginName || 'NA';
        let nameSplit = userName?.split(/[ _]+/);
        let initials = nameSplit?.length > 1 ? nameSplit[0]?.trim().substring(0, 1) + nameSplit[1]?.trim().substring(0, 1).toUpperCase() : nameSplit[0]?.trim().substring(0, 1).toUpperCase();

        const html = `
            <div class="border border-light-subtle rounded p-2 my-2" id="user-${data?.id}" data-index="${index}">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center gap-2">
                       <span class="Avatar_Logo"><span id="addUserLogo" class="Icon" title="${data?.loginName || 'NA'}">${initials || 'NA'}</span></span>
                        <div>
                            <p class="mb-0">
                                <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${data?.loginName}</span>
                            </p>
                            <span class="text-primary">${data?.roleName}</span>
                        </div>
                    </div>
                    <button type="button" data-id="${data?.id}" data-name="${data?.loginName}" data-role="${data?.roleName}" data-index="${index}" class="btn btn-primary btn-sm ADAddUser">
                        <span title="Add" class="cp-add"></span>
                    </button>
                </div>
            </div>`;

        const namelist = document.querySelectorAll("#addUserLogo");
        namelist.forEach((name) => {
            name.style.backgroundColor = randomColor();
        });
        $addUserApproval.append(html);
    });

    noDataApprover($addserApproval);
    //}    
}
function noDataApprover($addserApproval) {
 
    $(".unAddedtitle").removeClass('d-none')
    const noDataHtml = `
    <div class="border border-light-subtle rounded p-2 my-2 text-center no-approver-msg" id="no-approver-msg">
        <div class="d-flex align-items-center justify-content-center gap-2">
           
            <div>
                <p class="mb-0 fw-semibold text-muted">No approvers found</p>
            </div>
        </div>
    </div>`;

    $addserApproval.append(noDataHtml);
    $('#addserApprovalTag').show();
    $('.dataTable').removeClass('row-cols-1').addClass('row-cols-2');
}



function addNewApprover($addserApproval, userId, userName, userRole, index) {
    $addserApproval.find('#no-approver-msg').remove();
    let userNameData = userName || 'NA';
    let nameSplit = userNameData?.split(/[ _]+/);
    let initials = nameSplit?.length > 1 ? nameSplit[0]?.trim().substring(0, 1) + nameSplit[1]?.trim().substring(0, 1).toUpperCase() : nameSplit[0]?.trim().substring(0, 1).toUpperCase();

    $(".unAddedtitle").removeClass('d-none')
    const userHtml = `
            <div class="border border-light-subtle rounded p-2 my-2" id="selected-user-${userId}">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center gap-2">
                        <span class="Avatar_Logo">
                            <span id="addUserLogo" class="Icon" title="${userNameData}">${initials}</span>
                        </span>
                        <div>
                            <p class="mb-0">
                                <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${userName}</span>
                            </p>
                            <span class="text-primary">${userRole}</span>
                        </div>
                    </div>
                    <button type="button" data-id="${userId}" class="btn btn-danger btn-sm removeUser">
                        <span title="Remove" class="cp-subtract"></span>
                    </button>
                </div>
            </div>`;
    $addserApproval.append(userHtml);
    $("#user-" + userId).addClass('d-none');
    $('#addserApprovalTag').show();
    $('.dataTable').removeClass('row-cols-1').addClass('row-cols-2');
  
} 

function removeNewApprover($addserApproval, $addUserApproval, userId, userName, userRole, index) {
    $(`#selected-user-${userId}`).remove();

    const userHtml = `
        <div class="border border-light-subtle rounded p-2 my-2" id="user-${userId}" data-index="${index}">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center gap-2">
                    <span><img src="/img/profile-img/user.jpg" class="img-fluid rounded-circle" width="40" /></span>
                    <div>
                        <p class="mb-0">
                            <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${userName}</span>
                            <span class="badge bg-success rounded-pill px-2 ms-2">${userRole}</span>
                        </p>
                        <span class="text-primary">All group approver</span>
                    </div>
                </div>
                <button type="button" data-id="${userId}" data-name="${userName}" data-role="${userRole}" data-index="${index}" class="btn btn-primary btn-sm ADAddUser">
                    <span title="Add" class="cp-add"></span>
                </button>
            </div>
        </div>`;

    $(`#user-${userId}`).removeClass('d-none');
    $addUserApproval.append(userHtml);
    $(".ADAddUser").html('<span title="Add" class="cp-add"></span>').removeClass('btn-danger').addClass('btn-primary btn-sm');

    if ($addserApproval.children().length === 0) {
        $(".unAddedtitle").addClass('d-none')
       /* $('#addserApprovalTag').hide();*/
        /*$('.dataTable').removeClass('row-cols-2').addClass('row-cols-1');*/
        noDataApprover($addserApproval); 
    }
}

function GetBusinessServiceList(UserApproval = null) {
    $.ajax({
        type: "GET",
        url: RootUrl + approverURL.getBusinessServiceList,
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result) {
                if (result && result.length > 0) {
                    $('#selectBusinessService').append('<option value=""></option>');
                    result.forEach(item => {
                        $('#selectBusinessService').append('<option value="' + item.id + '">' + item.name + '</option>');
                    });

                    if (UserApproval && Object.keys(UserApproval)) {
                        $('#selectBusinessService').val(UserApproval.businessServiceProperties).trigger('change');
                    }
                }
            } else {
                errorNotification(result);
            }
        }
    });
}
function validateApprovers(approver, errorElement) {

    if (!approver || approver.length === 0) {
        errorElement.text("Select approvers").addClass('field-validation-error');
        return false;
    } else if (approver.length === 1) {
        errorElement.text("Select at least 2 approvers").addClass('field-validation-error');
        return false;
    } else {
        errorElement.text("").removeClass('field-validation-error');
        return true;
    }
}


async function validateGroupName(value, id = null, errorElement) {
    if (!value) {
        errorElement.text("Enter group name")
            .addClass('field-validation-error');
        return false;
    }

    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        //SpecialCharValidate(value),
        SpecialCharValidateCustom(value),
        ShouldNotBeginWithSpace(value),
        ShouldNotBeginWithUnderScore(value),
        OnlyNumericsValidate(value),
        ShouldNotBeginWithNumber(value),
        ShouldNotEndWithSpace(value),
        ShouldNotAllowMultipleSpace(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        minMaxlength(value),
        secondChar(value),
        //await IsFormNameExist(url, data, OnError)
    ];
    return CommonValidation(errorElement, validationResults);
}

async function validateUserName(value, id = null, errorElement) {   

    if (!value) {
        errorElement.text("Enter username")
            .addClass('field-validation-error');
        return false;
    }

    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + approverURL.nameExist;
    let data = {
        id: id,
        name: value
    };
    const validationResults = [
        //SpecialCharValidate(value),
        SpecialCharValidateCustom(value),
        ShouldNotBeginWithSpace(value),
        ShouldNotBeginWithUnderScore(value),
        OnlyNumericsValidate(value),
        ShouldNotBeginWithNumber(value),
        ShouldNotEndWithSpace(value),
        ShouldNotAllowMultipleSpace(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        minMaxlength(value),
        secondChar(value),
        await IsFormNameExist(url, data, OnError)
    ];
    return CommonValidation(errorElement, validationResults);
}

async function IsFormNameExist(url, data, errorFunc) {
    return !data.name.trim() ? true : (await GetFormAsync(url, data, errorFunc)) ? "Name already exists" : true;
};

async function GetFormAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
};

function validateDropDown(value, errorMessage, errorElement) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

function validateMobilePre(value) {
    const errorElement = $('#MobilePre-error');

    if (!value) {
        errorElement.text('Select country code').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true
    }
}

function validateMobile(value) {
    const errorElement = $('#Mobile-error');

    if (!value) {
        errorElement.text('Enter mobile number')
            .addClass('field-validation-error');
        return false;
    }
    else if (value) {
        const minLength = 7;
        if (value.length < minLength) {
            errorElement.text('Mobile number must be above 6 digits')
                .addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        errorElement.text('')
            .removeClass('field-validation-error');
        return true;
    }
}

function updateSiteType(UserApproval) {

    if (UserApproval.userType === 'Anonymous') {
        $("#profile-tab").trigger("click");
        $('#userNameId').val(UserApproval.id);
        $('#UserName').val(UserApproval.userName);
        $('#mail').val(UserApproval.email);
        $("input[name='flexCheckDefault']").prop('checked', UserApproval.isLink);
        GetBusinessServiceList(UserApproval)
        let splitNumber = UserApproval?.mobileNumber?.split(' ');
        $('#mobilenum').val(splitNumber[1])
        $('#mobilepre').val(splitNumber[0]);
        let errorElement = ['#UserName-error', '#Email-error', '#MobilePre-error', '#Mobile-error', '#BusinessService-error']
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    } else {
        $("#home-tab").trigger("click");
        $("#AMUserType").val('CP-User');
    }
}

async function validateEmail(value, id = null) {
    const errorElement = $('#Email-error');
    let format = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

    if (!value) {
        errorElement.text('Enter email')
            .addClass('field-validation-error');
        return false;
    } else if (value.length >= 321) {
        errorElement.text('Enter the value less than 320 characters')
            .addClass('field-validation-error');
        return false;
    } else if (value.length) {
        if (format.test(value) == false) {
            errorElement.text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else if (value.charAt(0) == "." || value.charAt(0) == "_") {
            errorElement.text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        errorElement.text('')
            .removeClass('field-validation-error');
        return true;
    }
    const validationResults = [await emailRegex(value)];
    return await CommonValidation(errorElement, validationResults);
}
