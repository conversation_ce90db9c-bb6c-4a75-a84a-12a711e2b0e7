﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Commands;

public class DeleteWorkflowProfileInfoTests : IClassFixture<WorkflowProfileInfoFixture>, IClassFixture<WorkflowOperationFixture>
{
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;

    private readonly WorkflowOperationFixture _workflowOperationFixture;

    private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;

    private readonly Mock<IWorkflowProfileInfoRepository> _mockWorkflowProfileInfoRepository;

    private readonly Mock<IPublisher> _mockPublisher;

    private readonly DeleteWorkflowProfileInfoCommandHandler _handler;
	public DeleteWorkflowProfileInfoTests(WorkflowProfileInfoFixture workflowProfileInfoFixture, WorkflowOperationFixture workflowOperationFixture)
    {
        _workflowProfileInfoFixture = workflowProfileInfoFixture;

        _workflowOperationFixture = workflowOperationFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockWorkflowProfileInfoRepository = WorkflowProfileInfoRepositoryMocks.DeleteWorkflowProfileInfoRepository(_workflowProfileInfoFixture.WorkflowProfileInfos);

        _mockWorkflowOperationRepository = WorkflowOperationRepositoryMocks.DeleteWorkflowOperationRepository(_workflowOperationFixture.WorkflowOperations);

        _handler = new DeleteWorkflowProfileInfoCommandHandler(_mockWorkflowProfileInfoRepository.Object, _mockPublisher.Object, _mockWorkflowOperationRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Success_When_Delete_WorkflowProfileInfo()
    {
        var validGuid = Guid.NewGuid().ToString();

        var workflowProfileInfo = _workflowProfileInfoFixture.WorkflowProfileInfos[0];

        workflowProfileInfo.ReferenceId = validGuid;

        _mockWorkflowProfileInfoRepository.Setup(repo => repo.GetByReferenceIdAsync(It.Is<string>(id => id == validGuid))).ReturnsAsync(workflowProfileInfo);

        var result = await _handler.Handle(new DeleteWorkflowProfileInfoCommand { Id = validGuid }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteWorkflowProfileInfoResponse));

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain($"Workflow '{workflowProfileInfo.WorkflowName}' has been removed successfully.");

        _mockWorkflowProfileInfoRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.WorkflowProfileInfo>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_Delete_WorkflowProfileInfo()
    {
        var validGuid = Guid.NewGuid().ToString();

        _mockWorkflowProfileInfoRepository.Setup(repo => repo.GetByReferenceIdAsync(validGuid)).ReturnsAsync(_workflowProfileInfoFixture.WorkflowProfileInfos[0]);

        var result = await _handler.Handle(new DeleteWorkflowProfileInfoCommand { Id = validGuid }, CancellationToken.None);

        var workflowProfileInfo = await _mockWorkflowProfileInfoRepository.Object.GetByReferenceIdAsync(validGuid);

        workflowProfileInfo.IsActive.ShouldBeFalse();

        result.ShouldBeOfType<DeleteWorkflowProfileInfoResponse>();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain("has been removed successfully");
    }

    [Fact]
    public async Task Handle_NotFoundException_When_Invalid_WorkflowProfileInfoId()
    {
        var nonExistentGuid = Guid.NewGuid().ToString();

        _mockWorkflowProfileInfoRepository.Setup(repo => repo.GetByReferenceIdAsync(nonExistentGuid)).ReturnsAsync((Domain.Entities.WorkflowProfileInfo)null);

        await Assert.ThrowsAsync<NotFoundException>(() =>_handler.Handle(new DeleteWorkflowProfileInfoCommand { Id = nonExistentGuid }, CancellationToken.None));
    }



	[Fact]
	public async Task Handle_ShouldThrowNotFoundException_WhenProfileInfoNotFound()
	{
		var command = new DeleteWorkflowProfileInfoCommand { Id = Guid.NewGuid().ToString() };
		_mockWorkflowProfileInfoRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
			.ReturnsAsync((Domain.Entities.WorkflowProfileInfo)null);

		await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
	}

    [Fact]
    public async Task Handle_ShouldThrowInvalidException_When_WorkflowProfileInfoIsInUse()
    {
        // Arrange
        var profileInfoId = Guid.NewGuid().ToString();


        var command = new DeleteWorkflowProfileInfoCommand
        {
            Id = profileInfoId
        };

        var workflowProfileInfo = new Domain.Entities.WorkflowProfileInfo
        {
            ReferenceId = profileInfoId,
            ProfileId = "Profile123",
            IsActive = true
        };

        var existingOperations = new List<Domain.Entities.WorkflowOperation>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ProfileId = "Profile123"
                // Populate other properties if needed
            }
        };

        _mockWorkflowProfileInfoRepository
            .Setup(x => x.GetByReferenceIdAsync(profileInfoId))
            .ReturnsAsync(workflowProfileInfo);




        _mockWorkflowOperationRepository
            .Setup(x => x.GetWorkflowOperationByProfileId(workflowProfileInfo.ProfileId))
            .ReturnsAsync(existingOperations);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe("The workflowProfileInfo is currently in use");
    }

}