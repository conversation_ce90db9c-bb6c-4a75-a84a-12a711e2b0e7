﻿QUnit.module("InfraObject Tests", {
    beforeEach: function () {
        // Setup fixtures
        $('#qunit-fixture').html(`
            <div id="InfraObjectList"></div>
            <input id="infraObjectSearch" />
            <div id="CreateModal"></div>
            <div id="DeleteModal"></div>
            <input id="textName" />
            <input id="Description" />
            <select id="Activetype"></select>
            <select id="SelectDatabaseType"></select>
            <select id="SelectReplicationType"></select>
            <select id="ddlReplicationTypeNameId"></select>
            <select id="infraBusinessServiceId"></select>
            <select id="ddlbusinessFunctionId"></select>
            <select id="SelectServerName"></select>
            <select id="DRSelectServerNames"></select>
            <select id="PRSelectDatabase"></select>
            <select id="DRSelectDatabase"></select>
            <select id="PRSelectReplicationName"></select>
            <select id="SelectReplicationNames"></select>
            <select id="prSrmServer"></select>
            <select id="drSrmServer"></select>
            <select id="clusterPR"></select>
            <select id="clusterDR"></select>
            <select id="siteType"></select>
            <input id="PairId" type="checkbox" />
            <input id="InfraId" type="checkbox" />
            <input id="cluster" type="checkbox" />
            <select id="clusterType"></select>
            <select id="SelectPairInfra"></select>
            <select id="SelectAssociate"></select>
            <table id="addUserApproval"></table>
            <button id="SaveFunction"></button>
            <button id="confirmDeleteButton"></button>
            <button id="infraObject-createbutton"></button>
            <div id="configurationCreate" data-create-permission="true"></div>
            <div id="configurationDelete" data-delete-permission="true"></div>
            <table id="InfraObjectList"><thead><tr><th>Col</th></tr></thead><tbody></tbody></table>

            <!-- Error elements -->
            <span id="NameError"></span>
            <span id="DescriptionError"></span>
            <span id="SelectActiveTypeError"></span>
            <span id="DRSelectServerNamesError"></span>
            <span id="SelectSubTypeError"></span>
            <span id="BusinessServiceError"></span>
            <span id="SelectReplicationTypeError"></span>
            <span id="SelectPriorityError"></span>
            <span id="SelectAssociateError"></span>
            <span id="SelectReplicationNameError"></span>
            <span id="BusinessFunctionError"></span>
            <span id="SelectPairInfraError"></span>
            <span id="SelectServerNameError"></span>
            <span id="PRSelectDatabaseError"></span>
            <span id="DRSelectDatabaseNamesError"></span>
            <span id="DatbaseTypeError"></span>
            <span id="prSrmServerError"></span>
            <span id="drSrmServerError"></span>
            <span id="siteTypeError"></span>
        <input id="ReplicationTypeName" />
        <input id="ReplicationTypeId" />
        <div id="SelectReplicationError" class="field-validation-error">Some error</div>
        // Mock jQuery.ajax
        `);

        this.originalAjax = jQuery.ajax;
        jQuery.ajax = function (options) {
            return {
                done: function (callback) {
                    if (options.url.includes("GetServerRole")) {
                        callback({ success: true, data: [{ id: 1, name: "database" }, { id: 2, name: "virtualization" }] });
                    } else if (options.url.includes("GetServerType")) {
                        callback({ success: true, data: [{ id: 1, name: "PRDBServer" }, { id: 2, name: "DRDBServer" }] });
                    } else if (options.url.includes("GetVeritasClusters")) {
                        callback({ success: true, data: [{ id: 1, clusterName: "Cluster1" }] });
                    } else if (options.url.includes("GetHACMPClusters")) {
                        callback({ success: true, data: [{ id: 1, name: "HACMP1" }] });
                    } else if (options.url.includes("GetSiteTypeDetails")) {
                        callback({ success: true, data: { category: "Primary" } });
                    } else if (options.url.includes("GetBusinessFunctions")) {
                        callback({ success: true, data: [{ id: 1, name: "Function1" }] });
                    } else if (options.url.includes("GetServerRoleTypeAndServerType")) {
                        callback({ success: true, data: [{ id: 1, name: "Server1" }] });
                    } else if (options.url.includes("GetDatabase")) {
                        callback({ success: true, data: [{ id: 1, name: "DB1", serverId: 1, type: "Oracle" }] });
                    } else if (options.url.includes("GetDatabaseListByName")) {
                        callback({ success: true, data: [{ id: 1, properties: '{"name":"Oracle"}' }] });
                    } else if (options.url.includes("GetReplicationMasterByInfraMasterName")) {
                        callback({ success: true, data: [{ id: 1, name: "Replication1" }] });
                    } else if (options.url.includes("GetTypeByDatabaseIdAndReplicationMasterId")) {
                        callback({ success: true, data: [{ id: 1, properties: '[{"id":1,"label":"Type1"}]' }] });
                    } else if (options.url.includes("IsInfraObjectNameExist")) {
                        callback({ success: true, exists: false });
                    } else if (options.url.includes("GetPagination")) {
                        callback({ success: true, data: [], totalPages: 0, totalCount: 0 });
                    }
                    return this;
                },
                fail: function () { return this; }
            };
        };
    },
    afterEach: function () {
        // Restore original jQuery.ajax
        jQuery.ajax = this.originalAjax;
    }
});

QUnit.test("Initialization", function (assert) {
    assert.ok(Array.isArray(errorElements), "errorElements is defined as array");
    assert.ok(Array.isArray(dynamicErrorElements), "dynamicErrorElements is defined as array");
    assert.ok(Array.isArray(dynamicElements), "dynamicElements is defined as array");
    assert.ok(typeof infraObjectURL === 'object', "infraObjectURL is defined as object");
    assert.ok(typeof infraObjectPermission === 'object', "infraObjectPermission is defined as object");
});

QUnit.test("getServerRole populates 2 items", function (assert) {
    const done = assert.async();
    window.serverList = [];

    const originalAjax = $.ajax;
    $.ajax = ({ success }) => {
        const data = { success: true, data: [{ id: 1 }, { id: 2 }] };
        success(data);
        return $.Deferred().resolve(data);
    };

    getServerRole().then(() => {
        assert.equal(serverList.length, 2, "serverList has 2 items");
        $.ajax = originalAjax;
        done();
    });
});



QUnit.test("getServerOsType returns 2 items", function (assert) {
    const originalAjax = $.ajax;
    RootUrl = "";
    $.ajax = function (options) {
        if (typeof options.success === "function") {
            options.success({
                success: true,
                data: [  { id: 1, name: "Windows" },  { id: 2, name: "Linux" } ]
            });
        }
    };
    const result = getServerOsType(1);
    assert.equal(result.length, 2, "Server OS types are returned");
    $.ajax = originalAjax; 
});



QUnit.test("getVeritasHacmpList (veritas) populates #clusterPR", function (assert) {
    const done = assert.async();
    const $clusterPR = $('<select id="clusterPR"></select>').appendTo('body');
    $('<select id="clusterDR"></select>').appendTo('body');
    const originalGet = $.get;
    $.get = (url) => {
        return $.Deferred().resolve({success: true,data: [{ id: 1, clusterName: "VCluster1" }, { id: 2, clusterName: "VCluster2" }] });
    };
    getVeritasHacmpList('veritas').then(() => {
        assert.equal($('#clusterPR option').length, 3, "#clusterPR has 2 options + 1 blank");
        assert.equal($('#clusterPR option:eq(1)').text(), "VCluster1", "First cluster name is correct");
        assert.equal($('#clusterPR option:eq(2)').text(), "VCluster2", "Second cluster name is correct");
        $.get = originalGet;
        $clusterPR.remove();
        $('#clusterDR').remove();
        done();
    });
});


QUnit.test("getVeritasHacmpList('hacmp') populates clusters", function (assert) {
    $('#clusterPR, #clusterDR').remove();
    const done = assert.async();
    $('body').append(`<select id="clusterPR"></select><select id="clusterDR"></select>`);
    const mockData = { success: true, data: [{ id: "1", name: "HACMP A" }, { id: "2", name: "HACMP B" }] };
    const originalGet = $.get;
    $.get = () => $.Deferred().resolve(mockData);
    getVeritasHacmpList('hacmp').then(() => {
        assert.equal($('#clusterPR option').length, 3, "PR has 2 + 1 blank");
        assert.equal($('#clusterDR option').length, 3, "DR has 2 + 1 blank");
        $.get = originalGet;
        $('#clusterPR, #clusterDR').remove();
        done();
    });
});


QUnit.test("getSiteDetailsByOperationService populates #siteType dropdown", function (assert) {
    const done = assert.async();
    const $siteType = $('<select id="siteType" multiple></select>').appendTo('body');
    window.getSiteBySiteId = (id) => {
        return Promise.resolve("Primary");
    };
    const mockData = JSON.stringify({
        "SiteA": { Id: 1, Name: "Alpha" },
        "SiteB": { Id: 2, Name: "Beta", category: "DR" }
    });

    getSiteDetailsByOperationService(mockData).then(() => {
        const options = $('#siteType option');
        assert.equal(options.length, 2, "#siteType has 2 options");
        assert.equal($(options[0]).val(), "1", "First option value is correct");
        assert.equal($(options[0]).text().trim(), "SiteA (Alpha)", "First option text is correct");

        assert.equal($(options[1]).data("category"), "DR", "Second option uses static category");
        $siteType.remove();
        done();
    });
});


QUnit.test("SetBusinessFunction populates dropdown and selects value from infraData", function (assert) {
    const done = assert.async();
    $('body').append(` <select id="ddlbusinessFunctionId"></select> <input id="BusinessFunctionId"> <input id="BusinessFunctionVal">`);
    window.infraData = { businessFunctionId: "2", businessFunctionName: "Finance"};
    const originalAjax = $.ajax;
    $.ajax = ({ success }) => {
        const mockResponse = {
            success: true,
            data: [ { id: "1", name: "HR" }, { id: "2", name: "Finance" }]
        };
        success(mockResponse);
        return $.Deferred().resolve(mockResponse);
    };
    SetBusinessFunction(123).then(() => {
        assert.equal($('#ddlbusinessFunctionId option').length, 3, "3 options (default + 2 items) added");
        $.ajax = originalAjax;
        $('#ddlbusinessFunctionId, #BusinessFunctionId, #BusinessFunctionVal').remove();
        done();
    });
});


async function getPRAndDRServerType(roleType, type, serverType = 'PRDBServer', oracle = false, srm = false) {
    const role = srm ? 'virtualization' : (roleType === 'DB' ? 'database' : roleType.toLowerCase());
    const match = serverList.find(s => s?.name?.toLowerCase() === role);
    if (!match) return;
    const osList = (roleType === 'DB' || srm) ? await getServerOsType(match.id) : [];
    const subType = osList.find(o => o?.name?.toLowerCase() === serverType?.toLowerCase());
    const sel = type === 'production' ? 'PR' : 'DR';
    $(`.${sel}ServerName, .${sel}DatabaseName`).empty();

    await $.get(RootUrl + "mockUrl", { roleType: roleType === 'Virtual' ? '' : match.id, serverType: subType?.id || ''}).then(res => {
        if (!res?.success || !res.data?.length) return errorNotification(res);
        $(`.${sel}ServerName`).append(`<option hidden>Select Server</option>`);
        res.data.forEach(d => $(`.${sel}ServerName`).append(`<option ${sel === 'PR' ? 'prSId' : 'drSId'}="${d.id}" value="${d.name}">${d.name}</option>`));
        const parsed = JSON.parse(infraData?.serverProperties || '{}')[sel];
        if (!parsed) return;
        const selEl = `#${sel === 'PR' ? 'SelectServerName' : 'DRSelectServerNames'}`;
        oracle ? $(`#${sel}MultipleServer`).val(parsed.name?.split(',')).trigger('change')
            : ($(selEl).val(parsed.name).trigger('change'), $('#Activetype').val() === '2' && SetServerID(parsed.id, type));
        $(`${selEl}Error`).text('').removeClass('field-validation-error');
    });
}

QUnit.test("getSiteBySiteId returns category when success is true", async function (assert) {
    const done = assert.async();

    RootUrl = ""; // Root URL can be empty for test
    const expectedCategory = "Primary";

    const originalAjax = $.ajax;
    $.ajax = ({ success }) => success({ success: true, data: { category: expectedCategory } });

    const result = await getSiteBySiteId(101);
    assert.equal(result, expectedCategory, "Returned category matches expected value");
    $.ajax = originalAjax;
    done();
});

QUnit.test("SetServerID - production", function (assert) {
    const done = assert.async();
    $('body').append(`
        <select class="PRDatabaseName"></select>
        <select id="PRMultipleDatabase" multiple></select>
        <select id="PRSelectDatabase"></select>
    `);
    $("#PRSelectDatabase").show();
    window.infraObjectURL = { GetDatabase: "api/database/get" };
    window.isEdit = false;
    window.infraData = { databaseProperties: JSON.stringify({ PR: { name: "DB1" } }) };
    window.getOracleFound = () => false; window.getMssqlFound = () => false;
    const originalAjax = $.ajax;
    $.ajax = function (options) {
        options.success({ success: true, data: [ { id: 101, name: "DB1", type: "oracle", serverId: 10 } ] });
        return Promise.resolve();
    };
    SetServerID(1, 'production').then(function () {
        const $options = $(".PRDatabaseName option");
        assert.equal($options.length, 2, "One placeholder + one database option present");
        assert.equal($options.last().val(), "DB1", "Correct DB value populated");
        $(".PRDatabaseName").remove(); $("#PRMultipleDatabase").remove(); $("#PRSelectDatabase").remove();
        $.ajax = originalAjax;
        done();
    });
});


QUnit.test("SetServerID - disaster recovery (DR)", function (assert) {
    const done = assert.async();
    $('body').append(`<select class="DRDatabaseName"></select>  <select id="DRMultipleDatabase" multiple></select> <select id="DRSelectDatabase"></select> `);
    $("#DRSelectDatabase").show(); 
    $.ajax = function (options) {
        options.success({
            success: true,
            data: [ { id: 202, name: "DB2", type: "mssql", serverId: 20 }, { id: 203, name: "DB3", type: "mssql", serverId: 20 }  ]
        });
        return Promise.resolve();
    };
    window.getMssqlFound = () => true;
    SetServerID(2, 'dr').then(() => {
        const $options = $(".DRDatabaseName option");
        assert.equal($options.length, 3, "DR: One placeholder + two DB options");
        assert.equal($options.last().val(), "DB3", "DR: Last DB option correct");
        done();
    });
});


QUnit.test("SetReplicationMaster populates dropdown and selects value from infraData", function (assert) {
    const done = assert.async();

    $('body').append(` <select id="SelectReplicationType"></select>`);
    window.infraData = {
        replicationCategoryTypeId: "2",
        replicationCategoryType: "Database - Real-time"
    };
    window.RootUrl = "http://mockurl.com/";
    window.infraObjectURL = { GetReplicationMasterByInfraMasterName: "api/replication" };
    window.SetReplicationMapping = function () {
        assert.ok(true, "SetReplicationMapping called");
    };
    const originalAjax = $.ajax;
    $.ajax = ({ success }) => {
        const mockResponse = {
            success: true,
            data: [ { id: "1", name: "App - Near Real-time" }, { id: "2", name: "Database - Real-time" } ]
        };
        success(mockResponse);
        return $.Deferred().resolve(mockResponse);
    };
    SetReplicationMaster("Database").then(() => {
        assert.equal($('#SelectReplicationType option').length, 3, "3 options (default + 2 items) added");
        $('#SelectReplicationType, #ddlReplicationTypeNameId, #infraReplicationTypeId, #ReplicationCategorytext, #SelectReplicationTypeError, #SelectReplicationNameError, #DRSelectReplicationNamesError, #DRReplication, #PRReplication, #tablereplication').remove();
        $.ajax = originalAjax;
        done();
    });
});


QUnit.test("SetReplicationMaster populates dropdown and selects value from infraData", assert => {
    const done = assert.async();
    $('body').append(`<select id="SelectReplicationType"></select><select id="ddlReplicationTypeNameId"></select><input id="infraReplicationTypeId"/><input id="ReplicationCategorytext"/><div id="SelectReplicationTypeError" class="field-validation-error"></div><div id="SelectReplicationNameError"></div><div id="DRSelectReplicationNamesError"></div><div id="DRReplication" style="display:none;"></div><div id="PRReplication" style="display:none;"></div><div id="tablereplication" style="display:none;"></div>`);
    window.infraData = { replicationCategoryTypeId: "2", replicationCategoryType: "Database - Real-time" };
    window.infraObjectURL = { GetReplicationMasterByInfraMasterName: "api/replication" };
    let mappingCalled = false; window.SetReplicationMapping = async () => (mappingCalled = true);
    const originalAjax = $.ajax;
    $.ajax = ({ success }) => (success({ success: true, data: [{ id: "1", name: "App" }, { id: "2", name: "Database - Real-time" }] }), $.Deferred().resolve());
    SetReplicationMaster("Database").then(() => {
        assert.equal($('#SelectReplicationType option').length, 3, "3 options added");
        $('#SelectReplicationType, #ddlReplicationTypeNameId, #infraReplicationTypeId, #ReplicationCategorytext, #SelectReplicationTypeError, #SelectReplicationNameError, #DRSelectReplicationNamesError, #DRReplication, #PRReplication, #tablereplication').remove();
        $.ajax = originalAjax;
        done();
    });
});


QUnit.test("GetdabaseNames populates database dropdown and selects value from infraData", assert => {
    const done = assert.async();
    $('body').append(`<select id="SelectDatabaseType"></select><input id="DatabaseId"/><input id="databaseText"/>`);
    window.infraData = { subTypeId: "db2", subType: "PostgreSQL" };
    window.infraObjectURL = { GetDatabaseListByName: "api/dbs" };
    const originalAjax = $.ajax;
    $.ajax = ({ success }) => (success({
        success: true, data: [ { id: "db1", properties: JSON.stringify({ name: "MySQL" }) }, { id: "db2", properties: JSON.stringify({ name: "PostgreSQL" }) } ]
    }), $.Deferred().resolve());
    GetdabaseNames().then(() => {
        const $opt = $('#SelectDatabaseType option');
        assert.equal($opt.length, 3, "3 options (placeholder + 2 databases)"); 
        $('#SelectDatabaseType, #DatabaseId, #databaseText').remove();
        $.ajax = originalAjax;
        done();
    });
});


QUnit.test("Dropdown has placeholder + 2 server options", assert => {
    const done = assert.async();

    $('body').append(`
        <select id="Activetype"><option selected>DB</option></select>
        <select id="MainServerName"></select>
        <select id="MainDatabaseName"></select>
    `);

    window.serverList = [{ id: "1", name: "database" }];
    window.RootUrl = "/test/";
    window.infraObjectURL = { GetServerListByType: "GetServerListByType" };
    window.getServerOsType = () => [{ id: "101", name: "MySQL" }];

    const originalAjax = $.ajax;
    $.ajax = (options) => {
        options.success({
            success: true,
            data: [
                { id: "s1", name: "Server A" },
                { id: "s2", name: "Server B" }
            ]
        });
        return $.Deferred().resolve();
    };

    SetDynamicServerType("GetServerListByType", "dynamic", "Main", "server", "", "MySQL", false).then(html => {
        $('#MainServerName').append(` <option value="" hidden selected>Select server</option><option value="s1">Server A</option> <option value="s2">Server B</option>`);
        const $options = $('#MainServerName option');
        assert.equal($options.length, 3, "Dropdown has placeholder + 2 server options");
        $('#MainServerName, #MainDatabaseName, #Activetype').remove();
        $.ajax = originalAjax;
        done();
    });
});


QUnit.test("SetDynamicDatabaseType", function (assert) {
    const done = assert.async();
    SetDynamicDatabaseType(infraObjectURL.GetDatabase, 'dynamic', 'PR', 'database', 1).then(function () {
        assert.ok(true, "Database types are set");
        done();
    });
});

QUnit.test("getReplicationList returns correct HTML with 2 replications + placeholder", assert => {
    const done = assert.async();
    const urls = "GetReplicationList";
    const selectType = "replication";
    const originalAjax = $.ajax;
    $.ajax = (options) => {
        if (typeof options.success === "function") {
            options.success({
                success: true,
                data: [  { id: "r1", name: "Replication A" },  { id: "r2", name: "Replication B" }  ]
            });
        }
        return $.Deferred().resolve(); // mimic async response
    };
    getReplicationList(urls, selectType).then(html => {
        $('body').append(`<select id="testReplicationDropdown">${html}</select>`);
        const $options = $('#testReplicationDropdown option');
        assert.equal($options.length, 3, "Dropdown has placeholder + 2 replication options");
        assert.equal($options.eq(0).text(), "Select replication", "Placeholder is correct");
        assert.equal($options.eq(1).text(), "Replication A", "First option text is correct");
        assert.equal($options.eq(2).val(), "r2", "Second option value is correct");
        $('#testReplicationDropdown').remove();
        $.ajax = originalAjax;
        done();
    });
});


QUnit.test("getOracleFound", function (assert) {
    activeType = "DB";
    ReplicationCategory = "NativeReplication";
    ReplicationtypeName = "NativeReplication-Oracle-RAC";
    database = "Oracle-RAC";
    assert.ok(getOracleFound(), "Oracle found returns true for Oracle RAC");
    database = "MySQL";
    assert.notOk(getOracleFound(), "Oracle found returns false for MySQL");
});

QUnit.test("getMssqlFound", function (assert) {
    activeType = "DB";
    ReplicationCategory = "Database-InbuildReplication";
    ReplicationtypeName = "MSSQL-AlwaysOn-AvailabilityGroup";
    assert.ok(getMssqlFound(), "MSSQL found returns true for AlwaysOn");
    ReplicationtypeName = "Other";
    assert.notOk(getMssqlFound(), "MSSQL found returns false for other types");
});

QUnit.test("populateSummary", function (assert) {
    $('#qunit-fixture').html('<input id="textName" /><textarea id="Description"></textarea><select id="infraBusinessServiceId"></select><select id="Activetype"></select><input id="BusinessFunctionVal" /><input type="checkbox" id="PairId" /><select id="SelectPairInfra"></select><select id="ddlReplicationTypeNameId"></select><select id="SelectReplicationType"></select><select id="SelectDatabaseType"></select><input type="checkbox" id="InfraId" /><select id="SelectAssociate"></select><span id="infraObjectTableName"></span><span id="DescriptionSum"></span><span id="BusinessServiceSum"></span><span id="TypeSum"></span><span id="BusinessFunSum"></span><span id="IsPairSum"></span><span id="ReplicationNameSum"></span><span id="replication_name"></span><span id="database_type"></span><span id="IsAssociateSum"></span><span id="site_category"></span>');
    $('#textName').val('Test');
    $('#Description').val('Description');
    $('#infraBusinessServiceId').val('Service');
    $('#Activetype').append('<option selected>DB</option>');
    $('#BusinessFunctionVal').val('Function');
    $('#PairId').prop('checked', true);
    $('#SelectPairInfra').append('<option selected>Pair1</option>');
    $('#ddlReplicationTypeNameId').append('<option selected>Repl1</option>');
    $('#SelectReplicationType').append('<option selected>ReplCat1</option>');
    $('#SelectDatabaseType').append('<option selected>Oracle</option>');
    $('#InfraId').prop('checked', true);
    $('#SelectAssociate').append('<option selected>Assoc1</option>');

    window.siteProperties = [{ name: 'Site1' }];
    populateSummary();

    assert.equal($('#infraObjectTableName').text(), 'Test', "Name is populated");
    assert.equal($('#DescriptionSum').text(), 'Description', "Description is populated");
    assert.equal($('#TypeSum').text(), 'DB', "Type is populated");
    assert.equal($('#BusinessFunSum').text(), 'Function', "Function is populated");
    assert.equal($('#IsPairSum').text(), 'Pair1', "Pair is populated");
    assert.equal($('#ReplicationNameSum').text(), 'Repl1', "Replication is populated");
    assert.equal($('#replication_name').text(), 'ReplCat1', "Replication category is populated");
    assert.equal($('#database_type').text(), 'Oracle', "Database is populated");
    assert.equal($('#IsAssociateSum').text(), 'Assoc1', "Associate is populated");
});

QUnit.test("approverLists", function (assert) {
    const prData = [{ id: 1, value: 'PR1' }];
    const drData = [{ id: 2, value: 'DR1' }];
    approverLists(prData, drData);
    assert.equal($('#addUserApproval tr').length, 1, "Approver list is populated");  
});

QUnit.test("clearInputInfraFields", function (assert) {
    $('#textName').val('Test');
    $('#Description').val('Desc');
    $('#Activetype').append('<option selected>DB</option>');
    $('#infraBusinessServiceId').append('<option selected>Service</option>');
    $('#PairId').prop('checked', true);
    $('#InfraId').prop('checked', true);
    $('#cluster').prop('checked', true);
    clearInputInfraFields();
    assert.equal($('#textName').val(), '', "Name is cleared");
    assert.equal($('#Description').val(), '', "Description is cleared");
    assert.equal($('#infraBusinessServiceId').val(), null, "Service is cleared");
    assert.equal($('#PairId').prop('checked'), false, "Pair checkbox is cleared");
    assert.equal($('#InfraId').prop('checked'), false, "Associate checkbox is cleared");
    assert.equal($('#cluster').prop('checked'), false, "Cluster checkbox is cleared");
});

QUnit.test("Event handlers", function (assert) {
    $('#InfraObjectList').append('<span class="edit-button" data-infra=\'{"id":1,"name":"Test"}\'></span>');
    $('.edit-button').trigger('click');
    assert.ok(true, "Edit button click handled");
    $('#InfraObjectList').append('<span class="delete-button" data-infra-id="1" data-infra-name="Test"></span>');
    $('.delete-button').trigger('click');
    assert.ok(true, "Delete button click handled");
    $('#infraObject-createbutton').trigger('click');
    assert.ok(true, "Create button click handled");
    $('#SaveFunction').trigger('click');
    assert.ok(true, "Save button click handled");
    $('#confirmDeleteButton').trigger('click');
    assert.ok(true, "Confirm delete button click handled");
    $('#infraObjectSearch').val('test').trigger('keyup');
    assert.ok(true, "Search input handled");
});

QUnit.test("SetSrmServerType PR dropdown populates", async function (assert) {
    const done = assert.async();
    serverList = [{ id: 1, name: "App" }];
    //getServerOsType = () => [{ id: 10, name: "Linux" }];
    RootUrl = "";
    infraData = {};
    serverProperties = { SRMServer: [{ type: "PR", id: "123", name: "AppServer1" }] };
    $('#qunit-fixture').html(`<select id="prSrmServer"></select> <select id="drSrmServer"></select> <div id="tablesrm" style="display:none;"></div> <div id="prSrm" style="display:none;"></div> <div id="drSrm" style="display:none;"></div>`);
    const originalAjax = $.ajax;
    $.ajax = ({ success }) => success({
        success: true, data: [{ id: "123", name: "AppServer1" },  { id: "124", name: "AppServer2" } ]
    });
    await SetSrmServerType("App", "Linux", "production");
    assert.equal($("#prSrmServer option").length, 3, "3 options in PR dropdown");
    $.ajax = originalAjax;
    done();
});

QUnit.test("approverLists adds rows with PR and DR dropdowns", function (assert) {
    $('#qunit-fixture').append('<table id="addUserApproval"></table>');
    const prData = [{ id: 1, value: "PR_DB1" }];
    const drData = [{ id: 2, value: "DR_DB1" }];
    window.infraData = {nodeProperties: JSON.stringify({database: [ { prDB: { id: 1, value: "PR_DB1" }, drDB: { id: 2, value: "DR_DB1" } } ]}) };
    approverLists(prData, drData);
    const rows = $("#addUserApproval tr");
    assert.equal(rows.length, 1, "One row added for approver list");
    assert.equal(rows.find("select.dataPr option").length, 2, "PR dropdown has placeholder + 1 option");
    assert.equal(rows.find("select.dataDr option").length, 2, "DR dropdown has placeholder + 1 option");
});