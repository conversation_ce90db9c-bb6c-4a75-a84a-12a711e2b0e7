﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Events.Create;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Events.Delete;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures
{
    public class WorkflowDrCalenderFixture : IDisposable
    {
        public IMapper Mapper { get; }
        public List<WorkflowDrCalender> WorkflowDrCalenderInfos { get; set; }
        public List<UserActivity> UserActivities { get; set; }
        public CreateWorkflowDrCalenderCommand CreateWorkflowDrCalenderCommand { get; set; }
        public UpdateWorkflowDrCalenderCommand UpdateWorkflowDrcalenderCommand { get; set; }
        public WorkflowDrCalenderSendEmailCommand SendEmailCommand { get; set; }
        public WorkflowDrCalenderCreatedEvent WorkflowDrCreatedEvent { get; set; }
        public WorkflowDrCalenderDeletedEvent WorkflowDrDeletedEvent { get; set; }
        public WorkflowDrCalenderUpdatedEvent WorkflowDrUpdatedEvent { get; set; }

        public WorkflowDrCalenderFixture()
        {
            WorkflowDrCalenderInfos = AutoWorkflowDrCalenderFixture.Create<List<WorkflowDrCalender>>();
            UserActivities = AutoWorkflowDrCalenderFixture.Create<List<UserActivity>>();
            CreateWorkflowDrCalenderCommand = AutoWorkflowDrCalenderFixture.Create<CreateWorkflowDrCalenderCommand>();
            UpdateWorkflowDrcalenderCommand = AutoWorkflowDrCalenderFixture.Create<UpdateWorkflowDrCalenderCommand>();
            SendEmailCommand = AutoWorkflowDrCalenderFixture.Create<WorkflowDrCalenderSendEmailCommand>();
            WorkflowDrCreatedEvent = AutoWorkflowDrCalenderFixture.Create<WorkflowDrCalenderCreatedEvent>();
            WorkflowDrDeletedEvent = AutoWorkflowDrCalenderFixture.Create<WorkflowDrCalenderDeletedEvent>();
            WorkflowDrUpdatedEvent = AutoWorkflowDrCalenderFixture.Create<WorkflowDrCalenderUpdatedEvent>();

            var configurationProvider = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<WorkflowDrCalenderProfile>();
            });
            Mapper = configurationProvider.CreateMapper();
        }

        public Fixture AutoWorkflowDrCalenderFixture
        {
            get
            {
                var fixture = new Fixture();

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateWorkflowDrCalenderCommand>(p => p.ProfileName, 10));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowDrCalenderCommand>(p => p.ProfileName, 10));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowDrCalenderSendEmailCommand>(p => p.ProfileId, 10));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
                fixture.Customize<UpdateWorkflowDrCalenderCommand>(c => c.With(b => b.Id, 0.ToString()));
                fixture.Customize<WorkflowDrCalender>(c => c.With(b => b.IsActive, true));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowDrCalenderCreatedEvent>(p => p.Name, 10));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowDrCalenderDeletedEvent>(p => p.Name, 10));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowDrCalenderUpdatedEvent>(p => p.Name, 10));

                return fixture;
            }
        }
        public void Dispose()
        {

        }
    }
}
