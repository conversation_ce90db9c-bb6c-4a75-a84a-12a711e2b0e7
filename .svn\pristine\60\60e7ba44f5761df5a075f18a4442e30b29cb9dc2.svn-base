using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoHacmpClusterDataAttribute : AutoDataAttribute
{
    public AutoHacmpClusterDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateHacmpClusterCommand>(p => p.Name, 10));
            fixture.Customize<CreateHacmpClusterCommand>(c => c.With(b => b.Name, "TestCluster"));
            fixture.Customize<CreateHacmpClusterCommand>(c => c.With(b => b.ServerId, "TestServerId"));
            fixture.Customize<CreateHacmpClusterCommand>(c => c.With(b => b.ServerName, "TestServerName"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateHacmpClusterCommand>(p => p.Name, 10));
            fixture.Customize<UpdateHacmpClusterCommand>(c => c.With(b => b.Id, 10.ToString()));
            fixture.Customize<UpdateHacmpClusterCommand>(c => c.With(b => b.Name, "TestCluster"));
            fixture.Customize<UpdateHacmpClusterCommand>(c => c.With(b => b.ServerId, "TestServerId"));
            fixture.Customize<UpdateHacmpClusterCommand>(c => c.With(b => b.ServerName, "TestServerName"));

            return fixture;
        })
    {

    }
}
