﻿using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Events.PaginatedView;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]
public class ApprovalMatrixController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<ApprovalMatrixController> _logger;

    public ApprovalMatrixController(IPublisher publisher,ILogger<ApprovalMatrixController> logger, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
    }
    
    [AntiXss]
    [EventCode(EventCodes.ApprovalMatrix.List)]
    public IActionResult List()
    {
        _publisher.Publish(new ApprovalMatrixPaginatedEvent());
        return View();
    }

    [HttpGet]
    [EventCode(EventCodes.ApprovalMatrix.GetPaginatedRequestList)]
    public async Task<JsonResult> GetPaginatedRequestList(GetApprovalMatrixRequestPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginatedRequestList method in ApprovalMatrix");
        try
        {
            var result = await _dataProvider.ApprovalMatrixRequest.GetPaginatedApprovalMatrixRequests(query);
            _logger.LogDebug("Successfully retrieved GetPaginatedRequestList in ApprovalMatrix");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Request page while retrieving RequestList.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ApprovalMatrix.GetPaginatedApprovalList)]
    public async Task<JsonResult> GetPaginatedApprovalList(GetApprovalMatrixApprovalPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginatedApprovalList method in ApprovalMatrix");
        try
        {
            var result = await _dataProvider.ApprovalMatrixApproval.GetPaginatedApprovalMatrixApprovals(query);
            _logger.LogDebug("Successfully retrieved GetPaginatedApprovalList in ApprovalMatrix");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Approval page while retrieving ApprovalList.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ApprovalMatrix.WithdrawRequest)]
    public async Task<JsonResult> WithdrawRequest(WithdrawApprovalMatrixRequestCommand command)
    {
        _logger.LogDebug("Entering WithdrawRequest method in ApprovalMatrix");
        try
        {
            var result = await _dataProvider.ApprovalMatrixRequest.WithdrawApprovalMatrixRequest(command);
            _logger.LogDebug("WithdrawRequest operation executed successfully in ApprovalMatrix");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on WithdrawRequest method.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ApprovalMatrix.ApproveReject)]
    public async Task<JsonResult> ApproveReject(ApprovalMatrixApprovalCommand command)
    {
        _logger.LogDebug("Entering ApproveReject method in ApprovalMatrix");
        try
        {
            var result = await _dataProvider.ApprovalMatrixApproval.UpdateApprovalMatrixStatus(command);
            _logger.LogDebug("Approve (or) Reject operation executed successfully in ApprovalMatrix");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ApproveReject method.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ApprovalMatrix.GetApprovalMatrixByRequestId)]
    public async Task<JsonResult> GetApprovalMatrixByRequestId(string requestId)
    {
        _logger.LogDebug("Entering GetApprovalMatrixByRequestId method in ApprovalMatrix");
        try
        {
            var result = await _dataProvider.ApprovalMatrixRequest.GetApprovalMatrixByRequestId(requestId);
            _logger.LogDebug("GetApprovalMatrixByRequestId operation executed successfully in ApprovalMatrix");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on GetApprovalMatrixByRequestId method.", ex);
            return ex.GetJsonException();
        }
    }
}