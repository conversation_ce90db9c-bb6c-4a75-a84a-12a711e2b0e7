﻿using ContinuityPatrol.Application.Features.BusinessService.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessService.Commands;

public class DeleteBusinessServiceTests : IClassFixture<BusinessServiceFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly Mock<IBusinessServiceRepository> _mockBusinessServiceRepository;
    private readonly DeleteBusinessServiceCommandHandler _handler;

    public DeleteBusinessServiceTests(BusinessServiceFixture businessServiceFixture)
    {
        _businessServiceFixture = businessServiceFixture;

        var mockPublisher = new Mock<IPublisher>();
        
        _mockBusinessServiceRepository =
            BusinessServiceRepositoryMocks.DeleteBusinessServiceRepository(_businessServiceFixture.BusinessServices);
       
        var mockInfraObjectViewRepository = new Mock<IInfraObjectViewRepository>();
        mockInfraObjectViewRepository.Setup(x => x.GetInfraObjectByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<InfraObjectView>());
       
        var mockBusinessFunctionRepository = new Mock<IBusinessFunctionRepository>();
        mockBusinessFunctionRepository.Setup(x => x.GetBusinessFunctionListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<Domain.Entities.BusinessFunction>());


        var mockServerViewRepository = new Mock<IServerViewRepository>();
        mockServerViewRepository.Setup(x => x.GetServerByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<ServerView>());

        var mockDatabaseViewRepository = new Mock<IDatabaseViewRepository>();
        mockDatabaseViewRepository.Setup(x => x.GetDatabaseByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<DatabaseView>());

        var mockReplicationViewRepository = new Mock<IReplicationViewRepository>();
        mockReplicationViewRepository.Setup(x => x.GetReplicationByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<ReplicationView>());

        var mockSingleSignOnRepository = SingleSignOnRepositoryMocks.GetSingleSignOnEmptyRepository();
       
        _handler = new DeleteBusinessServiceCommandHandler(_mockBusinessServiceRepository.Object, mockPublisher.Object,mockBusinessFunctionRepository.Object, mockServerViewRepository.Object,
            mockDatabaseViewRepository.Object, mockReplicationViewRepository.Object, mockSingleSignOnRepository.Object, mockInfraObjectViewRepository.Object);
      
     
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_BusinessServiceDeleted()
    {
        var validGuid = Guid.NewGuid();

        _businessServiceFixture.BusinessServices[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteBusinessServiceCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteBusinessServiceResponse_When_BusinessServiceDeleted()
    {
        var validGuid = Guid.NewGuid();

        _businessServiceFixture.BusinessServices[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteBusinessServiceCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteBusinessServiceResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_BusinessServiceDeleted()
    {
        var validGuid = Guid.NewGuid();

        _businessServiceFixture.BusinessServices[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteBusinessServiceCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var businessService = await _mockBusinessServiceRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        businessService.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidDatabaseId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteBusinessServiceCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid().ToString();

        _businessServiceFixture.BusinessServices[0].ReferenceId = validGuid;

        var result = await _handler.Handle(new DeleteBusinessServiceCommand() { Id = _businessServiceFixture.BusinessServices[0].ReferenceId }, CancellationToken.None);

        _mockBusinessServiceRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockBusinessServiceRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BusinessService>()), Times.Once);
    }
}