using ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

/// <summary>
/// Fixture class for CyberAlert module testing
/// Provides test data setup and AutoFixture configuration for comprehensive unit testing
/// Purpose: CyberAlert manages security alerts and notifications for cyber threats and incidents
/// </summary>
public class CyberAlertFixture : IDisposable
{
    public List<CyberAlert> CyberAlerts { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateCyberAlertCommand CreateCyberAlertCommand { get; set; }
    public UpdateCyberAlertCommand UpdateCyberAlertCommand { get; set; }
    public DeleteCyberAlertCommand DeleteCyberAlertCommand { get; set; }
    //public AcknowledgeCyberAlertCommand AcknowledgeCyberAlertCommand { get; set; }
    //public ResolveCyberAlertCommand ResolveCyberAlertCommand { get; set; }
    public IMapper Mapper { get; set; }

    public CyberAlertFixture()
    {
        // Initialize manual test data with known values for reliable testing
        CyberAlerts = new List<CyberAlert>
        {
            new CyberAlert
            {
                ReferenceId = Guid.NewGuid().ToString(),
               
                Severity = "High",
                
                Type = "Intrusion",
               
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalAlerts = AutoCyberAlertFixture.CreateMany<CyberAlert>(2).ToList();
            CyberAlerts.AddRange(additionalAlerts);
            
            UserActivities = AutoCyberAlertFixture.CreateMany<UserActivity>(3).ToList();
            CreateCyberAlertCommand = AutoCyberAlertFixture.Create<CreateCyberAlertCommand>();
            UpdateCyberAlertCommand = AutoCyberAlertFixture.Create<UpdateCyberAlertCommand>();
            DeleteCyberAlertCommand = AutoCyberAlertFixture.Create<DeleteCyberAlertCommand>();
            //AcknowledgeCyberAlertCommand = AutoCyberAlertFixture.Create<AcknowledgeCyberAlertCommand>();
            //ResolveCyberAlertCommand = AutoCyberAlertFixture.Create<ResolveCyberAlertCommand>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateCyberAlertCommand = new CreateCyberAlertCommand();
            UpdateCyberAlertCommand = new UpdateCyberAlertCommand();
            DeleteCyberAlertCommand = new DeleteCyberAlertCommand();
          
        }

        // Configure AutoMapper for CyberAlert mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CyberAlertProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }
    public Fixture AutoCyberAlertFixture
    {
        get
        {
            var fixture = new Fixture();
            
            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations for commands
           // fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateCyberAlertCommand>(p => p.Title, 200));
            fixture.Customize<CreateCyberAlertCommand>(c => c
                //.With(b => b.AlertId, () => Guid.NewGuid().ToString())
                //.With(b => b.Title, () => $"Test Cyber Alert {fixture.Create<int>():000}")
                //.With(b => b.Description, () => $"Test cyber security alert description {fixture.Create<int>()}")
                //.With(b => b.Severity, () => new[] { "Low", "Medium", "High", "Critical" }[fixture.Create<int>() % 4])
                //.With(b => b.Priority, () => new[] { "Low", "Medium", "High", "Critical", "Urgent" }[fixture.Create<int>() % 5])
                //.With(b => b.Category, () => new[] { "Security", "Performance", "Availability", "Compliance" }[fixture.Create<int>() % 4])
                //.With(b => b.Type, () => new[] { "Intrusion", "Malware", "DDoS", "Phishing", "Vulnerability" }[fixture.Create<int>() % 5])
                //.With(b => b.Source, () => new[] { "IDS", "IPS", "SIEM", "Firewall", "Antivirus" }[fixture.Create<int>() % 5])
                //.With(b => b.SourceId, () => Guid.NewGuid().ToString())
                //.With(b => b.TargetSystem, () => $"Target System {fixture.Create<int>():00}")
                //.With(b => b.TargetId, () => Guid.NewGuid().ToString())
                //.With(b => b.Status, "Open")
                //.With(b => b.AlertData, () => $"{{\"ip\":\"192.168.1.{fixture.Create<int>() % 255}\",\"port\":\"{fixture.Create<int>() % 65535}\",\"protocol\":\"HTTP\"}}")
                //.With(b => b.RuleId, () => Guid.NewGuid().ToString())
                //.With(b => b.RuleName, () => $"Security Rule {fixture.Create<int>():000}")
                );

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateCyberAlertCommand>(p => p.Title, 200));
            fixture.Customize<UpdateCyberAlertCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                //.With(b => b.Title, () => $"Updated Cyber Alert {fixture.Create<int>():000}")
                //.With(b => b.Description, () => $"Updated cyber security alert description {fixture.Create<int>()}")
                //.With(b => b.Severity, () => new[] { "Medium", "High", "Critical" }[fixture.Create<int>() % 3])
                //.With(b => b.Priority, () => new[] { "Medium", "High", "Critical", "Urgent" }[fixture.Create<int>() % 4])
                //.With(b => b.Status, () => new[] { "Open", "In Progress", "Under Investigation" }[fixture.Create<int>() % 3])
                );

            fixture.Customize<DeleteCyberAlertCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            //fixture.Customize<AcknowledgeCyberAlertCommand>(c => c
            //    .With(b => b.Id, () => Guid.NewGuid().ToString())
            //    .With(b => b.AcknowledgementNotes, () => $"Alert acknowledged - {fixture.Create<int>()}"));

            //fixture.Customize<ResolveCyberAlertCommand>(c => c
            //    .With(b => b.Id, () => Guid.NewGuid().ToString())
            //    .With(b => b.ResolutionNotes, () => $"Alert resolved - {fixture.Create<int>()}"));

            // CyberAlert entity customizations
            fixture.Customize<CyberAlert>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                //.With(b => b.AlertId, () => Guid.NewGuid().ToString())
                //.With(b => b.Title, () => $"Test Cyber Alert {fixture.Create<int>():000}")
                //.With(b => b.Description, () => $"Test cyber security alert description {fixture.Create<int>()}")
                //.With(b => b.Severity, () => new[] { "Low", "Medium", "High", "Critical" }[fixture.Create<int>() % 4])
                //.With(b => b.Priority, () => new[] { "Low", "Medium", "High", "Critical", "Urgent" }[fixture.Create<int>() % 5])
                //.With(b => b.Category, () => new[] { "Security", "Performance", "Availability", "Compliance" }[fixture.Create<int>() % 4])
                //.With(b => b.Type, () => new[] { "Intrusion", "Malware", "DDoS", "Phishing", "Vulnerability" }[fixture.Create<int>() % 5])
                //.With(b => b.Source, () => new[] { "IDS", "IPS", "SIEM", "Firewall", "Antivirus" }[fixture.Create<int>() % 5])
                //.With(b => b.SourceId, () => Guid.NewGuid().ToString())
                //.With(b => b.TargetSystem, () => $"Target System {fixture.Create<int>():00}")
                //.With(b => b.TargetId, () => Guid.NewGuid().ToString())
                //.With(b => b.Status, () => new[] { "Open", "Acknowledged", "In Progress", "Resolved", "Closed" }[fixture.Create<int>() % 5])
                //.With(b => b.AlertData, () => $"{{\"ip\":\"192.168.1.{fixture.Create<int>() % 255}\",\"port\":\"{fixture.Create<int>() % 65535}\",\"protocol\":\"HTTP\"}}")
                //.With(b => b.RuleId, () => Guid.NewGuid().ToString())
                //.With(b => b.RuleName, () => $"Security Rule {fixture.Create<int>():000}")
                //.With(b => b.DetectedAt, () => DateTime.UtcNow.AddMinutes(-fixture.Create<int>() % 1440))
                //.With(b => b.AcknowledgedAt, () => fixture.Create<bool>() ? DateTime.UtcNow.AddMinutes(-fixture.Create<int>() % 720) : (DateTime?)null)
                //.With(b => b.AcknowledgedBy, () => fixture.Create<bool>() ? $"User{fixture.Create<int>():000}" : null)
                //.With(b => b.ResolvedAt, () => fixture.Create<bool>() ? DateTime.UtcNow.AddMinutes(-fixture.Create<int>() % 360) : (DateTime?)null)
                //.With(b => b.ResolvedBy, () => fixture.Create<bool>() ? $"User{fixture.Create<int>():000}" : null)
                //.With(b => b.ResolutionNotes, () => fixture.Create<bool>() ? $"Resolution notes {fixture.Create<int>()}" : null)
                );

            // UserActivity customization for CyberAlert
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "CyberAlert")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test cyber alert activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
