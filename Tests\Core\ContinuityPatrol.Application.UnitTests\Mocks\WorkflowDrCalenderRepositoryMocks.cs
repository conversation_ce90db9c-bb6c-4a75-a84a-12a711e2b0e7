﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks
{
   public class WorkflowDrCalenderRepositoryMocks
    {
        public static Mock<IWorkflowDrCalenderRepository> CreateWorkflowDrcalenderRepository(List<WorkflowDrCalender> workflowDrcalenderInfos)
        {
            var workflowDrcalenderRepository = new Mock<IWorkflowDrCalenderRepository>();

            workflowDrcalenderRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowDrcalenderInfos);

            workflowDrcalenderRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowDrCalender>())).ReturnsAsync(
             (WorkflowDrCalender workflowOperation) =>
             {
                 workflowOperation.Id = new Fixture().Create<int>();
                 workflowOperation.ReferenceId = new Fixture().Create<Guid>().ToString();
                 workflowDrcalenderInfos.Add(workflowOperation);
                 return workflowOperation;
             });
            return workflowDrcalenderRepository;
        }

        public static Mock<IWorkflowDrCalenderRepository> UpdateworkflowDrcalenderRepository(List<WorkflowDrCalender> workflowDrcalenderInfos)
        {
            var workflowDrcalenderRepository = new Mock<IWorkflowDrCalenderRepository>();

            workflowDrcalenderRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowDrcalenderInfos);

            workflowDrcalenderRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowDrcalenderInfos.SingleOrDefault(x => x.ReferenceId == i));

            workflowDrcalenderRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowDrCalender>())).ReturnsAsync((WorkflowDrCalender workflowDrcalenderInfo) =>
            {
                var index = workflowDrcalenderInfos.FindIndex(item => item.ReferenceId == workflowDrcalenderInfo.ReferenceId);
                workflowDrcalenderInfos[index] = workflowDrcalenderInfo;
                return workflowDrcalenderInfo;
            });
            return workflowDrcalenderRepository;
        }

        public static Mock<IWorkflowDrCalenderRepository> DeleteworkflowDrcalenderRepository(List<WorkflowDrCalender> workflowDrcalenderInfos)
        {
            var workflowDrcalenderRepository = new Mock<IWorkflowDrCalenderRepository>();
            workflowDrcalenderRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowDrcalenderInfos);

            workflowDrcalenderRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowDrcalenderInfos.SingleOrDefault(x => x.ReferenceId == i));

            workflowDrcalenderRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowDrCalender>())).ReturnsAsync((WorkflowDrCalender workflowDrcalenderInfo) =>
            {
                var index = workflowDrcalenderInfos.FindIndex(item => item.ReferenceId == workflowDrcalenderInfo.ReferenceId);
                workflowDrcalenderInfo.IsActive = false;
                workflowDrcalenderInfos[index] = workflowDrcalenderInfo;

                return workflowDrcalenderInfo;
            });

            return workflowDrcalenderRepository;
        }

        public static Mock<IWorkflowDrCalenderRepository> GetworkflowDrcalenderRepository(List<WorkflowDrCalender> workflowDrcalenderInfos)
        {
            var workflowDrcalenderRepository = new Mock<IWorkflowDrCalenderRepository>();

            workflowDrcalenderRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowDrcalenderInfos);

            workflowDrcalenderRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowDrcalenderInfos.SingleOrDefault(x => x.ReferenceId == i));

            return workflowDrcalenderRepository;
        }

        //Events
        public static Mock<IUserActivityRepository> CreateWorkflowDrCalenderEventRepository(List<UserActivity> userActivities)
        {
            var workflowDrcalenderRepository = new Mock<IUserActivityRepository>();

            workflowDrcalenderRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
              (UserActivity userActivity) =>
              {
                  userActivity.LoginName = new Fixture().Create<string>();

                  userActivities.Add(userActivity);

                  return userActivity;
              });

            return workflowDrcalenderRepository;
        }

        public static Mock<IWorkflowDrCalenderRepository> GetWorkflowDrEmptyRepository()
        {
            var workflowDrEmptyRepository = new Mock<IWorkflowDrCalenderRepository>();

            workflowDrEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowDrCalender>());

            return workflowDrEmptyRepository;
        }

        public static Mock<IWorkflowDrCalenderRepository> GetWorkflowDrcalenderNameUniqueRepository(List<WorkflowDrCalender> workflowDr)
        {
            var workflowDrUniqueNameRepository = new Mock<IWorkflowDrCalenderRepository>();

            workflowDrUniqueNameRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => workflowDr.Exists(x => x.ProfileName == i && x.ReferenceId == j));

            return workflowDrUniqueNameRepository;
        }
    }
}
