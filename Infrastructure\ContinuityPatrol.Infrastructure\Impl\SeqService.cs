﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Hubs;

namespace ContinuityPatrol.Infrastructure.Impl;

public class SeqService : ISeqService
{
    private readonly RestClient _client;
   // public static Dictionary<string, List<LogHubVm>> MyDict = new();
    private readonly IConfiguration _config;
    private readonly IHubContext<LogHub> _hubContext;
    private readonly ILogger<SeqService> _logger;
    private string _authCookie; 
    private string _domain;
    private string _path;
    private bool _isConnected;


    public SeqService(IConfiguration config, IHubContext<LogHub> hubContext, ILogger<SeqService> logger)
    {
        _config = config;
        _hubContext = hubContext;
        _logger = logger;

        var options = new RestClientOptions(_config.GetSection("SeqConfig:ServerUrl").Value!)
        {
            RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true
        };
        _client = new RestClient(options);
    }

    public async Task LoginAsync()
    {
        try
        {
            _logger.LogDebug("Attempting to log in to the Seq API.");

            var username = _config.GetSection("SeqConfig:Username").Value;
            var password = _config.GetSection("SeqConfig:Password").Value;

            var request = new RestRequest("api/users/login", Method.Post); // Endpoint for login

            request.AddJsonBody(new
            {
                Username = SecurityHelper.Decrypt(username),
                Password = SecurityHelper.Decrypt(password)
            });

            _logger.LogInformation("Sending login request to Seq API.");

            var response = await _client.ExecuteAsync(request);

            if(response.ErrorMessage!.Equals("Simulated failure"))
            {
                throw new SeqApiException("Simulated failure during Seq login.",HttpStatusCode.BadRequest);
            }

            if (response.IsSuccessful)
            {
                var cookie = response.Cookies!.FirstOrDefault(c => c.Name == "Seq-Session");
                if (cookie != null)
                {
                    _authCookie = cookie.Value;
                    _domain = cookie.Domain;
                    _path = cookie.Path;
                    _isConnected = true;

                    _logger.LogDebug("Login successful. Seq-Session cookie obtained.");
                }
            }
            else
            {
                _isConnected = false;

                _logger.LogWarning("Login failed. No valid response from Seq API.");
            }
        }
        catch (SeqApiException ex)
        {
            _isConnected = false;
            _logger.LogError($"LoginAsync SeqApiException : {ex.GetMessage()}");
        }
        catch (Exception e)
        {
            _isConnected = false;
            _logger.LogError($"LoginAsync Exception : {e.GetMessage()}");
        }
        finally
        {
            if (_isConnected)
            {
                _logger.LogInformation("Seq Connection established successfully!.");
            }
            else
            {
                _logger.LogWarning("Unable to establish seq connection.");
            }
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            _logger.LogDebug("Attempting to log out from Seq API.");

            var request = new RestRequest("api/users/logout", Method.Post);

            if (_authCookie.IsNotNullOrEmpty())
            {
                _logger.LogDebug("Adding 'Seq-Session' cookie to the request.");

                request.AddCookie("Seq-Session", _authCookie, _path, _domain); // Add the session cookie to request headers

                var response = await _client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    _logger.LogInformation("Logout successful.");
                }
                else
                {
                    _logger.LogError($"Logout failed. Status code: {response.StatusCode}, Error: {response.ErrorMessage}");
                }
            }
            else
            {
                _logger.LogDebug("No 'Seq-Session' cookie found. Skipping cookie addition.");
            }

            _authCookie = null;
            _domain = null;
            _path = null;
            _isConnected = false;
        }
        catch (Exception ex)
        {
            _logger.LogError($"LogoutAsync encountered an error: {ex.Message}");
        }
    }

    public async Task<List<LogHubVm>> GetSeqLogsByGroupIdAsync(string groupId, bool isHub = false, bool isFilter = false)
    {
        try
        {
            if(!_isConnected)
                await LoginAsync();

            var request = new RestRequest("api/data");
            request.AddHeader("Content-Type", "application/json");

            request.AddCookie("Seq-Session", $"{_authCookie}", _path, _domain);

            var query = isFilter
                ? $"SELECT * FROM stream WHERE GroupId = '{groupId}' ORDER BY 'Timestamp' DESC limit 20"
                : $"SELECT * FROM Stream WHERE GroupId = '{groupId}'";

            request.AddQueryParameter("q", query);
           
            var response = await _client.ExecuteGetAsync(request);

            if (response.IsSuccessful)
            {
                _logger.LogDebug("Data fetched successfully.");

                var logResponse = JsonConvert.DeserializeObject<LogResponse>(response.Content!);

                var result = logResponse.Rows
                    .Select(row =>
                    {
                        var rowData = JsonConvert.DeserializeObject<LogHubVm>(row[2]!.ToString()!);

                        var utcTimestamp = row[0].ToDateTime();


                       // var utcTimestamp = row[0]?.ToString();
                        var arrived = row[1]?.ToString();

                        rowData.Timestamp = ConvertToFormat(utcTimestamp!);
                        rowData.Arrived = arrived;

                        return rowData;

                    })
                    .ToList();

                if (isHub)
                    await _hubContext.Clients.All.SendAsync("logevent", result);

                return result;
            }

            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                await LoginAsync();
                await GetSeqLogsByGroupIdAsync(groupId, isHub, isFilter);
            }

            _logger.LogError($"seq service FilteredValues : {response.ErrorMessage}");

        }
        catch (SeqApiException ex)
        {
            _logger.LogError($"GetFilteredValuesAsync SeqApiException : {ex.GetMessage()}");
        }
        catch (Exception ex)
        {
            _logger.LogError($"GetFilteredValuesAsync Exception : {ex.GetMessage()}");
        }
        return new List<LogHubVm>();
    }

    public async Task DeleteSeqLog(Dictionary<string, string> group)
    {
        var logFilePath = _config.GetSection("SeqConfig:path").Value;

        var connection = await CreateSeqConnection();

        if(connection is not null)
        {
            foreach (var id in group)
            {
                var events = await connection.Events.ListAsync(filter: "GroupId = '" + id.Value + "'", count: 500000);

                if (events.Count > 0)
                {
                    CreateAndWriteLogFileAsync(events, logFilePath, id.Key);

                    await DeleteAllEvents(connection, events);
                }
            }
        }       
    }


    #region OldCode
    //public async Task<List<LogHubVm>> GetLogDataByGroupId(string groupId, bool isHub = false)
    //{
    //    var connection = await CreateSeqConnection();

    //    if (connection is null)
    //    {
    //        return new List<LogHubVm>();
    //    }

    //    if (MyDict.TryGetValue(groupId, out var logHubVms))
    //    {
    //        var lastValue = logHubVms.LastOrDefault();
    //        var filterQuery = $"Select * From Stream Where GroupId = '{groupId}' and @Arrived > {lastValue!.Arrived}";
    //        await ProcessRows(connection, filterQuery, logHubVms);
    //        MyDict[groupId] = logHubVms;

    //        if (isHub)
    //            await _hubContext.Clients.All.SendAsync("logevent", logHubVms);

    //    }
    //    else
    //    {
    //        var query = $"Select * From Stream Where GroupId = '{groupId}' limit 5000";
    //        var newLogHubVms = new List<LogHubVm>();
    //        await ProcessRows(connection, query, newLogHubVms);
    //        if (newLogHubVms.Count > 0)
    //        {
    //            MyDict[groupId] = MyDict.ContainsKey(groupId) ? newLogHubVms : new List<LogHubVm>(newLogHubVms);

    //            if (isHub)
    //                await _hubContext.Clients.All.SendAsync("logevent", newLogHubVms);
    //        }
    //    }

    //    // _memoryCache.Set("GroupId", Encoding.UTF8.GetBytes(groupId));

    //    return logHubVms;
    //}

    //private async Task ProcessRows(SeqConnection connection, string query, List<LogHubVm> logHubVms)
    //{
    //    try
    //    {
    //        var result = await connection.Data.QueryAsync(query);

    //        var rows = result.Rows.ToList();

    //        rows.ForEach(x =>
    //        {
    //            var rowValue = JToken.FromObject(x);
    //            var logLevel = rowValue[2]?.SelectToken("@l")?.ToString() ?? "Information";
    //            var message = rowValue[2]?.SelectToken("@mt")?.ToString();
    //            var groupId = rowValue[2]?.SelectToken("GroupId")?.ToString();
    //            var arrived = rowValue[1]?.ToString();

    //            var utcTimestamp = rowValue[0]?.ToString();

    //            var utcDateTime = ConvertToFormat(utcTimestamp!);

    //            //var dd = utcDateTime

    //            //var utcTimestamp1 = rowValue[0]?.ToString();
    //            //var utcDateTime1 = DateTime.ParseExact(utcTimestamp!, "dd-MM-yyyy HH:mm:ss", CultureInfo.InvariantCulture);
    //            //var localDateTime = utcDateTime1.ToLocalTime();
    //            //var formattedTime = localDateTime.ToString("dd-MM-yyyy hh:mm:ss tt", CultureInfo.InvariantCulture);

    //            var logHubVm = new LogHubVm
    //            {
    //                WorkflowOperationGroupId = groupId,
    //                Message = message,
    //                Timestamp = utcDateTime,
    //                Arrived = arrived,
    //                Level = logLevel
    //            };

    //            logHubVms.Add(logHubVm);
    //        });
    //    }
    //    catch (SeqApiException ex)
    //    {
    //        _logger.LogError($"SeqApiException : {ex.GetMessage()}");
    //    }
    //}
    private async Task<SeqConnection> CreateSeqConnection()
    {
        try
        {

            var serverUrl = _config.GetSection("SeqConfig:ServerUrl").Value;
            //var apiKey = _config.GetSection("SeqConfig:ApiKey").Value;
            var username = _config.GetSection("SeqConfig:Username").Value;
            var password = _config.GetSection("SeqConfig:Password").Value;

            var connection = new SeqConnection(serverUrl);
            connection.Client.HttpClient.Timeout = TimeSpan.FromMinutes(30);

            await connection.Users.LoginAsync(SecurityHelper.Decrypt(username), SecurityHelper.Decrypt(password));

            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Seq Connection Failed : {ex.GetMessage()}");

            return null;
        }
    }
    #endregion

    private async Task DeleteAllEvents(SeqConnection connection, List<EventEntity> eventEntities)
    {
        foreach (var item in eventEntities)
        {
            await connection.Events.DeleteAsync(filter: "@Id = '" + item.Id + "'");
        }
    }

    private void CreateAndWriteLogFileAsync(List<EventEntity> events, string logFilePath, string workflowName)
    { 
        var now = DateTime.Now;

        var fileName = $"{now:yyyy-MM-dd HH-mm-ss}.log";

        var filePath = Path.Combine(logFilePath, workflowName, fileName);

        Directory.CreateDirectory(Path.GetDirectoryName(filePath) ?? string.Empty);

        using StreamWriter writer = new(filePath, append: true);
        foreach (var evt in events)
        {
            DateTime dateTime = DateTime.Parse(evt.Timestamp);

            writer.WriteLine($"{dateTime:yyyy-MM-dd HH:mm:ss.fff zzz} [{evt.Level}] {evt.MessageTemplateTokens.LastOrDefault()?.Text}");
        }
    }


    public string ConvertToFormat(DateTime input)
    {
        try
        {
            _logger.LogInformation($"Starting ConvertToFormat with input: {input}");

            var utcDateTime = input.Kind == DateTimeKind.Utc
                ? input
                : DateTime.SpecifyKind(input, DateTimeKind.Utc);

            _logger.LogDebug($"Interpreted UTC DateTime: {utcDateTime}");

            var systemTimeZone = TimeZoneInfo.Local;

            _logger.LogDebug($"System Time Zone: {systemTimeZone.Id}");

            var localTime = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, systemTimeZone);

            var formatted = localTime.ToString("dd-MM-yyyy hh:mm:ss tt", CultureInfo.InvariantCulture);

            _logger.LogInformation($"Formatted local time: {formatted}");

            return formatted;
        }
        catch (Exception ex)
        {
            _logger.Exception($"Error in ConvertToFormat for input: {input}", ex);

            return input.ToString("dd-MM-yyyy hh:mm:ss tt", CultureInfo.InvariantCulture); 
        }
    }
}

public class LogResponse
{
    public List<List<object>> Rows { get; set; }
}