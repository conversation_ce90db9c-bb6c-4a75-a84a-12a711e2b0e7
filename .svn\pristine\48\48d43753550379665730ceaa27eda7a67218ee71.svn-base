using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Delete;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetList;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetNameUnique;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class FiaImpactCategoryControllerTests : IDisposable
{
    private readonly FiaImpactCategoryController _controller;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly FiaImpactCategoryFixture _fiaImpactCategoryFixture;

    public FiaImpactCategoryControllerTests()
    {
        _fiaImpactCategoryFixture = new FiaImpactCategoryFixture();
        var testBuilder = new ControllerTestBuilder<FiaImpactCategoryController>();
        _controller = testBuilder.CreateController(
            _ => new FiaImpactCategoryController(),
            out _mediatorMock);
    }

    public void Dispose()
    {
        _fiaImpactCategoryFixture?.Dispose();
    }

    #region Core CRUD Operations

    [Fact]
    public async Task GetFiaImpactCategories_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _fiaImpactCategoryFixture.FiaImpactCategoryListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaImpactCategoryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetFiaImpactCategorys();

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<FiaImpactCategoryListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedList = Assert.IsType<List<FiaImpactCategoryListVm>>(okResult.Value);
        Assert.Equal(5, returnedList.Count);
        Assert.All(returnedList, category => Assert.Contains("Enterprise Operations Impact Category", category.Name));
    }

    [Fact]
    public async Task GetFiaImpactCategoryById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var fiaImpactCategoryId = Guid.NewGuid().ToString();
        var expectedDetail = _fiaImpactCategoryFixture.FiaImpactCategoryDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaImpactCategoryDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetFiaImpactCategoryById(fiaImpactCategoryId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<FiaImpactCategoryDetailVm>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedDetail = Assert.IsType<FiaImpactCategoryDetailVm>(okResult.Value);
        Assert.NotNull(returnedDetail.Id);
        Assert.Equal("Enterprise Operations Impact Category", returnedDetail.Name);
        Assert.Contains("enterprise operations", returnedDetail.Description.ToLower());
    }

    [Fact]
    public async Task GetPaginatedFiaImpactCategories_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _fiaImpactCategoryFixture.GetFiaImpactCategoryPaginatedListQuery;
        var expectedResult = _fiaImpactCategoryFixture.FiaImpactCategoryPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedFiaImpactCategorys(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<FiaImpactCategoryListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResult = Assert.IsType<PaginatedResult<FiaImpactCategoryListVm>>(okResult.Value);
        Assert.Equal(5, returnedResult.Data.Count);
        Assert.Equal(1, returnedResult.CurrentPage);
        Assert.Equal(10, returnedResult.PageSize);
    }

    [Fact]
    public async Task CreateFiaImpactCategory_WithValidCommand_ReturnsCreatedAtActionResult()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.CreateFiaImpactCategoryCommand;
        var expectedResponse = _fiaImpactCategoryFixture.CreateFiaImpactCategoryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateFiaImpactCategory(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<CreateFiaImpactCategoryResponse>>(result);
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<CreateFiaImpactCategoryResponse>(createdAtActionResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateFiaImpactCategory_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.UpdateFiaImpactCategoryCommand;
        var expectedResponse = _fiaImpactCategoryFixture.UpdateFiaImpactCategoryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateFiaImpactCategory(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<UpdateFiaImpactCategoryResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<UpdateFiaImpactCategoryResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteFiaImpactCategory_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var fiaImpactCategoryId = Guid.NewGuid().ToString();
        var expectedResponse = _fiaImpactCategoryFixture.DeleteFiaImpactCategoryResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteFiaImpactCategoryCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteFiaImpactCategory(fiaImpactCategoryId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<DeleteFiaImpactCategoryResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<DeleteFiaImpactCategoryResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task IsFiaImpactCategoryNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var fiaImpactCategoryName = "Enterprise Operations Impact Category";
        var fiaImpactCategoryId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetFiaImpactCategoryNameUniqueQuery>(q => 
                q.Name == fiaImpactCategoryName && q.Id == fiaImpactCategoryId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsFiaImpactCategoryNameExist(fiaImpactCategoryName, fiaImpactCategoryId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Arrange & Act & Assert
        Assert.NotNull(_controller);
        
        var method = typeof(FiaImpactCategoryController).GetMethod("ClearDataCache");
        Assert.NotNull(method);
        Assert.True(method.IsPublic);
        
        var nonActionAttribute = method.GetCustomAttributes(typeof(Microsoft.AspNetCore.Mvc.NonActionAttribute), false);
        Assert.NotEmpty(nonActionAttribute);
    }

    [Fact]
    public async Task CreateFiaImpactCategory_CallsClearDataCache()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.CreateFiaImpactCategoryCommand;
        var expectedResponse = _fiaImpactCategoryFixture.CreateFiaImpactCategoryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateFiaImpactCategory(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateFiaImpactCategory_CallsClearDataCache()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.UpdateFiaImpactCategoryCommand;
        var expectedResponse = _fiaImpactCategoryFixture.UpdateFiaImpactCategoryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateFiaImpactCategory(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task DeleteFiaImpactCategory_CallsClearDataCache()
    {
        // Arrange
        var fiaImpactCategoryId = Guid.NewGuid().ToString();
        var expectedResponse = _fiaImpactCategoryFixture.DeleteFiaImpactCategoryResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteFiaImpactCategoryCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteFiaImpactCategory(fiaImpactCategoryId);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.IsAny<DeleteFiaImpactCategoryCommand>(), default), Times.Once);
    }

    #endregion

    #region Additional Test Cases

    [Fact]
    public async Task GetFiaImpactCategories_WithEmptyResult_ReturnsEmptyList()
    {
        // Arrange
        var emptyList = new List<FiaImpactCategoryListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaImpactCategoryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetFiaImpactCategorys();

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<FiaImpactCategoryListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedList = Assert.IsType<List<FiaImpactCategoryListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetFiaImpactCategoryById_WithNonExistentId_ReturnsEmptyResult()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var emptyDetail = new FiaImpactCategoryDetailVm();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaImpactCategoryDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyDetail);

        // Act
        var result = await _controller.GetFiaImpactCategoryById(nonExistentId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<FiaImpactCategoryDetailVm>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedDetail = Assert.IsType<FiaImpactCategoryDetailVm>(okResult.Value);
        Assert.NotNull(returnedDetail);
    }

    [Fact]
    public async Task GetPaginatedFiaImpactCategories_WithSearchFilter_ReturnsFilteredResults()
    {
        // Arrange
        var query = _fiaImpactCategoryFixture.GetFiaImpactCategoryPaginatedListQuery;
        query.SearchString = "Operations";
        var filteredData = _fiaImpactCategoryFixture.FiaImpactCategoryListVm
            .Where(x => x.Name.Contains("Operations")).ToList();
        var filteredResult = PaginatedResult<FiaImpactCategoryListVm>.Success(
            data: filteredData,
            count: filteredData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedFiaImpactCategorys(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<FiaImpactCategoryListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResult = Assert.IsType<PaginatedResult<FiaImpactCategoryListVm>>(okResult.Value);
        Assert.All(returnedResult.Data, category => Assert.Contains("Operations", category.Name));
    }

    [Fact]
    public async Task CreateFiaImpactCategory_WithInvalidCommand_ReturnsErrorResponse()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.CreateFiaImpactCategoryCommand;
        command.Name = null; // Invalid data
        var errorResponse = new CreateFiaImpactCategoryResponse
        {
            Success = false,
            Message = "Impact Category Name is required"
        };

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(errorResponse);

        // Act
        var result = await _controller.CreateFiaImpactCategory(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<CreateFiaImpactCategoryResponse>>(result);
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<CreateFiaImpactCategoryResponse>(createdAtActionResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("required", returnedResponse.Message);
    }

    [Fact]
    public async Task UpdateFiaImpactCategory_WithNonExistentId_ReturnsErrorResponse()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.UpdateFiaImpactCategoryCommand;
        command.Id = Guid.NewGuid().ToString(); // Non-existent ID
        var errorResponse = new UpdateFiaImpactCategoryResponse
        {
            Success = false,
            Message = "FIA Impact Category not found"
        };

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(errorResponse);

        // Act
        var result = await _controller.UpdateFiaImpactCategory(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<UpdateFiaImpactCategoryResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<UpdateFiaImpactCategoryResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("not found", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteFiaImpactCategory_WithNonExistentId_ReturnsErrorResponse()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var errorResponse = new DeleteFiaImpactCategoryResponse
        {
            Success = false,
            Message = "FIA Impact Category not found"
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteFiaImpactCategoryCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(errorResponse);

        // Act
        var result = await _controller.DeleteFiaImpactCategory(nonExistentId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<DeleteFiaImpactCategoryResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<DeleteFiaImpactCategoryResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("not found", returnedResponse.Message);
    }

    [Fact]
    public async Task IsFiaImpactCategoryNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var nonExistingName = "Non-Existing Impact Category";
        var fiaImpactCategoryId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetFiaImpactCategoryNameUniqueQuery>(q =>
                q.Name == nonExistingName && q.Id == fiaImpactCategoryId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsFiaImpactCategoryNameExist(nonExistingName, fiaImpactCategoryId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsFiaImpactCategoryNameExist_WithNullId_ReturnsCorrectResult()
    {
        // Arrange
        var fiaImpactCategoryName = "New Impact Category";
        string? nullId = null;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetFiaImpactCategoryNameUniqueQuery>(q =>
                q.Name == fiaImpactCategoryName && q.Id == nullId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsFiaImpactCategoryNameExist(fiaImpactCategoryName, nullId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    #endregion

    #region Category Management and Business Rules Tests

    [Fact]
    public async Task CreateFiaImpactCategory_WithFinancialCategory_ReturnsSuccess()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.CreateFiaImpactCategoryCommand;
        command.Name = "Financial Impact";
        command.Description = "Categories related to financial losses and revenue impact";

        var expectedResponse = _fiaImpactCategoryFixture.CreateFiaImpactCategoryResponse;
        expectedResponse.Message = "FIA Impact Category Financial Impact created successfully";

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateFiaImpactCategory(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<CreateFiaImpactCategoryResponse>>(result);
        var createdResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<CreateFiaImpactCategoryResponse>(createdResult.Value);
        Assert.Contains("Financial Impact", returnedResponse.Message);
    }

    [Fact]
    public async Task GetFiaImpactCategories_WithOperationalFilter_ReturnsOperationalCategories()
    {
        // Arrange
        var operationalCategories = _fiaImpactCategoryFixture.FiaImpactCategoryListVm
            .Where(x => x.Name.Contains("Operational")).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaImpactCategoryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(operationalCategories);

        // Act
        var result = await _controller.GetFiaImpactCategorys();

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<FiaImpactCategoryListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedCategories = Assert.IsType<List<FiaImpactCategoryListVm>>(okResult.Value);
        Assert.All(returnedCategories, category => Assert.Contains("Operational", category.Name));
    }

    [Fact]
    public async Task UpdateFiaImpactCategory_WithDescriptionChange_UpdatesSuccessfully()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.UpdateFiaImpactCategoryCommand;
        command.Description = "Updated description for comprehensive impact analysis including regulatory compliance aspects";

        var expectedResponse = _fiaImpactCategoryFixture.UpdateFiaImpactCategoryResponse;
        expectedResponse.Message = "FIA Impact Category updated with enhanced description successfully";

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateFiaImpactCategory(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<UpdateFiaImpactCategoryResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<UpdateFiaImpactCategoryResponse>(okResult.Value);
        Assert.Contains("enhanced", returnedResponse.Message.ToLower());
    }

    [Fact]
    public async Task GetPaginatedFiaImpactCategories_WithComplianceFilter_ReturnsComplianceCategories()
    {
        // Arrange
        var query = _fiaImpactCategoryFixture.GetFiaImpactCategoryPaginatedListQuery;
        query.SearchString = "Compliance";
        var complianceData = _fiaImpactCategoryFixture.FiaImpactCategoryListVm
            .Where(x => x.Name.Contains("Compliance") || x.Description.Contains("Compliance")).ToList();
        var complianceResult = PaginatedResult<FiaImpactCategoryListVm>.Success(
            complianceData,
            complianceData.Count,
            1,
            10);

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(complianceResult);

        // Act
        var result = await _controller.GetPaginatedFiaImpactCategorys(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<FiaImpactCategoryListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResult = Assert.IsType<PaginatedResult<FiaImpactCategoryListVm>>(okResult.Value);
        Assert.All(returnedResult.Data, category =>
            Assert.True(category.Name.Contains("Compliance") || category.Description.Contains("Compliance")));
    }

    [Fact]
    public async Task IsFiaImpactCategoryNameExist_WithExistingStrategicName_ReturnsTrue()
    {
        // Arrange
        var existingName = "Strategic Impact";
        var categoryId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaImpactCategoryNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsFiaImpactCategoryNameExist(existingName, categoryId);

        // Assert
        var actionResult = Assert.IsType<OkObjectResult>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task DeleteFiaImpactCategory_WithAssociatedTemplates_ThrowsException()
    {
        // Arrange
        var categoryId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteFiaImpactCategoryCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Cannot delete FIA Impact Category that is used in active FIA templates"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.DeleteFiaImpactCategory(categoryId));
    }

    [Fact]
    public async Task CreateFiaImpactCategory_WithDuplicateName_ThrowsException()
    {
        // Arrange
        var command = _fiaImpactCategoryFixture.CreateFiaImpactCategoryCommand;
        command.Name = "Financial Impact"; // Duplicate name

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("FIA Impact Category name already exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateFiaImpactCategory(command));
    }

    #endregion
}
