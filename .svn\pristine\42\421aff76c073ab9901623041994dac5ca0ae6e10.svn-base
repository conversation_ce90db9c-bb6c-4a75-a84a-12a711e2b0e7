﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.InfraObjectSchedulerLogPagination;
using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Report.ReportTemplate;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.ITAutomation.Controllers;

[Area("ITAutomation")]

public class WorkflowScheduleExecutionHistoryController : Controller
{
    private readonly IPublisher _publisher;
    public static ILogger<WorkflowScheduleExecutionHistoryController> Logger;
    private readonly IDataProvider _dataProvider;
    public string CompanyLogo { get; set; }


    public WorkflowScheduleExecutionHistoryController(IPublisher publisher, ILogger<WorkflowScheduleExecutionHistoryController> logger, IDataProvider dataProvider)
    {
        _publisher = publisher;
        Logger = logger;
        _dataProvider = dataProvider;
    }
    public IActionResult List()
    {
        return View();
    }
    [HttpGet]
    public async Task<JsonResult> GetPagination(GetInfraObjectSchedulerLogsPaginationQuery query)
    {
        Logger.LogDebug("Entering GetPagination method in WorkflowScheduleExecutionHistory");
        try
        {
            var paginatedList = await _dataProvider.DrReady.GetPaginatedInfraObjectSchedulerLogs(query);
            Logger.LogDebug("Successfully retrieved alert paginatedList in WorkflowScheduleExecutionHistory");

            return Json(new { Success = true, data = paginatedList });
        }
        catch (Exception ex)
        {
            Logger.Exception("An error occurred on WorkflowScheduleExecutionHistory page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> DownloadReport(string workflowId, string workflowName, string infraReferenceId)
    {
        Logger.LogInformation("Enter into Scheduler Job Workflow DownloadReport method");
        var reportsDirectory = "";
        try
        {
            var workflowDetails = await _dataProvider.Report.GetScheduleWorkflowActionResultReport(workflowId, infraReferenceId);
            if (workflowDetails is not null && workflowDetails.ScheduleWorkflowActionResultsReportVms.Count > 0)
            {
                Logger.LogInformation("Schedule Job Workflow not null");
                CompanyLogo = string.Empty;
                var companyDetails = await _dataProvider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
                if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo; }

                var reportData = JsonConvert.SerializeObject(workflowDetails);
                XtraReport report = new ScheduledJobWorkflowReport(reportData);
                var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
                var fileName = "ScheduledJobWorkflowReport_" + workflowName + "_" + filenameSuffix + ".pdf";
                reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
                report.ExportToPdf(reportsDirectory);
                var fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                Logger.LogDebug("download initaiated for Scheduler Job Workflow Report");

                await _publisher.Publish(new ReportViewedEvent { ActivityType = ActivityType.Generate.ToString(), ReportName = "ScheduledJobWorkflowReport" });
                return File(fileBytes, "application/pdf", fileName);
            }
            else
            {
                Logger.LogInformation("Scheduled Job Workflow null");
                return Json(new { success = false, message = "No data found" });
            }


        }
        catch (Exception)
        {
            Logger.LogError("Scheduled Job Workflow Download Report method throw exception");
            return Json(new { success = false, message = "Downloading report error" });
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }
}
