//using ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;

//namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Commands;

//public class AcknowledgeCyberAlertTests : IClassFixture<CyberAlertFixture>
//{
//    private readonly CyberAlertFixture _cyberAlertFixture;
//    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
//    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
//    private readonly Mock<ILoggedInUserService> _mockUserService;
//    private readonly CreateCyberAlertCommandHandler _handler;

//    public AcknowledgeCyberAlertTests(CyberAlertFixture cyberAlertFixture)
//    {
//        _cyberAlertFixture = cyberAlertFixture;
//        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);
//        _mockUserActivityRepository = CyberRepositoryMocks.CreateUserActivityRepository(_cyberAlertFixture.UserActivities);
//        _mockUserService = new Mock<ILoggedInUserService>();

//        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
//        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");

//        _handler = new AcknowledgeCyberAlertCommandHandler(
//            _mockCyberAlertRepository.Object,
//            _mockUserService.Object,
//            _mockLogger.Object,
//            _mockUserActivityRepository.Object);
//    }

//    [Fact]
//    public async Task Handle_AcknowledgeCyberAlert_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Open");
//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            AcknowledgementNotes = "Alert acknowledged by security team"
//        };

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.Success.ShouldBeTrue();
//        result.Data.ShouldNotBeNull();
//        result.Data.Status.ShouldBe("Acknowledged");
//        result.Data.AcknowledgedBy.ShouldBe("TestUser123");
//        result.Data.AcknowledgedAt.ShouldNotBeNull();
//        _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync(existingAlert.ReferenceId), Times.Once);
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Once);
//    }

//    [Fact]
//    public async Task Handle_UpdateEntityWithAcknowledgmentProperties_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Open");
//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            AcknowledgementNotes = "Acknowledged - investigating potential security breach"
//        };

//        CyberAlert updatedEntity = null;
//        _mockCyberAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<CyberAlert>()))
//            .Callback<CyberAlert>(entity => updatedEntity = entity)
//            .ReturnsAsync((CyberAlert entity) => entity);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        updatedEntity.ShouldNotBeNull();
//        updatedEntity.Status.ShouldBe("Acknowledged");
//        updatedEntity.AcknowledgedBy.ShouldBe("TestUser123");
//        updatedEntity.AcknowledgedAt.ShouldNotBeNull();
//        updatedEntity.AcknowledgedAt.Value.ShouldBeInRange(DateTime.UtcNow.AddMinutes(-1), DateTime.UtcNow.AddMinutes(1));
//        updatedEntity.IsActive.ShouldBeTrue();
//    }

//    [Fact]
//    public async Task Handle_ThrowNotFoundException_When_AlertNotFound()
//    {
//        // Arrange
//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = "non-existent-alert-id",
//            AcknowledgementNotes = "Attempting to acknowledge non-existent alert"
//        };

//        // Act & Assert
//        await Should.ThrowAsync<NotFoundException>(async () =>
//            await _handler.Handle(command, CancellationToken.None));

//        _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync("non-existent-alert-id"), Times.Once);
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Never);
//    }

//    [Fact]
//    public async Task Handle_ThrowNotFoundException_When_AlertIsInactive()
//    {
//        // Arrange
//        var inactiveAlert = new CyberAlert
//        {
//            ReferenceId = "inactive-alert-id",
//            Title = "Inactive Alert",
//            Status = "Open",
//            IsActive = false
//        };
//        _cyberAlertFixture.CyberAlerts.Add(inactiveAlert);

//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = "inactive-alert-id",
//            AcknowledgementNotes = "Attempting to acknowledge inactive alert"
//        };

//        // Act & Assert
//        await Should.ThrowAsync<NotFoundException>(async () =>
//            await _handler.Handle(command, CancellationToken.None));
//    }

//    /// <summary>
//    /// Test: Command handler throws BadRequestException for already acknowledged alert
//    /// Expected: BadRequestException when alert is already acknowledged
//    /// </summary>
//    [Fact]
//    public async Task Handle_ThrowBadRequestException_When_AlertAlreadyAcknowledged()
//    {
//        // Arrange
//        var acknowledgedAlert = new CyberAlert
//        {
//            ReferenceId = "acknowledged-alert-id",
//            Title = "Already Acknowledged Alert",
//            Status = "Acknowledged",
//            AcknowledgedBy = "PreviousUser",
//            AcknowledgedAt = DateTime.UtcNow.AddHours(-1),
//            IsActive = true
//        };
//        _cyberAlertFixture.CyberAlerts.Add(acknowledgedAlert);

//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = "acknowledged-alert-id",
//            AcknowledgementNotes = "Attempting to re-acknowledge alert"
//        };

//        // Act & Assert
//        await Should.ThrowAsync<BadRequestException>(async () =>
//            await _handler.Handle(command, CancellationToken.None));

//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Never);
//    }

//    /// <summary>
//    /// Test: Command handler supports cancellation
//    /// Expected: OperationCanceledException when cancellation is requested
//    /// </summary>
//    [Fact]
//    public async Task Handle_SupportCancellation_When_CancellationRequested()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Open");
//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            AcknowledgementNotes = "Test cancellation"
//        };

//        using var cts = new CancellationTokenSource();
//        cts.Cancel();

//        // Act & Assert
//        await Should.ThrowAsync<OperationCanceledException>(async () =>
//            await _handler.Handle(command, cts.Token));
//    }

//    /// <summary>
//    /// Test: Multiple acknowledgment commands are processed correctly
//    /// Expected: Each command acknowledges the correct alert
//    /// </summary>
//    [Fact]
//    public async Task Handle_ProcessMultipleAcknowledgmentCommands_When_ValidCommands()
//    {
//        // Arrange
//        var openAlerts = _cyberAlertFixture.CyberAlerts.Where(a => a.Status == "Open").Take(3).ToList();
//        var commands = openAlerts.Select((alert, index) => new AcknowledgeCyberAlertCommand
//        {
//            Id = alert.ReferenceId,
//            AcknowledgementNotes = $"Acknowledged alert {index + 1}"
//        }).ToArray();

//        // Act
//        foreach (var command in commands)
//        {
//            var result = await _handler.Handle(command, CancellationToken.None);
//            result.Success.ShouldBeTrue();
//        }

//        // Assert
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Exactly(3));
//    }

//    /// <summary>
//    /// Test: Command with different severity levels
//    /// Expected: Alerts of all severity levels can be acknowledged
//    /// </summary>
//    [Fact]
//    public async Task Handle_AcknowledgeAlertsWithDifferentSeverities_When_ValidCommands()
//    {
//        // Arrange
//        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };
//        var alerts = _cyberAlertFixture.CyberAlerts.Where(a => a.Status == "Open").Take(severityLevels.Length).ToList();

//        for (int i = 0; i < alerts.Count && i < severityLevels.Length; i++)
//        {
//            alerts[i].Severity = severityLevels[i];
//            var command = new AcknowledgeCyberAlertCommand
//            {
//                Id = alerts[i].ReferenceId,
//                AcknowledgementNotes = $"Acknowledged {severityLevels[i]} severity alert"
//            };

//            // Act
//            var result = await _handler.Handle(command, CancellationToken.None);

//            // Assert
//            result.Success.ShouldBeTrue();
//            result.Data.Status.ShouldBe("Acknowledged");
//        }

//        // Verify all acknowledgments were processed
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Exactly(alerts.Count));
//    }

//    /// <summary>
//    /// Test: Command with long acknowledgment notes
//    /// Expected: Long acknowledgment notes are handled correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_HandleLongAcknowledgmentNotes_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Open");
//        var longNotes = new string('A', 1000) + " - Detailed acknowledgment notes with extensive information about the security incident and initial assessment.";

//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            AcknowledgementNotes = longNotes
//        };

//        CyberAlert updatedEntity = null;
//        _mockCyberAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<CyberAlert>()))
//            .Callback<CyberAlert>(entity => updatedEntity = entity)
//            .ReturnsAsync((CyberAlert entity) => entity);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.Success.ShouldBeTrue();
//        updatedEntity.ShouldNotBeNull();
//        updatedEntity.Status.ShouldBe("Acknowledged");
//    }

//    /// <summary>
//    /// Test: Command with special characters in acknowledgment notes
//    /// Expected: Special characters are handled correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_HandleSpecialCharactersInNotes_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Open");
//        var specialCharNotes = "Acknowledged: Alert contains special chars !@#$%^&*()_+-=[]{}|;':\",./<>? and HTML <script>alert('test')</script>";

//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            AcknowledgementNotes = specialCharNotes
//        };

//        CyberAlert updatedEntity = null;
//        _mockCyberAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<CyberAlert>()))
//            .Callback<CyberAlert>(entity => updatedEntity = entity)
//            .ReturnsAsync((CyberAlert entity) => entity);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.Success.ShouldBeTrue();
//        updatedEntity.ShouldNotBeNull();
//        updatedEntity.Status.ShouldBe("Acknowledged");
//    }

//    /// <summary>
//    /// Test: Command without acknowledgment notes
//    /// Expected: Alert can be acknowledged without notes
//    /// </summary>
//    [Fact]
//    public async Task Handle_AcknowledgeWithoutNotes_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Open");
//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId
//            // No acknowledgment notes provided
//        };

//        CyberAlert updatedEntity = null;
//        _mockCyberAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<CyberAlert>()))
//            .Callback<CyberAlert>(entity => updatedEntity = entity)
//            .ReturnsAsync((CyberAlert entity) => entity);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.Success.ShouldBeTrue();
//        updatedEntity.ShouldNotBeNull();
//        updatedEntity.Status.ShouldBe("Acknowledged");
//        updatedEntity.AcknowledgedBy.ShouldBe("TestUser123");
//        updatedEntity.AcknowledgedAt.ShouldNotBeNull();
//    }

//    /// <summary>
//    /// Test: Command with user activity logging
//    /// Expected: User activity is logged for audit trail
//    /// </summary>
//    [Fact]
//    public async Task Handle_LogUserActivity_When_ValidAcknowledgment()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Open");
//        var command = new AcknowledgeCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            AcknowledgementNotes = "Alert acknowledged for audit trail test"
//        };

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.Success.ShouldBeTrue();
//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//    }

//    /// <summary>
//    /// Test: Command performance with rapid succession
//    /// Expected: Multiple rapid acknowledgment operations are handled correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_ProcessRapidAcknowledgmentOperations_When_MultipleCommands()
//    {
//        // Arrange
//        var openAlerts = _cyberAlertFixture.CyberAlerts.Where(a => a.Status == "Open").Take(10).ToList();
//        var commands = openAlerts.Select((alert, index) => new AcknowledgeCyberAlertCommand
//        {
//            Id = alert.ReferenceId,
//            AcknowledgementNotes = $"Rapid acknowledgment {index + 1}"
//        }).ToList();

//        // Act
//        var tasks = commands.Select(cmd => _handler.Handle(cmd, CancellationToken.None));
//        var results = await Task.WhenAll(tasks);

//        // Assert
//        results.ShouldAllBe(result => result.Success);
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Exactly(10));
//    }
//}
