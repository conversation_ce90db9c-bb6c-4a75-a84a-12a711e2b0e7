using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

/// <summary>
/// Fixture class for CyberComponentMapping module testing
/// Provides test data setup and AutoFixture configuration for comprehensive unit testing
/// Purpose: CyberComponentMapping manages relationships and dependencies between cyber security components
/// </summary>
public class CyberComponentMappingFixture : IDisposable
{
    public List<CyberComponentMapping> CyberComponentMappings { get; set; }
    public List<CyberComponent> CyberComponents { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateCyberComponentMappingCommand CreateCyberComponentMappingCommand { get; set; }
    public UpdateCyberComponentMappingCommand UpdateCyberComponentMappingCommand { get; set; }
    public DeleteCyberComponentMappingCommand DeleteCyberComponentMappingCommand { get; set; }
    //public MapComponentsCommand MapComponentsCommand { get; set; }
    //public UnmapComponentsCommand UnmapComponentsCommand { get; set; }
    public IMapper Mapper { get; set; }

    public CyberComponentMappingFixture()
    {
        // Initialize related components first
        CyberComponents = new List<CyberComponent>
        {
            new CyberComponent
            {
                ReferenceId = Guid.NewGuid().ToString(),
              //  ComponentId = Guid.NewGuid().ToString(),
                Name = "Source Component 01",
                Type = "Firewall",
                Status = "Active",
                IsActive = true
            },
            new CyberComponent
            {
                ReferenceId = Guid.NewGuid().ToString(),
               // ComponentId = Guid.NewGuid().ToString(),
                Name = "Target Component 01",
                Type = "IDS",
                Status = "Active",
                IsActive = true
            }
        };

        // Initialize manual test data with known values for reliable testing
        CyberComponentMappings = new List<CyberComponentMapping>
        {
            new CyberComponentMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
               // MappingId = Guid.NewGuid().ToString(),
                Name = "Test Cyber Component Mapping 01",
                //Description = "Test cyber security component mapping for unit testing",
                //SourceComponentId = CyberComponents[0].ComponentId,
                //SourceComponentName = CyberComponents[0].Name,
                //TargetComponentId = CyberComponents[1].ComponentId,
                //TargetComponentName = CyberComponents[1].Name,
                //MappingType = "Dependency",
                //RelationshipType = "Feeds",
                //Direction = "Unidirectional",
                //Priority = "High",
                //Status = "Active",
                //Configuration = "{\"protocol\":\"HTTPS\",\"port\":443,\"encryption\":true}",
                //Metadata = "{\"bandwidth\":\"1Gbps\",\"latency\":\"5ms\",\"reliability\":\"99.9%\"}",
                //Rules = "{\"allowedTraffic\":[\"HTTP\",\"HTTPS\"],\"blockedPorts\":[22,23,135]}",
                //Conditions = "{\"timeWindow\":\"24/7\",\"loadThreshold\":80,\"failoverEnabled\":true}",
                //LastValidated = DateTime.UtcNow.AddHours(-2),
                //NextValidation = DateTime.UtcNow.AddHours(22),
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalMappings = AutoCyberComponentMappingFixture.CreateMany<CyberComponentMapping>(2).ToList();
            CyberComponentMappings.AddRange(additionalMappings);
            
            var additionalComponents = AutoCyberComponentMappingFixture.CreateMany<CyberComponent>(3).ToList();
            CyberComponents.AddRange(additionalComponents);
            
            UserActivities = AutoCyberComponentMappingFixture.CreateMany<UserActivity>(3).ToList();
            CreateCyberComponentMappingCommand = AutoCyberComponentMappingFixture.Create<CreateCyberComponentMappingCommand>();
            UpdateCyberComponentMappingCommand = AutoCyberComponentMappingFixture.Create<UpdateCyberComponentMappingCommand>();
            DeleteCyberComponentMappingCommand = AutoCyberComponentMappingFixture.Create<DeleteCyberComponentMappingCommand>();
           // MapComponentsCommand = AutoCyberComponentMappingFixture.Create<MapComponentsCommand>();
           // UnmapComponentsCommand = AutoCyberComponentMappingFixture.Create<UnmapComponentsCommand>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateCyberComponentMappingCommand = new CreateCyberComponentMappingCommand();
            UpdateCyberComponentMappingCommand = new UpdateCyberComponentMappingCommand();
            DeleteCyberComponentMappingCommand = new DeleteCyberComponentMappingCommand();
           // MapComponentsCommand = new MapComponentsCommand();
            //UnmapComponentsCommand = new UnmapComponentsCommand();
        }

        // Configure AutoMapper for CyberComponentMapping mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CyberComponentMappingProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    /// <summary>
    /// AutoFixture configuration for CyberComponentMapping entities and commands
    /// Handles circular references and provides realistic test data
    /// </summary>
    public Fixture AutoCyberComponentMappingFixture
    {
        get
        {
            var fixture = new Fixture();
            
            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations for commands
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateCyberComponentMappingCommand>(p => p.Name, 100));
            fixture.Customize<CreateCyberComponentMappingCommand>(c => c
                //.With(b => b.MappingId, () => Guid.NewGuid().ToString())
                //.With(b => b.Name, () => $"Test Cyber Component Mapping {fixture.Create<int>():000}")
                //.With(b => b.Description, () => $"Test cyber security component mapping description {fixture.Create<int>()}")
                //.With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                //.With(b => b.SourceComponentName, () => $"Source Component {fixture.Create<int>():00}")
                //.With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                //.With(b => b.TargetComponentName, () => $"Target Component {fixture.Create<int>():00}")
                //.With(b => b.MappingType, () => new[] { "Dependency", "Communication", "Data Flow", "Control Flow" }[fixture.Create<int>() % 4])
                //.With(b => b.RelationshipType, () => new[] { "Feeds", "Controls", "Monitors", "Protects", "Depends On" }[fixture.Create<int>() % 5])
                //.With(b => b.Direction, () => new[] { "Unidirectional", "Bidirectional" }[fixture.Create<int>() % 2])
                //.With(b => b.Priority, () => new[] { "Low", "Medium", "High", "Critical" }[fixture.Create<int>() % 4])
                //.With(b => b.Status, "Active")
                //.With(b => b.Configuration, () => $"{{\"protocol\":\"HTTPS\",\"port\":{fixture.Create<int>() % 65535 + 1024},\"encryption\":{fixture.Create<bool>().ToString().ToLower()}}}")
                //.With(b => b.Metadata, () => $"{{\"bandwidth\":\"{fixture.Create<int>() % 10}Gbps\",\"latency\":\"{fixture.Create<int>() % 100}ms\",\"reliability\":\"{fixture.Create<int>() % 100}.{fixture.Create<int>() % 10}%\"}}")
                //.With(b => b.Rules, () => $"{{\"allowedTraffic\":[\"HTTP\",\"HTTPS\"],\"blockedPorts\":[{fixture.Create<int>() % 1000 + 1000},{fixture.Create<int>() % 1000 + 2000}]}}")
                //.With(b => b.Conditions, () => $"{{\"timeWindow\":\"24/7\",\"loadThreshold\":{fixture.Create<int>() % 100 + 50},\"failoverEnabled\":{fixture.Create<bool>().ToString().ToLower()}}}")
                );

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateCyberComponentMappingCommand>(p => p.Name, 100));
            fixture.Customize<UpdateCyberComponentMappingCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.Name, () => $"Updated Cyber Component Mapping {fixture.Create<int>():000}")
                //.With(b => b.Description, () => $"Updated cyber security component mapping description {fixture.Create<int>()}")
                //.With(b => b.Priority, () => new[] { "Medium", "High", "Critical" }[fixture.Create<int>() % 3])
               // .With(b => b.Status, () => new[] { "Active", "Inactive", "Maintenance" }[fixture.Create<int>() % 3])
               );

            fixture.Customize<DeleteCyberComponentMappingCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            //fixture.Customize<MapComponentsCommand>(c => c
            //    .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
            //    .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
            //    .With(b => b.MappingType, () => new[] { "Dependency", "Communication", "Data Flow" }[fixture.Create<int>() % 3])
            //    .With(b => b.RelationshipType, () => new[] { "Feeds", "Controls", "Monitors" }[fixture.Create<int>() % 3]));

            //fixture.Customize<UnmapComponentsCommand>(c => c
            //    .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
            //    .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString()));

            // CyberComponentMapping entity customizations
            fixture.Customize<CyberComponentMapping>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
               // .With(b => b.MappingId, () => Guid.NewGuid().ToString())
                .With(b => b.Name, () => $"Test Cyber Component Mapping {fixture.Create<int>():000}")
                //.With(b => b.Description, () => $"Test cyber security component mapping description {fixture.Create<int>()}")
                //.With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                //.With(b => b.SourceComponentName, () => $"Source Component {fixture.Create<int>():00}")
                //.With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                //.With(b => b.TargetComponentName, () => $"Target Component {fixture.Create<int>():00}")
                //.With(b => b.MappingType, () => new[] { "Dependency", "Communication", "Data Flow", "Control Flow" }[fixture.Create<int>() % 4])
                //.With(b => b.RelationshipType, () => new[] { "Feeds", "Controls", "Monitors", "Protects", "Depends On" }[fixture.Create<int>() % 5])
                //.With(b => b.Direction, () => new[] { "Unidirectional", "Bidirectional" }[fixture.Create<int>() % 2])
                //.With(b => b.Priority, () => new[] { "Low", "Medium", "High", "Critical" }[fixture.Create<int>() % 4])
                //.With(b => b.Status, () => new[] { "Active", "Inactive", "Maintenance", "Error" }[fixture.Create<int>() % 4])
                //.With(b => b.Configuration, () => $"{{\"protocol\":\"HTTPS\",\"port\":{fixture.Create<int>() % 65535 + 1024},\"encryption\":{fixture.Create<bool>().ToString().ToLower()}}}")
                //.With(b => b.Metadata, () => $"{{\"bandwidth\":\"{fixture.Create<int>() % 10}Gbps\",\"latency\":\"{fixture.Create<int>() % 100}ms\",\"reliability\":\"{fixture.Create<int>() % 100}.{fixture.Create<int>() % 10}%\"}}")
                //.With(b => b.Rules, () => $"{{\"allowedTraffic\":[\"HTTP\",\"HTTPS\"],\"blockedPorts\":[{fixture.Create<int>() % 1000 + 1000},{fixture.Create<int>() % 1000 + 2000}]}}")
                //.With(b => b.Conditions, () => $"{{\"timeWindow\":\"24/7\",\"loadThreshold\":{fixture.Create<int>() % 100 + 50},\"failoverEnabled\":{fixture.Create<bool>().ToString().ToLower()}}}")
                //.With(b => b.LastValidated, () => DateTime.UtcNow.AddHours(-fixture.Create<int>() % 24))
                //.With(b => b.NextValidation, () => DateTime.UtcNow.AddHours(fixture.Create<int>() % 24))
                );

            // CyberComponent entity customizations for mapping relationships
            fixture.Customize<CyberComponent>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
               // .With(b => b.ComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.Name, () => $"Test Component {fixture.Create<int>():000}")
                .With(b => b.Type, () => new[] { "Firewall", "IDS", "IPS", "Antivirus", "SIEM", "Router", "Switch" }[fixture.Create<int>() % 7])
                .With(b => b.Status, () => new[] { "Active", "Inactive", "Maintenance" }[fixture.Create<int>() % 3]));

            // UserActivity customization for CyberComponentMapping
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "CyberComponentMapping")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test cyber component mapping activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
