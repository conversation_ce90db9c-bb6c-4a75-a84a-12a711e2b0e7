using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapLog.Commands;

public class CreateCyberAirGapLogTests : IClassFixture<CyberAirGapLogFixture>
{
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;
    private readonly Mock<ICyberAirGapLogRepository> _mockCyberAirGapLogRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateCyberAirGapLogCommandHandler _handler;

    public CreateCyberAirGapLogTests(CyberAirGapLogFixture cyberAirGapLogFixture)
    {
        _cyberAirGapLogFixture = cyberAirGapLogFixture;
        _mockCyberAirGapLogRepository = CyberAirGapLogRepositoryMocks.CreateCyberAirGapLogRepository(_cyberAirGapLogFixture.CyberAirGapLogs);
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        _handler = new CreateCyberAirGapLogCommandHandler(
            _mockMapper.Object,
            _mockCyberAirGapLogRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapLog_When_ValidCommand()
    {
        // Arrange
        var command = _cyberAirGapLogFixture.CreateCyberAirGapLogCommand;
        var expectedEntity = new Domain.Entities.CyberAirGapLog
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            SourceSiteName = command.SourceSiteName,
            TargetSiteName = command.TargetSiteName,
            Port = command.Port,
            Description = command.Description
        };

        Domain.Entities.CyberAirGapLog createdEntity = null;
        CyberAirGapLogCreatedEvent publishedEvent = null;

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(expectedEntity);

        _mockCyberAirGapLogRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapLog>()))
            .Callback<Domain.Entities.CyberAirGapLog>(entity => createdEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAirGapLog entity) => entity);

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapLogCreatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapLogCreatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CreateCyberAirGapLogResponse>();
        result.Success.ShouldBeTrue();

        createdEntity.ShouldNotBeNull();
        createdEntity.AirGapId.ShouldBe(command.AirGapId);
        createdEntity.AirGapName.ShouldBe(command.AirGapName);
        createdEntity.SourceSiteName.ShouldBe(command.SourceSiteName);
        createdEntity.TargetSiteName.ShouldBe(command.TargetSiteName);
        createdEntity.Port.ShouldBe(command.Port);

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(expectedEntity.AirGapName);

        _mockMapper.Verify(x => x.Map<Domain.Entities.CyberAirGapLog>(command), Times.Once);
        _mockCyberAirGapLogRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapLog>()), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapLog_When_ComplexJsonProperties()
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = "complex-airgap-001",
            AirGapName = "Complex Enterprise Air Gap",
            SourceSiteName = "Production Data Center",
            TargetSiteName = "Disaster Recovery Center",
            Port = 8443,
            Description = "Complex air gap with multiple components",
            Source = @"{
                ""type"": ""database"",
                ""cluster"": {
                    ""name"": ""PROD-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""PROD-DB-01"", ""role"": ""Primary""},
                        {""server"": ""PROD-DB-02"", ""role"": ""Secondary""}
                    ]
                },
                ""databases"": [""EnterpriseDB"", ""LoggingDB"", ""AuditDB""]
            }",
            Target = @"{
                ""type"": ""database"",
                ""cluster"": {
                    ""name"": ""DR-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""DR-DB-01"", ""role"": ""Primary""},
                        {""server"": ""DR-DB-02"", ""role"": ""Secondary""}
                    ]
                },
                ""databases"": [""EnterpriseDB_DR"", ""LoggingDB_DR"", ""AuditDB_DR""]
            }"
        };

        var expectedEntity = new Domain.Entities.CyberAirGapLog
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Source = command.Source,
            Target = command.Target
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockCyberAirGapLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.CyberAirGapLog>(e =>
            e.Source.Contains("PROD-CLUSTER-01") &&
            e.Target.Contains("DR-CLUSTER-01"))), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapLog_When_CancellationRequested()
    {
        // Arrange
        var command = _cyberAirGapLogFixture.CreateCyberAirGapLogCommand;
        var cancellationToken = new CancellationToken(true);

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(new Domain.Entities.CyberAirGapLog());

       
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapLog_When_RepositoryFails()
    {
        // Arrange
        var command = _cyberAirGapLogFixture.CreateCyberAirGapLogCommand;
        var mockFailingRepository = CyberAirGapLogRepositoryMocks.CreateFailingCyberAirGapLogRepository();

        var handler = new CreateCyberAirGapLogCommandHandler(
            _mockMapper.Object,
            mockFailingRepository.Object,
            _mockPublisher.Object);

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(new Domain.Entities.CyberAirGapLog());

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe("Database connection failed");
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("Database", "Production Database Replication")]
    [InlineData("File", "File System Synchronization")]
    [InlineData("Archive", "Long-term Archive Storage")]
    [InlineData("Backup", "Backup Data Transfer")]
    [InlineData("Network", "Network Configuration Sync")]
    public async Task Handle_CreateCyberAirGapLog_When_DifferentAirGapTypes(string airGapType, string description)
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = $"airgap-{airGapType.ToLower()}-001",
            AirGapName = $"{airGapType} Air Gap System",
            Description = description,
            SourceSiteName = $"{airGapType} Source Site",
            TargetSiteName = $"{airGapType} Target Site",
            Port = 8443
        };

        var expectedEntity = new Domain.Entities.CyberAirGapLog
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Description = command.Description
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain(command.AirGapName);

        _mockCyberAirGapLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.CyberAirGapLog>(e =>
            e.AirGapName.Contains(airGapType))), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapLog_When_NullProperties()
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = "null-test-001",
            AirGapName = "Null Properties Test",
            Description = null,
            Source = null,
            Target = null,
            SourceComponentName = null,
            TargetComponentName = null
        };

        var expectedEntity = new Domain.Entities.CyberAirGapLog
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Description = command.Description,
            Source = command.Source,
            Target = command.Target
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockCyberAirGapLogRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapLog>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapLog_When_SpecialCharacters()
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = "special-chars-001",
            AirGapName = "Special Characters Test & <script>alert('xss')</script>",
            Description = "Description with special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?",
            SourceSiteName = "Source Site with émojis 🚀💻📊",
            TargetSiteName = "Target Site with unicode 测试数据"
        };

        var expectedEntity = new Domain.Entities.CyberAirGapLog
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Description = command.Description,
            SourceSiteName = command.SourceSiteName,
            TargetSiteName = command.TargetSiteName
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain("Special Characters Test");

        _mockCyberAirGapLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.CyberAirGapLog>(e =>
            e.AirGapName.Contains("Special Characters Test") &&
            e.SourceSiteName.Contains("🚀💻📊") &&
            e.TargetSiteName.Contains("测试数据"))), Times.Once);
    }

    [Theory]
    [InlineData(1, "Minimum Port")]
    [InlineData(65535, "Maximum Port")]
    [InlineData(8443, "Standard HTTPS Port")]
    [InlineData(22, "SSH Port")]
    [InlineData(3389, "RDP Port")]
    public async Task Handle_CreateCyberAirGapLog_When_BoundaryPortValues(int port, string description)
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = $"port-test-{port}",
            AirGapName = $"Port Test {port}",
            Description = description,
            Port = port
        };

        var expectedEntity = new Domain.Entities.CyberAirGapLog
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Port = command.Port
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        _mockCyberAirGapLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.CyberAirGapLog>(e =>
            e.Port == port)), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapLog_When_ValidatingResponse()
    {
        // Arrange
        var command = _cyberAirGapLogFixture.CreateCyberAirGapLogCommand;
        var expectedEntity = new Domain.Entities.CyberAirGapLog
        {
            ReferenceId = "test-reference-id",
            AirGapName = command.AirGapName
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapLog>(command))
            .Returns(expectedEntity);

        _mockCyberAirGapLogRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapLog>()))
            .ReturnsAsync(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CreateCyberAirGapLogResponse>();
        result.Success.ShouldBeTrue();
        result.Id.ShouldBe(expectedEntity.ReferenceId);
        result.Message.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("CyberAirGapLog");
        result.Message.ShouldContain("created successfully");
        result.Message.ShouldContain(expectedEntity.AirGapName);
    }
}
