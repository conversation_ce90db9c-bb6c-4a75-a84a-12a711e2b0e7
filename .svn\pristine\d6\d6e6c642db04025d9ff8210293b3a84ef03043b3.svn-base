﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Queries;

public class GetWorkflowProfileDetailQueryHandlerTests : IClassFixture<WorkflowProfileFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;

    private readonly Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;

    private readonly GetWorkflowProfileDetailQueryHandler _handler;

    public GetWorkflowProfileDetailQueryHandlerTests(WorkflowProfileFixture workflowProfileFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;

        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetWorkflowProfileRepository(_workflowProfileFixture.WorkflowProfiles);

        _handler = new GetWorkflowProfileDetailQueryHandler(_workflowProfileFixture.Mapper, _mockWorkflowProfileRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_WorkflowProfile_Details_When_Valid()
    {
        var result = await _handler.Handle(new GetWorkflowProfileDetailQuery { Id = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<WorkflowProfileDetailVm>();
        result.Id.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].ReferenceId);
        result.Name.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Name);
        result.Status.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Status);
        result.Password.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Password);
        result.ExecutionPolicy.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].ExecutionPolicy);
        result.GroupPolicyId.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].GroupPolicyId);
        result.GroupPolicyName.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].GroupPolicyName);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowProfileId()
    {
        var handler = new GetWorkflowProfileDetailQueryHandler(_workflowProfileFixture.Mapper, _mockWorkflowProfileRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetWorkflowProfileDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowProfileByReferenceId_OneTime()
    {
        await _handler.Handle(new GetWorkflowProfileDetailQuery { Id = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowProfileRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}