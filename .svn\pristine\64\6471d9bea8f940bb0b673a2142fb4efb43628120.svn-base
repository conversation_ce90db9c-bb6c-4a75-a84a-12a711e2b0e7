using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Commands;

public class CreateCyberAirGapStatusTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<ICyberAirGapStatusRepository> _mockCyberAirGapStatusRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateCyberAirGapStatusCommandHandler _handler;

    public CreateCyberAirGapStatusTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockCyberAirGapStatusRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(_cyberAirGapStatusFixture.CyberAirGapStatuses);
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        _handler = new CreateCyberAirGapStatusCommandHandler(
            _mockMapper.Object,
            _mockCyberAirGapStatusRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapStatus_When_ValidCommand()
    {
        // Arrange
        var command = _cyberAirGapStatusFixture.CreateCyberAirGapStatusCommand;
        var expectedEntity = new Domain.Entities.CyberAirGapStatus
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            SourceSiteName = command.SourceSiteName,
            TargetSiteName = command.TargetSiteName,
            Port = command.Port,
            Description = command.Description
        };

        Domain.Entities.CyberAirGapStatus createdEntity = null;
        CyberAirGapStatusCreatedEvent publishedEvent = null;

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(expectedEntity);

        _mockCyberAirGapStatusRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapStatus>()))
            .Callback<Domain.Entities.CyberAirGapStatus>(entity => createdEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAirGapStatus entity) => entity);

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusCreatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapStatusCreatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CreateCyberAirGapStatusResponse>();
        result.Success.ShouldBeTrue();

        createdEntity.ShouldNotBeNull();
        createdEntity.AirGapId.ShouldBe(command.AirGapId);
        createdEntity.AirGapName.ShouldBe(command.AirGapName);
        createdEntity.SourceSiteName.ShouldBe(command.SourceSiteName);
        createdEntity.TargetSiteName.ShouldBe(command.TargetSiteName);
        createdEntity.Port.ShouldBe(command.Port);

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(expectedEntity.AirGapName);

        _mockMapper.Verify(x => x.Map<Domain.Entities.CyberAirGapStatus>(command), Times.Once);
        _mockCyberAirGapStatusRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapStatus>()), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    
    [Fact]
    public async Task Handle_CreateCyberAirGapStatus_When_ComplexJsonProperties()
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = "complex-airgap-status-001",
            AirGapName = "Complex Enterprise Air Gap Status",
            SourceSiteName = "Production Data Center",
            TargetSiteName = "Disaster Recovery Center",
            Port = 8443,
            Description = "Complex air gap status with multiple components",
            Source = @"{
                ""type"": ""database"",
                ""cluster"": {
                    ""name"": ""PROD-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""PROD-DB-01"", ""role"": ""Primary""},
                        {""server"": ""PROD-DB-02"", ""role"": ""Secondary""}
                    ]
                },
                ""databases"": [""EnterpriseDB"", ""LoggingDB"", ""AuditDB""]
            }",
            Target = @"{
                ""type"": ""database"",
                ""cluster"": {
                    ""name"": ""DR-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""DR-DB-01"", ""role"": ""Primary""},
                        {""server"": ""DR-DB-02"", ""role"": ""Secondary""}
                    ]
                },
                ""databases"": [""EnterpriseDB_DR"", ""LoggingDB_DR"", ""AuditDB_DR""]
            }"
        };

        var expectedEntity = new Domain.Entities.CyberAirGapStatus
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Source = command.Source,
            Target = command.Target
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockCyberAirGapStatusRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.CyberAirGapStatus>(e =>
            e.Source.Contains("PROD-CLUSTER-01") &&
            e.Target.Contains("DR-CLUSTER-01"))), Times.Once);
    }

    /// <summary>
    /// Test: Create cyber air gap status with cancellation token
    /// Expected: Respects cancellation and throws OperationCanceledException
    /// </summary>
    [Fact]
    public async Task Handle_CreateCyberAirGapStatus_When_CancellationRequested()
    {
        // Arrange
        var command = _cyberAirGapStatusFixture.CreateCyberAirGapStatusCommand;
        var cancellationToken = new CancellationToken(true);

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(new Domain.Entities.CyberAirGapStatus());

        
    }

    /// <summary>
    /// Test: Create cyber air gap status when repository fails
    /// Expected: Throws exception and does not publish event
    /// </summary>
    [Fact]
    public async Task Handle_CreateCyberAirGapStatus_When_RepositoryFails()
    {
        // Arrange
        var command = _cyberAirGapStatusFixture.CreateCyberAirGapStatusCommand;
        var mockFailingRepository = CyberAirGapStatusRepositoryMocks.CreateFailingCyberAirGapStatusRepository();

        var handler = new CreateCyberAirGapStatusCommandHandler(
            _mockMapper.Object,
            mockFailingRepository.Object,
            _mockPublisher.Object);

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(new Domain.Entities.CyberAirGapStatus());

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe("Database connection failed");
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("Active", "System is running normally")]
    [InlineData("Warning", "System has minor issues")]
    [InlineData("Error", "System has critical errors")]
    [InlineData("Maintenance", "System is under maintenance")]
    [InlineData("Disabled", "System is temporarily disabled")]
    public async Task Handle_CreateCyberAirGapStatus_When_DifferentStatusTypes(string status, string description)
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = $"airgap-status-{status.ToLower()}-001",
            AirGapName = $"{status} Air Gap Status System",
            Description = description,
            SourceSiteName = $"{status} Source Site",
            TargetSiteName = $"{status} Target Site",
            Port = 8443
        };

        var expectedEntity = new Domain.Entities.CyberAirGapStatus
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Description = command.Description
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain(command.AirGapName);

        _mockCyberAirGapStatusRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.CyberAirGapStatus>(e =>
            e.AirGapName.Contains(status))), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGapStatus_When_NullProperties()
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = "null-test-001",
            AirGapName = "Null Properties Test",
            Description = null,
            Source = null,
            Target = null,
            SourceComponentName = null,
            TargetComponentName = null
        };

        var expectedEntity = new Domain.Entities.CyberAirGapStatus
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Description = command.Description,
            Source = command.Source,
            Target = command.Target
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockCyberAirGapStatusRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapStatus>()), Times.Once);
    }

    /// <summary>
    /// Test: Create cyber air gap status with special characters
    /// Expected: Handles special characters in names and descriptions
    /// </summary>
    [Fact]
    public async Task Handle_CreateCyberAirGapStatus_When_SpecialCharacters()
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = "special-chars-001",
            AirGapName = "Special Characters Test & <script>alert('xss')</script>",
            Description = "Description with special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?",
            SourceSiteName = "Source Site with émojis 🚀💻📊",
            TargetSiteName = "Target Site with unicode 测试数据"
        };

        var expectedEntity = new Domain.Entities.CyberAirGapStatus
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Description = command.Description,
            SourceSiteName = command.SourceSiteName,
            TargetSiteName = command.TargetSiteName
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain("Special Characters Test");

        _mockCyberAirGapStatusRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.CyberAirGapStatus>(e =>
            e.AirGapName.Contains("Special Characters Test") &&
            e.SourceSiteName.Contains("🚀💻📊") &&
            e.TargetSiteName.Contains("测试数据"))), Times.Once);
    }

    /// <summary>
    /// Test: Create cyber air gap status with boundary values
    /// Expected: Handles minimum and maximum values correctly
    /// </summary>
    [Theory]
    [InlineData(1, "Minimum Port")]
    [InlineData(65535, "Maximum Port")]
    [InlineData(8443, "Standard HTTPS Port")]
    [InlineData(22, "SSH Port")]
    [InlineData(3389, "RDP Port")]
    public async Task Handle_CreateCyberAirGapStatus_When_BoundaryPortValues(int port, string description)
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = $"port-test-{port}",
            AirGapName = $"Port Test {port}",
            Description = description,
            Port = port
        };

        var expectedEntity = new Domain.Entities.CyberAirGapStatus
        {
            AirGapId = command.AirGapId,
            AirGapName = command.AirGapName,
            Port = command.Port
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        _mockCyberAirGapStatusRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.CyberAirGapStatus>(e =>
            e.Port == port)), Times.Once);
    }

    /// <summary>
    /// Test: Create cyber air gap status response validation
    /// Expected: Response contains all required properties
    /// </summary>
    [Fact]
    public async Task Handle_CreateCyberAirGapStatus_When_ValidatingResponse()
    {
        // Arrange
        var command = _cyberAirGapStatusFixture.CreateCyberAirGapStatusCommand;
        var expectedEntity = new Domain.Entities.CyberAirGapStatus
        {
            ReferenceId = "test-reference-id",
            AirGapName = command.AirGapName
        };

        _mockMapper.Setup(x => x.Map<Domain.Entities.CyberAirGapStatus>(command))
            .Returns(expectedEntity);

        _mockCyberAirGapStatusRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapStatus>()))
            .ReturnsAsync(expectedEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CreateCyberAirGapStatusResponse>();
        result.Success.ShouldBeTrue();
        result.Id.ShouldBe(expectedEntity.ReferenceId);
        result.Message.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("CyberAirGapStatus");
        result.Message.ShouldContain("created successfully");
        result.Message.ShouldContain(expectedEntity.AirGapName);
    }
}
