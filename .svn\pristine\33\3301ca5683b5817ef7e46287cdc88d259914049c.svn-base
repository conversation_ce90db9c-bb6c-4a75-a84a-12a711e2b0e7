﻿//using ContinuityPatrol.Application.Features.Server.Commands.Delete;
//using ContinuityPatrol.Application.Features.Server.Events.Delete;
//using ContinuityPatrol.Application.Features.Server.Events.LicenseInfoEvents.Delete;
//using ContinuityPatrol.Domain.Entities;
//using ContinuityPatrol.Shared.Core.Exceptions;

//namespace ContinuityPatrol.Application.UnitTests.Features.Server.Commands
//{
//    public class DeleteServerTests
//    {
//        private readonly Mock<IServerRepository> _mockServerRepository;
//        private readonly Mock<IDatabaseRepository> _mockDatabaseRepository;
//        private readonly Mock<IPublisher> _mockPublisher;
//        private readonly Mock<IWorkflowRepository> _mockWorkflowRepository;
//        private readonly Mock<INodeRepository> _mockNodeRepository;
//        private readonly Mock<ICyberAirGapRepository> _mockCyberAirGapRepository;
//        private readonly DeleteServerCommandHandler _handler;

//        public DeleteServerTests()
//        {
//            _mockServerRepository = new Mock<IServerRepository>();
//            _mockDatabaseRepository = new Mock<IDatabaseRepository>();
//            _mockPublisher = new Mock<IPublisher>();
//            _mockWorkflowRepository = new Mock<IWorkflowRepository>();
//            _mockNodeRepository = new Mock<INodeRepository>();
//            _mockCyberAirGapRepository = new Mock<ICyberAirGapRepository>();

//            _handler = new DeleteServerCommandHandler(
//                _mockServerRepository.Object,
//                _mockDatabaseRepository.Object,
//                _mockPublisher.Object,
//                _mockWorkflowRepository.Object,
//                _mockNodeRepository.Object,
//                _mockCyberAirGapRepository.Object);
//        }

//        [Fact]
//        public async Task Handle_ShouldReturnResponse_WhenServerIsDeletedSuccessfully()
//        {
//            var serverId = Guid.NewGuid().ToString();
//            var command = new DeleteServerCommand { Id = serverId };

//            var server = new Domain.Entities.Server
//            {
//                ReferenceId = serverId,
//                Name = "Test Server",
//                IsActive = true,
//                LicenseKey = "EncryptedLicenseKey",
//                RoleType = "Server"
//            };

//            _mockServerRepository
//                .Setup(repo => repo.GetByReferenceIdAsync(serverId))
//                .ReturnsAsync(server);

//            _mockDatabaseRepository
//                .Setup(repo => repo.GetDatabaseByServerId(serverId))
//                .ReturnsAsync(new List<Domain.Entities.Database>());

//            _mockWorkflowRepository
//                .Setup(repo => repo.GetWorkflowPropertiesByServerId(serverId))
//                .ReturnsAsync(new List<Domain.Entities.Workflow>());

//            _mockNodeRepository
//                .Setup(repo => repo.GetNodeByServerId(serverId))
//                .ReturnsAsync(new List<Domain.Entities.Node>());

//            _mockCyberAirGapRepository
//                .Setup(repo => repo.GetAirGapByServerId(serverId))
//                .ReturnsAsync(new List<CyberAirGap>());

//            _mockServerRepository
//                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.Server>()))
//                .ReturnsAsync(server);

//            _mockPublisher
//                .Setup(pub => pub.Publish(It.IsAny<ServerDeletedEvent>(), It.IsAny<CancellationToken>()))
//                .Returns(Task.CompletedTask);

//            _mockPublisher
//                .Setup(pub => pub.Publish(It.IsAny<ServerLicenseInfoDeletedEvent>(), It.IsAny<CancellationToken>()))
//                .Returns(Task.CompletedTask);

//            var result = await _handler.Handle(command, CancellationToken.None);

//            Assert.NotNull(result);
//            Assert.Equal("Server deleted: Test Server", result.Message);
//            Assert.False(result.IsActive);

//            _mockServerRepository.Verify(repo => repo.GetByReferenceIdAsync(serverId), Times.Once);
//            _mockServerRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.Server>()), Times.Once);
//            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<ServerDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
//        }

//        [Fact]
//        public async Task Handle_ShouldThrowInvalidException_WhenServerIsInUse()
//        {
//            var serverId = Guid.NewGuid().ToString();
//            var command = new DeleteServerCommand { Id = serverId };

//            var server = new Domain.Entities.Server
//            {
//                ReferenceId = serverId,
//                Name = "Test Server",
//                IsActive = true,
//                LicenseKey = "EncryptedLicenseKey"
//            };

//            var databases = new List<Domain.Entities.Database> { new Domain.Entities.Database() };

//            _mockServerRepository
//                .Setup(repo => repo.GetByReferenceIdAsync(serverId))
//                .ReturnsAsync(server);

//            _mockDatabaseRepository
//                .Setup(repo => repo.GetDatabaseByServerId(serverId))
//                .ReturnsAsync(databases);

//            var exception = await Assert.ThrowsAsync<InvalidException>(() =>
//                _handler.Handle(command, CancellationToken.None));

//            Assert.Equal("The server is currently in use", exception.Message);
//            _mockServerRepository.Verify(repo => repo.GetByReferenceIdAsync(serverId), Times.Once);
//            _mockDatabaseRepository.Verify(repo => repo.GetDatabaseByServerId(serverId), Times.Once);
//        }

//        [Fact]
//        public async Task Handle_ShouldThrowNotFoundException_WhenServerNotFound()
//        {
//            var serverId = Guid.NewGuid().ToString();
//            var command = new DeleteServerCommand { Id = serverId };

//            _mockServerRepository
//                .Setup(repo => repo.GetByReferenceIdAsync(serverId))
//                .ReturnsAsync((Domain.Entities.Server)null);

//            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
//                _handler.Handle(command, CancellationToken.None));

//            Assert.Equal($"Server not found: {serverId}", exception.Message);
//            _mockServerRepository.Verify(repo => repo.GetByReferenceIdAsync(serverId), Times.Once);
//        }

//        [Fact]
//        public async Task Handle_ShouldNotPublishEvents_WhenServerIsDatabaseRole()
//        {
//            var serverId = Guid.NewGuid().ToString();
//            var command = new DeleteServerCommand { Id = serverId };

//            var server = new Domain.Entities.Server
//            {
//                ReferenceId = serverId,
//                Name = "Test Server",
//                IsActive = true,
//                LicenseKey = "EncryptedLicenseKey",
//                RoleType = "database"
//            };

//            _mockServerRepository
//                .Setup(repo => repo.GetByReferenceIdAsync(serverId))
//                .ReturnsAsync(server);

//            _mockDatabaseRepository
//                .Setup(repo => repo.GetDatabaseByServerId(serverId))
//                .ReturnsAsync(new List<Domain.Entities.Database>());

//            _mockWorkflowRepository
//                .Setup(repo => repo.GetWorkflowPropertiesByServerId(serverId))
//                .ReturnsAsync(new List<Domain.Entities.Workflow>());

//            _mockNodeRepository
//                .Setup(repo => repo.GetNodeByServerId(serverId))
//                .ReturnsAsync(new List<Domain.Entities.Node>());

//            _mockCyberAirGapRepository
//                .Setup(repo => repo.GetAirGapByServerId(serverId))
//                .ReturnsAsync(new List<CyberAirGap>());

//            _mockServerRepository
//                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.Server>()))
//                .ReturnsAsync(server);

//            _mockPublisher
//                .Setup(pub => pub.Publish(It.IsAny<ServerDeletedEvent>(), It.IsAny<CancellationToken>()))
//                .Returns(Task.CompletedTask);

//            _mockPublisher
//                .Setup(pub => pub.Publish(It.IsAny<ServerLicenseInfoDeletedEvent>(), It.IsAny<CancellationToken>()))
//                .Returns(Task.CompletedTask);

//            var result = await _handler.Handle(command, CancellationToken.None);

//            Assert.NotNull(result);
//            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<ServerLicenseInfoDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
//        }
//    }
//}
