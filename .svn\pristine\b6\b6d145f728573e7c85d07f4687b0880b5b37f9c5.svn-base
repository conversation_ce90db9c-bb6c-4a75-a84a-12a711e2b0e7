﻿global using AutoFixture;
global using AutoFixture.Xunit2;
global using AutoMapper;
global using MediatR;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Logging;
global using MockQueryable.Moq;
global using Moq;
global using Shouldly;
global using Microsoft.Extensions.Configuration;
global using Xunit;
global using Microsoft.Extensions.Caching.Memory;
global using ContinuityPatrol.Application.Contracts.Persistence;
global using ContinuityPatrol.Shared.Core.Contracts.Identity;
global using ContinuityPatrol.Shared.Core.Specifications;
global using MockQueryable;
global using Microsoft.AspNetCore.SignalR;
global using System.Globalization;