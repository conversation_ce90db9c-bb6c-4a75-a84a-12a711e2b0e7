using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

/// <summary>
/// Fixture class for CyberAirGapStatus module testing
/// Provides test data setup and AutoFixture configuration for comprehensive unit testing
/// Purpose: CyberAirGapStatus manages status tracking and monitoring for cyber air gap operations
/// </summary>
public class CyberAirGapStatusFixture : IDisposable
{
    public List<CyberAirGapStatus> CyberAirGapStatuses { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateCyberAirGapStatusCommand CreateCyberAirGapStatusCommand { get; set; }
    public UpdateCyberAirGapStatusCommand UpdateCyberAirGapStatusCommand { get; set; }
    public DeleteCyberAirGapStatusCommand DeleteCyberAirGapStatusCommand { get; set; }
    public UpdateAirGapStatusCommand UpdateAirGapStatusCommand { get; set; }
    public CyberAirGapStatusListVm CyberAirGapStatusListVm { get; set; }
    public CyberAirGapStatusDetailVm CyberAirGapStatusDetailVm { get; set; }
    public IMapper Mapper { get; set; }

    public CyberAirGapStatusFixture()
    {
        // Initialize manual test data with known values for reliable testing
        CyberAirGapStatuses = new List<CyberAirGapStatus>
        {
            new CyberAirGapStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                AirGapId = Guid.NewGuid().ToString(),
                AirGapName = "TestAirGap_Status01",
                Description = "Test Air Gap Status for Unit Testing",
                SourceSiteId = Guid.NewGuid().ToString(),
                SourceSiteName = "Source Site Status 01",
                TargetSiteId = Guid.NewGuid().ToString(),
                TargetSiteName = "Target Site Status 01",
                Port = 8080,
                Source = "{\"serverId\":\"status-server-001\",\"componentId\":\"status-comp-001\"}",
                Target = "{\"serverId\":\"status-server-002\",\"componentId\":\"status-comp-002\"}",
                SourceComponentId = Guid.NewGuid().ToString(),
                SourceComponentName = "Source Component Status 01",
                TargetComponentId = Guid.NewGuid().ToString(),
                TargetComponentName = "Target Component Status 01",
                EnableWorkflowId = Guid.NewGuid().ToString(),
                DisableWorkflowId = Guid.NewGuid().ToString(),
                Status = "Active",
                //StatusCode = "200",
                //StatusMessage = "Air gap is operational",
                //StatusData = "{\"health\":\"good\",\"latency\":\"10ms\"}",
                //LastChecked = DateTime.UtcNow,
                //NextCheck = DateTime.UtcNow.AddMinutes(30),
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalStatuses = AutoCyberAirGapStatusFixture.CreateMany<CyberAirGapStatus>(2).ToList();
            CyberAirGapStatuses.AddRange(additionalStatuses);
            
            UserActivities = AutoCyberAirGapStatusFixture.CreateMany<UserActivity>(3).ToList();
            CreateCyberAirGapStatusCommand = AutoCyberAirGapStatusFixture.Create<CreateCyberAirGapStatusCommand>();
            UpdateCyberAirGapStatusCommand = AutoCyberAirGapStatusFixture.Create<UpdateCyberAirGapStatusCommand>();
            DeleteCyberAirGapStatusCommand = AutoCyberAirGapStatusFixture.Create<DeleteCyberAirGapStatusCommand>();
            UpdateAirGapStatusCommand = AutoCyberAirGapStatusFixture.Create<UpdateAirGapStatusCommand>();
            CyberAirGapStatusListVm = AutoCyberAirGapStatusFixture.Create<CyberAirGapStatusListVm>();
            CyberAirGapStatusDetailVm = AutoCyberAirGapStatusFixture.Create<CyberAirGapStatusDetailVm>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateCyberAirGapStatusCommand = new CreateCyberAirGapStatusCommand();
            UpdateCyberAirGapStatusCommand = new UpdateCyberAirGapStatusCommand();
            DeleteCyberAirGapStatusCommand = new DeleteCyberAirGapStatusCommand();
            UpdateAirGapStatusCommand = new UpdateAirGapStatusCommand();
            CyberAirGapStatusListVm = new CyberAirGapStatusListVm();
            CyberAirGapStatusDetailVm = new CyberAirGapStatusDetailVm();
        }

        // Configure AutoMapper for CyberAirGapStatus mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CyberAirGapStatusProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    /// <summary>
    /// AutoFixture configuration for CyberAirGapStatus entities and commands
    /// Handles circular references and provides realistic test data
    /// </summary>
    public Fixture AutoCyberAirGapStatusFixture
    {
        get
        {
            var fixture = new Fixture();
            
            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations for commands
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateCyberAirGapStatusCommand>(p => p.AirGapName, 100));
            fixture.Customize<CreateCyberAirGapStatusCommand>(c => c
                .With(b => b.AirGapId, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapName, () => $"TestAirGapStatus{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Test Air Gap Status Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"SourceSiteStatus{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"TargetSiteStatus{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"status-server-{fixture.Create<int>():000}\",\"componentId\":\"status-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"status-server-{fixture.Create<int>():000}\",\"componentId\":\"status-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"SourceComponentStatus{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"TargetComponentStatus{fixture.Create<int>():00}")
                //.With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                //.With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString())
                //.With(b => b.Status, () => new[] { "Active", "Inactive", "Warning", "Error", "Maintenance" }[fixture.Create<int>() % 5])
                //.With(b => b.StatusCode, () => new[] { "200", "404", "500", "503", "429" }[fixture.Create<int>() % 5])
                //.With(b => b.StatusMessage, () => $"Status message {fixture.Create<int>()}")
                //.With(b => b.StatusData, () => $"{{\"health\":\"good\",\"latency\":\"{fixture.Create<int>() % 100}ms\"}}")
                );

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateCyberAirGapStatusCommand>(p => p.AirGapName, 100));
            fixture.Customize<UpdateCyberAirGapStatusCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapId, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapName, () => $"UpdatedAirGapStatus{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Updated Air Gap Status Description {fixture.Create<int>()}")
                .With(b => b.Status, () => new[] { "Updated", "Modified", "Changed", "Refreshed" }[fixture.Create<int>() % 4])
                //.With(b => b.StatusCode, () => new[] { "201", "202", "204", "304" }[fixture.Create<int>() % 4])
                //.With(b => b.StatusMessage, () => $"Updated status message {fixture.Create<int>()}")
                //.With(b => b.StatusData, () => $"{{\"health\":\"updated\",\"latency\":\"{fixture.Create<int>() % 50}ms\"}}")
                );

            fixture.Customize<DeleteCyberAirGapStatusCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            // UpdateAirGapStatusCommand customizations
            fixture.Customize<UpdateAirGapStatusCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.Status, () => new[] { "Active", "Inactive", "Warning", "Error", "Maintenance" }[fixture.Create<int>() % 5]));

            // CyberAirGapStatus entity customizations
            fixture.Customize<CyberAirGapStatus>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.AirGapId, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapName, () => $"TestAirGapStatus{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Test Air Gap Status Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"SourceSiteStatus{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"TargetSiteStatus{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"status-server-{fixture.Create<int>():000}\",\"componentId\":\"status-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"status-server-{fixture.Create<int>():000}\",\"componentId\":\"status-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"SourceComponentStatus{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"TargetComponentStatus{fixture.Create<int>():00}")
                .With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                .With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString())
                .With(b => b.Status, () => new[] { "Active", "Inactive", "Warning", "Error", "Maintenance" }[fixture.Create<int>() % 5])
                //.With(b => b.StatusCode, () => new[] { "200", "404", "500", "503", "429" }[fixture.Create<int>() % 5])
                //.With(b => b.StatusMessage, () => $"Status message {fixture.Create<int>()}")
                //.With(b => b.StatusData, () => $"{{\"health\":\"good\",\"latency\":\"{fixture.Create<int>() % 100}ms\"}}")
                //.With(b => b.LastChecked, () => DateTime.UtcNow.AddMinutes(-fixture.Create<int>() % 60))
                //.With(b => b.NextCheck, () => DateTime.UtcNow.AddMinutes(fixture.Create<int>() % 60))
                );

            // UserActivity customization for CyberAirGapStatus
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "CyberAirGapStatus")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test cyber air gap status activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            // CyberAirGapStatusListVm customizations
            fixture.Customize<CyberAirGapStatusListVm>(c => c
                .With(a => a.Id, () => Guid.NewGuid().ToString())
                .With(a => a.AirGapId, () => Guid.NewGuid().ToString())
                .With(a => a.AirGapName, () => $"TestAirGapStatusList{fixture.Create<int>():000}")
                .With(a => a.SourceSiteName, () => $"SourceSiteList{fixture.Create<int>():00}")
                .With(a => a.TargetSiteName, () => $"TargetSiteList{fixture.Create<int>():00}")
                .With(a => a.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(a => a.Description, () => $"Test Air Gap Status List Description {fixture.Create<int>()}")
                .With(a => a.Status, "Active")
                .With(a => a.IsFileTransfered, true));

            // CyberAirGapStatusDetailVm customizations
            fixture.Customize<CyberAirGapStatusDetailVm>(c => c
                .With(a => a.Id, () => Guid.NewGuid().ToString())
                .With(a => a.AirGapId, () => Guid.NewGuid().ToString())
                .With(a => a.AirGapName, () => $"TestAirGapStatusDetail{fixture.Create<int>():000}")
                .With(a => a.SourceSiteName, () => $"SourceSiteDetail{fixture.Create<int>():00}")
                .With(a => a.TargetSiteName, () => $"TargetSiteDetail{fixture.Create<int>():00}")
                .With(a => a.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(a => a.Description, () => $"Test Air Gap Status Detail Description {fixture.Create<int>()}")
                .With(a => a.Status, "Active")
                .With(a => a.IsFileTransfered, true)
                .With(a => a.WorkflowStatus, "Completed")
                .With(a => a.RPO, "5 minutes")
                .With(a => a.StartTime, () => DateTime.Now.AddHours(-2))
                .With(a => a.EndTime, () => DateTime.Now.AddHours(-1)));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
