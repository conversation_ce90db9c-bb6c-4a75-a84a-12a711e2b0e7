﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel
@using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" title="Solution Mapping"><i class="cp-solution-mapping"></i><span>Solution Mapping</span></h6>
            <form class="d-flex gap-2 align-items-center">
                <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                <button type="button" title="Create" class="btn btn-primary btn-sm createbutton" data-bs-toggle="modal" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="pt-1 card-body" style="height: calc(100vh - 141px);">
            <div>
                <table class="table" id="tblSolutionMapping">
                    <thead>
                        <tr>
                            <th>Sr No</th>
                            <th>Page Builder</th>
                            <th></th>
                            <th>Solution Mapping</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @* <tr>
                            <th>1</th>
                            <td>ODG - Page build data</td>
                            <td><img src="/img/isomatric/solution_matched.svg" class="img-fluid" /></td>
                            <td>Oracle Database </td>
                            <td>
                                <div class="d-flex align-items-center gap-2">
                                    <span role="button" title="Edit" class="database-edit-button">
                                        <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" title="Delete" class="database-delete-button" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>

                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th>2</th>
                            <td>ExchangeDAG19_Infra</td>
                            <td><img src="/img/isomatric/solution_not_matched.svg" class="img-fluid" /></td>
                            <td>ExchangeDAG19_Infra</td>
                            <td>
                                <div class="d-flex align-items-center gap-2">
                                    <span role="button" title="Edit" class="database-edit-button">
                                        <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" title="Delete" class="database-delete-button" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>

                                </div>
                            </td>
                        </tr> *@
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<!--Modal Create-->
<div class="modal fade" data-bs-backdrop="static" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">

            <div class="modal-header">
                <h6 class="page_title"><i class="cp-solution-mapping"></i><span>Solution Mapping</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label" title="Y-axis Name">
                                    Solution Name
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input type="text" placeholder="Enter Solution Name" id="solutionName" class="form-control" />

                                </div>
                                <span id="Name-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Page Builder</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-page-builder"></i></span>
                                    <select class=" form-select-modal" id="pageBuilderDropdown" data-live-search="true" data-placeholder="Select Page Builder Name">
                                        @*  <option value=""></option>
                                <option value="Application">Application</option>
                                <option value="Database">Database</option> *@
                                    </select>
                                </div>
                                <span id="pageBuilderName-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Activity Type</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-activity-type"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Activity Type" id="Activetype">
                                        <option value=""></option>
                                        <option value="1">Application</option>
                                        <option value="2">DB</option>
                                        <option value="3">Virtual</option>
                                    </select>
                                </div>
                                <span id="activityName-error"></span>
                            </div>
                            <div class="form-group databaseType d-none">
                                <div class="form-label">Database Type</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-database"></i></span>
                                    <select class=" form-select-modal" id="activityDatabaseType" data-live-search="true" data-placeholder="Select Database Type">
                                    </select>
                                </div>
                                <span id="activityDatabaseName-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Replication Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-replication-on"></i></span>
                                    <select class="form-select-modal" id="replicationNmae" data-live-search="true" data-placeholder="Select Replication Name">
                                    </select>
                                </div>
                                <span id="replicationName-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Replication Type</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-replication-on"></i></span>
                                    <select class="form-select-modal" id="replicationType" data-live-search="true" data-placeholder="Select Replication Type">
                                    </select>
                                </div>
                                <span id="replicationType-error"></span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-search"></i></span>
                                    <input type="text" placeholder="Search.." class="form-control" />

                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Workflow Template</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-workflow-templates"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Activity Type" multiple>
                                        <option value=""></option>
                                        <option value="1">Application</option>
                                        <option value="2">DB</option>
                                        <option value="3">Virtual</option>
                                    </select>
                                </div>
                                <span id="activityName-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Form Builder</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-form-builder"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Activity Type" multiple>
                                        <option value=""></option>
                                        <option value="1">Application</option>
                                        <option value="2">DB</option>
                                        <option value="3">Virtual</option>
                                    </select>
                                </div>
                                <span id="activityName-error"></span>
                            </div>
                            <div style="max-height:305px; overflow-y:auto;">
                                <details class="ms-0">
                                    <summary><input type="checkbox" class="form-check-input me-2" id="exampleCheck1">Database</summary>
                                    <ul class="nav flex-column gap-1" style="padding-left:33px;">
                                        <li class="nav-item"><input type="checkbox" class="form-check-input me-2" id="exampleCheck3">MySQL</li>
                                        <li class="nav-item"><input type="checkbox" class="form-check-input me-2" id="exampleCheck4">MSSQL</li>
                                        <li class="nav-item"><input type="checkbox" class="form-check-input me-2" id="exampleCheck5">Oracle</li>
                                    </ul>
                                </details>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm btn_save">Save</button>
                </div>
            </div>

        </div>
    </div>
</div>


<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <form asp-controller="SolutionMapping" asp-action="Delete" asp-route-id="textDeleteId" method="post" enctype="multipart/form-data" class="w-100">
        <div class="modal-content">

            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body   text-center pt-0">
                <h4>Are you sure?</h4>
                <p>You want to delete <span class="font-weight-bolder text-primary" id="deleteData"></span> data?</p>
                <input  id="textDeleteId" name="id" class="form-control d-none" />
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
            </div>
        </div>
        </form>
    </div>
</div>

@section Scripts
{
    <partial name="_ValidationScriptsPartial" />
}

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/PageBuilder/SolutionMapping.js"></script>
                                       