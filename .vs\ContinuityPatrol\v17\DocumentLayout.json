{"Version": 1, "WorkspaceRootPath": "D:\\TestCase UI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\events\\createhacmpclustereventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\events\\createhacmpclustereventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\events\\deletehacmpclustereventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\events\\deletehacmpclustereventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\events\\paginatedviewhacmpclustereventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\events\\paginatedviewhacmpclustereventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\events\\updatehacmpclustereventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\events\\updatehacmpclustereventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\createglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\createglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\validators\\updatehacmpclustervalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\validators\\updatehacmpclustervalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\validators\\createhacmpclustervalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\validators\\createhacmpclustervalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 219, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 4, "Title": "GetGlobalVariableDetailQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "ViewState": "AgIAADUAAAAAAAAAAAAIwEQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:32:25.775Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "CreateHacmpClusterEventTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\CreateHacmpClusterEventTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\CreateHacmpClusterEventTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\CreateHacmpClusterEventTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\CreateHacmpClusterEventTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T13:55:37.904Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DeleteHacmpClusterEventTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\DeleteHacmpClusterEventTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\DeleteHacmpClusterEventTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\DeleteHacmpClusterEventTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\DeleteHacmpClusterEventTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T13:55:35.889Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "PaginatedViewHacmpClusterEventTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\PaginatedViewHacmpClusterEventTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\PaginatedViewHacmpClusterEventTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\PaginatedViewHacmpClusterEventTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\PaginatedViewHacmpClusterEventTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T13:55:34.56Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UpdateHacmpClusterEventTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\UpdateHacmpClusterEventTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\UpdateHacmpClusterEventTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\UpdateHacmpClusterEventTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Events\\UpdateHacmpClusterEventTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T13:55:25.001Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "CreateGlobalVariableValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T13:37:08.293Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "UpdateHacmpClusterValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Validators\\UpdateHacmpClusterValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Validators\\UpdateHacmpClusterValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Validators\\UpdateHacmpClusterValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Validators\\UpdateHacmpClusterValidatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T13:36:52.245Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CreateHacmpClusterValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Validators\\CreateHacmpClusterValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Validators\\CreateHacmpClusterValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Validators\\CreateHacmpClusterValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Validators\\CreateHacmpClusterValidatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T13:36:03.338Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}]}, {"DockedHeight": 57, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}]}]}]}