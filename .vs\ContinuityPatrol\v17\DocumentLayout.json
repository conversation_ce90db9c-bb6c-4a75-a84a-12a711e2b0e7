{"Version": 1, "WorkspaceRootPath": "D:\\TestCase UI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\commands\\createhacmpclustertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\hacmpcluster\\commands\\createhacmpclustertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\hacmpcluster\\queries\\getpaginatedlist\\gethacmpclusterpaginatedlistqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\hacmpcluster\\queries\\getpaginatedlist\\gethacmpclusterpaginatedlistqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 174, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "GetGlobalVariableDetailQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAABcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:32:25.775Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "CreateHacmpClusterTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Commands\\CreateHacmpClusterTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Commands\\CreateHacmpClusterTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Commands\\CreateHacmpClusterTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\HacmpCluster\\Commands\\CreateHacmpClusterTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:47:54.614Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "GetHacmpClusterPaginatedListQueryHandler.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\HacmpCluster\\Queries\\GetPaginatedList\\GetHacmpClusterPaginatedListQueryHandler.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\HacmpCluster\\Queries\\GetPaginatedList\\GetHacmpClusterPaginatedListQueryHandler.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\HacmpCluster\\Queries\\GetPaginatedList\\GetHacmpClusterPaginatedListQueryHandler.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\HacmpCluster\\Queries\\GetPaginatedList\\GetHacmpClusterPaginatedListQueryHandler.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAB4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:46:49.764Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}]}, {"DockedHeight": 102, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}]}]}]}