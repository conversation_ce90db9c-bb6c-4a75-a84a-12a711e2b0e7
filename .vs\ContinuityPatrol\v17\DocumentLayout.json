{"Version": 1, "WorkspaceRootPath": "D:\\TestCase UI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\mocks\\incidentrepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\mocks\\incidentrepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2950A847-AFC7-4861-9515-F23C46CCE376}|Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj|d:\\testcase ui\\shared\\continuitypatrol.shared.core\\specifications\\specification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2950A847-AFC7-4861-9515-F23C46CCE376}|Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj|solutionrelative:shared\\continuitypatrol.shared.core\\specifications\\specification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\incident\\validators\\updateincidentvalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\incident\\validators\\updateincidentvalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\incident\\validators\\createincidentvalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\incident\\validators\\createincidentvalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\constants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\constants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\incident\\commands\\delete\\deleteincidentcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\incident\\commands\\delete\\deleteincidentcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\company\\validators\\createcompanyvalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\company\\validators\\createcompanyvalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 219, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}]}, {"DockedHeight": 57, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 5, "Title": "DeleteIncidentCommand.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Delete\\DeleteIncidentCommand.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Delete\\DeleteIncidentCommand.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Delete\\DeleteIncidentCommand.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Delete\\DeleteIncidentCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:37:04.477Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Specification.cs", "DocumentMoniker": "D:\\TestCase UI\\Shared\\ContinuityPatrol.Shared.Core\\Specifications\\Specification.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Core\\Specifications\\Specification.cs", "ToolTip": "D:\\TestCase UI\\Shared\\ContinuityPatrol.Shared.Core\\Specifications\\Specification.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Core\\Specifications\\Specification.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwAYAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:36:00.997Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "IncidentRepositoryMocks.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\IncidentRepositoryMocks.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\IncidentRepositoryMocks.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\IncidentRepositoryMocks.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\IncidentRepositoryMocks.cs", "ViewState": "AgIAAIcAAAAAAAAAAAAmwIUAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:31:41.489Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Constants.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Constants.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Constants.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Constants.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Constants.cs", "ViewState": "AgIAAI8AAAAAAAAAAAAIwIMAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:24:41.639Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "UpdateIncidentValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Validators\\UpdateIncidentValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Validators\\UpdateIncidentValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Validators\\UpdateIncidentValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Validators\\UpdateIncidentValidatorTests.cs", "ViewState": "AgIAAKkAAAAAAAAAAAAvwLAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:21:22.296Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "CreateCompanyValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Company\\Validators\\CreateCompanyValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Company\\Validators\\CreateCompanyValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Company\\Validators\\CreateCompanyValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Company\\Validators\\CreateCompanyValidatorTests.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAQwCgAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:19:58.095Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CreateIncidentValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Validators\\CreateIncidentValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Validators\\CreateIncidentValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Validators\\CreateIncidentValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Validators\\CreateIncidentValidatorTests.cs", "ViewState": "AgIAALMAAAAAAAAAAADwv2oAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:18:18.283Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}]}]}]}