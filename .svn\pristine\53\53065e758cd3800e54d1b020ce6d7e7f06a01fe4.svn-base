﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Create;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Delete;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Update;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.UpdateConditionActionId;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes
{
    public class AutoCGExecutionReportDataAttribute : AutoDataAttribute
    {
        public AutoCGExecutionReportDataAttribute()
            : base(() =>
            {
                var fixture = new Fixture();
                var referenceId = Guid.NewGuid().ToString();
                var workflowOperationId = Guid.NewGuid().ToString();
                var workflowName = "CG-Workflow-" + Guid.NewGuid().ToString("N").Substring(0, 8);
                var conditionActionId = Guid.NewGuid().ToString();

                // COMMON CONFIGURATION
                fixture.OmitAutoProperties = false;

                // CREATE COMMAND
                fixture.Customizations.Add(
                    new StringPropertyTruncateSpecimenBuilder<CreateCGExecutionCommand>(
                        p => p.WorkflowName, 30));
                fixture.Customize<CreateCGExecutionCommand>(c => c
                    .With(cmd => cmd.WorkflowName, workflowName)
                    .With(cmd => cmd.WorkflowOperationId, referenceId));

                // UPDATE COMMAND
                fixture.Customizations.Add(
                    new StringPropertyTruncateSpecimenBuilder<UpdateCGExecutionCommand>(
                        p => p.WorkflowName, 30));
                fixture.Customize<UpdateCGExecutionCommand>(c => c
                    .With(cmd => cmd.WorkflowName, "Updated-" + workflowName)
                    .With(cmd => cmd.Id, referenceId));

                // DELETE COMMAND
                fixture.Customize<DeleteCGExecutionCommand>(c => c
                    .With(cmd => cmd.Id, referenceId));

                // RESILIENCY WORKFLOW SCHEDULE LOG COMMAND
                fixture.Customizations.Add(
                    new StringPropertyTruncateSpecimenBuilder<ResiliencyReadinessWorkflowScheduleLogCommand>(
                        p => p.WorkflowOperationId, 36));
                fixture.Customize<ResiliencyReadinessWorkflowScheduleLogCommand>(c => c
                    .With(cmd => cmd.WorkflowOperationId, workflowOperationId));
                //.With(cmd => cmd.ConditionActionId, conditionActionId)
                //.Without(cmd => cmd.AdditionalProperties)); // Example for excluding properties

                return fixture;
            })
        {
        }
    }
}
