using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ApprovalMatrixUsersFilterSpecification : Specification<ApprovalMatrixUsers>
{
    public ApprovalMatrixUsersFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.UserName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)          
                	if (stringItem.Contains("username=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.UserName.Contains(stringItem.Replace("username=", "",StringComparison.OrdinalIgnoreCase)));
					else if (stringItem.Contains("email=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Email.Contains(stringItem.Replace("email=", "",StringComparison.OrdinalIgnoreCase)));
					else if (stringItem.Contains("mobilenumber=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.MobileNumber.Contains(stringItem.Replace("mobilenumber=", "",StringComparison.OrdinalIgnoreCase)));					
					else if (stringItem.Contains("usertype=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.UserType.Contains(stringItem.Replace("usertype=", "",StringComparison.OrdinalIgnoreCase)));
					else if (stringItem.Contains("accept=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.AcceptType.Contains(stringItem.Replace("accept=", "",StringComparison.OrdinalIgnoreCase)));
					else if (stringItem.Contains("usergroup=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.UserGroupProperties.Contains(stringItem.Replace("usergroup=", "",StringComparison.OrdinalIgnoreCase)));
                  
            }
            else
            {
                Criteria = p =>p.UserName.Contains(searchString)||p.Email.Contains(searchString)||p.MobileNumber.Contains(searchString)||p.UserGroupProperties.Contains(searchString)
                ||p.UserType.Contains(searchString)||p.AcceptType.Contains(searchString);
            }
        }
    }
}
