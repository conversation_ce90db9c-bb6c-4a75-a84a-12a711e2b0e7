using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

/// <summary>
/// Fixture class for CyberAirGapLog module testing
/// Provides test data setup and AutoFixture configuration for comprehensive unit testing
/// Purpose: CyberAirGapLog manages logging and audit trail for cyber air gap operations
/// </summary>
public class CyberAirGapLogFixture : IDisposable
{
    public List<CyberAirGapLog> CyberAirGapLogs { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateCyberAirGapLogCommand CreateCyberAirGapLogCommand { get; set; }
    public UpdateCyberAirGapLogCommand UpdateCyberAirGapLogCommand { get; set; }
    public DeleteCyberAirGapLogCommand DeleteCyberAirGapLogCommand { get; set; }
    public CyberAirGapLogListVm CyberAirGapLogListVm { get; set; }
    public CyberAirGapLogDetailVm CyberAirGapLogDetailVm { get; set; }
    public IMapper Mapper { get; set; }

    public CyberAirGapLogFixture()
    {
        // Initialize manual test data with known values for reliable testing
        CyberAirGapLogs = new List<CyberAirGapLog>
        {
            new CyberAirGapLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                AirGapId = Guid.NewGuid().ToString(),
                AirGapName = "TestAirGap_Log01",
                Description = "Test Air Gap Log for Unit Testing",
                SourceSiteId = Guid.NewGuid().ToString(),
                SourceSiteName = "Source Site Log 01",
                TargetSiteId = Guid.NewGuid().ToString(),
                TargetSiteName = "Target Site Log 01",
                Port = 8080,
                Source = "{\"serverId\":\"log-server-001\",\"componentId\":\"log-comp-001\"}",
                Target = "{\"serverId\":\"log-server-002\",\"componentId\":\"log-comp-002\"}",
                SourceComponentId = Guid.NewGuid().ToString(),
                SourceComponentName = "Source Component Log 01",
                TargetComponentId = Guid.NewGuid().ToString(),
                TargetComponentName = "Target Component Log 01",
                EnableWorkflowId = Guid.NewGuid().ToString(),
                DisableWorkflowId = Guid.NewGuid().ToString(),
                ErrorMessage = null,
                WorkflowStatus = "Completed",
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now.AddHours(-1),
                RPO = "5 minutes",
                Status = "Active",
                IsFileTransfered = true,
                IsActive = true,
                CreatedBy = "TestUser",
                CreatedDate = DateTime.Now.AddDays(-1),
                LastModifiedBy = "TestUser",
                LastModifiedDate = DateTime.Now.AddDays(-1)
            }
        };

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalLogs = AutoCyberAirGapLogFixture.CreateMany<CyberAirGapLog>(2).ToList();
            CyberAirGapLogs.AddRange(additionalLogs);
            
            UserActivities = AutoCyberAirGapLogFixture.CreateMany<UserActivity>(3).ToList();
            CreateCyberAirGapLogCommand = AutoCyberAirGapLogFixture.Create<CreateCyberAirGapLogCommand>();
            UpdateCyberAirGapLogCommand = AutoCyberAirGapLogFixture.Create<UpdateCyberAirGapLogCommand>();
            DeleteCyberAirGapLogCommand = AutoCyberAirGapLogFixture.Create<DeleteCyberAirGapLogCommand>();
            CyberAirGapLogListVm = AutoCyberAirGapLogFixture.Create<CyberAirGapLogListVm>();
            CyberAirGapLogDetailVm = AutoCyberAirGapLogFixture.Create<CyberAirGapLogDetailVm>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateCyberAirGapLogCommand = new CreateCyberAirGapLogCommand();
            UpdateCyberAirGapLogCommand = new UpdateCyberAirGapLogCommand();
            DeleteCyberAirGapLogCommand = new DeleteCyberAirGapLogCommand();
            CyberAirGapLogListVm = new CyberAirGapLogListVm();
            CyberAirGapLogDetailVm = new CyberAirGapLogDetailVm();
        }

        // Configure AutoMapper for CyberAirGapLog mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CyberAirGapLogProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    /// <summary>
    /// AutoFixture configuration for CyberAirGapLog entities and commands
    /// Handles circular references and provides realistic test data
    /// </summary>
    public Fixture AutoCyberAirGapLogFixture
    {
        get
        {
            var fixture = new Fixture();
            
            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations for commands
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateCyberAirGapLogCommand>(p => p.AirGapName, 100));
            fixture.Customize<CreateCyberAirGapLogCommand>(c => c
                .With(b => b.AirGapId, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapName, () => $"TestAirGapLog{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Test Air Gap Log Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"SourceSiteLog{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"TargetSiteLog{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"log-server-{fixture.Create<int>():000}\",\"componentId\":\"log-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"log-server-{fixture.Create<int>():000}\",\"componentId\":\"log-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"SourceComponentLog{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"TargetComponentLog{fixture.Create<int>():00}")
                //.With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                //.With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString())
                //.With(b => b.LogLevel, "Info")
                //.With(b => b.LogMessage, () => $"Test log message {fixture.Create<int>()}")
                //.With(b => b.LogData, () => $"{{\"operation\":\"test-{fixture.Create<int>()}\",\"status\":\"success\"}}")
                );

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateCyberAirGapLogCommand>(p => p.AirGapName, 100));
            fixture.Customize<UpdateCyberAirGapLogCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapId, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapName, () => $"UpdatedAirGapLog{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Updated Air Gap Log Description {fixture.Create<int>()}")
                //.With(b => b.LogLevel, "Warning")
                //.With(b => b.LogMessage, () => $"Updated log message {fixture.Create<int>()}")
                //.With(b => b.LogData, () => $"{{\"operation\":\"update-{fixture.Create<int>()}\",\"status\":\"updated\"}}")
                );

            fixture.Customize<DeleteCyberAirGapLogCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            // CyberAirGapLog entity customizations
            fixture.Customize<CyberAirGapLog>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.AirGapId, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapName, () => $"TestAirGapLog{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Test Air Gap Log Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"SourceSiteLog{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"TargetSiteLog{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"log-server-{fixture.Create<int>():000}\",\"componentId\":\"log-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"log-server-{fixture.Create<int>():000}\",\"componentId\":\"log-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"SourceComponentLog{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"TargetComponentLog{fixture.Create<int>():00}")
                .With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                .With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString())
                //.With(b => b.LogLevel, () => new[] { "Debug", "Info", "Warning", "Error", "Critical" }[fixture.Create<int>() % 5])
                //.With(b => b.LogMessage, () => $"Test log message {fixture.Create<int>()}")
                //.With(b => b.LogData, () => $"{{\"operation\":\"test-{fixture.Create<int>()}\",\"status\":\"success\"}}")
                //.With(b => b.Timestamp, () => DateTime.UtcNow.AddMinutes(-fixture.Create<int>() % 1440))
                );

            // UserActivity customization for CyberAirGapLog
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "CyberAirGapLog")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test cyber air gap log activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            // CyberAirGapLogListVm customizations
            fixture.Customize<CyberAirGapLogListVm>(c => c
                .With(a => a.Id, () => Guid.NewGuid().ToString())
                .With(a => a.AirGapId, () => Guid.NewGuid().ToString())
                .With(a => a.AirGapName, () => $"TestAirGapLogList{fixture.Create<int>():000}")
                .With(a => a.SourceSiteName, () => $"SourceSiteList{fixture.Create<int>():00}")
                .With(a => a.TargetSiteName, () => $"TargetSiteList{fixture.Create<int>():00}")
                .With(a => a.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(a => a.Description, () => $"Test Air Gap Log List Description {fixture.Create<int>()}")
                .With(a => a.Status, "Active")
                .With(a => a.IsFileTransfered, true));

            // CyberAirGapLogDetailVm customizations
            fixture.Customize<CyberAirGapLogDetailVm>(c => c
                .With(a => a.Id, () => Guid.NewGuid().ToString())
                .With(a => a.AirGapId, () => Guid.NewGuid().ToString())
                .With(a => a.AirGapName, () => $"TestAirGapLogDetail{fixture.Create<int>():000}")
                .With(a => a.SourceSiteName, () => $"SourceSiteDetail{fixture.Create<int>():00}")
                .With(a => a.TargetSiteName, () => $"TargetSiteDetail{fixture.Create<int>():00}")
                .With(a => a.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(a => a.Description, () => $"Test Air Gap Log Detail Description {fixture.Create<int>()}")
                .With(a => a.Status, "Active")
                .With(a => a.IsFileTransfered, true)
                .With(a => a.WorkflowStatus, "Completed")
                .With(a => a.RPO, "5 minutes")
                .With(a => a.StartTime, () => DateTime.Now.AddHours(-2))
                .With(a => a.EndTime, () => DateTime.Now.AddHours(-1)));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
