﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Persistence.Repositories;

public class ReplicationViewRepository : BaseRepository<ReplicationView>, IReplicationViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IInfraObjectViewRepository _infraObjectViewRepository;

    public ReplicationViewRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService,IInfraObjectViewRepository infraObjectViewRepository) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _infraObjectViewRepository = infraObjectViewRepository;
    }
    public override async Task<IReadOnlyList<ReplicationView>> ListAllAsync()
    {
        var replication = SelectReplicationView(base.QueryAll(replica =>
            replica.CompanyId.Equals(_loggedInUserService.CompanyId)));               

        return _loggedInUserService.IsAllInfra
            ? await replication.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replication);
    }

    public async Task<List<ReplicationView>> GetByReplicationIdsAsync(List<string> ids)
    {
        return await (_loggedInUserService.IsParent
                ? base.FilterBy(x => ids.Contains(x.ReferenceId))
                : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && ids.Contains(x.ReferenceId)))
            .Select(x => new ReplicationView
            {
                ReferenceId = x.ReferenceId,
                Name = x.Name,
                Type = x.Type,
            }).ToListAsync();
    }


    public async Task<int> ReplicationCountAsync()
    {
        var replication = SelectReplicationView(base.QueryAll(replica =>
            replica.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await replication.CountAsync()
            : GetAssignedBusinessServicesByReplications(replication).Count;
    }

    public async Task<List<ReplicationView>> GetReplicationNames()
    {
        var replication = base
            .QueryAll(replica => replica.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new ReplicationView { ReferenceId = x.ReferenceId, Name = x.Name });

        return _loggedInUserService.IsAllInfra
            ? await replication.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replication).ToList();
    }
    public async Task<List<ReplicationView>> GetType(string typeId)
    {
        var replication = SelectReplicationView(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.TypeId.Equals(typeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.TypeId.Equals(typeId)));

      
        return _loggedInUserService.IsAllInfra
            ? await replication.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replication).ToList();
    }
    public async Task<IReadOnlyList<ReplicationView>> GetReplicationList()
    {
        var replication = base.QueryAll(replica =>
            replica.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new ReplicationView {ReferenceId=x.ReferenceId,Name=x.Name, Type =x.Type, TypeId=x.TypeId, Properties=x.Properties, BusinessServiceId = x.BusinessServiceId });

        return _loggedInUserService.IsAllInfra
            ? await replication.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replication);
    }
    public async Task<List<ReplicationView>> GetReplicationListByLicenseKey(string licenseId)
    {
        var replication = SelectReplicationView(_loggedInUserService.IsParent
              ? base.FilterBy(x => x.LicenseId.Equals(licenseId))
              : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.LicenseId.Equals(licenseId)));

             return _loggedInUserService.IsAllInfra
            ? await replication.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replication).ToList();
    }
    public async Task<List<ReplicationView>> GetReplicationBySiteId(string siteId)
    {
        var replication = SelectReplicationView(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.SiteId.Equals(siteId))
            : base.FilterBy(x => x.SiteId.Equals(siteId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await replication.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replication).ToList();
    }
    public async Task<List<ReplicationView>> GetReplicationByBusinessServiceId(string businessServiceId)
    {
        var replication = SelectReplicationView(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

       return _loggedInUserService.IsAllInfra
            ? await replication.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replication).ToList();
    }

    public async Task<PaginatedResult<ReplicationView>> GetReplicationByType(string typeId, int pageNumber, int pageSize, Specification<ReplicationView> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await SelectReplicationView(_loggedInUserService.IsAllInfra
                ? Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId)).DescOrderById()
                : GetPaginatedAssignedBusinessServicesByReplications(Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId))
                    .DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }
        return await SelectReplicationView(_loggedInUserService.IsAllInfra
                ? Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
                : GetPaginatedAssignedBusinessServicesByReplications(Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId)
                    && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override async Task<PaginatedResult<ReplicationView>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<ReplicationView> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await SelectReplicationView(_loggedInUserService.IsAllInfra
                ? Entities.Specify(productFilterSpec).DescOrderById()
                : GetPaginatedAssignedBusinessServicesByReplications(Entities.Specify(productFilterSpec)
                    .DescOrderById()))
                   .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }
        return await SelectReplicationView(_loggedInUserService.IsAllInfra
               ? Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
               : GetPaginatedAssignedBusinessServicesByReplications(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                   .DescOrderById()))
                   .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    private IReadOnlyList<ReplicationView> GetAssignedBusinessServicesByReplications(
        IQueryable<ReplicationView> businessServices)
    {
        var replications = new List<ReplicationView>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                replications.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                      where businessService.BusinessServiceId == assignedBusinessService.Id
                                      select businessService);

        var infraObjects = _infraObjectViewRepository.GetPaginatedQuery();

        replications = replications.Where(replication => infraObjects.Any(x =>
        x.ReplicationProperties.Contains( replication.ReferenceId))).ToList();

        //replications = replications.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRReplicationId) ||
        //    server.ReferenceId.Equals(x.DRReplicationId) ||
        //    server.ReferenceId.Equals(x.NearDRReplicationId))).ToList();

        return replications;
    }
    private IQueryable<ReplicationView> GetPaginatedAssignedBusinessServicesByReplications(
        IQueryable<ReplicationView> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

        businessServices = businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));

        var infraObjects = _infraObjectViewRepository.GetPaginatedQuery();

        businessServices = businessServices.Where(replication => infraObjects.Any(x =>x.ReplicationProperties.Contains(replication.ReferenceId)));
        //businessServices = businessServices.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRReplicationId) ||
        //    server.ReferenceId.Equals(x.DRReplicationId) ||
        //    server.ReferenceId.Equals(x.NearDRReplicationId)));

        return businessServices;
    }
    public static IQueryable<ReplicationView> SelectReplicationView(IQueryable<ReplicationView> query)
    {
        return query.Select(x => new ReplicationView
        {
            Id=x.Id,
            ReferenceId=x.ReferenceId,
            Name=x.Name,
            CompanyId=x.CompanyId,
            TypeId=x.TypeId,
            Type=x.Type,
            SiteId=x.SiteId,
            SiteName=x.SiteName,
            Properties=x.Properties,
            LicenseId=x.LicenseId,
            LicenseKey=x.LicenseKey,
            BusinessServiceId=x.BusinessServiceId,
            BusinessServiceName=x.BusinessServiceName,
            FormVersion=x.FormVersion
        });
    }
}
