﻿using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalSetting.Queries;

public class GetGlobalSettingDetailQueryHandlerTests : IClassFixture<GlobalSettingFixture>
{
    private readonly GlobalSettingFixture _globalSettingFixture;
    private readonly Mock<IGlobalSettingRepository> _mockGlobalSettingRepository;
    private readonly GetGlobalSettingDetailQueryHandler _handler;
    private readonly GetGlobalSettingDetailQueryHandler _invalidHandler;

    public GetGlobalSettingDetailQueryHandlerTests(GlobalSettingFixture globalSettingFixture)
    {
        _globalSettingFixture = globalSettingFixture;

        _mockGlobalSettingRepository = GlobalSettingRepositoryMocks.GetGlobalSettingRepository(_globalSettingFixture.GlobalSettings);

        _handler = new GetGlobalSettingDetailQueryHandler(_globalSettingFixture.Mapper, _mockGlobalSettingRepository.Object);

        var mockInvalidGlobalSettingRepository = GlobalSettingRepositoryMocks.GetGlobalSettingRepository(_globalSettingFixture.InvalidGlobalSettings);

        _invalidHandler = new GetGlobalSettingDetailQueryHandler(_globalSettingFixture.Mapper, mockInvalidGlobalSettingRepository.Object);

        _globalSettingFixture.GlobalSettings[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
    }

    [Fact]
    public async Task Handle_Return_GlobalSettingDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetGlobalSettingDetailQuery { Id = _globalSettingFixture.GlobalSettings[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<GlobalSettingDetailVm>();
        result.Id.ShouldBeGreaterThan(0.ToString());
        result.GlobalSettingKey.ShouldNotBeEmpty();
        result.GlobalSettingValue.ShouldNotBeEmpty();
        result.LoginUserId.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidGlobalSettingId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetGlobalSettingDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        await _handler.Handle(new GetGlobalSettingDetailQuery { Id = _globalSettingFixture.GlobalSettings[0].ReferenceId }, CancellationToken.None);

        _mockGlobalSettingRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_CorrectGlobalSettingProperties_When_ValidId()
    {
        // Arrange
        var testGlobalSetting = _globalSettingFixture.GlobalSettings[0];
        testGlobalSetting.GlobalSettingKey = "TestKey";
        testGlobalSetting.GlobalSettingValue = "TestValue";
        testGlobalSetting.LoginUserId = "TestUser";
        testGlobalSetting.PasswordProtection = "TestPassword";

        // Act
        var result = await _handler.Handle(new GetGlobalSettingDetailQuery { Id = testGlobalSetting.ReferenceId }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<GlobalSettingDetailVm>();
        result.Id.ShouldBe(testGlobalSetting.ReferenceId);
        result.GlobalSettingKey.ShouldBe("TestKey");
        result.GlobalSettingValue.ShouldBe("TestValue");
        result.LoginUserId.ShouldBe("TestUser");
        result.PasswordProtection.ShouldBe("TestPassword");
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InactiveGlobalSetting()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _invalidHandler.Handle(new GetGlobalSettingDetailQuery { Id = _globalSettingFixture.InvalidGlobalSettings[0].ReferenceId }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Return_EmptyPasswordProtection_When_PasswordProtectionIsNull()
    {
        // Arrange
        var testGlobalSetting = _globalSettingFixture.GlobalSettings[0];
        testGlobalSetting.PasswordProtection = null;

        // Act
        var result = await _handler.Handle(new GetGlobalSettingDetailQuery { Id = testGlobalSetting.ReferenceId }, CancellationToken.None);

        // Assert
        result.PasswordProtection.ShouldBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Return_GlobalSettingDetailVm_With_AllProperties_Mapped()
    {
        // Arrange
        var testGlobalSetting = _globalSettingFixture.GlobalSettings[0];

        // Act
        var result = await _handler.Handle(new GetGlobalSettingDetailQuery { Id = testGlobalSetting.ReferenceId }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<GlobalSettingDetailVm>();
        result.Id.ShouldNotBeNullOrEmpty();
        result.GlobalSettingKey.ShouldNotBeNullOrEmpty();
        result.GlobalSettingValue.ShouldNotBeNullOrEmpty();
        result.LoginUserId.ShouldNotBeNullOrEmpty();
        // PasswordProtection can be null or empty, so we don't assert on it
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_NullId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetGlobalSettingDetailQuery { Id = null }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_EmptyId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetGlobalSettingDetailQuery { Id = string.Empty }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Return_CorrectMappedData_When_AutoMapperUsed()
    {
        // Arrange
        var testGlobalSetting = _globalSettingFixture.GlobalSettings[0];

        // Act
        var result = await _handler.Handle(new GetGlobalSettingDetailQuery { Id = testGlobalSetting.ReferenceId }, CancellationToken.None);

        // Assert - Verify AutoMapper correctly mapped the entity to ViewModel
        result.Id.ShouldBe(testGlobalSetting.ReferenceId);
        result.GlobalSettingKey.ShouldBe(testGlobalSetting.GlobalSettingKey);
        result.GlobalSettingValue.ShouldBe(testGlobalSetting.GlobalSettingValue);
        result.LoginUserId.ShouldBe(testGlobalSetting.LoginUserId);
        result.PasswordProtection.ShouldBe(testGlobalSetting.PasswordProtection);
    }
}