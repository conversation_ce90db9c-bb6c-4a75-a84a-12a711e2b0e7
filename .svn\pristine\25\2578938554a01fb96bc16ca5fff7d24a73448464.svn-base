﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.UpdateWorkflowProfilePassword;
using ContinuityPatrol.Application.Features.WorkflowProfile.Events.UpdatePassword;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Commands
{
    public class UpdateWorkflowProfilePasswordTests
    {
        private readonly Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;

        private readonly Mock<IPublisher> _mockPublisher;

        private readonly UpdateWorkflowProfilePasswordCommandHandler _handler;

        public UpdateWorkflowProfilePasswordTests()
        {
            _mockWorkflowProfileRepository = new Mock<IWorkflowProfileRepository>();

            _mockPublisher = new Mock<IPublisher>();

            _handler = new UpdateWorkflowProfilePasswordCommandHandler(_mockPublisher.Object, _mockWorkflowProfileRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidPasswordException_WhenOldPasswordIsIncorrect()
        {
            var command = new UpdateWorkflowProfilePasswordCommand
            {
                Id = Guid.NewGuid().ToString(),
                OldPassword = "Admin@321",
                NewPassword = "Admin@123",
                ConfirmPassword = "Admin@123"
            };

            var existingProfile = new Domain.Entities.WorkflowProfile { Password = SecurityHelper.Encrypt("Admin@321") };

            _mockWorkflowProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id)).ReturnsAsync(existingProfile);

            await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidPasswordException_WhenNewPasswordMatchesOldPassword()
        {
            var command = new UpdateWorkflowProfilePasswordCommand
            {
                Id = Guid.NewGuid().ToString(),
                OldPassword = "Admin@321",
                NewPassword = "Admin@123",
                ConfirmPassword = "Admin@123"
            };

            var existingProfile = new Domain.Entities.WorkflowProfile { Password = SecurityHelper.Encrypt("Admin@321") };

            _mockWorkflowProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id)).ReturnsAsync(existingProfile);

            await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidPasswordException_WhenNewPasswordAndConfirmPasswordDoNotMatch()
        {
            var command = new UpdateWorkflowProfilePasswordCommand
            {
                Id = Guid.NewGuid().ToString(),
                OldPassword = "Admin@321",
                NewPassword = "Admin@123",
                ConfirmPassword = "Admin@123"
            };

            var existingProfile = new Domain.Entities.WorkflowProfile { Password = SecurityHelper.Encrypt("Admin@321") };

            _mockWorkflowProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id)).ReturnsAsync(existingProfile);

            await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldUpdatePasswordAndPublishEvent_WhenPasswordIsValid()
        {
            var command = new UpdateWorkflowProfilePasswordCommand
            {
                Id = Guid.NewGuid().ToString(),
                OldPassword = "hVDxKXjEcMZPiwHc0IvA493n0zc3ikPfCR/FB8CzCXA=$U6L7TlsYuOq5zo/BQaoOeDLrHaQf4PjEgyBv1l17ltIDK6FDyQ==",
                NewPassword = "Admin@123",
                ConfirmPassword = "Admin@123"
            };
            var existingProfile = new Domain.Entities.WorkflowProfile
            {
                Password = "hVDxKXjEcMZPiwHc0IvA493n0zc3ikPfCR/FB8CzCXA=$U6L7TlsYuOq5zo/BQaoOeDLrHaQf4PjEgyBv1l17ltIDK6FDyQ==",
                ReferenceId = command.Id,
                Name = "TestProfile"
            };
            _mockWorkflowProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id)).ReturnsAsync(existingProfile);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.Equal("Workflow profile 'TestProfile' password has been updated successfully.", result.Message);

            Assert.Equal(command.Id, result.Id);

            _mockWorkflowProfileRepository.Verify(repo => repo.UpdateAsync(existingProfile), Times.Once);

            _mockPublisher.Verify(p => p.Publish(It.IsAny<UpdatePasswordEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidPasswordException_When_NewPasswordIsSameAsOld()
        {
            // Arrange
            var password = "same-password";
            var command = new UpdateWorkflowProfilePasswordCommand
            {
                Id = "profile-id",
                OldPassword = SecurityHelper.Encrypt(password),
                NewPassword = SecurityHelper.Encrypt(password),
                ConfirmPassword = SecurityHelper.Encrypt(password)
            };

            var existing = new Domain.Entities.WorkflowProfile
            {
                ReferenceId = "profile-id",
                Password = SecurityHelper.Encrypt(password)
            };

            _mockWorkflowProfileRepository
                .Setup(x => x.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existing);

            // Act & Assert
            var ex = await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(command, CancellationToken.None));
            ex.Message.ShouldBe(Authentication.PasswordUnique);
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidPasswordException_When_NewAndConfirmPasswordsMismatch()
        {
            // Arrange
            var command = new UpdateWorkflowProfilePasswordCommand
            {
                Id = "profile-id",
                OldPassword = SecurityHelper.Encrypt("correct-old"),
                NewPassword = SecurityHelper.Encrypt("new-password"),
                ConfirmPassword = SecurityHelper.Encrypt("different-password")
            };

            var existing = new Domain.Entities.WorkflowProfile
            {
                ReferenceId = "profile-id",
                Password = SecurityHelper.Encrypt("correct-old")
            };

            _mockWorkflowProfileRepository
                .Setup(x => x.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existing);

            // Act & Assert
            var ex = await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(command, CancellationToken.None));
            ex.Message.ShouldBe(Authentication.InvalidConfirmPassword);
        }
        [Fact]
        public async Task Handle_ShouldThrowInvalidPasswordException_When_OldPasswordDoesNotMatch()
        {
            // Arrange
            var command = new UpdateWorkflowProfilePasswordCommand
            {
                Id = "profile-id",
                OldPassword = SecurityHelper.Encrypt("wrong-old-password"),
                NewPassword = SecurityHelper.Encrypt("new-password"),
                ConfirmPassword = SecurityHelper.Encrypt("new-password")
            };

            var existing = new Domain.Entities.WorkflowProfile
            {
                ReferenceId = "profile-id",
                Password = SecurityHelper.Encrypt("correct-old-password")
            };

            _mockWorkflowProfileRepository
                .Setup(x => x.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existing);

            // Act & Assert
            var ex = await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(command, CancellationToken.None));
            ex.Message.ShouldBe(Authentication.InvalidOldPassword);
        }

    }
}
