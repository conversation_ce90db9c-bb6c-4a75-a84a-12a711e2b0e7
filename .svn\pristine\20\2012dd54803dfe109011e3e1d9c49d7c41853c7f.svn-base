﻿using ContinuityPatrol.Application.Features.WorkflowTemp.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowTemp.Commands
{
    public class UpdateWorkflowTempTest : IClassFixture<WorkflowTempFixture>
    {
        private readonly Mock<IWorkflowTempRepository> _mockWorkflowTempRepository;

        private readonly WorkflowTempFixture _workflowTempFixture;

        private readonly UpdateWorkflowTempCommandHandler _handler;

        public UpdateWorkflowTempTest(WorkflowTempFixture workflowTempFixture)
        {
            _workflowTempFixture = workflowTempFixture;

            var mockPublisher = new Mock<IPublisher>();

            _mockWorkflowTempRepository = WorkflowTempRepositoryMocks.UpdateWorkflowTempRepository(_workflowTempFixture.WorkflowTempInfos);

            _handler = new UpdateWorkflowTempCommandHandler(_workflowTempFixture.Mapper,_mockWorkflowTempRepository.Object, mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ValidWorkflowProfileInfo_UpdatedTo_WorkflowProfileInfoRepo()
        {
            _workflowTempFixture.UpdateWorkflowTempCommand.Id = _workflowTempFixture.WorkflowTempInfos[0].ReferenceId;

            var result = await _handler.Handle(_workflowTempFixture.UpdateWorkflowTempCommand, CancellationToken.None);

            var workflowProfileInfo = await _mockWorkflowTempRepository.Object.GetByReferenceIdAsync(result.Id);

            Assert.Equal(_workflowTempFixture.UpdateWorkflowTempCommand.Name, workflowProfileInfo.Name);
        }

        [Fact]
        public async Task Handle_Return_Valid_WorkflowProfileInfoResponse()
        {
            _workflowTempFixture.UpdateWorkflowTempCommand.Id = _workflowTempFixture.WorkflowTempInfos[0].ReferenceId;

            var result = await _handler.Handle(_workflowTempFixture.UpdateWorkflowTempCommand, CancellationToken.None);

            result.ShouldBeOfType(typeof(UpdateWorkflowTempResponse));

            result.Id.ShouldBe(_workflowTempFixture.UpdateWorkflowTempCommand.Id);

            var expectedMessage = $" WorkflowTemp '{_workflowTempFixture.WorkflowTempInfos[0].Name}' has been updated successfully";

            result.Message.ShouldBe(expectedMessage);
        }

        [Fact]
        public async Task Handle_Throw_NotFoundException_When_Invalid_WorkflowProfileInfoId()
        {
            _workflowTempFixture.UpdateWorkflowTempCommand.Id = int.MaxValue.ToString();

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_workflowTempFixture.UpdateWorkflowTempCommand, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
        {
            _workflowTempFixture.UpdateWorkflowTempCommand.Id = _workflowTempFixture.WorkflowTempInfos[0].ReferenceId;

            await _handler.Handle(_workflowTempFixture.UpdateWorkflowTempCommand, CancellationToken.None);

            _mockWorkflowTempRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

            _mockWorkflowTempRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowTemp>()), Times.Once);
        }
    }
}
