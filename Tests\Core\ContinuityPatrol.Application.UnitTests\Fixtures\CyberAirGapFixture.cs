using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.IsAttached;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.UpdateStatus;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

/// <summary>
/// Fixture class for CyberAirGap module testing
/// Provides test data setup and AutoFixture configuration for comprehensive unit testing
/// </summary>
public class CyberAirGapFixture : IDisposable
{
    public List<CyberAirGap> CyberAirGaps { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public List<CyberAirGapStatus> CyberAirGapStatuses { get; set; }
    public List<CyberAirGapLog> CyberAirGapLogs { get; set; }
    public CreateCyberAirGapCommand CreateCyberAirGapCommand { get; set; }
    public UpdateCyberAirGapCommand UpdateCyberAirGapCommand { get; set; }
    public DeleteCyberAirGapCommand DeleteCyberAirGapCommand { get; set; }
    public AirGapAttachedCommand IsAttachedCyberAirGapCommand { get; set; }
    public AirGapStatusUpdateCommand AirGapStatusUpdateCommand { get; set; }
    public IMapper Mapper { get; set; }

    public CyberAirGapFixture()
    {
        // Initialize manual test data with known values for reliable testing
        CyberAirGaps = new List<CyberAirGap>
        {
            new CyberAirGap
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "TestAirGap01",
                Description = "Test Air Gap for Unit Testing",
                SourceSiteId = Guid.NewGuid().ToString(),
                SourceSiteName = "Source Site 01",
                TargetSiteId = Guid.NewGuid().ToString(),
                TargetSiteName = "Target Site 01",
                Port = 8080,
                Source = "{\"serverId\":\"server-001\",\"componentId\":\"comp-001\"}",
                Target = "{\"serverId\":\"server-002\",\"componentId\":\"comp-002\"}",
                SourceComponentId = Guid.NewGuid().ToString(),
                SourceComponentName = "Source Component 01",
                TargetComponentId = Guid.NewGuid().ToString(),
                TargetComponentName = "Target Component 01",
                WorkflowStatus = "Active",
                EnableWorkflowId = Guid.NewGuid().ToString(),
                DisableWorkflowId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalAirGaps = AutoCyberAirGapFixture.CreateMany<CyberAirGap>(2).ToList();
            CyberAirGaps.AddRange(additionalAirGaps);
            
            UserActivities = AutoCyberAirGapFixture.CreateMany<UserActivity>(3).ToList();
            CyberAirGapStatuses = AutoCyberAirGapFixture.CreateMany<CyberAirGapStatus>(3).ToList();
            CyberAirGapLogs = AutoCyberAirGapFixture.CreateMany<CyberAirGapLog>(3).ToList();
            CreateCyberAirGapCommand = AutoCyberAirGapFixture.Create<CreateCyberAirGapCommand>();
            UpdateCyberAirGapCommand = AutoCyberAirGapFixture.Create<UpdateCyberAirGapCommand>();
            DeleteCyberAirGapCommand = AutoCyberAirGapFixture.Create<DeleteCyberAirGapCommand>();
            IsAttachedCyberAirGapCommand = AutoCyberAirGapFixture.Create<AirGapAttachedCommand>();
            AirGapStatusUpdateCommand = AutoCyberAirGapFixture.Create<AirGapStatusUpdateCommand>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CyberAirGapStatuses = new List<CyberAirGapStatus>();
            CyberAirGapLogs = new List<CyberAirGapLog>();
            CreateCyberAirGapCommand = new CreateCyberAirGapCommand();
            UpdateCyberAirGapCommand = new UpdateCyberAirGapCommand();
            DeleteCyberAirGapCommand = new DeleteCyberAirGapCommand();
            IsAttachedCyberAirGapCommand = new AirGapAttachedCommand();
            AirGapStatusUpdateCommand = new AirGapStatusUpdateCommand();
        }

        // Configure AutoMapper for CyberAirGap mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CyberAirGapProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    /// <summary>
    /// AutoFixture configuration for CyberAirGap entities and commands
    /// Handles circular references and provides realistic test data
    /// </summary>
    public Fixture AutoCyberAirGapFixture
    {
        get
        {
            var fixture = new Fixture();
            
            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations for commands
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateCyberAirGapCommand>(p => p.Name, 100));
            fixture.Customize<CreateCyberAirGapCommand>(c => c
                .With(b => b.Name, () => $"TestAirGap{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Test Air Gap Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"SourceSite{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"TargetSite{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"server-{fixture.Create<int>():000}\",\"componentId\":\"comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"server-{fixture.Create<int>():000}\",\"componentId\":\"comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"SourceComponent{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"TargetComponent{fixture.Create<int>():00}")
                .With(b => b.WorkflowStatus, "Active")
                .With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                .With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString()));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateCyberAirGapCommand>(p => p.Name, 100));
            fixture.Customize<UpdateCyberAirGapCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.Name, () => $"UpdatedAirGap{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Updated Air Gap Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"UpdatedSourceSite{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"UpdatedTargetSite{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"updated-server-{fixture.Create<int>():000}\",\"componentId\":\"updated-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"updated-server-{fixture.Create<int>():000}\",\"componentId\":\"updated-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"UpdatedSourceComponent{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"UpdatedTargetComponent{fixture.Create<int>():00}")
                .With(b => b.WorkflowStatus, "Updated")
                .With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                .With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString()));

            fixture.Customize<DeleteCyberAirGapCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));
            
            fixture.Customize<AirGapAttachedCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            fixture.Customize<AirGapStatusUpdateCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.Status, () => $"Status{fixture.Create<int>():00}"));

            // CyberAirGap entity customizations
            fixture.Customize<CyberAirGap>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.Name, () => $"TestAirGap{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Test Air Gap Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"SourceSite{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"TargetSite{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"server-{fixture.Create<int>():000}\",\"componentId\":\"comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"server-{fixture.Create<int>():000}\",\"componentId\":\"comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"SourceComponent{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"TargetComponent{fixture.Create<int>():00}")
                .With(b => b.WorkflowStatus, "Active")
                .With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                .With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString()));

            // CyberAirGapStatus entity customizations
            fixture.Customize<CyberAirGapStatus>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.AirGapId, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapName, () => $"StatusAirGap{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Status Air Gap Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"StatusSourceSite{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"StatusTargetSite{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"status-server-{fixture.Create<int>():000}\",\"componentId\":\"status-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"status-server-{fixture.Create<int>():000}\",\"componentId\":\"status-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"StatusSourceComponent{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"StatusTargetComponent{fixture.Create<int>():00}")
                .With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                .With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString()));

            // CyberAirGapLog entity customizations
            fixture.Customize<CyberAirGapLog>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.AirGapId, () => Guid.NewGuid().ToString())
                .With(b => b.AirGapName, () => $"LogAirGap{fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Log Air Gap Description {fixture.Create<int>()}")
                .With(b => b.SourceSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceSiteName, () => $"LogSourceSite{fixture.Create<int>():00}")
                .With(b => b.TargetSiteId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetSiteName, () => $"LogTargetSite{fixture.Create<int>():00}")
                .With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                .With(b => b.Source, () => $"{{\"serverId\":\"log-server-{fixture.Create<int>():000}\",\"componentId\":\"log-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.Target, () => $"{{\"serverId\":\"log-server-{fixture.Create<int>():000}\",\"componentId\":\"log-comp-{fixture.Create<int>():000}\"}}")
                .With(b => b.SourceComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.SourceComponentName, () => $"LogSourceComponent{fixture.Create<int>():00}")
                .With(b => b.TargetComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.TargetComponentName, () => $"LogTargetComponent{fixture.Create<int>():00}")
                .With(b => b.EnableWorkflowId, () => Guid.NewGuid().ToString())
                .With(b => b.DisableWorkflowId, () => Guid.NewGuid().ToString()));

            // UserActivity customization for CyberAirGap
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "CyberAirGap")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test cyber air gap activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
