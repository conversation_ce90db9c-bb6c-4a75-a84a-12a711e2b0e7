﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class ReplicationMasterRepository : BaseRepository<ReplicationMaster>, IReplicationMasterRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ReplicationMasterRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<ReplicationMaster>> GetReplicationNames()
    {
        if (!_loggedInUserService.IsParent)
            return await  _dbContext.ReplicationMasters.Active()
                .Select(x => new ReplicationMaster { ReferenceId = x.ReferenceId, Name = x.Name })
                .OrderBy(x => x.Name)
                .ToListAsync();

        return await  _dbContext.ReplicationMasters.Active()
            .Select(x => new ReplicationMaster { ReferenceId = x.ReferenceId, Name = x.Name })
            .OrderBy(x => x.Name)
            .ToListAsync();
    }

    public Task<bool> IsReplicationMasterNameUnique(string name)
    {
        var matches = _dbContext.ReplicationMasters.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<bool> IsReplicationMasterNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? _dbContext.ReplicationMasters.Any(e => e.Name.Equals(name))
            : _dbContext.ReplicationMasters.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public async Task<List<ReplicationMaster>> GetReplicationMasterByInfraMasterName(string infraMasterName)
    {
        if (string.IsNullOrWhiteSpace(infraMasterName))
            return new List<ReplicationMaster>();

        var lowerName = infraMasterName.Trim().ToLower();

        var result =await _dbContext.ReplicationMasters.Active()
            .Where(x => x.InfraMasterName != null && x.InfraMasterName.Trim().ToLower() == lowerName)
            .Select(x => new ReplicationMaster {Id=x.Id, ReferenceId = x.ReferenceId, 
                Name = x.Name,InfraMasterId=x.InfraMasterId,InfraMasterName=x.InfraMasterName })
            .ToListAsync();


        return result;
    }
}