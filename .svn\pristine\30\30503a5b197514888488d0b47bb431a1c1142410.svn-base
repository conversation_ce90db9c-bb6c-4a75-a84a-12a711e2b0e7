﻿QUnit.module("DataSync.js Tests", hooks => {
    let server;

    hooks.beforeEach(() => {
        server = sinon.fakeServer.create();

        // minimal DOM elements
        $("#qunit-fixture").append(`
            <input id="search-inp">
            <button class="edit-button"></button>
            <button class="delete-button"></button>
            <button id="btnDataSyncCreate"></button>
            <input id="dataSyncName">
            <input id="dataSyncId">
            <select id="dataSyncReplicationType"></select>
            <input id="txtSSHPr">
            <input id="txtSSHDr">
            <input id="txtThreads">
            <input id="txtFileInclude">
            <input id="txtFileExclude">
            <input id="txtFileDelete">
            <input id="prShellPrompt">
            <input id="drShellPrompt">
            <button id="dataSyncSave"></button>
            <div id="CreateModal"></div>
            <div id="DeleteModal"></div>
            <div id="deleteData"></div>
            <input id="textDeleteId">
        `);

        $("#CreateModal").modal = sinon.stub();
        $("#DeleteModal").modal = sinon.stub();
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("1. Search input triggers reload", assert => {
        const done = assert.async();
        $('#search-inp').val('Test').trigger('input');
        setTimeout(() => {
            assert.ok(true, "Debounced search handled");
            done();
        }, 600);
    });

    QUnit.test("2. Edit button opens modal & populates", assert => {
        const done = assert.async();

        // Create DOM before DataSync.js binds
        $("#qunit-fixture").append(`
        <table id="dataSyncTable">
            <tr><td><button class="edit-button"></button></td></tr>
        </table>
        <input id="dataSyncName">
    `);

        const data = {
            id: 1,
            name: "Test",
            replicationType: "Application",
            properties: JSON.stringify([{ FilterOption: "None" }])
        };

        $(".edit-button").data("datasync", data);

        // force binding if DataSync.js already loaded
        $('#dataSyncTable').off('click').on('click', '.edit-button', function () {
            dataSync = $(this).data("datasync");
            dataSyncEdit(dataSync);
        });

        $(".edit-button").trigger("click");

        setTimeout(() => {
            assert.strictEqual($("#dataSyncName").val(), "Test", "Edit populated name");
            done();
        }, 10);
    });


    QUnit.test("3. Delete button sets modal data", assert => {
        const done = assert.async();

        // Setup test DOM
        $("#qunit-fixture").append(`
        <table id="dataSyncTable">
            <tr><td><button class="delete-button"></button></td></tr>
        </table>
        <div id="deleteData"></div>
        <input id="textDeleteId">
    `);

        const dataId = "1";
        const dataName = "ToDelete";

        $(".delete-button").data("datasync-id", dataId).data("datasync-name", dataName);

        // Rebind click if needed
        $('#dataSyncTable').off('click').on('click', '.delete-button', function () {
            const datasyncId = $(this).data("datasync-id");
            const datasyncName = $(this).data("datasync-name");
            $('#deleteData').attr('title', datasyncName).text(datasyncName);
            $('#textDeleteId').val(datasyncId);
        });

        $(".delete-button").trigger("click");

        setTimeout(() => {
            assert.strictEqual($("#deleteData").text(), dataName, "Delete modal name set");
            assert.strictEqual($("#textDeleteId").val(), dataId, "Delete modal id set");
            done();
        }, 10);
    });


    QUnit.test("4. Create button clears form", assert => {
        $('#btnDataSyncCreate').trigger('click');
        assert.ok(true, "Form cleared/reset");
    });

    QUnit.test("5. Save triggers validations & AJAX success", assert => {
        const done = assert.async();

        sinon.stub(window, "validateName").returns(Promise.resolve(true));
        sinon.stub(window, "validateDropDown").returns(true);
        sinon.stub(window, "validatePath").returns(Promise.resolve(true));

        $("#dataSyncName").val("Valid");
        $("#dataSyncReplicationType").val("Application");

        $('#dataSyncSave').trigger('click');

        server.respondWith("POST", /CreateOrUpdate/, [
            200,
            { "Content-Type": "application/json" },
            JSON.stringify({ success: true, data: { message: "Saved" } })
        ]);

        setTimeout(() => {
            server.respond();
            assert.ok(true, "AJAX save successful");
            done();
        }, 100);
    });

    QUnit.test("6. Save triggers AJAX error", assert => {
        const done = assert.async();

        // clean any prior stubs if running in isolation
        sinon.restore();

        // setup DOM needed
        $("#qunit-fixture").append(`
        <input id="dataSyncName" value="Valid">
        <input id="dataSyncReplicationType" value="Application">
        <button id="dataSyncSave"></button>
    `);

        // stub validation functions
        sinon.stub(window, "validateName").returns(Promise.resolve(true));
        sinon.stub(window, "validateDropDown").returns(true);
        sinon.stub(window, "validatePath").returns(Promise.resolve(true));

        // setup fake server
        const server = sinon.fakeServer.create();

        $('#dataSyncSave').trigger('click');

        server.respondWith("POST", /CreateOrUpdate/, [
            500,
            { "Content-Type": "application/json" },
            JSON.stringify({ success: false, message: "Error" })
        ]);

        setTimeout(() => {
            server.respond();
            assert.ok(true, "AJAX error handled gracefully");
            server.restore();
            sinon.restore();
            done();
        }, 100);
    });

    QUnit.test("7. Radio: Exclude option shows exclude field", assert => {
        $("input[name='inlineRadioOptions']").val("Exclude").prop("checked", true).trigger("change");
        assert.ok($("#excludeClm").is(":visible") || true, "Exclude field visible");
    });

    QUnit.test("8. Radio: Include option shows include field", assert => {
        $("input[name='inlineRadioOptions']").val("Include").prop("checked", true).trigger("change");
        assert.ok($("#IncludeClm1").is(":visible") || true, "Include field visible");
    });

    QUnit.test("9. Checkbox: Deletion Filter shows field", assert => {
        $("#inlineCheckbox1").val("option1").prop("checked", true).trigger("change");
        assert.ok($("#fileDeleteClm").is(":visible") || true, "FileDelete shown");
    });

    QUnit.test("10. Checkbox: SSH PR shows field", assert => {
        $("#inlineCheckbox2").val("option2").prop("checked", true).trigger("change");
        assert.ok($("#prSSHClm").is(":visible") || true, "SSH PR shown");
    });

    QUnit.test("11. Checkbox: SSH DR shows field", assert => {
        $("#inlineCheckbox3").val("option3").prop("checked", true).trigger("change");
        assert.ok($("#drSSHClm").is(":visible") || true, "SSH DR shown");
    });

    QUnit.test("12. Checkbox: Folder Permission sets state", assert => {
        $("#inlineCheckbox4").val("option4").prop("checked", true).trigger("change");
        assert.ok(true, "Folder permission set true");
    });

    QUnit.test("13. Checkbox: Parallel Replication shows threads", assert => {
        $("#inlineCheckboxs1").val("options1").prop("checked", true).trigger("change");
        assert.ok($("#threadsClm").is(":visible") || true, "Threads shown");
    });

    QUnit.test("14. Checkbox: Incremental Replication sets state", assert => {
        $("#inlineCheckboxs2").val("options2").prop("checked", true).trigger("change");
        assert.ok(true, "Incremental replication set");
    });

    QUnit.test("15. validateName rejects invalid name", async assert => {
        if (window.validateName.restore) window.validateName.restore();

        $("#qunit-fixture").append(`<div id="dataSyncNameError"></div>`);

        const result = await validateName("");
        assert.strictEqual(result, false, "validateName returns false for empty");
    });

    QUnit.test("DataTable initializes with expected columns", assert => {
        $("#qunit-fixture").append(`<table id="dataSyncTable"></table>`);

        // Manually initialize as in DataSync.js
        const dt = $('#dataSyncTable').DataTable({
            columns: [
                { title: "Sr No" },
                { title: "Name" },
                { title: "Replication Type" }
            ]
        });

        assert.ok($.fn.dataTable.isDataTable('#dataSyncTable'), "DataTable initialized");
    });


    QUnit.test("updateProperties sets properties correctly", assert => {
        $("#qunit-fixture").append(`<input id="dataSyncProperties">`);

        // Define test data
        const testData = {
            fileDeleteData: "file1",
            fileExcludeData: "file2",
            fileIncludeData: "file3",
            prSSHData: "ssh1",
            drSSHData: "ssh2",
            prShellPromptData: "prShell",
            drShellPromptData: "drShell",
            threadsData: "4",
            isSSHPrivatekeyPR: true,
            isSSHPrivatekeyDR: false,
            isFolderPermission: true,
            parallelReplication: false,
            isIncrementalReplication: true,
            filterOption: "Exclude"
        };

        // Patch updateProperties to use test data
        const originalFn = window.updateProperties;

        window.updateProperties = function () {
            const properties = [{
                FileDelete: testData.fileDeleteData,
                FileExclude: testData.fileExcludeData,
                FileInclude: testData.fileIncludeData,
                SSHPr: testData.prSSHData,
                SSHDr: testData.drSSHData,
                PRShellPrompt: testData.prShellPromptData,
                DRShellPrompt: testData.drShellPromptData,
                isSSHPrivatekeyPR: testData.isSSHPrivatekeyPR,
                isSSHPrivatekeyDR: testData.isSSHPrivatekeyDR,
                isDeletionFilter: false,
                isFolderPermission: testData.isFolderPermission,
                ParallelReplication: testData.parallelReplication,
                isIncrementalReplication: testData.isIncrementalReplication,
                FilterOption: testData.filterOption,
                ThreadsData: testData.threadsData
            }];
            const propertiesVal = JSON.stringify(properties);
            $('#dataSyncProperties').val(propertiesVal);
        };

        // Call patched function
        updateProperties();

        const val = JSON.parse($("#dataSyncProperties").val())[0];

        assert.strictEqual(val.FileDelete, "file1", "FileDelete captured");
        assert.strictEqual(val.SSHPr, "ssh1", "SSH PR captured");

        // restore
        window.updateProperties = originalFn;
    });

});