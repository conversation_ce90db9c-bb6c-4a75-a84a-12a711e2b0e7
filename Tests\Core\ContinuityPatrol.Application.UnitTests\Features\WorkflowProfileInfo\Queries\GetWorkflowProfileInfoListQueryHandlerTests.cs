﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Queries;

public class GetWorkflowProfileInfoListQueryHandlerTests : IClassFixture<WorkflowProfileInfoFixture>
{
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;

    private Mock<IWorkflowViewRepository> _mockWorkflowViewRepository;

    private readonly GetWorkflowProfileInfoListQueryHandler _handler;

    public GetWorkflowProfileInfoListQueryHandlerTests(WorkflowProfileInfoFixture workflowProfileInfoFixture)
    {
        _workflowProfileInfoFixture = workflowProfileInfoFixture;

        _mockWorkflowViewRepository = WorkflowViewRepositoryMocks.ListAllAsync(_workflowProfileInfoFixture.WorkflowViews);

        _handler = new GetWorkflowProfileInfoListQueryHandler(_workflowProfileInfoFixture.Mapper, _mockWorkflowViewRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowProfileInfoCount()
    {
        var result = await _handler.Handle(new GetWorkflowProfileInfoListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileInfoListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowProfileInfoListQuery(), CancellationToken.None);

        _mockWorkflowViewRepository.Verify(x => x.ListAllAsync(), Times.Once());
    }

    [Fact]

    public async Task Handle_Return_Valid_WorkflowProfileInfoDetail()
    {
        var result = await _handler.Handle(new GetWorkflowProfileInfoListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileInfoListVm>>();

        result[0].Id.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].ReferenceId);
        result[0].ProfileId.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].ProfileId);
        result[0].ProfileName.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].ProfileName);
        result[0].InfraObjectId.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].InfraObjectName);
        result[0].BusinessFunctionId.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].BusinessFunctionId);
        result[0].BusinessFunctionName.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].BusinessFunctionName);
        result[0].BusinessServiceId.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].BusinessServiceId);
        result[0].BusinessServiceName.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].BusinessServiceName);
        //result[0].WorkflowId.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].ReferenceId);
        result[0].WorkflowName.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].WorkflowName);
        //result[0].CurrentActionId.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].CurrentActionId);
        //result[0].CurrentActionName.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].CurrentActionName);
        //result[0].Message.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].Message);
        //result[0].ConditionalOperation.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].ConditionalOperation);
        result[0].WorkflowType.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].WorkflowType);
        //result[0].ActionMode.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].ActionMode);
        //result[0].Status.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].Status);
        //result[0].ProgressStatus.ShouldBe(_workflowProfileInfoFixture.WorkflowViews[0].ProgressStatus);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowViewRepository = WorkflowViewRepositoryMocks.GetWorkflowViewEmptyRepository();

        var handler = new GetWorkflowProfileInfoListQueryHandler(_workflowProfileInfoFixture.Mapper, _mockWorkflowViewRepository.Object);

        var result = await handler.Handle(new GetWorkflowProfileInfoListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
}