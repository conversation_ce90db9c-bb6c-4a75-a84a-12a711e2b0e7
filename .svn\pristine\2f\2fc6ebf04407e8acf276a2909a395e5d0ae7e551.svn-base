﻿namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;

public class CreateBulkImportValidatorCommandHandler : IRequestHandler<CreateBulkImportValidatorCommand,
    CreateBulkImportValidatorCommandResponse>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IGlobalSettingRepository _globalSettingRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IServerRepository _serverRepository;
    private readonly ITemplateRepository _templateRepository;

    public CreateBulkImportValidatorCommandHandler(IMapper mapper, IServerRepository serverRepository,
        IDatabaseRepository databaseRepository, IReplicationRepository replicationRepository,
        IInfraObjectRepository infraObjectRepository, ITemplateRepository templateRepository,
        IGlobalSettingRepository globalSettingRepository)
    {
        _mapper = mapper;
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
        _replicationRepository = replicationRepository;
        _infraObjectRepository = infraObjectRepository;
        _templateRepository = templateRepository;
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task<CreateBulkImportValidatorCommandResponse> Handle(CreateBulkImportValidatorCommand request,
        CancellationToken cancellationToken)
    {
        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("Bulk Import");

        if (globalSetting is null || globalSetting.GlobalSettingValue.Equals("false"))
            throw new InvalidException("The bulk import feature is not enabled in the global settings.");

        var validatorResponse = new CreateBulkImportValidatorCommandResponse();

        foreach (var bulkImportValidator in request.BulkImportOperationList)
        {
            var mappedRequest = _mapper.Map<BulkImportValidatorCommand>(bulkImportValidator);

            var validator = new CreateBulkImportValidatorCommandValidator(_serverRepository, _databaseRepository,
                _replicationRepository, _infraObjectRepository);

            var validationResult = await validator.ValidateAsync(mappedRequest, cancellationToken);

            var validatorCommandResponseDetail = new CreateBulkImportValidatorCommandResponseDetail
            {
                InfraObjectName = bulkImportValidator?.InfraObject?.Name ?? "NA"
            };

            if (!validationResult.IsValid)
                foreach (var failure in validationResult.Errors)
                {
                    var errorMessage = failure.ErrorMessage;
                    var attemptedValue = failure.AttemptedValue?.ToString() ?? "null";

                    if (failure.PropertyName.ToLower().StartsWith("server"))
                        validatorCommandResponseDetail.ServerCommand.Add(new ValidationDetail
                        {
                            PropertyName = GetAttemptedValue(attemptedValue, 0),
                            Name = GetAttemptedValue(attemptedValue, 1).Replace(";", ""),
                            Exception = errorMessage
                        });
                    else if (failure.PropertyName.ToLower().StartsWith("database"))
                        validatorCommandResponseDetail.DatabaseCommand.Add(new ValidationDetail
                        {
                            PropertyName = GetAttemptedValue(attemptedValue, 0),
                            Name = GetAttemptedValue(attemptedValue, 1).Replace(";", ""),
                            Exception = errorMessage
                        });
                    else if (failure.PropertyName.ToLower().StartsWith("replication"))
                        validatorCommandResponseDetail.ReplicationCommand.Add(new ValidationDetail
                        {
                            PropertyName = GetAttemptedValue(attemptedValue, 0),
                            Name = GetAttemptedValue(attemptedValue, 1).Replace(";", ""),
                            Exception = errorMessage
                        });
                    else if (failure.PropertyName.ToLower().StartsWith("infraobject"))
                        validatorCommandResponseDetail.InfraObjectCommand.Add(new ValidationDetail
                        {
                            PropertyName = GetAttemptedValue(attemptedValue, 0),
                            Name = GetAttemptedValue(attemptedValue, 1).Replace(";", ""),
                            Exception = errorMessage
                        });
                }

            if (bulkImportValidator is not null)
            {
                await SetTemplateIfValid(bulkImportValidator.IsSwitchOver, "SwitchOver", bulkImportValidator,
                    validatorCommandResponseDetail);
                await SetTemplateIfValid(bulkImportValidator.IsSwitchBack, "SwitchBack", bulkImportValidator,
                    validatorCommandResponseDetail);
                await SetTemplateIfValid(bulkImportValidator.IsFailOver, "FailOver", bulkImportValidator,
                    validatorCommandResponseDetail);
                await SetTemplateIfValid(bulkImportValidator.IsFailBack, "FailBack", bulkImportValidator,
                    validatorCommandResponseDetail);
            }

            validatorCommandResponseDetail.IsServer = bulkImportValidator!.IsServer;
            validatorCommandResponseDetail.IsDatabase = bulkImportValidator!.IsDatabase;
            validatorCommandResponseDetail.IsReplication = bulkImportValidator!.IsReplication;
            validatorCommandResponseDetail.IsInfraObject = bulkImportValidator!.IsInfraObject;

            validatorResponse.ValidatorResponse.Add(validatorCommandResponseDetail);
        }

      

        return validatorResponse;
    }

    private async Task SetTemplateIfValid(bool isActionEnabled, string actionType,
        CreateBulkImportValidatorCommandList bulkImportValidator,
        CreateBulkImportValidatorCommandResponseDetail validatorCommandResponseDetail)
    {
        if (!isActionEnabled) return;

        var replicationTypeId = bulkImportValidator.InfraObject?.ReplicationTypeId;
        var template =
            await _templateRepository.GetTemplateByReplicationTypeIdAndActionType(replicationTypeId, actionType);

        var templateProperties = template?.Properties ?? string.Empty;

        switch (actionType.ToLower())
        {
            case "switchover":
                validatorCommandResponseDetail.SwitchOverTemplate = templateProperties;
                validatorCommandResponseDetail.IsSwitchOver = template is not null;
                break;

            case "switchback":
                validatorCommandResponseDetail.SwitchBackTemplate = templateProperties;
                validatorCommandResponseDetail.IsSwitchBack = template is not null;
                break;

            case "failover":
                validatorCommandResponseDetail.FailOverTemplate = templateProperties;
                validatorCommandResponseDetail.IsFailOver = template is not null;
                break;

            case "failback":
                validatorCommandResponseDetail.FailBackTemplate = templateProperties;
                validatorCommandResponseDetail.IsFailBack = template is not null;
                break;
        }
    }


    //private static string GetPropertyName(string propertyName)
    //{
    //    if (propertyName.IsNotNullOrWhiteSpace())
    //    {
    //        var parts = propertyName.Split(new[] { ':', ';', '.' }, StringSplitOptions.RemoveEmptyEntries);
    //        return parts.Length > 1 ? parts[1].Trim() : string.Empty;
    //    }

    //    return propertyName;
    //}

    private static string GetAttemptedValue(string attemptedValue, int index)
    {
        if (attemptedValue.IsNotNullOrWhiteSpace())
        {
            var parts = attemptedValue.Split(new[] { ':', ';' }, StringSplitOptions.RemoveEmptyEntries);
            return parts.Length > 1 ? parts[index].Trim() : string.Empty;
        }

        return attemptedValue;
    }
}