﻿using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries;

public class GetAirGapListQueryHandlerTests
{
    private readonly Mock<ICyberAirGapLogRepository> _mockCyberAirGapLogRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetAirGapListQueryHandler _handler;

    public GetAirGapListQueryHandlerTests()
    {
        _mockCyberAirGapLogRepository = new Mock<ICyberAirGapLogRepository>();
        _mockMapper = new Mock<IMapper>();
        _handler = new GetAirGapListQueryHandler(_mockCyberAirGapLogRepository.Object, _mockMapper.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenRepositoryReturnsNoData()
    {
        var request = new GetAirGapListQuery
        {
            StartDate = "DateTime.UtcNow.AddDays(-7)",
            EndDate = "DateTime.UtcNow"
        };

        _mockCyberAirGapLogRepository
            .Setup(repo => repo.GetAirGapList(request.StartDate, request.EndDate))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGapLog>());

        var result = await _handler.Handle(request, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Empty(result);

        _mockMapper.Verify(mapper => mapper.Map<List<GetAirGapListVm>>(It.IsAny<List<Domain.Entities.CyberAirGapLog>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldReturnMappedList_WhenRepositoryReturnsData()
    {
        var request = new GetAirGapListQuery
        {
            StartDate = "DateTime.UtcNow.AddDays(-7)",
            EndDate = "DateTime.UtcNow"
        };

        var airGapLogs = new List<Domain.Entities.CyberAirGapLog>
        {
            new() { AirGapId = Guid.NewGuid().ToString(), AirGapName = "Event 1" },
            new() { AirGapId = Guid.NewGuid().ToString(), AirGapName = "Event 2" },
            new() { AirGapId = Guid.NewGuid().ToString(), AirGapName = "Event 1 Duplicate" }
        };

        var expectedViewModel = new List<GetAirGapListVm>
        {
            new() { AirGapId = Guid.NewGuid().ToString(), AirGapName = "Event 1" },
            new() { AirGapId = Guid.NewGuid().ToString(), AirGapName = "Event 2" }
        };

        _mockCyberAirGapLogRepository
            .Setup(repo => repo.GetAirGapList(request.StartDate, request.EndDate))
            .ReturnsAsync(airGapLogs);

        _mockMapper
            .Setup(mapper => mapper.Map<List<GetAirGapListVm>>(It.IsAny<List<Domain.Entities.CyberAirGapLog>>()))
            .Returns(expectedViewModel);

        var result = await _handler.Handle(request, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(expectedViewModel.Count, result.Count);
        Assert.Equal(expectedViewModel[0].AirGapId, result[0].AirGapId);
        Assert.Equal(expectedViewModel[1].AirGapId, result[1].AirGapId);

        _mockMapper.Verify(mapper => mapper.Map<List<GetAirGapListVm>>(It.Is<List<Domain.Entities.CyberAirGapLog>>(x => x.Count == 2)), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldCallRepositoryWithCorrectDates()
    {
        var request = new GetAirGapListQuery
        {
            StartDate = "DateTime.UtcNow.AddDays(-7)",
            EndDate = "DateTime.UtcNow"
        };

        _mockCyberAirGapLogRepository
            .Setup(repo => repo.GetAirGapList(request.StartDate, request.EndDate))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGapLog>());

        await _handler.Handle(request, CancellationToken.None);

        _mockCyberAirGapLogRepository.Verify(repo => repo.GetAirGapList(request.StartDate, request.EndDate), Times.Once);
    }
}