//using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;
//using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Update;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;
//using ContinuityPatrol.Domain.Entities;

//namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Commands;

///// <summary>
///// Unit tests for UpdateAirGapStatusCommandHandler
///// Tests the status update of cyber air gap statuses with comprehensive scenarios
///// </summary>
//public class UpdateAirGapStatusTests : IClassFixture<CyberAirGapStatusFixture>
//{
//    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
//    private readonly Mock<ICyberAirGapStatusRepository> _mockCyberAirGapStatusRepository;
//    private readonly Mock<IPublisher> _mockPublisher;
//    private readonly UpdateAirGapStatusCommandHandler _handler;

//    public UpdateAirGapStatusTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
//    {
//        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
//        _mockCyberAirGapStatusRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(_cyberAirGapStatusFixture.CyberAirGapStatuses);
//        _mockPublisher = new Mock<IPublisher>();

//        _handler = new UpdateAirGapStatusCommandHandler(
//            _mockCyberAirGapStatusRepository.Object,
//            _mockPublisher.Object);
//    }

//    /// <summary>
//    /// Test: Update air gap status with valid command
//    /// Expected: Successfully updates status and returns response
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_ValidCommand()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = "Warning"
//        };

//        CyberAirGapStatusUpdatedEvent publishedEvent = null;

//        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()))
//            .Callback<CyberAirGapStatusUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.ShouldBeOfType<UpdateAirGapStatusResponse>();
//        result.Success.ShouldBeTrue();
//        result.Id.ShouldBe(existingEntity.ReferenceId);
//        result.Message.ShouldContain("status updated successfully");

//        existingEntity.Status.ShouldBe("Warning");

//        publishedEvent.ShouldNotBeNull();
//        publishedEvent.Name.ShouldBe(existingEntity.AirGapName);
//        publishedEvent.Status.ShouldBe("Warning");

//        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.Once);
//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
//        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
//    }

//    /// <summary>
//    /// Test: Update air gap status when entity not found
//    /// Expected: Throws NotFoundException
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_EntityNotFound()
//    {
//        // Arrange
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = "non-existent-id",
//            Status = "Active"
//        };

//        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
//            .ReturnsAsync((CyberAirGapStatus)null);

//        // Act & Assert
//        var exception = await Should.ThrowAsync<NotFoundException>(
//            async () => await _handler.Handle(command, CancellationToken.None));

//        exception.Message.ShouldContain("CyberAirGapStatus");
//        exception.Message.ShouldContain(command.Id);

//        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.Once);
//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAirGapStatus>()), Times.Never);
//        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
//    }

//    /// <summary>
//    /// Test: Update air gap status with different status values
//    /// Expected: Successfully updates to various status values
//    /// </summary>
//    [Theory]
//    [InlineData("Active", "System is running normally")]
//    [InlineData("Warning", "System has minor issues")]
//    [InlineData("Error", "System has critical errors")]
//    [InlineData("Maintenance", "System is under maintenance")]
//    [InlineData("Disabled", "System is temporarily disabled")]
//    [InlineData("Unknown", "System status is unknown")]
//    public async Task Handle_UpdateAirGapStatus_When_DifferentStatusValues(string status, string description)
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = status
//        };

//        CyberAirGapStatusUpdatedEvent publishedEvent = null;

//        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()))
//            .Callback<CyberAirGapStatusUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.Success.ShouldBeTrue();
//        result.Message.ShouldContain(status);

//        existingEntity.Status.ShouldBe(status);

//        publishedEvent.ShouldNotBeNull();
//        publishedEvent.Status.ShouldBe(status);

//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.Is<CyberAirGapStatus>(e => 
//            e.Status == status)), Times.Once);
//    }

//    /// <summary>
//    /// Test: Update air gap status with cancellation token
//    /// Expected: Respects cancellation and throws OperationCanceledException
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_CancellationRequested()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = "Active"
//        };
//        var cancellationToken = new CancellationToken(true);

//        // Act & Assert
//        await Should.ThrowAsync<OperationCanceledException>(
//            async () => await _handler.Handle(command, cancellationToken));
//    }

//    /// <summary>
//    /// Test: Update air gap status when repository fails
//    /// Expected: Throws exception and does not publish event
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_RepositoryFails()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = "Error"
//        };

//        var mockFailingRepository = CyberAirGapStatusRepositoryMocks.CreateFailingCyberAirGapStatusRepository();
//        mockFailingRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
//            .ReturnsAsync(existingEntity);

//        var handler = new UpdateAirGapStatusCommandHandler(
//            mockFailingRepository.Object,
//            _mockPublisher.Object);

//        // Act & Assert
//        var exception = await Should.ThrowAsync<InvalidOperationException>(
//            async () => await handler.Handle(command, CancellationToken.None));

//        exception.Message.ShouldBe("Update operation failed");
//        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
//    }

//    /// <summary>
//    /// Test: Update air gap status with null status
//    /// Expected: Handles null status gracefully
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_NullStatus()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = null
//        };

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.Success.ShouldBeTrue();

//        existingEntity.Status.ShouldBeNull();

//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
//    }

//    /// <summary>
//    /// Test: Update air gap status with empty status
//    /// Expected: Handles empty status gracefully
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_EmptyStatus()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = string.Empty
//        };

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.Success.ShouldBeTrue();

//        existingEntity.Status.ShouldBe(string.Empty);

//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
//    }

//    /// <summary>
//    /// Test: Update air gap status with special characters
//    /// Expected: Handles special characters in status values
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_SpecialCharacters()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = "Status with special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?"
//        };

//        CyberAirGapStatusUpdatedEvent publishedEvent = null;

//        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()))
//            .Callback<CyberAirGapStatusUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.Success.ShouldBeTrue();

//        existingEntity.Status.ShouldContain("!@#$%^&*()");

//        publishedEvent.ShouldNotBeNull();
//        publishedEvent.Status.ShouldContain("!@#$%^&*()");

//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
//    }

//    /// <summary>
//    /// Test: Update air gap status with unicode characters
//    /// Expected: Handles unicode characters in status values
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_UnicodeCharacters()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = "状态正常 🚀💻📊"
//        };

//        CyberAirGapStatusUpdatedEvent publishedEvent = null;

//        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()))
//            .Callback<CyberAirGapStatusUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.Success.ShouldBeTrue();

//        existingEntity.Status.ShouldContain("状态正常");
//        existingEntity.Status.ShouldContain("🚀💻📊");

//        publishedEvent.ShouldNotBeNull();
//        publishedEvent.Status.ShouldContain("状态正常");
//        publishedEvent.Status.ShouldContain("🚀💻📊");

//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
//    }

//    /// <summary>
//    /// Test: Update air gap status multiple times
//    /// Expected: Successfully handles multiple status updates
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_MultipleUpdates()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var statuses = new[] { "Active", "Warning", "Error", "Maintenance", "Active" };

//        // Act & Assert
//        foreach (var status in statuses)
//        {
//            var command = new UpdateAirGapStatusCommand
//            {
//                Id = existingEntity.ReferenceId,
//                Status = status
//            };

//            var result = await _handler.Handle(command, CancellationToken.None);

//            result.ShouldNotBeNull();
//            result.Success.ShouldBeTrue();
//            existingEntity.Status.ShouldBe(status);
//        }

//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Exactly(statuses.Length));
//        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Exactly(statuses.Length));
//    }

//    /// <summary>
//    /// Test: Update air gap status response validation
//    /// Expected: Response contains all required properties
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_ValidatingResponse()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var command = new UpdateAirGapStatusCommand
//        {
//            Id = existingEntity.ReferenceId,
//            Status = "Response Validation Test"
//        };

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.ShouldBeOfType<UpdateAirGapStatusResponse>();
//        result.Success.ShouldBeTrue();
//        result.Id.ShouldBe(existingEntity.ReferenceId);
//        result.Message.ShouldNotBeNullOrEmpty();
//        result.Message.ShouldContain("CyberAirGapStatus");
//        result.Message.ShouldContain("status updated successfully");
//        result.Message.ShouldContain(command.Status);
//    }

//    /// <summary>
//    /// Test: Update air gap status with concurrent operations
//    /// Expected: Handles concurrent status updates correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateAirGapStatus_When_ConcurrentOperations()
//    {
//        // Arrange
//        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
//        var commands = new[]
//        {
//            new UpdateAirGapStatusCommand { Id = existingEntity.ReferenceId, Status = "Active" },
//            new UpdateAirGapStatusCommand { Id = existingEntity.ReferenceId, Status = "Warning" },
//            new UpdateAirGapStatusCommand { Id = existingEntity.ReferenceId, Status = "Error" }
//        };

//        // Act - Simulate concurrent status update operations
//        var tasks = commands.Select(async command =>
//        {
//            try
//            {
//                return await _handler.Handle(command, CancellationToken.None);
//            }
//            catch (Exception)
//            {
//                return null; // Some operations might fail due to concurrency
//            }
//        });

//        var results = await Task.WhenAll(tasks);

//        // Assert
//        var successfulResults = results.Where(r => r != null).ToList();
//        successfulResults.ShouldNotBeEmpty();
//        successfulResults.ShouldAllBe(r => r.Success);

//        // The final status should be one of the attempted statuses
//        new[] { "Active", "Warning", "Error" }.ShouldContain(existingEntity.Status);

//        // Verify that repository operations were called
//        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId), Times.AtLeastOnce);
//        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.AtLeastOnce);
//    }
//}
