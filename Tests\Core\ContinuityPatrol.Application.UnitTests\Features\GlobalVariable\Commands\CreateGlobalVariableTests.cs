﻿using AutoMapper;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Constants;
using MediatR;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Commands;

public class CreateGlobalVariableTests : IClassFixture<GlobalVariableFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;
    private readonly Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateGlobalVariableCommandHandler _handler;

    public CreateGlobalVariableTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.CreateGlobalVariableRepository(_globalVariableFixture.GlobalVariables);

        _handler = new CreateGlobalVariableCommandHandler(_globalVariableFixture.Mapper, _mockGlobalVariableRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseGlobalVariableCount_When_GlobalVariableCreated()
    {
        await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        var allGlobalVariables = await _mockGlobalVariableRepository.Object.ListAllAsync();

        allGlobalVariables.Count.ShouldBe(_globalVariableFixture.GlobalVariables.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateGlobalVariableResponse_When_GlobalVariableCreated()
    {
        var result = await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateGlobalVariableResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);

        result.Success.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        _mockGlobalVariableRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.GlobalVariable>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_GlobalVariableCreated()
    {
        await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<GlobalVariableCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_MapCommand_ToEntity_Correctly()
    {
        var command = _globalVariableFixture.CreateGlobalVariableCommand;

        await _handler.Handle(command, CancellationToken.None);

        _mockGlobalVariableRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.GlobalVariable>(
            entity => entity.VariableName == command.VariableName &&
                     entity.VariableValue == command.VariableValue &&
                     entity.Type == command.Type)), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_CorrectMessage_When_GlobalVariableCreated()
    {
        var result = await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        result.Message.ShouldContain("GlobalVariable");
        result.Message.ShouldContain(_globalVariableFixture.CreateGlobalVariableCommand.VariableName);
        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_EntityReferenceId_When_GlobalVariableCreated()
    {
        var result = await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        result.Id.ShouldNotBeNullOrEmpty();

        // The result ID should be the newly created entity's ReferenceId
        var allGlobalVariables = await _mockGlobalVariableRepository.Object.ListAllAsync();
        var createdGlobalVariable = allGlobalVariables.Last();
        result.Id.ShouldBe(createdGlobalVariable.ReferenceId);
    }

    [Fact]
    public async Task Handle_PublishEvent_WithCorrectEventData()
    {
        var command = _globalVariableFixture.CreateGlobalVariableCommand;

        await _handler.Handle(command, CancellationToken.None);

        _mockPublisher.Verify(x => x.Publish(It.Is<GlobalVariableCreatedEvent>(
            evt => evt.Name == command.VariableName), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallMapper_ToMapCommandToEntity()
    {
        var command = _globalVariableFixture.CreateGlobalVariableCommand;

        await _handler.Handle(command, CancellationToken.None);

        // Verify mapper was called to map command to entity
        _mockGlobalVariableRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.GlobalVariable>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetSuccessResponse_When_GlobalVariableCreated()
    {
        var result = await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        result.Success.ShouldBe(true);
        result.Message.ShouldNotBeNullOrEmpty();
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_SetCreatedDate_When_GlobalVariableCreated()
    {
        await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        var allGlobalVariables = await _mockGlobalVariableRepository.Object.ListAllAsync();
        var createdGlobalVariable = allGlobalVariables.Last();

        createdGlobalVariable.CreatedDate.ShouldNotBe(default(DateTime));
        createdGlobalVariable.CreatedDate.ShouldBeLessThanOrEqualTo(DateTime.UtcNow);
    }

    [Fact]
    public async Task Handle_SetIsActiveTrue_When_GlobalVariableCreated()
    {
        await _handler.Handle(_globalVariableFixture.CreateGlobalVariableCommand, CancellationToken.None);

        var allGlobalVariables = await _mockGlobalVariableRepository.Object.ListAllAsync();
        var createdGlobalVariable = allGlobalVariables.Last();

        createdGlobalVariable.IsActive.ShouldBe(true);
    }
}