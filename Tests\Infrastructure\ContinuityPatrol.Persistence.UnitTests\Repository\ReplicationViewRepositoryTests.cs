using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ReplicationViewRepositoryTests : IClassFixture<ReplicationViewFixture>, IDisposable
{
    private readonly ReplicationViewFixture _replicationViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ReplicationViewRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<IInfraObjectViewRepository> _mockInfraObjectViewRepository;

    public ReplicationViewRepositoryTests(ReplicationViewFixture replicationViewFixture)
    {
        _replicationViewFixture = replicationViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockInfraObjectViewRepository = new Mock<IInfraObjectViewRepository>();
        _repository = new ReplicationViewRepository(_dbContext, _mockLoggedInUserService.Object, _mockInfraObjectViewRepository.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.ReplicationViews.RemoveRange(_dbContext.ReplicationViews);
        await _dbContext.SaveChangesAsync();
    }

    private void SetupLoggedInUserService(bool isParent = false, bool isAllInfra = true, string companyId = ReplicationViewFixture.CompanyId, string assignedInfras = null)
    {
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(isParent);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(isAllInfra);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfras ?? "{}");
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ReturnsAllReplications_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsFilteredReplications_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId },
            new InfraObjectView { ReplicationProperties = testReplications[1].ReferenceId }
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count <= 3);
    }

    #endregion

    #region GetByReplicationIdsAsync Tests

    [Fact]
    public async Task GetByReplicationIdsAsync_ReturnsMatchingReplications_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var ids = testReplications.Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetByReplicationIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Contains(r.ReferenceId, ids));
    }

    [Fact]
    public async Task GetByReplicationIdsAsync_ReturnsMatchingReplications_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: false);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var ids = testReplications.Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetByReplicationIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Contains(r.ReferenceId, ids));
      
    }

    [Fact]
    public async Task GetByReplicationIdsAsync_ReturnsEmptyList_WhenNoMatchingIds()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService();

        var nonExistentIds = new List<string> { "NON_EXISTENT_1", "NON_EXISTENT_2" };

        // Act
        var result = await _repository.GetByReplicationIdsAsync(nonExistentIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region ReplicationCountAsync Tests

    [Fact]
    public async Task ReplicationCountAsync_ReturnsCorrectCount_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(5).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ReplicationCountAsync();

        // Assert
        Assert.Equal(5, result);
    }

    [Fact]
    public async Task ReplicationCountAsync_ReturnsFilteredCount_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(5).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId },
            new InfraObjectView { ReplicationProperties = testReplications[1].ReferenceId }
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.ReplicationCountAsync();

        // Assert
        Assert.True(result >= 0);
        Assert.True(result <= 5);
    }

    #endregion

    #region GetReplicationNames Tests

    [Fact]
    public async Task GetReplicationNames_ReturnsReplicationNames_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.NotNull(r.Name));
        Assert.All(result, r => Assert.NotNull(r.ReferenceId));
    }

    [Fact]
    public async Task GetReplicationNames_ReturnsFilteredNames_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId }
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count <= 3);
    }

    #endregion

    #region GetType Tests

    [Fact]
    public async Task GetType_ReturnsMatchingReplications_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: true, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.TypeId = ReplicationViewFixture.TypeId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetType(ReplicationViewFixture.TypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.TypeId, r.TypeId));
    }

    [Fact]
    public async Task GetType_ReturnsMatchingReplications_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: false, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.TypeId = ReplicationViewFixture.TypeId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetType(ReplicationViewFixture.TypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.TypeId, r.TypeId));
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.CompanyId, r.CompanyId));
    }

    [Fact]
    public async Task GetType_ReturnsFilteredReplications_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isParent: false, isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.TypeId = ReplicationViewFixture.TypeId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId }
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.GetType(ReplicationViewFixture.TypeId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count <= 3);
    }

    [Fact]
    public async Task GetType_ReturnsEmptyList_WhenNoMatchingType()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService();

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.TypeId = "DIFFERENT_TYPE");
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetType(ReplicationViewFixture.TypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetReplicationList Tests

    [Fact]
    public async Task GetReplicationList_ReturnsReplicationList_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.NotNull(r.Type));
        Assert.All(result, r => Assert.NotNull(r.TypeId));
        Assert.All(result, r => Assert.NotNull(r.Properties));
        Assert.All(result, r => Assert.NotNull(r.BusinessServiceId));
    }

    [Fact]
    public async Task GetReplicationList_ReturnsFilteredList_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId }
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.GetReplicationList();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count <= 3);
    }

    #endregion

    #region GetReplicationListByLicenseKey Tests

    [Fact]
    public async Task GetReplicationListByLicenseKey_ReturnsMatchingReplications_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: true, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.LicenseId = ReplicationViewFixture.LicenseId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationListByLicenseKey(ReplicationViewFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.LicenseId, r.LicenseId));
    }

    [Fact]
    public async Task GetReplicationListByLicenseKey_ReturnsMatchingReplications_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: false, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.LicenseId = ReplicationViewFixture.LicenseId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationListByLicenseKey(ReplicationViewFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.LicenseId, r.LicenseId));
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.CompanyId, r.CompanyId));
    }

    [Fact]
    public async Task GetReplicationListByLicenseKey_ReturnsFilteredReplications_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isParent: false, isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.LicenseId = ReplicationViewFixture.LicenseId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId }
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.GetReplicationListByLicenseKey(ReplicationViewFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count <= 3);
    }

    [Fact]
    public async Task GetReplicationListByLicenseKey_ReturnsEmptyList_WhenNoMatchingLicense()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService();

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.LicenseId = "DIFFERENT_LICENSE");
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationListByLicenseKey(ReplicationViewFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetReplicationBySiteId Tests

    [Fact]
    public async Task GetReplicationBySiteId_ReturnsMatchingReplications_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: true, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.SiteId = ReplicationViewFixture.SiteId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationBySiteId(ReplicationViewFixture.SiteId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.SiteId, r.SiteId));
    }

    [Fact]
    public async Task GetReplicationBySiteId_ReturnsMatchingReplications_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: false, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.SiteId = ReplicationViewFixture.SiteId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationBySiteId(ReplicationViewFixture.SiteId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.SiteId, r.SiteId));
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.CompanyId, r.CompanyId));
    }

    [Fact]
    public async Task GetReplicationBySiteId_ReturnsFilteredReplications_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isParent: false, isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.SiteId = ReplicationViewFixture.SiteId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId }
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.GetReplicationBySiteId(ReplicationViewFixture.SiteId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count <= 3);
    }

    [Fact]
    public async Task GetReplicationBySiteId_ReturnsEmptyList_WhenNoMatchingSite()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService();

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.SiteId = "DIFFERENT_SITE");
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationBySiteId(ReplicationViewFixture.SiteId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetReplicationByBusinessServiceId Tests

    [Fact]
    public async Task GetReplicationByBusinessServiceId_ReturnsMatchingReplications_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: true, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.BusinessServiceId = ReplicationViewFixture.BusinessServiceId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationByBusinessServiceId(ReplicationViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.BusinessServiceId, r.BusinessServiceId));
    }

    [Fact]
    public async Task GetReplicationByBusinessServiceId_ReturnsMatchingReplications_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: false, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.BusinessServiceId = ReplicationViewFixture.BusinessServiceId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationByBusinessServiceId(ReplicationViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.BusinessServiceId, r.BusinessServiceId));
        Assert.All(result, r => Assert.Equal(ReplicationViewFixture.CompanyId, r.CompanyId));
    }

    [Fact]
    public async Task GetReplicationByBusinessServiceId_ReturnsFilteredReplications_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isParent: false, isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.BusinessServiceId = ReplicationViewFixture.BusinessServiceId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId }
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.GetReplicationByBusinessServiceId(ReplicationViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count <= 3);
    }

    [Fact]
    public async Task GetReplicationByBusinessServiceId_ReturnsEmptyList_WhenNoMatchingBusinessService()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService();

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        testReplications.ForEach(x => x.BusinessServiceId = "DIFFERENT_BS");
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationByBusinessServiceId(ReplicationViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetReplicationByType Tests

    [Fact]
    public async Task GetReplicationByType_ReturnsCorrectPaginatedResult_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: true, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewPaginationList.Take(10).ToList();
        testReplications.ForEach(x => x.TypeId = ReplicationViewFixture.TypeId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetReplicationByType(ReplicationViewFixture.TypeId, 1, 5, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count <= 5);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
        Assert.All(result.Data, r => Assert.Equal(ReplicationViewFixture.TypeId, r.TypeId));
    }

    [Fact]
    public async Task GetReplicationByType_ReturnsCorrectPaginatedResult_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: false, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewPaginationList.Take(10).ToList();
        testReplications.ForEach(x => x.TypeId = ReplicationViewFixture.TypeId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();



        // Act
        var result = await _repository.GetReplicationByType(ReplicationViewFixture.TypeId, 1, 5, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count <= 5);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
        Assert.All(result.Data, r => Assert.Equal(ReplicationViewFixture.TypeId, r.TypeId));
        Assert.All(result.Data, r => Assert.Equal(ReplicationViewFixture.CompanyId, r.CompanyId));
    }

    [Fact]
    public async Task GetReplicationByType_ReturnsFilteredPaginatedResult_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isParent: false, isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewPaginationList.Take(10).ToList();
        testReplications.ForEach(x => x.TypeId = ReplicationViewFixture.TypeId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = testReplications.Take(3).Select(x => new InfraObjectView { ReplicationProperties = x.ReferenceId }).AsQueryable();
        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.GetReplicationByType(ReplicationViewFixture.TypeId, 1, 5,null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count <= 5);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsCorrectPaginatedResult_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: true, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewPaginationList.Take(10).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var specification = new Application.Specifications.ReplicationFilterSpecification("");


        // Act
        var result = await _repository.PaginatedListAllAsync(1, 5, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count <= 5);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsCorrectPaginatedResult_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isParent: false, isAllInfra: true);

        var testReplications = _replicationViewFixture.ReplicationViewPaginationList.Take(10).ToList();
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();



        // Act
        var result = await _repository.PaginatedListAllAsync(1, 5, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count <= 5);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
        Assert.All(result.Data, r => Assert.Equal(ReplicationViewFixture.CompanyId, r.CompanyId));
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsFilteredPaginatedResult_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isParent: false, isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewPaginationList.Take(10).ToList();
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = testReplications.Take(3).Select(x => new InfraObjectView { ReplicationProperties = x.ReferenceId }).AsQueryable();
        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 5, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count <= 5);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
    }

    #endregion

    #region SelectReplicationView Tests

    [Fact]
    public void SelectReplicationView_ReturnsCorrectProjection()
    {
        // Arrange
        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).AsQueryable();

        // Act
        var result = ReplicationViewRepository.SelectReplicationView(testReplications);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(3, resultList.Count);

        // Verify all properties are projected correctly
        foreach (var item in resultList)
        {
            Assert.NotNull(item.Id);
            Assert.NotNull(item.ReferenceId);
            Assert.NotNull(item.Name);
            Assert.NotNull(item.CompanyId);
            Assert.NotNull(item.TypeId);
            Assert.NotNull(item.Type);
            Assert.NotNull(item.SiteId);
            Assert.NotNull(item.SiteName);
            Assert.NotNull(item.Properties);
            Assert.NotNull(item.LicenseId);
            Assert.NotNull(item.LicenseKey);
            Assert.NotNull(item.BusinessServiceId);
            Assert.NotNull(item.BusinessServiceName);
            Assert.NotNull(item.FormVersion);
        }
    }

    #endregion

    #region Private Helper Method Tests

    [Fact]
    public async Task GetAssignedBusinessServicesByReplications_FiltersCorrectly_WhenAssignedBusinessServicesExist()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(5).ToList();
        testReplications[0].BusinessServiceId = ReplicationViewFixture.BusinessServiceId;
        testReplications[1].BusinessServiceId = ReplicationViewFixture.BusinessServiceId;
        testReplications[2].BusinessServiceId = "DIFFERENT_BS";


        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = new List<InfraObjectView>
        {
            new InfraObjectView { ReplicationProperties = testReplications[0].ReferenceId },
            new InfraObjectView { ReplicationProperties = testReplications[1].ReferenceId },
        }.AsQueryable();

        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Should only return replications that match both assigned business services and infra object properties
        Assert.True(result.Count <= 3);
    }

    [Fact]
    public async Task GetAssignedBusinessServicesByReplications_ReturnsEmpty_WhenNoAssignedBusinessServices()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[]}";
        SetupLoggedInUserService(isAllInfra: false, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewList.Take(3).ToList();
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = testReplications.Select(x => new InfraObjectView { ReplicationProperties = x.ReferenceId }).AsQueryable();
        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetPaginatedAssignedBusinessServicesByReplications_FiltersCorrectly()
    {
        // Arrange
        await ClearDatabase();
        var assignedInfras = "{\"AssignedBusinessServices\":[{\"Id\":\"" + ReplicationViewFixture.BusinessServiceId + "\"}]}";
        SetupLoggedInUserService(isParent: false, isAllInfra: true, assignedInfras: assignedInfras);

        var testReplications = _replicationViewFixture.ReplicationViewPaginationList.Take(10).ToList();
        testReplications.ForEach(x => x.BusinessServiceId = ReplicationViewFixture.BusinessServiceId);
        testReplications.ForEach(x => x.CompanyId = ReplicationViewFixture.CompanyId);
        _dbContext.ReplicationViews.AddRange(testReplications);
        await _dbContext.SaveChangesAsync();

        var mockInfraObjects = testReplications.Take(5).Select(x => new InfraObjectView { ReplicationProperties = x.ReferenceId }).AsQueryable();
        _mockInfraObjectViewRepository.Setup(x => x.GetPaginatedQuery()).Returns(mockInfraObjects);

        //var specification = new Mock<Specification<ReplicationView>>();
        //specification.Setup(s => s.Criteria).Returns(x => true);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 5, null, "Name", "asc"); ;

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count <= 5);
        Assert.All(result.Data, r => Assert.Equal(ReplicationViewFixture.BusinessServiceId, r.BusinessServiceId));
    }

    #endregion

    #region Edge Cases and Error Scenarios

    [Fact]
    public async Task GetByReplicationIdsAsync_HandlesNullIds()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService();

        // Act
        var result = await _repository.GetByReplicationIdsAsync(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByReplicationIdsAsync_HandlesEmptyIds()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService();

        // Act
        var result = await _repository.GetByReplicationIdsAsync(new List<string>());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ReplicationCountAsync_ReturnsZero_WhenNoData()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isAllInfra: true);

        // Act
        var result = await _repository.ReplicationCountAsync();

        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public async Task GetReplicationNames_ReturnsEmpty_WhenNoData()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isAllInfra: true);

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReplicationList_ReturnsEmpty_WhenNoData()
    {
        // Arrange
        await ClearDatabase();
        SetupLoggedInUserService(isAllInfra: true);

        // Act
        var result = await _repository.GetReplicationList();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion
}
