using ContinuityPatrol.Application.Features.CyberAirGap.Commands.UpdateStatus;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Commands;

public class AirGapStatusUpdateTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<ICyberAirGapRepository> _mockCyberAirGapRepository;
    private readonly Mock<ICyberAirGapStatusRepository> _mockCyberAirGapStatusRepository;
    private readonly AirGapStatusUpdateCommandHandler _handler;

    private readonly Mock<ICyberAirGapRepository> _airGapRepository;
    private readonly Mock<IPublisher> _publisher;
    private readonly Mock<ILoadBalancerRepository> _loadBalancerRepository;
    private readonly Mock<IJobScheduler> _client;
    public AirGapStatusUpdateTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockCyberAirGapRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(_cyberAirGapFixture.CyberAirGaps);
        _mockCyberAirGapStatusRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapStatusRepository(_cyberAirGapFixture.CyberAirGapStatuses);

        _airGapRepository = new Mock<ICyberAirGapRepository>();
        _publisher = new Mock<IPublisher>();
        _loadBalancerRepository = new Mock<ILoadBalancerRepository>();
        _client = new Mock<IJobScheduler>();
        _handler = new AirGapStatusUpdateCommandHandler(
            _airGapRepository.Object ,
            _publisher.Object,
            _loadBalancerRepository.Object, _client.Object );
    }

    [Fact]
    public async Task Handle_UpdateAirGapStatus_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = "Updated Status"
        };
        _airGapRepository
       .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
       .ReturnsAsync(existingAirGap);

        var loadBalancer = new Domain.Entities.LoadBalancer
        {
            // Set required properties for your handler logic
            ConnectionType = "http",
            IPAddress = "127.0.0.1",
            Port = 8080,
            TypeCategory = "Load Balancer"
        };

        _loadBalancerRepository
            .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(loadBalancer);

        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<AirGapStatusUpdateResponse>();
        result.Id.ShouldBe(existingAirGap.ReferenceId);
        result.Message.ShouldContain(existingAirGap.Name);
       
    }

    [Fact]
    public async Task Handle_CallGetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = "Test Status"
        };
        var loadBalancer = new Domain.Entities.LoadBalancer
        {
            // Set required properties for your handler logic
            ConnectionType = "http",
            IPAddress = "127.0.0.1",
            Port = 8080,
            TypeCategory = "Load Balancer"
        };

        _loadBalancerRepository
            .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(loadBalancer);

        _airGapRepository
      .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        command.ShouldNotBeNull();

    }

    [Fact]
    public async Task Handle_CallStatusAddAsync_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = "Test Status Add"
        };

        var loadBalancer = new Domain.Entities.LoadBalancer
        {
            // Set required properties for your handler logic
            ConnectionType = "http",
            IPAddress = "127.0.0.1",
            Port = 8080,
            TypeCategory = "Load Balancer"
        };

        _loadBalancerRepository
            .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(loadBalancer);

        _airGapRepository
      .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);

        await _handler.Handle(command, CancellationToken.None);

        command.ShouldNotBeNull();
    }


    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new AirGapStatusUpdateCommand
        {
            Id = nonExistentId,
            Status = "Test Status NotFound"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapIsInactive()
    {
        // Arrange
        var inactiveAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        inactiveAirGap.IsActive = false;
        var command = new AirGapStatusUpdateCommand
        {
            Id = inactiveAirGap.ReferenceId,
            Status = "Test Status Inactive"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_VerifyStatusEntityCreation_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = "Verification Status"
        };
        var loadBalancer = new Domain.Entities.LoadBalancer
        {
            // Set required properties for your handler logic
            ConnectionType = "http",
            IPAddress = "127.0.0.1",
            Port = 8080,
            TypeCategory = "Load Balancer"
        };

        _loadBalancerRepository
            .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(loadBalancer);

        _airGapRepository
      .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);

        Domain.Entities.CyberAirGapStatus addedStatus = null;

        _mockCyberAirGapStatusRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGapStatus>()))
            .Callback<Domain.Entities.CyberAirGapStatus>(status => addedStatus = status)
            .ReturnsAsync((Domain.Entities.CyberAirGapStatus status) => status);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        command.ShouldNotBeNull();
    }
    [Fact]
    public async Task Handle_UpdateWithDifferentStatuses_When_ValidStatuses()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var statuses = new[] { "Active", "Inactive", "Pending", "Error", "Completed", "Processing" };

        foreach (var status in statuses)
        {
            var command = new AirGapStatusUpdateCommand
            {
                Id = existingAirGap.ReferenceId,
                Status = status
            };
            var loadBalancer = new Domain.Entities.LoadBalancer
            {
                // Set required properties for your handler logic
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "Load Balancer"
            };

            _loadBalancerRepository
                .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(loadBalancer);

            _airGapRepository
          .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
          .ReturnsAsync(existingAirGap);
            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(existingAirGap.ReferenceId);
            result.Message.ShouldContain(existingAirGap.Name);
        }
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = "Cancellation Test"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();
    }

    //[Fact]
    //public async Task Handle_UpdateMultipleAirGapStatuses_When_ValidCommands()
    //{
    //    // Arrange
    //    var airGap = _cyberAirGapFixture.CyberAirGaps.Take(2).ToList();
       
    //    var command = new AirGapStatusUpdateCommand
    //        {
    //            Id = "1",
    //            Status = $"Status_"
    //        };
    //    var loadBalancer = new Domain.Entities.LoadBalancer
    //    {
    //        // Set required properties for your handler logic
    //        ConnectionType = "http",
    //        IPAddress = "127.0.0.1",
    //        Port = 8080,
    //        TypeCategory = "Load Balancer"
    //    };

    //    _loadBalancerRepository
    //        .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
    //        .ReturnsAsync(loadBalancer);

    //    var result = await _handler.Handle(command, CancellationToken.None);
            
    //    result.ShouldNotBeNull();
    //}

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = "Type Test Status"
        };
        var loadBalancer = new Domain.Entities.LoadBalancer
        {
            // Set required properties for your handler logic
            ConnectionType = "http",
            IPAddress = "127.0.0.1",
            Port = 8080,
            TypeCategory = "Load Balancer"
        };

        _loadBalancerRepository
            .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(loadBalancer);

        _airGapRepository
      .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<AirGapStatusUpdateResponse>();
        result.GetType().Name.ShouldBe("AirGapStatusUpdateResponse");
    }

    [Fact]
    public async Task Handle_UpdateWithEmptyStatus_When_EmptyString()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = ""
        };
        var loadBalancer = new Domain.Entities.LoadBalancer
        {
            // Set required properties for your handler logic
            ConnectionType = "http",
            IPAddress = "127.0.0.1",
            Port = 8080,
            TypeCategory = "Load Balancer"
        };

        _loadBalancerRepository
            .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(loadBalancer);

        _airGapRepository
      .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingAirGap.ReferenceId);
        result.Message.ShouldContain(existingAirGap.Name);
    }

    [Fact]
    public async Task Handle_UpdateWithLongStatus_When_LongString()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var longStatus = new string('A', 500); // 500 character status
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = longStatus
        };

        var loadBalancer = new Domain.Entities.LoadBalancer
        {
            // Set required properties for your handler logic
            ConnectionType = "http",
            IPAddress = "127.0.0.1",
            Port = 8080,
            TypeCategory = "Load Balancer"
        };

        _loadBalancerRepository
            .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(loadBalancer);

        _airGapRepository
      .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingAirGap.ReferenceId);
        result.Message.ShouldContain(existingAirGap.Name);
    }

    [Fact]
    public async Task Handle_UpdateWithSpecialCharacters_When_SpecialCharsInStatus()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var specialStatus = "Status with special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?";
        var command = new AirGapStatusUpdateCommand
        {
            Id = existingAirGap.ReferenceId,
            Status = specialStatus
        };

        var loadBalancer = new Domain.Entities.LoadBalancer
        {
            // Set required properties for your handler logic
            ConnectionType = "http",
            IPAddress = "127.0.0.1",
            Port = 8080,
            TypeCategory = "Load Balancer"
        };

        _loadBalancerRepository
            .Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(loadBalancer);

        _airGapRepository
      .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingAirGap.ReferenceId);
        result.Message.ShouldContain(existingAirGap.Name);
    }
}
