//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;

//namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Commands;

///// <summary>
///// Unit tests for ResolveCyberAlertCommand and ResolveCyberAlertCommandHandler
///// Tests command handling, repository interactions, and business logic validation
///// Purpose: Tests resolution of cyber security alerts with proper workflow validation
///// </summary>
//public class ResolveCyberAlertTests : IClassFixture<CyberAlertFixture>
//{
//    private readonly CyberAlertFixture _cyberAlertFixture;
//    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
//    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
//    private readonly Mock<ILoggedInUserService> _mockUserService;
//    private readonly Mock<ILogger<ResolveCyberAlertCommandHandler>> _mockLogger;
//    private readonly ResolveCyberAlertCommandHandler _handler;

//    public ResolveCyberAlertTests(CyberAlertFixture cyberAlertFixture)
//    {
//        _cyberAlertFixture = cyberAlertFixture;
//        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);
//        _mockUserActivityRepository = CyberRepositoryMocks.CreateUserActivityRepository(_cyberAlertFixture.UserActivities);
//        _mockUserService = new Mock<ILoggedInUserService>();
//        _mockLogger = new Mock<ILogger<ResolveCyberAlertCommandHandler>>();

//        // Setup default user service behavior
//        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
//        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");

//        _handler = new ResolveCyberAlertCommandHandler(
//            _mockCyberAlertRepository.Object,
//            _mockUserService.Object,
//            _mockLogger.Object,
//            _mockUserActivityRepository.Object);
//    }

//    /// <summary>
//    /// Test: Command handler resolves cyber alert successfully
//    /// Expected: Repository UpdateAsync called once and alert status updated to Resolved
//    /// </summary>
//    [Fact]
//    public async Task Handle_ResolveCyberAlert_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Acknowledged" || a.Status == "In Progress");
//        var command = new ResolveCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            ResolutionNotes = "Alert resolved - threat neutralized and systems secured"
//        };

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.ShouldNotBeNull();
//        result.Success.ShouldBeTrue();
//        result.Data.ShouldNotBeNull();
//        result.Data.Status.ShouldBe("Resolved");
//        result.Data.ResolvedBy.ShouldBe("TestUser123");
//        result.Data.ResolvedAt.ShouldNotBeNull();
//        _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync(existingAlert.ReferenceId), Times.Once);
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Once);
//    }

//    /// <summary>
//    /// Test: Repository interaction updates entity with resolution properties
//    /// Expected: Entity is updated with resolution timestamp, user, and notes
//    /// </summary>
//    [Fact]
//    public async Task Handle_UpdateEntityWithResolutionProperties_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Acknowledged");
//        var command = new ResolveCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            ResolutionNotes = "Resolved - Malware removed, systems patched, and monitoring enhanced"
//        };

//        CyberAlert updatedEntity = null;
//        _mockCyberAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<CyberAlert>()))
//            .Callback<CyberAlert>(entity => updatedEntity = entity)
//            .ReturnsAsync((CyberAlert entity) => entity);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        updatedEntity.ShouldNotBeNull();
//        updatedEntity.Status.ShouldBe("Resolved");
//        updatedEntity.ResolvedBy.ShouldBe("TestUser123");
//        updatedEntity.ResolvedAt.ShouldNotBeNull();
//        updatedEntity.ResolvedAt.Value.ShouldBeInRange(DateTime.UtcNow.AddMinutes(-1), DateTime.UtcNow.AddMinutes(1));
//        updatedEntity.ResolutionNotes.ShouldBe("Resolved - Malware removed, systems patched, and monitoring enhanced");
//        updatedEntity.IsActive.ShouldBeTrue();
//    }

//    /// <summary>
//    /// Test: Command handler throws NotFoundException for non-existent alert
//    /// Expected: NotFoundException when alert is not found
//    /// </summary>
//    [Fact]
//    public async Task Handle_ThrowNotFoundException_When_AlertNotFound()
//    {
//        // Arrange
//        var command = new ResolveCyberAlertCommand
//        {
//            Id = "non-existent-alert-id",
//            ResolutionNotes = "Attempting to resolve non-existent alert"
//        };

//        // Act & Assert
//        await Should.ThrowAsync<NotFoundException>(async () =>
//            await _handler.Handle(command, CancellationToken.None));

//        _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync("non-existent-alert-id"), Times.Once);
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Never);
//    }

//    /// <summary>
//    /// Test: Command handler throws NotFoundException for inactive alert
//    /// Expected: NotFoundException when alert is inactive
//    /// </summary>
//    [Fact]
//    public async Task Handle_ThrowNotFoundException_When_AlertIsInactive()
//    {
//        // Arrange
//        var inactiveAlert = new CyberAlert
//        {
//            ReferenceId = "inactive-alert-id",
//            Title = "Inactive Alert",
//            Status = "Acknowledged",
//            IsActive = false
//        };
//        _cyberAlertFixture.CyberAlerts.Add(inactiveAlert);

//        var command = new ResolveCyberAlertCommand
//        {
//            Id = "inactive-alert-id",
//            ResolutionNotes = "Attempting to resolve inactive alert"
//        };

//        // Act & Assert
//        await Should.ThrowAsync<NotFoundException>(async () =>
//            await _handler.Handle(command, CancellationToken.None));
//    }

//    /// <summary>
//    /// Test: Command handler throws BadRequestException for already resolved alert
//    /// Expected: BadRequestException when alert is already resolved
//    /// </summary>
//    [Fact]
//    public async Task Handle_ThrowBadRequestException_When_AlertAlreadyResolved()
//    {
//        // Arrange
//        var resolvedAlert = new CyberAlert
//        {
//            ReferenceId = "resolved-alert-id",
//            Title = "Already Resolved Alert",
//            Status = "Resolved",
//            ResolvedBy = "PreviousUser",
//            ResolvedAt = DateTime.UtcNow.AddHours(-1),
//            ResolutionNotes = "Previously resolved",
//            IsActive = true
//        };
//        _cyberAlertFixture.CyberAlerts.Add(resolvedAlert);

//        var command = new ResolveCyberAlertCommand
//        {
//            Id = "resolved-alert-id",
//            ResolutionNotes = "Attempting to re-resolve alert"
//        };

//        // Act & Assert
//        await Should.ThrowAsync<BadRequestException>(async () =>
//            await _handler.Handle(command, CancellationToken.None));

//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Never);
//    }

//    /// <summary>
//    /// Test: Command handler throws BadRequestException for open alert
//    /// Expected: BadRequestException when alert is still open (not acknowledged)
//    /// </summary>
//    [Fact]
//    public async Task Handle_ThrowBadRequestException_When_AlertIsOpen()
//    {
//        // Arrange
//        var openAlert = new CyberAlert
//        {
//            ReferenceId = "open-alert-id",
//            Title = "Open Alert",
//            Status = "Open",
//            IsActive = true
//        };
//        _cyberAlertFixture.CyberAlerts.Add(openAlert);

//        var command = new ResolveCyberAlertCommand
//        {
//            Id = "open-alert-id",
//            ResolutionNotes = "Attempting to resolve open alert without acknowledgment"
//        };

//        // Act & Assert
//        await Should.ThrowAsync<BadRequestException>(async () =>
//            await _handler.Handle(command, CancellationToken.None));

//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Never);
//    }

//    /// <summary>
//    /// Test: Command handler supports cancellation
//    /// Expected: OperationCanceledException when cancellation is requested
//    /// </summary>
//    [Fact]
//    public async Task Handle_SupportCancellation_When_CancellationRequested()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Acknowledged");
//        var command = new ResolveCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            ResolutionNotes = "Test cancellation"
//        };

//        using var cts = new CancellationTokenSource();
//        cts.Cancel();

//        // Act & Assert
//        await Should.ThrowAsync<OperationCanceledException>(async () =>
//            await _handler.Handle(command, cts.Token));
//    }

//    /// <summary>
//    /// Test: Multiple resolution commands are processed correctly
//    /// Expected: Each command resolves the correct alert
//    /// </summary>
//    [Fact]
//    public async Task Handle_ProcessMultipleResolutionCommands_When_ValidCommands()
//    {
//        // Arrange
//        var acknowledgedAlerts = _cyberAlertFixture.CyberAlerts.Where(a => a.Status == "Acknowledged").Take(3).ToList();
//        var commands = acknowledgedAlerts.Select((alert, index) => new ResolveCyberAlertCommand
//        {
//            Id = alert.ReferenceId,
//            ResolutionNotes = $"Resolved alert {index + 1} - threat neutralized"
//        }).ToArray();

//        // Act
//        foreach (var command in commands)
//        {
//            var result = await _handler.Handle(command, CancellationToken.None);
//            result.Success.ShouldBeTrue();
//        }

//        // Assert
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Exactly(3));
//    }

//    /// <summary>
//    /// Test: Command with different severity levels
//    /// Expected: Alerts of all severity levels can be resolved
//    /// </summary>
//    [Fact]
//    public async Task Handle_ResolveAlertsWithDifferentSeverities_When_ValidCommands()
//    {
//        // Arrange
//        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };
//        var alerts = _cyberAlertFixture.CyberAlerts.Where(a => a.Status == "Acknowledged").Take(severityLevels.Length).ToList();

//        for (int i = 0; i < alerts.Count && i < severityLevels.Length; i++)
//        {
//            alerts[i].Severity = severityLevels[i];
//            var command = new ResolveCyberAlertCommand
//            {
//                Id = alerts[i].ReferenceId,
//                ResolutionNotes = $"Resolved {severityLevels[i]} severity alert - appropriate measures taken"
//            };

//            // Act
//            var result = await _handler.Handle(command, CancellationToken.None);

//            // Assert
//            result.Success.ShouldBeTrue();
//            result.Data.Status.ShouldBe("Resolved");
//        }

//        // Verify all resolutions were processed
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Exactly(alerts.Count));
//    }

//    /// <summary>
//    /// Test: Command with comprehensive resolution notes
//    /// Expected: Detailed resolution notes are handled correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_HandleComprehensiveResolutionNotes_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Acknowledged");
//        var comprehensiveNotes = @"RESOLUTION SUMMARY:
//1. Threat Analysis: SQL injection attempt detected on web application
//2. Immediate Actions: Blocked malicious IP addresses, isolated affected systems
//3. Investigation: Reviewed logs, identified attack vector through contact form
//4. Remediation: Applied security patches, updated WAF rules, enhanced input validation
//5. Monitoring: Increased monitoring on web applications, implemented additional alerting
//6. Documentation: Updated incident response procedures, conducted team briefing
//7. Follow-up: Scheduled security assessment, planned penetration testing";

//        var command = new ResolveCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            ResolutionNotes = comprehensiveNotes
//        };

//        CyberAlert updatedEntity = null;
//        _mockCyberAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<CyberAlert>()))
//            .Callback<CyberAlert>(entity => updatedEntity = entity)
//            .ReturnsAsync((CyberAlert entity) => entity);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.Success.ShouldBeTrue();
//        updatedEntity.ShouldNotBeNull();
//        updatedEntity.Status.ShouldBe("Resolved");
//        updatedEntity.ResolutionNotes.ShouldBe(comprehensiveNotes);
//        updatedEntity.ResolutionNotes.ShouldContain("RESOLUTION SUMMARY");
//        updatedEntity.ResolutionNotes.ShouldContain("Follow-up");
//    }

//    /// <summary>
//    /// Test: Command with special characters in resolution notes
//    /// Expected: Special characters are handled correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_HandleSpecialCharactersInResolutionNotes_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Acknowledged");
//        var specialCharNotes = "Resolved: Alert contained special chars !@#$%^&*()_+-=[]{}|;':\",./<>? and HTML <script>alert('resolved')</script> - sanitized and secured";

//        var command = new ResolveCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            ResolutionNotes = specialCharNotes
//        };

//        CyberAlert updatedEntity = null;
//        _mockCyberAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<CyberAlert>()))
//            .Callback<CyberAlert>(entity => updatedEntity = entity)
//            .ReturnsAsync((CyberAlert entity) => entity);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.Success.ShouldBeTrue();
//        updatedEntity.ShouldNotBeNull();
//        updatedEntity.Status.ShouldBe("Resolved");
//        updatedEntity.ResolutionNotes.ShouldContain("!@#$%^&*()");
//    }

//    /// <summary>
//    /// Test: Command without resolution notes
//    /// Expected: Alert can be resolved without detailed notes
//    /// </summary>
//    [Fact]
//    public async Task Handle_ResolveWithoutNotes_When_ValidCommand()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Acknowledged");
//        var command = new ResolveCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId
//            // No resolution notes provided
//        };

//        CyberAlert updatedEntity = null;
//        _mockCyberAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<CyberAlert>()))
//            .Callback<CyberAlert>(entity => updatedEntity = entity)
//            .ReturnsAsync((CyberAlert entity) => entity);

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.Success.ShouldBeTrue();
//        updatedEntity.ShouldNotBeNull();
//        updatedEntity.Status.ShouldBe("Resolved");
//        updatedEntity.ResolvedBy.ShouldBe("TestUser123");
//        updatedEntity.ResolvedAt.ShouldNotBeNull();
//    }

//    /// <summary>
//    /// Test: Command with user activity logging
//    /// Expected: User activity is logged for audit trail
//    /// </summary>
//    [Fact]
//    public async Task Handle_LogUserActivity_When_ValidResolution()
//    {
//        // Arrange
//        var existingAlert = _cyberAlertFixture.CyberAlerts.First(a => a.Status == "Acknowledged");
//        var command = new ResolveCyberAlertCommand
//        {
//            Id = existingAlert.ReferenceId,
//            ResolutionNotes = "Alert resolved for audit trail test"
//        };

//        // Act
//        var result = await _handler.Handle(command, CancellationToken.None);

//        // Assert
//        result.Success.ShouldBeTrue();
//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//    }

//    /// <summary>
//    /// Test: Command performance with rapid succession
//    /// Expected: Multiple rapid resolution operations are handled correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_ProcessRapidResolutionOperations_When_MultipleCommands()
//    {
//        // Arrange
//        var acknowledgedAlerts = _cyberAlertFixture.CyberAlerts.Where(a => a.Status == "Acknowledged").Take(10).ToList();
//        var commands = acknowledgedAlerts.Select((alert, index) => new ResolveCyberAlertCommand
//        {
//            Id = alert.ReferenceId,
//            ResolutionNotes = $"Rapid resolution {index + 1}"
//        }).ToList();

//        // Act
//        var tasks = commands.Select(cmd => _handler.Handle(cmd, CancellationToken.None));
//        var results = await Task.WhenAll(tasks);

//        // Assert
//        results.ShouldAllBe(result => result.Success);
//        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Exactly(10));
//    }
//}
