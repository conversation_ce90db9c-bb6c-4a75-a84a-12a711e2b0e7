using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Queries;

public class GetCyberAirGapStatusListTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<ICyberAirGapStatusRepository> _mockCyberAirGapStatusRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetCyberAirGapStatusListQueryHandler _handler;

    public GetCyberAirGapStatusListTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockCyberAirGapStatusRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(_cyberAirGapStatusFixture.CyberAirGapStatuses);
        _mockMapper = new Mock<IMapper>();

        _handler = new GetCyberAirGapStatusListQueryHandler(
            _mockMapper.Object,
            _mockCyberAirGapStatusRepository.Object);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_ValidData()
    {
        // Arrange
        var query = new GetCyberAirGapStatusListQuery();
        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select(e => new CyberAirGapStatusListVm
        {
            Id = e.ReferenceId,
            AirGapId = e.AirGapId,
            AirGapName = e.AirGapName,
            SourceSiteName = e.SourceSiteName,
            TargetSiteName = e.TargetSiteName,
            Port = e.Port,
            Status = e.Status
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapStatusListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<CyberAirGapStatusListVm>>();
        result.Count.ShouldBe(activeEntities.Count);
        result.ShouldAllBe(vm => !string.IsNullOrEmpty(vm.Id));
        result.ShouldAllBe(vm => !string.IsNullOrEmpty(vm.AirGapName));

        _mockCyberAirGapStatusRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockMapper.Verify(x => x.Map<List<CyberAirGapStatusListVm>>(activeEntities), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap status list when no data exists
    /// Expected: Returns empty list
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_NoData()
    {
        // Arrange
        var query = new GetCyberAirGapStatusListQuery();
        var emptyRepository = CyberAirGapStatusRepositoryMocks.CreateEmptyCyberAirGapStatusRepository();

        var handler = new GetCyberAirGapStatusListQueryHandler(
            _mockMapper.Object,
            emptyRepository.Object);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<CyberAirGapStatusListVm>>();
        result.Count.ShouldBe(0);

        emptyRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockMapper.Verify(x => x.Map<List<CyberAirGapStatusListVm>>(It.IsAny<List<Domain.Entities.CyberAirGapStatus>>()), Times.Never);
    }

    /// <summary>
    /// Test: Get cyber air gap status list with cancellation token
    /// Expected: Respects cancellation and throws OperationCanceledException
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_CancellationRequested()
    {
        // Arrange
        var query = new GetCyberAirGapStatusListQuery();
        var cancellationToken = new CancellationToken(true);

       
    }

    /// <summary>
    /// Test: Get cyber air gap status list when repository fails
    /// Expected: Throws exception
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_RepositoryFails()
    {
        // Arrange
        var query = new GetCyberAirGapStatusListQuery();
        var mockFailingRepository = CyberAirGapStatusRepositoryMocks.CreateFailingCyberAirGapStatusRepository();

        var handler = new GetCyberAirGapStatusListQueryHandler(
            _mockMapper.Object,
            mockFailingRepository.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(query, CancellationToken.None));

        exception.Message.ShouldBe("List operation failed");
        _mockMapper.Verify(x => x.Map<List<CyberAirGapStatusListVm>>(It.IsAny<List<Domain.Entities.CyberAirGapStatus>>()), Times.Never);
    }

    /// <summary>
    /// Test: Get cyber air gap status list with mapper integration
    /// Expected: Correctly maps entities to view models
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_MapperIntegration()
    {
        // Arrange
        var query = new GetCyberAirGapStatusListQuery();
        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select((e, index) => new CyberAirGapStatusListVm
        {
            Id = e.ReferenceId,
            AirGapId = e.AirGapId,
            AirGapName = e.AirGapName,
            SourceSiteId = e.SourceSiteId,
            SourceSiteName = e.SourceSiteName,
            TargetSiteId = e.TargetSiteId,
            TargetSiteName = e.TargetSiteName,
            Port = e.Port,
            Description = e.Description,
            Source = e.Source,
            Target = e.Target,
            SourceComponentId = e.SourceComponentId,
            SourceComponentName = e.SourceComponentName,
            TargetComponentId = e.TargetComponentId,
            TargetComponentName = e.TargetComponentName,
            EnableWorkflowId = e.EnableWorkflowId,
            DisableWorkflowId = e.DisableWorkflowId,
            ErrorMessage = e.ErrorMessage,
            WorkflowStatus = e.WorkflowStatus,
            StartTime = e.StartTime,
            EndTime = e.EndTime,
            RPO = e.RPO,
            Status = e.Status,
            IsFileTransfered = e.IsFileTransfered
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapStatusListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(expectedVmList.Count);

        for (int i = 0; i < result.Count; i++)
        {
            result[i].Id.ShouldBe(expectedVmList[i].Id);
            result[i].AirGapId.ShouldBe(expectedVmList[i].AirGapId);
            result[i].AirGapName.ShouldBe(expectedVmList[i].AirGapName);
            result[i].SourceSiteName.ShouldBe(expectedVmList[i].SourceSiteName);
            result[i].TargetSiteName.ShouldBe(expectedVmList[i].TargetSiteName);
            result[i].Port.ShouldBe(expectedVmList[i].Port);
            result[i].Status.ShouldBe(expectedVmList[i].Status);
        }

        _mockMapper.Verify(x => x.Map<List<CyberAirGapStatusListVm>>(activeEntities), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap status list for different status types
    /// Expected: Successfully retrieves statuses for various status types
    /// </summary>
    [Theory]
    [InlineData("Active")]
    [InlineData("Warning")]
    [InlineData("Error")]
    [InlineData("Maintenance")]
    [InlineData("Disabled")]
    public async Task Handle_GetCyberAirGapStatusList_When_DifferentStatusTypes(string status)
    {
        // Arrange
        var testEntities = new List<Domain.Entities.CyberAirGapStatus>
        {
            new Domain.Entities.CyberAirGapStatus
            {
                ReferenceId = $"test-{status.ToLower()}-001",
                AirGapId = $"airgap-{status.ToLower()}-001",
                AirGapName = $"{status} Air Gap Status System",
                SourceSiteName = $"{status} Source Site",
                TargetSiteName = $"{status} Target Site",
                Status = status,
                IsActive = true
            }
        };

        var mockRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(testEntities);
        var handler = new GetCyberAirGapStatusListQueryHandler(_mockMapper.Object, mockRepository.Object);

        var query = new GetCyberAirGapStatusListQuery();

        var expectedVmList = testEntities.Select(e => new CyberAirGapStatusListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName,
            SourceSiteName = e.SourceSiteName,
            TargetSiteName = e.TargetSiteName,
            Status = e.Status
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapStatusListVm>>(testEntities))
            .Returns(expectedVmList);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        result.First().AirGapName.ShouldContain(status);
        result.First().SourceSiteName.ShouldContain(status);
        result.First().TargetSiteName.ShouldContain(status);
        result.First().Status.ShouldBe(status);

        mockRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap status list only returns active entities
    /// Expected: Filters out inactive entities
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_OnlyActiveEntities()
    {
        // Arrange
        var testEntities = new List<Domain.Entities.CyberAirGapStatus>
        {
            new Domain.Entities.CyberAirGapStatus
            {
                ReferenceId = "active-001",
                AirGapName = "Active Air Gap Status 1",
                IsActive = true
            },
            new Domain.Entities.CyberAirGapStatus
            {
                ReferenceId = "active-002",
                AirGapName = "Active Air Gap Status 2",
                IsActive = true
            },
            new Domain.Entities.CyberAirGapStatus
            {
                ReferenceId = "inactive-001",
                AirGapName = "Inactive Air Gap Status 1",
                IsActive = false
            },
            new Domain.Entities.CyberAirGapStatus
            {
                ReferenceId = "inactive-002",
                AirGapName = "Inactive Air Gap Status 2",
                IsActive = false
            }
        };

        var mockRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(testEntities);
        var handler = new GetCyberAirGapStatusListQueryHandler(_mockMapper.Object, mockRepository.Object);

        var query = new GetCyberAirGapStatusListQuery();
        var activeEntities = testEntities.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select(e => new CyberAirGapStatusListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapStatusListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(2); // Only active entities
        result.ShouldAllBe(vm => vm.AirGapName.Contains("Active"));
        result.ShouldNotContain(vm => vm.AirGapName.Contains("Inactive"));

        mockRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_LargeDataset()
    {
        // Arrange
        var largeRepository = CyberAirGapStatusRepositoryMocks.CreateLargeCyberAirGapStatusRepository(1000);
        var handler = new GetCyberAirGapStatusListQueryHandler(_mockMapper.Object, largeRepository.Object);

        var query = new GetCyberAirGapStatusListQuery();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapStatusListVm>>(It.IsAny<List<Domain.Entities.CyberAirGapStatus>>()))
            .Returns((List<Domain.Entities.CyberAirGapStatus> entities) => entities.Select(e => new CyberAirGapStatusListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName,
                Status = e.Status
            }).ToList());

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await handler.Handle(query, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1000);
        stopwatch.ElapsedMilliseconds.ShouldBeLessThan(1000); // Should complete within 1 second

        largeRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

   
    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_ValidatingResponse()
    {
        // Arrange
        var query = new GetCyberAirGapStatusListQuery();
        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select(e => new CyberAirGapStatusListVm
        {
            Id = e.ReferenceId,
            AirGapId = e.AirGapId,
            AirGapName = e.AirGapName,
            SourceSiteId = e.SourceSiteId,
            SourceSiteName = e.SourceSiteName,
            TargetSiteId = e.TargetSiteId,
            TargetSiteName = e.TargetSiteName,
            Port = e.Port,
            Description = e.Description,
            Source = e.Source,
            Target = e.Target,
            SourceComponentId = e.SourceComponentId,
            SourceComponentName = e.SourceComponentName,
            TargetComponentId = e.TargetComponentId,
            TargetComponentName = e.TargetComponentName,
            EnableWorkflowId = e.EnableWorkflowId,
            DisableWorkflowId = e.DisableWorkflowId,
            ErrorMessage = e.ErrorMessage,
            WorkflowStatus = e.WorkflowStatus,
            StartTime = e.StartTime,
            EndTime = e.EndTime,
            RPO = e.RPO,
            Status = e.Status,
            IsFileTransfered = e.IsFileTransfered
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapStatusListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<CyberAirGapStatusListVm>>();

        // Validate each item in the list has required properties
        foreach (var item in result)
        {
            item.Id.ShouldNotBeNullOrEmpty();
            item.AirGapName.ShouldNotBeNullOrEmpty();
            // Other properties can be null/empty but should be mapped correctly
        }
    }

    [Fact]
    public async Task Handle_GetCyberAirGapStatusList_When_RepositoryVerification()
    {
        // Arrange
        var query = new GetCyberAirGapStatusListQuery();
        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select(e => new CyberAirGapStatusListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapStatusListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();

        // Verify repository was called exactly once
        _mockCyberAirGapStatusRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockCyberAirGapStatusRepository.VerifyNoOtherCalls();

        // Verify mapper was called exactly once with the correct entities
        _mockMapper.Verify(x => x.Map<List<CyberAirGapStatusListVm>>(activeEntities), Times.Once);
        _mockMapper.VerifyNoOtherCalls();
    }
}
