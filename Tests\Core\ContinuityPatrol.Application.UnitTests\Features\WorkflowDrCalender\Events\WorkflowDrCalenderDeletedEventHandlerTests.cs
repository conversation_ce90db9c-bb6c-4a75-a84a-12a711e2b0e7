﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Events
{
  public class WorkflowDrCalenderDeletedEventHandlerTests : IClassFixture<WorkflowDrCalenderFixture>
    {

        private readonly WorkflowDrCalenderFixture _workflowDrcalenderFixture;

        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

        private readonly WorkflowDrCalenderDeletedEventHandler _handler;

        public WorkflowDrCalenderDeletedEventHandlerTests(WorkflowDrCalenderFixture workflowDrcalenderFixture)
        {
            _workflowDrcalenderFixture = workflowDrcalenderFixture;

            var mockLoggedInUserService = new Mock<ILoggedInUserService>();

            var mockWorkflowDrCalenderEventLogger = new Mock<ILogger<WorkflowDrCalenderDeletedEventHandler>>();

            _mockUserActivityRepository = WorkflowDrCalenderRepositoryMocks.CreateWorkflowDrCalenderEventRepository(_workflowDrcalenderFixture.UserActivities);

            _handler = new WorkflowDrCalenderDeletedEventHandler(mockLoggedInUserService.Object, mockWorkflowDrCalenderEventLogger.Object, _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_IncreaseUserActivityCount_When_DeleteWorkflowDrEventDeleted()
        {
            _workflowDrcalenderFixture.UserActivities[0].LoginName = "Test";

            var result = _handler.Handle(_workflowDrcalenderFixture.WorkflowDrDeletedEvent, CancellationToken.None);

            result.Equals(_workflowDrcalenderFixture.UserActivities[0].LoginName);

            await Task.CompletedTask;
        }

        [Fact]
        public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
        {
            await _handler.Handle(_workflowDrcalenderFixture.WorkflowDrDeletedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_UserActivityCount_When_DeleteWorkflowDrEventDeleted()
        {
            _workflowDrcalenderFixture.UserActivities[0].LoginName = "Test";

            var result = _handler.Handle(_workflowDrcalenderFixture.WorkflowDrDeletedEvent, CancellationToken.None);

            result.Equals(_workflowDrcalenderFixture.UserActivities[0].Id);

            result.Equals(_workflowDrcalenderFixture.WorkflowDrDeletedEvent.Name);

            await Task.CompletedTask;
        }
    }
}
