﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowDrCalenderModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Queries
{
  public class GetWorkflowDrCalenderListQueryHandlerTests : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly WorkflowDrCalenderFixture _workflowDrcalenderFixture;

        private Mock<IWorkflowDrCalenderRepository> _mockWorkflowDrCalenderRepository;

        private readonly GetWorkflowDrCalenderListQueryHandler _handler;

        public GetWorkflowDrCalenderListQueryHandlerTests(WorkflowDrCalenderFixture workflowDrCalenderFixture)
        {
            _workflowDrcalenderFixture = workflowDrCalenderFixture;

            _mockWorkflowDrCalenderRepository = new Mock<IWorkflowDrCalenderRepository>();

            _handler = new GetWorkflowDrCalenderListQueryHandler(_workflowDrcalenderFixture.Mapper, _mockWorkflowDrCalenderRepository.Object);
        }

        [Fact]
        public async Task Handle_Return_Active_WorkflowTempCount()
        {
            _mockWorkflowDrCalenderRepository
       .Setup(x => x.ListAllAsync())
       .ReturnsAsync(_workflowDrcalenderFixture.WorkflowDrCalenderInfos);

            var result = await _handler.Handle(new GetWorkflowDrCalenderListQuery(), CancellationToken.None);

            result.ShouldBeOfType<List<WorkflowDrCalenderListVm>>();

            result.Count.ShouldBe(_workflowDrcalenderFixture.WorkflowDrCalenderInfos.Count);
        }

        [Fact]
        public async Task Handle_Call_GetAllMethod_OneTime()
        {
            _mockWorkflowDrCalenderRepository
        .Setup(x => x.ListAllAsync())
        .ReturnsAsync(_workflowDrcalenderFixture.WorkflowDrCalenderInfos);

            await _handler.Handle(new GetWorkflowDrCalenderListQuery(), CancellationToken.None);

            _mockWorkflowDrCalenderRepository.Verify(x => x.ListAllAsync(), Times.Once());
        }

        [Fact]

        public async Task Handle_Return_Valid_WorkflowTempDetail()
        {
            _mockWorkflowDrCalenderRepository
       .Setup(x => x.ListAllAsync())
       .ReturnsAsync(_workflowDrcalenderFixture.WorkflowDrCalenderInfos);

            var result = await _handler.Handle(new GetWorkflowDrCalenderListQuery(), CancellationToken.None);

            result.ShouldBeOfType<List<WorkflowDrCalenderListVm>>();

            result[0].Id.ShouldBe(_workflowDrcalenderFixture.WorkflowDrCalenderInfos[0].ReferenceId);
            result[0].ProfileId.ShouldBe(_workflowDrcalenderFixture.WorkflowDrCalenderInfos[0].ProfileId);
            result[0].WorkflowOperationId.ShouldBe(_workflowDrcalenderFixture.WorkflowDrCalenderInfos[0].WorkflowOperationId);
            result[0].Properties.ShouldBe(_workflowDrcalenderFixture.WorkflowDrCalenderInfos[0].Properties);
        }

        [Fact]
        public async Task Handle_Return_EmptyList_When_NoRecords()
        {
            _mockWorkflowDrCalenderRepository = WorkflowDrCalenderRepositoryMocks.GetWorkflowDrEmptyRepository();

            var handler = new GetWorkflowDrCalenderListQueryHandler(_workflowDrcalenderFixture.Mapper, _mockWorkflowDrCalenderRepository.Object);

            var result = await handler.Handle(new GetWorkflowDrCalenderListQuery(), CancellationToken.None);

            result.Count.ShouldBe(0);
        }
    }
}
