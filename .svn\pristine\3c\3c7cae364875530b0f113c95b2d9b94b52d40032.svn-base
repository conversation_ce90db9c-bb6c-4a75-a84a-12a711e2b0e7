﻿using ContinuityPatrol.Application.Features.Job.Commands.Create;
using ContinuityPatrol.Application.Features.Job.Commands.Update;
using ContinuityPatrol.Application.Features.Job.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.Job.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Job.Queries.GetInfraSolutionTypeNameUnique;
using ContinuityPatrol.Application.Features.Job.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.JobModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]
public class MonitoringJobController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly ILogger<MonitoringJobController> _logger;
    private readonly IDataProvider _provider;

    public MonitoringJobController(IPublisher publisher, IMapper mapper, ILogger<MonitoringJobController> logger, IDataProvider provider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _provider = provider;
    }
    [EventCode(EventCodes.MonitoringJob.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in monitoring job.");
        try
        {
            await _publisher.Publish(new JobPaginatedEvent());

            // var jobView = await _provider.JobService.GetJobPaginatedList(new GetJobPaginatedListQuery());

            var templateName = await _provider.Template.GetTemplateList();

            var monitoringTemplates = templateName.SelectMany(item => item.TemplateListVm).Where(item1 => item1.ActionType == "Monitoring").ToDictionary(item1 => item1.Id, item1 => item1.Name);

            var infraObjects = await _provider.InfraObject.GetInfraObjectNames();

            var groupPolicies = await _provider.GroupPolicy.GetGroupPolicies();

            var replicationTypeList = await _provider.ComponentType.GetComponentTypeListByName("Replication");

            var replicationTypeDictionary = new Dictionary<string, string>();

            foreach (var item in replicationTypeList)
            {
                var value = JsonConvert.DeserializeObject<dynamic>(item.Properties);

                var name = value!.SelectToken("name").ToString();

                var id = item.Id;

                if (id != null && name != null)
                {
                    replicationTypeDictionary[name] = item.Id;
                }
            }

            var jobModel = new JobViewModel
            {
                //PaginatedJob = jobView,
                Template = monitoringTemplates,
                ReplicationTypes = replicationTypeDictionary,
                InfraObjectNameVms = infraObjects,
                GroupPolicies = groupPolicies
            };
            _logger.LogDebug("Successfully retrieved list in monitoring job.");

            return View(jobModel);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring job page while retrieving the list.", ex);

            TempData.Set(new NotificationMessage(NotificationType.Error, ex.GetMessage()));

            return View("List");
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.MonitoringJob.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(JobViewModel jobModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in monitoring job.");

        BaseResponse result;
        try
        {
            if (string.IsNullOrEmpty(jobModel.Id))
            {
                var jobCommand = _mapper.Map<CreateJobCommand>(jobModel);

                _logger.LogDebug($"Creating monitoring job '{jobCommand.Name}'.");

                result = await _provider.JobService.CreateAsync(jobCommand);
            }
            else
            {
                var jobCommand = _mapper.Map<UpdateJobCommand>(jobModel);

                _logger.LogDebug($"Updating monitoring job '{jobCommand.Name}'.");

                result = await _provider.JobService.UpdateAsync(jobCommand);
            }

            _logger.LogDebug("CreateOrUpdate operation completed successfully in monitoring job, returning view.");

            return RouteToPostView(result);
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation Error on monitoring job : {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring job page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    private IActionResult RouteToPostView(BaseResponse result)
    {
        _logger.LogDebug("Entering RouteToPostView method in replication job.");
        try
        {
            TempData.Set(result.Success
            ? new NotificationMessage(NotificationType.Success, result.Message)
            : new NotificationMessage(NotificationType.Error, result.Message)
            );

            return RedirectToAction("List", "MonitoringJob", new { Area = "Manage" });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication job page while redirect to list.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.MonitoringJob.ResetMonitoringJob)]
    public async Task<IActionResult> ResetMonitoringJob(JobViewModel jobModel)
    {
        _logger.LogDebug("Entering ResetMonitoringJob method in monitoring job.");

        try
        {
            jobModel.Status = "Pending";

            var jobCommand = _mapper.Map<UpdateJobCommand>(jobModel);

            var result = await _provider.JobService.UpdateAsync(jobCommand);

            _logger.LogDebug($"Successfully reset job status to {jobCommand.Status} in monitoring job.");

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring job page while reset job status.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [EventCode(EventCodes.MonitoringJob.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in monitoring job.");
        try
        {
            var job = await _provider.JobService.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in monitoring job.");

            TempData.NotifySuccess(job.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring job page while deleting record.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }


    [HttpGet]
    [EventCode(EventCodes.MonitoringJob.IsExist)]
    public async Task<bool> IsJobNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsJobNameExist method in monitoring job.");
        try
        {
            var isJobNameExist = await _provider.JobService.IsJobNameExist(name, id);

            _logger.LogDebug($"Successfully retrieved name exist detail for :'{name}' in monitoring job.");

            return isJobNameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on monitoring job page while retrieving name exist detail for :'{name}'", ex);
            return false;
        }
    }

    [HttpGet]
    [EventCode(EventCodes.MonitoringJob.GetGroupNodeList)]
    public async Task<JsonResult> GetGroupNodeList()
    {
        _logger.LogDebug("Entering IsJobNameExist method in monitoring job.");
        try
        {
            var groupNodeList = await _provider.GroupPolicy.GetGroupPolicies();

            _logger.LogDebug($"Successfully retrieved group policies detail for : in monitoring job.");

            return Json(new {Success = true, data = groupNodeList });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on monitoring job page while retrieving group policies detail for :", ex);
            return ex.GetJsonException();
        }

    }
    [EventCode(EventCodes.MonitoringJob.Pagination)]
    public async Task<JsonResult> GetPagination(GetJobPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in monitoring job.");
        try
        {
            var paginationList = await _provider.JobService.GetJobPaginatedList(query);

            _logger.LogDebug("Successfully retrieved pagination list in monitoring job.");

            return Json(new { Success = true, data = paginationList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring job page while retrieving pagination list.", ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [EventCode(EventCodes.MonitoringJob.GetGroupPolicyNames)]
    public async Task<IActionResult> GetGroupPolicyNames()
    {
        _logger.LogDebug("Entering GetGroupPolicyNames method in monitoring job.");
        try
        {
            var groupPolicyNames = await _provider.GroupPolicy.GetGroupPolicyNames();

            _logger.LogDebug("Successfully retrieved group policy names in monitoring job.");

            return Json(groupPolicyNames);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring job page while retrieving group policy names", ex);
            return ex.GetJsonException();
        }

    }
    [HttpPut]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.MonitoringJob.UpdateJobState)]
    public async Task<IActionResult> UpdateJobState(UpdateJobStateCommand updateJobStateCommand)
    {
        _logger.LogDebug("Entering UpdateJobState method in monitoring job.");
        try
        {
            var getState = await _provider.JobService.UpdateJobState(updateJobStateCommand);

            _logger.LogDebug("Successfully updated job state in monitoring job.");

            return Json(new { Success = true, data = getState });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring job page while updating job state.", ex);

            return ex.GetJsonException();
        }

    }
    [EventCode(EventCodes.MonitoringJob.GetInfraObjectListByReplicationTypeId)]
    public async Task<JsonResult> GetInfraObjectListByReplicationTypeId(string replicationTypeId)
    {
        _logger.LogDebug("Entering GetInfraObjectListByReplicationTypeId method in monitoring job.");

        if (string.IsNullOrWhiteSpace(replicationTypeId))
        {
            return Json(new { Success = false, Message = "replicationTypeId is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var result = await _provider.InfraObject.GetInfraObjectListByReplicationTypeId(replicationTypeId);

                _logger.LogDebug($"Successfully retrieved infra object list by replication type id:'{replicationTypeId}' in monitoring job.");

                return Json(new { Success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on monitoring job page while retrieving infra object list by replication type id.", ex);

                return ex.GetJsonException();
            }
        }
    }
    [EventCode(EventCodes.MonitoringJob.GetTemplateByReplicationTypeId)]
    public async Task<JsonResult> GetTemplateByReplicationTypeId(string replicationTypeId)
    {
        _logger.LogDebug("Entering GetTemplateByReplicationTypeId method in monitoring job.");

        if (string.IsNullOrWhiteSpace(replicationTypeId))
        {
            return Json(new { Success = false, Message = "replicationTypeId is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var result = await _provider.Template.GetTemplateByReplicationTypeId(replicationTypeId);

                _logger.LogDebug($"Successfully retrieved infra object list by replication type id:'{replicationTypeId}' in monitoring job.");

                return Json(new { Success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on monitoring job page while retrieving template list by replication type id.", ex);

                return ex.GetJsonException();
            }
        }
    }
    [EventCode(EventCodes.MonitoringJob.GetSolutionTypeByPolicy)]
    public async Task<JsonResult> GetSolutionTypeByPolicy(string policy)
    {
        _logger.LogDebug("Entering GetTemplateByReplicationTypeId method in monitoring job.");

        if (string.IsNullOrWhiteSpace(policy))
        {
            return Json(new { Success = false, Message = "replicationTypeId is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var result = await _provider.JobService.GetSolutionTypeByPolicy(policy);

                _logger.LogDebug($"Successfully retrieved infra object list by replication type id:'{policy}' in monitoring job.");

                return Json(new { Success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on monitoring job page while retrieving template list by replication type id.", ex);

                return ex.GetJsonException();
            }
        }
    }
    [EventCode(EventCodes.MonitoringJob.IsInfraObjectConfiguredWithSolutionType)]
    public async Task<IActionResult> IsInfraObjcetConfiguredWithSolutionType(string solutionTypeId,string infraObjectDtl,string Id)
    {
        _logger.LogDebug("Entering IsInfraObjcetConfiguredWithSolutionType method in monitoring job.");

        if (solutionTypeId.IsNullOrWhiteSpace() || infraObjectDtl.IsNullOrWhiteSpace() )
        {
            return Json(new { Success = false, Message = "InfraObjectId and SolutionTypeId are not in a valid format", ErrorCode = 0 });
        }

        try
        {
            var infraObjectDtlList = JsonConvert.DeserializeObject<List<InfraObjectDtl>>(infraObjectDtl);

            var query = new GetInfraSolutionTypeNameUniqueQuery
            {
                SolutionTypeId = solutionTypeId,
                InfraObjectDtl = infraObjectDtlList,
                Id=Id
            };
            var result = await _provider.JobService.GetInfraSolutionTypeNameUnique(query);
            _logger.LogDebug($"Successfully retrieved InfraObjectId and SolutionTypeId by replication type '{query}' in monitoring job.");

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred on the monitoring job page while retrieving template list by replication type ID.");

            return Json(new { Success = false, Message = "An error occurred while processing your request.", ErrorCode = 500, ExceptionMessage = ex.Message });
        }
    }

}
