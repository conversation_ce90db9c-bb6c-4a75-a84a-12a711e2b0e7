﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowIdUnique;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowprofileByInfraobjectId;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowProfileByReferenceId;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowProfileInfoByProfileId;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Orchestration;

public class WorkflowProfileInfoService : BaseService, IWorkflowProfileInfoService
{
    public WorkflowProfileInfoService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowProfileInfoCommand createWorkflowProfileInfo)
    {
        Logger.LogDebug($"Create WorkflowProfileInfo '{createWorkflowProfileInfo.ProfileName}'");

        return await Mediator.Send(createWorkflowProfileInfo);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfo)
    {
        Logger.LogDebug($"Update WorkflowProfileInfo '{updateWorkflowProfileInfo.ProfileName}'");

        return await Mediator.Send(updateWorkflowProfileInfo);
    }

    public async Task<BaseResponse> DeleteAsync(string workflowProfileInfoId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowProfileInfoId, "WorkflowProfileInfo Id");

        Logger.LogDebug($"Delete WorkflowProfileInfo Details by Id '{workflowProfileInfoId}'");

        return await Mediator.Send(new DeleteWorkflowProfileInfoCommand { Id = workflowProfileInfoId });
    }

    public async Task<WorkflowProfileInfoDetailVm> GetByReferenceId(string workflowProfileId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowProfileId, "WorkflowProfileInfo Id");

        Logger.LogDebug($"Get WorkflowProfileInfo Detail by Id '{workflowProfileId}'");

        return await Mediator.Send(new GetWorkflowProfileInfoDetailQuery { Id = workflowProfileId });
    }

    public async Task<List<GetWorkflowProfileInfoByProfileIdVm>> GetWorkflowProfileInfoByProfileId(string profileId)
    {
        //Guard.Against.InvalidGuidOrEmpty(profileId, "Profile Id");

        Logger.LogDebug($"Get WorkflowProfileInfo Detail by Profile Id '{profileId}'");

        return await Mediator.Send(new GetWorkflowProfileInfoByProfileIdQuery { ProfileId = profileId });
    }

    public async Task<bool> IsWorkflowProfileInfoNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "WorkflowProfileInfo Name");

        Logger.LogDebug($"Check Name Exists Detail by WorkflowProfileInfo Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetWorkflowProfileInfoNameUniqueQuery
            { WorkflowProfileName = name, WorkflowProfileId = id });
    }

    public async Task<PaginatedResult<WorkflowProfileInfoListVm>> GetPaginatedWorkflowProfileInfos(
        GetWorkflowProfileInfoPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in WorkflowProfileInfo Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<GetWorkflowProfileInfoByWorkflowIdVm> WorkflowProfileInfoByWorkflowIdExist(string workflowId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "Workflow Id");

        Logger.LogDebug($"Check Workflow Id '{workflowId}'");

        return await Mediator.Send(new GetWorkflowProfileInfoByWorkflowIdUniqueQuery { WorkflowId = workflowId });
    }

    //public async Task<List<WorkflowProfileList>> GetAllWorkflow_profileInfo()
    //{
    //    Logger.LogInformation("Get All WorkflowProfileInfo");

    //    return await Mediator.Send(new WorkflowProfileListQuery());
    //}

    //public async Task<BaseResponse> UpdateIsFourEye(string id, string isFourEye)
    //{
    //    Guard.Against.NullOrWhiteSpace(id, " : Workflow-Profile reference Id");

    //    return await Mediator.Send(new UpdateWorkflowProfileIsFourEyeCommand { Id = id, IsFourEye = isFourEye });
    //}

    public async Task<List<WorkflowProfileInfoNameVm>> GetWorkflowProfileInfoByInfraObjectId(string infraId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraId, "WorkflowProfileInfo by InfraObject Id");

        Logger.LogDebug($"Get WorkflowProfileInfo Detail by Id '{infraId}'");

        return await Mediator.Send(new GetWorkflowprofileByInfraobjectIdQuery { infraobjectId = infraId });
    }

    //public async Task<PaginatedResult<WorkflowProfileInfoListVm>> GetManagedWorkflowProfileInfos(
    //    GetManagedWorkflowProfileInfosQuery query)
    //{
    //    Logger.LogDebug("====== Getting list for Managed Workflow Profiles ======");

    //    return await Mediator.Send(query);
    //}

    public async Task<WorkflowProfileInfo> GetWorkflowProfileByReferenceId(string referenceId)
    {
        Guard.Against.NullOrWhiteSpace(referenceId, "Workflow Profile reference Id");

        return await Mediator.Send(new GetWorkflowProfileByReferenceIdQuery { ReferenceId = referenceId });
    }

    public async Task<List<WorkflowProfileInfoNameVm>> WorkflowProfileInfoNames()
    {
        Logger.LogDebug("Get All WorkflowProfileInfo Names");

        return await Mediator.Send(new GetWorkflowProfileInfoNameQuery());
    }
}