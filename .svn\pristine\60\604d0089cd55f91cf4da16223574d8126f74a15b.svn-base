using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapLog.Queries;

public class GetCyberAirGapLogPaginatedListTests : IClassFixture<CyberAirGapLogFixture>
{
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;
    private readonly Mock<ICyberAirGapLogRepository> _mockCyberAirGapLogRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetCyberAirGapLogPaginatedListQueryHandler _handler;

    public GetCyberAirGapLogPaginatedListTests(CyberAirGapLogFixture cyberAirGapLogFixture)
    {
        _cyberAirGapLogFixture = cyberAirGapLogFixture;
        _mockCyberAirGapLogRepository = CyberAirGapLogRepositoryMocks.CreateCyberAirGapLogRepository(_cyberAirGapLogFixture.CyberAirGapLogs);
        _mockMapper = new Mock<IMapper>();

        _handler = new GetCyberAirGapLogPaginatedListQueryHandler(
            _mockMapper.Object,
            _mockCyberAirGapLogRepository.Object);
    }

    /// <summary>
    /// Test: Get paginated cyber air gap log list with valid parameters
    /// Expected: Successfully retrieves paginated list
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_ValidParameters()
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapLog>
        {
            Data = activeEntities.Take(10).ToList(),
            PageSize = 10,
            TotalCount = activeEntities.Count,
            TotalPages = (int)Math.Ceiling((double)activeEntities.Count / 10)
        };

        var expectedVmList = paginatedEntities.Data.Select(e => new CyberAirGapLogListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName,
            Status = e.Status
        }).ToList();

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapLogListVm>
        {
            Data = expectedVmList,
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapLogListVm>>(paginatedEntities))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

         }

    [Theory]
    [InlineData("Enterprise", "Should find entities with 'Enterprise' in name")]
    [InlineData("Primary", "Should find entities with 'Primary' in description")]
    [InlineData("Active", "Should find entities with 'Active' status")]
    [InlineData("8443", "Should find entities with port 8443")]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_SearchFiltering(string searchString, string description)
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = searchString,
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var filteredEntities = _cyberAirGapLogFixture.CyberAirGapLogs
            .Where(x => x.IsActive &&
                       (x.AirGapName.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                        x.Description?.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true ||
                        x.Status?.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true ||
                        x.Port.ToString().Contains(searchString)))
            .ToList();

        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapLog>
        {
            Data = filteredEntities.Take(10).ToList(),
            PageSize = 10,
            TotalCount = filteredEntities.Count,
            TotalPages = (int)Math.Ceiling((double)filteredEntities.Count / 10)
        };

        var expectedVmList = paginatedEntities.Data.Select(e => new CyberAirGapLogListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName,
            Status = e.Status
        }).ToList();

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapLogListVm>
        {
            Data = expectedVmList,
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapLogListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapLog>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Data.ShouldNotBeNull();

    }

    [Theory]
    [InlineData("AirGapName", "asc", "Sort by air gap name ascending")]
    [InlineData("AirGapName", "desc", "Sort by air gap name descending")]
    [InlineData("Status", "asc", "Sort by status ascending")]
    [InlineData("Status", "desc", "Sort by status descending")]
    [InlineData("CreatedDate", "asc", "Sort by created date ascending")]
    [InlineData("CreatedDate", "desc", "Sort by created date descending")]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_DifferentSorting(string sortColumn, string sortOrder, string description)
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = sortColumn,
            SortOrder = sortOrder
        };

        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapLog>
        {
            Data = activeEntities.Take(10).ToList(),
            PageSize = 10,
            TotalCount = activeEntities.Count,
            TotalPages = (int)Math.Ceiling((double)activeEntities.Count / 10)
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapLogListVm>
        {
            Data = paginatedEntities.Data.Select(e => new CyberAirGapLogListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName,
                Status = e.Status
            }).ToList(),
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapLogListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapLog>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Data.ShouldNotBeNull();

    }

    /// <summary>
    /// Test: Get paginated cyber air gap log list with different page sizes
    /// Expected: Successfully handles various page sizes
    /// </summary>
    [Theory]
    [InlineData(1, 5, "Small page size")]
    [InlineData(1, 10, "Standard page size")]
    [InlineData(1, 25, "Medium page size")]
    [InlineData(1, 50, "Large page size")]
    [InlineData(1, 100, "Very large page size")]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_DifferentPageSizes(int pageNumber, int pageSize, string description)
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapLog>
        {
            Data = activeEntities.Take(pageSize).ToList(),
            PageSize = pageSize,
            TotalCount = activeEntities.Count,
            TotalPages = (int)Math.Ceiling((double)activeEntities.Count / pageSize)
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapLogListVm>
        {
            Data = paginatedEntities.Data.Select(e => new CyberAirGapLogListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName
            }).ToList(),
            PageSize = pageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapLogListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapLog>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.PageSize.ShouldBe(pageSize);
        result.Data.Count.ShouldBeLessThanOrEqualTo(pageSize);

    }

    /// <summary>
    /// Test: Get paginated cyber air gap log list with pagination logic
    /// Expected: Correctly calculates pagination information
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_PaginationLogic()
    {
        // Arrange
        var largeRepository = CyberAirGapLogRepositoryMocks.CreateLargeCyberAirGapLogRepository(100);
        var handler = new GetCyberAirGapLogPaginatedListQueryHandler(_mockMapper.Object, largeRepository.Object);

        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 3,
            PageSize = 15,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapLog>
        {
            Data = new List<Domain.Entities.CyberAirGapLog>(), 
            PageSize = 15,
            TotalCount = 100,
            TotalPages = 7 // 100 / 15 = 6.67, rounded up to 7
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapLogListVm>
        {
            Data = new List<CyberAirGapLogListVm>(),
            PageSize = 15,
            TotalCount = 100,
            TotalPages = 7
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapLogListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapLog>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.PageSize.ShouldBe(15);
        result.TotalCount.ShouldBe(100);
        result.TotalPages.ShouldBe(7);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_EmptyResults()
    {
        // Arrange
        var emptyRepository = CyberAirGapLogRepositoryMocks.CreateEmptyCyberAirGapLogRepository();
        var handler = new GetCyberAirGapLogPaginatedListQueryHandler(_mockMapper.Object, emptyRepository.Object);

        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var emptyPaginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapLog>
        {
            Data = new List<Domain.Entities.CyberAirGapLog>(),
            PageSize = 10,
            TotalCount = 0,
            TotalPages = 0
        };

        var expectedEmptyPaginatedVm = new PaginatedResult<CyberAirGapLogListVm>
        {
            Data = new List<CyberAirGapLogListVm>(),
            PageSize = 10,
            TotalCount = 0,
            TotalPages = 0
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapLogListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapLog>>()))
            .Returns(expectedEmptyPaginatedVm);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Data.ShouldNotBeNull();
        result.Data.Count.ShouldBe(0);
        result.TotalCount.ShouldBe(0);
        result.TotalPages.ShouldBe(0);

       
    }

    /// <summary>
    /// Test: Get paginated cyber air gap log list with cancellation token
    /// Expected: Respects cancellation and throws OperationCanceledException
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_CancellationRequested()
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };
        var cancellationToken = new CancellationToken(true);

    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_RepositoryFails()
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var mockFailingRepository = CyberAirGapLogRepositoryMocks.CreateFailingCyberAirGapLogRepository();
        //mockFailingRepository.Setup(x => x.PaginatedListAllAsync(
        //        It.IsAny<int>(), It.IsAny<int>(), It.IsAny<object>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .ThrowsAsync(new InvalidOperationException("Pagination operation failed"));

        var handler = new GetCyberAirGapLogPaginatedListQueryHandler(_mockMapper.Object, mockFailingRepository.Object);

  }

    /// <summary>
    /// Test: Get paginated cyber air gap log list with default parameters
    /// Expected: Uses default values when parameters are not specified
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_DefaultParameters()
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery(); // No parameters set

        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapLog>
        {
            Data = activeEntities.Take(10).ToList(),
            PageSize = 10,
            TotalCount = activeEntities.Count,
            TotalPages = (int)Math.Ceiling((double)activeEntities.Count / 10)
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapLogListVm>
        {
            Data = paginatedEntities.Data.Select(e => new CyberAirGapLogListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName
            }).ToList(),
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapLogListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapLog>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Data.ShouldNotBeNull();

        
    }

    /// <summary>
    /// Test: Get paginated cyber air gap log list response validation
    /// Expected: Response contains all required pagination properties
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogPaginatedList_When_ValidatingResponse()
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 5,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).Take(5).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapLog>
        {
            Data = activeEntities,
            PageSize = 5,
            TotalCount = _cyberAirGapLogFixture.CyberAirGapLogs.Count(x => x.IsActive),
            TotalPages = (int)Math.Ceiling((double)_cyberAirGapLogFixture.CyberAirGapLogs.Count(x => x.IsActive) / 5)
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapLogListVm>
        {
            Data = activeEntities.Select(e => new CyberAirGapLogListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName,
                Status = e.Status
            }).ToList(),
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapLogListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapLog>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<PaginatedResult<CyberAirGapLogListVm>>();

        // Validate pagination properties
        result.PageSize.ShouldBe(5);
        result.TotalCount.ShouldBeGreaterThanOrEqualTo(0);
        result.TotalPages.ShouldBeGreaterThanOrEqualTo(0);

        // Validate data
        result.Data.ShouldNotBeNull();
        result.Data.Count.ShouldBeLessThanOrEqualTo(5);

        // Validate each item has required properties
        foreach (var item in result.Data)
        {
            item.Id.ShouldNotBeNullOrEmpty();
            item.AirGapName.ShouldNotBeNullOrEmpty();
        }
    }
}
