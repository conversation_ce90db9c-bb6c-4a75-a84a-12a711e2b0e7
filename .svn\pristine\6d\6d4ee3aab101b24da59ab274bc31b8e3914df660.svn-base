﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class RiskMitigationRepository : BaseRepository<RiskMitigation>, IRiskMitigationRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public RiskMitigationRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<RiskMitigation>> ListAllAsync()
    {
        var businessServices = base.QueryAll(x => x.IsActive);

        return _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            : AssignedBusinessServices(businessServices);
    }

    public override Task<RiskMitigation> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilterAsync(risk => risk.ReferenceId.Equals(id)).Result.SingleOrDefault());
    }


    public IReadOnlyList<RiskMitigation> AssignedBusinessServices(IQueryable<RiskMitigation> businessServices)
    {
        var services = new List<RiskMitigation>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                    where businessService.BusinessServiceId == assignedBusinessService.Id
                    select businessService);
        return services;
    }
}