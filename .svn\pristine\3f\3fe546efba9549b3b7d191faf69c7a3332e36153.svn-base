using ContinuityPatrol.Application.Features.CyberAirGap.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Events;

public class CreateCyberAirGapEventTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<CyberAirGapCreatedEventHandler>> _mockLogger;
    private readonly CyberAirGapCreatedEventHandler _handler;

    public CreateCyberAirGapEventTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockUserActivityRepository = CyberAirGapRepositoryMocks.CreateUserActivityRepository(_cyberAirGapFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<CyberAirGapCreatedEventHandler>>();

        // Setup default user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");

        _handler = new CyberAirGapCreatedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }
    

    [Fact]
    public async Task Handle_ProcessCyberAirGapCreatedEvent_When_ValidEvent()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "TestAirGap_Created",
           
        };

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivityWithCorrectProperties_When_ValidEvent()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
           
            Name = "TestAirGap_UserActivity"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "TestAirGap_Cancellation"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

    }

    [Fact]
    public async Task Handle_ProcessMultipleEvents_When_ValidEvents()
    {
        // Arrange
        var events = new[]
        {
            new CyberAirGapCreatedEvent
            {
                Name = "TestAirGap_Multiple_1"
            },
            new CyberAirGapCreatedEvent
            {
                Name = "TestAirGap_Multiple_2"
            },
            new CyberAirGapCreatedEvent
            {
                Name = "TestAirGap_Multiple_3"
            }
        };

        // Act
        foreach (var eventItem in events)
        {
            await _handler.Handle(eventItem, CancellationToken.None);
        }

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(3));
    }

    [Fact]
    public async Task Handle_CreateCompleteUserActivity_When_EventWithAllProperties()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "CompleteTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_CreateBasicUserActivity_When_EventWithMinimalProperties()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "MinimalTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleSpecialCharacters_When_EventWithSpecialCharsInName()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "Special-AirGap_Test@123!",
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain("Special-AirGap_Test@123!");
    }

    [Fact]
    public async Task Handle_HandleLongStrings_When_EventWithLongNameAndDescription()
    {
        // Arrange
        var longName = new string('A', 200);
        var longDescription = new string('B', 500);
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = longName
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_UseLoggedInUserService_When_ValidEvent()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "UserServiceTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        // Verify user service was called
        _mockUserService.Verify(x => x.UserId, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.LoginName, Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_ProcessRapidEvents_When_MultipleEventsInSuccession()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new CyberAirGapCreatedEvent
        {
            Name = $"RapidTestAirGap_{i:00}"
        }).ToList();

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(10));
    }

    [Fact]
    public async Task Handle_HandleComplexJson_When_EventWithComplexSourceTarget()
    {
        // Arrange
        var complexSource = "{\"serverId\":\"srv-001\",\"componentId\":\"comp-001\",\"metadata\":{\"type\":\"primary\",\"region\":\"us-east\",\"config\":{\"ssl\":true,\"port\":443}}}";
        var complexTarget = "{\"serverId\":\"srv-002\",\"componentId\":\"comp-002\",\"metadata\":{\"type\":\"secondary\",\"region\":\"us-west\",\"config\":{\"ssl\":false,\"port\":80}}}";

        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "ComplexJsonTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain("ComplexJsonTestAirGap");
        createdActivity.UserId.ShouldBe("TestUser123");
    }

    [Fact]
    public async Task Handle_LogActivityWithEntityReference_When_ValidEvent()
    {
        // Arrange
        var airGapId = "entity-reference-airgap-id";
        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "EntityReferenceTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleDifferentUserServiceStates_When_ValidEvent()
    {
        // Arrange - Setup different user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("DifferentUser456");
        _mockUserService.Setup(x => x.LoginName).Returns("DifferentLoginName");

        var cyberAirGapEvent = new CyberAirGapCreatedEvent
        {
            Name = "DifferentUserTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.UserId.ShouldBe("DifferentUser456");
        createdActivity.LoginName.ShouldBe("DifferentLoginName");
        createdActivity.ActivityDetails.ShouldContain("DifferentUserTestAirGap");
    }
}
