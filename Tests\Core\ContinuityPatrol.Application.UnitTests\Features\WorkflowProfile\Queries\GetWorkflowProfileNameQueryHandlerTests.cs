﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Queries;

public class GetWorkflowProfileNameQueryHandlerTests : IClassFixture<WorkflowProfileFixture>, IClassFixture<WorkflowProfileInfoViewFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;

    private readonly Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;

    private readonly GetWorkflowProfileNameQueryHandler _handler;

    public GetWorkflowProfileNameQueryHandlerTests(WorkflowProfileFixture workflowProfileFixture, WorkflowProfileInfoViewFixture workflowProfileInfoViewFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;

        var workflowProfileInfoViewFixture1 = workflowProfileInfoViewFixture;

        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetWorkflowProfileNamesRepository(_workflowProfileFixture.WorkflowProfiles);

        workflowProfileInfoViewFixture1.WorkflowProfileInfoViews[0].ProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId;
        workflowProfileInfoViewFixture1.WorkflowProfileInfoViews[1].ProfileId = _workflowProfileFixture.WorkflowProfiles[1].ReferenceId;
        workflowProfileInfoViewFixture1.WorkflowProfileInfoViews[2].ProfileId = _workflowProfileFixture.WorkflowProfiles[2].ReferenceId;

        Mock<IWorkflowProfileInfoViewRepository> mockWorkflowProfileInfoViewRepository = WorkflowProfileInfoViewRepositoryMocks.GetRunningProfileByProfileIds(workflowProfileInfoViewFixture1.WorkflowProfileInfoViews);

        _handler = new GetWorkflowProfileNameQueryHandler(_workflowProfileFixture.Mapper, _mockWorkflowProfileRepository.Object, mockWorkflowProfileInfoViewRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowProfile_Name()
    {
        var result = await _handler.Handle(new GetWorkflowProfileNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileNameVm>>();

        result[0].Id.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].ReferenceId);
        result[0].Name.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Name);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowProfileNamesCount()
    {
        var result = await _handler.Handle(new GetWorkflowProfileNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileNameVm>>();

        result.Count.ShouldBe(_workflowProfileFixture.WorkflowProfiles.Count);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        var mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetWorkflowProfileNamesEmptyRepository();
        var mockWorkflowProfileInfoViewRepository = WorkflowProfileInfoViewRepositoryMocks.GetRunningProfileByProfileIdsEmptyRepository();

        var handler = new GetWorkflowProfileNameQueryHandler(_workflowProfileFixture.Mapper, mockWorkflowProfileRepository.Object, mockWorkflowProfileInfoViewRepository.Object);

        var result = await handler.Handle(new GetWorkflowProfileNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowProfileNamesMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowProfileNameQuery(), CancellationToken.None);

        _mockWorkflowProfileRepository.Verify(x => x.GetWorkflowProfileNames(), Times.Once);
    }
}