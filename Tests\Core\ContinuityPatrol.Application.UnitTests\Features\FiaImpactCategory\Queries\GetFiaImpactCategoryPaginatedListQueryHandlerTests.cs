﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactCategory.Queries
{
    public class GetFiaImpactCategoryPaginatedListQueryHandlerTests
    {
        private readonly Mock<IFiaImpactCategoryRepository> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetFiaImpactCategoryPaginatedListQueryHandler _handler;

        public GetFiaImpactCategoryPaginatedListQueryHandlerTests()
        {
            var fiaImpactCategories = new List<Domain.Entities.FiaImpactCategory>
            {
                new Domain.Entities.FiaImpactCategory { Id = 1, ReferenceId = "ref-001", Name = "Network" },
                new Domain.Entities.FiaImpactCategory { Id = 2, ReferenceId = "ref-002", Name = "System" }
            };

            _mockRepository = new Mock<IFiaImpactCategoryRepository>();
            _mockMapper = new Mock<IMapper>();

            var domainPaginatedResult = new PaginatedResult<Domain.Entities.FiaImpactCategory>(
                succeeded: true,
                data: fiaImpactCategories,
                messages: new List<string>(),
                pageSize: 10
            );

            var vmResult = new PaginatedResult<FiaImpactCategoryListVm>(
                succeeded: true,
                data: new List<FiaImpactCategoryListVm>
                {
                    new FiaImpactCategoryListVm { Id = Guid.NewGuid().ToString(), Name = "Network" },
                    new FiaImpactCategoryListVm { Id = Guid.NewGuid().ToString(), Name = "System" }
                },
                messages: new List<string>(),
                pageSize: 10
            );

            _mockRepository.Setup(repo =>
                repo.PaginatedListAllAsync(
                    It.IsAny<int>(),
                    It.IsAny<int>(),
                    It.IsAny<Specification<Domain.Entities.FiaImpactCategory>>(),
                    It.IsAny<string>(),
                    It.IsAny<string>()
                )).ReturnsAsync(domainPaginatedResult);

            _mockMapper.Setup(mapper =>
                mapper.Map<PaginatedResult<FiaImpactCategoryListVm>>(It.IsAny<PaginatedResult<Domain.Entities.FiaImpactCategory>>()))
                .Returns(vmResult);

            _handler = new GetFiaImpactCategoryPaginatedListQueryHandler(_mockMapper.Object, _mockRepository.Object);
        }

        [Fact(DisplayName = "Handle_Should_Return_Paginated_List_Of_ImpactCategories")]
        public async Task Handle_Should_Return_Paginated_List_Of_ImpactCategories()
        {
            // Arrange
            var query = new GetFiaImpactCategoryPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "",
                SortColumn = "Name",
                SortOrder = "asc"
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(0, result.TotalCount);
            Assert.Equal("Network", result.Data[0].Name);
            Assert.Equal("System", result.Data[1].Name);
            _mockRepository.Verify(repo => repo.PaginatedListAllAsync(
                query.PageNumber,
                query.PageSize,
                It.IsAny<Specification<Domain.Entities.FiaImpactCategory>>(),
                query.SortColumn,
                query.SortOrder
            ), Times.Once);
        }
    }
}
