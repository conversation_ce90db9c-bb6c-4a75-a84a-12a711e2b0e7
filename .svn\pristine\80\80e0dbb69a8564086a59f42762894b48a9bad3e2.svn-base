using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Queries;

public class GetCyberAlertDetailTests : IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
    private readonly Mock<ILogger<GetCyberAlertDetailsQueryHandler>> _mockLogger; 
    private readonly GetCyberAlertDetailsQueryHandler _handler;
    private readonly Mock<IMapper> _mapper;
    public GetCyberAlertDetailTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);
        _mockLogger = new Mock<ILogger<GetCyberAlertDetailsQueryHandler>>();

        _mapper = new Mock<IMapper>();
        _handler = new GetCyberAlertDetailsQueryHandler(_mapper.Object ,
            _mockCyberAlertRepository.Object);
    }

    [Fact]
    public async Task Handle_GetCyberAlertDetail_When_ValidQuery()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var query = new GetCyberAlertDetailQuery
        {
            Id = existingAlert.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnCompleteAlertInformation_When_ValidQuery()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var query = new GetCyberAlertDetailQuery
        {
            Id = existingAlert.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        //result.Data.AlertId.ShouldBe(existingAlert.AlertId);
        //result.Data.Title.ShouldBe(existingAlert.Title);
        //result.Data.Description.ShouldBe(existingAlert.Description);
        //result.Data.Severity.ShouldBe(existingAlert.Severity);
        //result.Data.Priority.ShouldBe(existingAlert.Priority);
        //result.Data.Category.ShouldBe(existingAlert.Category);
        //result.Data.Type.ShouldBe(existingAlert.Type);
        //result.Data.Source.ShouldBe(existingAlert.Source);
        //result.Data.SourceId.ShouldBe(existingAlert.SourceId);
        //result.Data.TargetSystem.ShouldBe(existingAlert.TargetSystem);
        //result.Data.TargetId.ShouldBe(existingAlert.TargetId);
        //result.Data.Status.ShouldBe(existingAlert.Status);
        //result.Data.AlertData.ShouldBe(existingAlert.AlertData);
        //result.Data.RuleId.ShouldBe(existingAlert.RuleId);
        //result.Data.RuleName.ShouldBe(existingAlert.RuleName);
        //result.Data.DetectedAt.ShouldBe(existingAlert.DetectedAt);
        //result.Data.AcknowledgedAt.ShouldBe(existingAlert.AcknowledgedAt);
        //result.Data.AcknowledgedBy.ShouldBe(existingAlert.AcknowledgedBy);
        //result.Data.ResolvedAt.ShouldBe(existingAlert.ResolvedAt);
        //result.Data.ResolvedBy.ShouldBe(existingAlert.ResolvedBy);
        //result.Data.ResolutionNotes.ShouldBe(existingAlert.ResolutionNotes);
    }

    //[Fact]
    //public async Task Handle_ThrowNotFoundException_When_AlertNotFound()
    //{
    //    // Arrange
    //    var query = new GetCyberAlertDetailQuery
    //    {
    //        Id = "non-existent-alert-id"
    //    };

    //    // Act & Assert
    //    await Should.ThrowAsync<NotFoundException>(async () =>
    //        await _handler.Handle(query, CancellationToken.None));

    //    _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync("non-existent-alert-id"), Times.Once);
    //}

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AlertIsInactive()
    {
        // Arrange
        var inactiveAlert = new Domain.Entities .CyberAlert
        {
            ReferenceId = "inactive-alert-id",
            IsActive = false
        };
        _cyberAlertFixture.CyberAlerts.Add(inactiveAlert);

        var query = new GetCyberAlertDetailQuery
        {
            Id = "inactive-alert-id"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var query = new GetCyberAlertDetailQuery
        {
            Id = existingAlert.ReferenceId
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        
    }

    [Fact]
    public async Task Handle_ProcessMultipleDetailQueries_When_ValidQueries()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlerts.Take(3).ToList();
        var queries = alerts.Select(alert => new GetCyberAlertDetailQuery
        {
            Id = alert.ReferenceId
        }).ToArray();

        // Act
        foreach (var query in queries)
        {
            var result = await _handler.Handle(query, CancellationToken.None);
            
        }

        // Verify all queries were processed
        _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Exactly(3));
    }

    [Fact]
    public async Task Handle_GetAlertsWithDifferentSeverities_When_ValidQueries()
    {
        // Arrange
        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(severityLevels.Length).ToList();

        for (int i = 0; i < alerts.Count && i < severityLevels.Length; i++)
        {
            alerts[i].Severity = severityLevels[i];
            alerts[i].IsActive = true; // Ensure the alert is active
            _mockCyberAlertRepository
                .Setup(x => x.GetByReferenceIdAsync(alerts[i].ReferenceId))
                .ReturnsAsync(alerts[i]);
        }

        for (int i = 0; i < alerts.Count && i < severityLevels.Length; i++)
        {
            var query = new GetCyberAlertDetailQuery
            {
                Id = alerts[i].ReferenceId
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            query.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Handle_GetAlertsWithDifferentStatuses_When_ValidQueries()
    {
        // Arrange
        var statuses = new[] { "Open", "Acknowledged", "In Progress", "Resolved", "Closed" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(statuses.Length).ToList();

        // Ensure all alerts are active and setup the mock
        for (int i = 0; i < alerts.Count && i < statuses.Length; i++)
        {
            alerts[i].IsActive = true;
            // If you want to set a status property, do it here: alerts[i].Status = statuses[i];
            _mockCyberAlertRepository
                .Setup(x => x.GetByReferenceIdAsync(alerts[i].ReferenceId))
                .ReturnsAsync(alerts[i]);
        }

        for (int i = 0; i < alerts.Count && i < statuses.Length; i++)
        {
            var query = new GetCyberAlertDetailQuery
            {
                Id = alerts[i].ReferenceId
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            query.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Handle_GetAlertWithComplexData_When_ValidQuery()
    {
        // Arrange
        var complexAlertData = @"{
            ""source_ip"": ""*************"",
            ""destination_ip"": ""*********"",
            ""port"": 443,
            ""protocol"": ""HTTPS"",
            ""payload_size"": 1024,
            ""attack_vector"": ""SQL Injection"",
            ""confidence_score"": 0.95,
            ""metadata"": {
                ""user_agent"": ""Mozilla/5.0"",
                ""request_method"": ""POST"",
                ""response_code"": 200
            }
        }";

        var alertWithComplexData = _cyberAlertFixture.CyberAlerts.First();

        var query = new GetCyberAlertDetailQuery
        {
            Id = alertWithComplexData.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        
    }
    [Fact]
    public async Task Handle_GetAcknowledgedAlert_When_ValidQuery()
    {
        // Arrange
        var acknowledgedAlert = _cyberAlertFixture.CyberAlerts.First();

        var query = new GetCyberAlertDetailQuery
        {
            Id = acknowledgedAlert.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

    }

    [Fact]
    public async Task Handle_GetResolvedAlert_When_ValidQuery()
    {
        // Arrange
        var resolvedAlert = _cyberAlertFixture.CyberAlerts.First();

        var query = new GetCyberAlertDetailQuery
        {
            Id = resolvedAlert.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

      
    }

    
    [Fact]
    public async Task Handle_ProcessRapidDetailQueries_When_MultipleQueries()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlerts.Take(10).ToList();
        var queries = alerts.Select(alert => new GetCyberAlertDetailQuery
        {
            Id = alert.ReferenceId
        }).ToList();

        // Act
        var tasks = queries.Select(query => _handler.Handle(query, CancellationToken.None));

       
    }

    
    [Fact]
    public async Task Handle_HandleInvalidId_When_NullOrEmptyId()
    {
        // Arrange
        var query = new GetCyberAlertDetailQuery
        {
            Id = null
        };

       
    }

    [Fact]
    public async Task Handle_GetAlertWithSpecialCharacters_When_ValidQuery()
    {
        // Arrange
        var alertWithSpecialChars = _cyberAlertFixture.CyberAlerts.First();

        var query = new GetCyberAlertDetailQuery
        {
            Id = alertWithSpecialChars.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

    }
}
