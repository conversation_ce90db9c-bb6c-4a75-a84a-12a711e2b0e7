﻿using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowProfileInfoViewRepositoryMocks
{
    public static Mock<IWorkflowProfileInfoViewRepository> GetRunningProfileByProfileIds(List<WorkflowProfileInfoView> workflowProfileInfoViews)
    {
        var workflowProfileInfoViewRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewRepository.Setup(repo => repo.GetRunningProfileByProfileIds(It.IsAny<List<string>>())).ReturnsAsync(workflowProfileInfoViews);

        return workflowProfileInfoViewRepository;
    }

    public static Mock<IWorkflowProfileInfoViewRepository> GetRunningProfileByProfileIdsEmptyRepository()
    {
        var workflowProfileInfoViewRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewRepository.Setup(repo => repo.GetRunningProfileByProfileIds(It.IsAny<List<string>>())).ReturnsAsync(new List<WorkflowProfileInfoView>());

        return workflowProfileInfoViewRepository;
    }

    public static Mock<IWorkflowProfileInfoViewRepository> GetWorkflowProfileInfoNames(List<WorkflowProfileInfoView> workflowProfileInfoViews)
    {
        var workflowProfileInfoViewRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewRepository.Setup(repo => repo.GetWorkflowProfileInfoNames()).ReturnsAsync(workflowProfileInfoViews);

        return workflowProfileInfoViewRepository;
    }

    public static Mock<IWorkflowProfileInfoViewRepository> GetWorkflowProfileInfoViewRepository(List<WorkflowProfileInfoView> workflowProfileInfoViews)
    {
        var workflowProfileInfoViewRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfileInfoViews);

        workflowProfileInfoViewRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfileInfoViews.SingleOrDefault(x => x.ReferenceId == i));

        return workflowProfileInfoViewRepository;
    }

    public static Mock<IWorkflowProfileInfoViewRepository> GetWorkflowProfileInfoViewEmptyRepository()
    {
        var workflowProfileInfoViewEmptyRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowProfileInfoView>());

        return workflowProfileInfoViewEmptyRepository;
    }

    public static Mock<IWorkflowProfileInfoViewRepository> GetWorkflowProfileInfoByProfileIds(List<WorkflowProfileInfoView> workflowProfileInfoViews)
    {
        var workflowProfileInfoViewEmptyRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewEmptyRepository.Setup(repo => repo.GetWorkflowProfileInfoByProfileIds(It.IsAny<List<string>>())).ReturnsAsync(workflowProfileInfoViews);

        return workflowProfileInfoViewEmptyRepository;
    }


    public static Mock<IWorkflowViewRepository> GetPaginatedWorkflowViewRepository(List<WorkflowView> workflowProfileInfoViews)
    {
        var workflowViewRepository = new Mock<IWorkflowViewRepository>();

        workflowViewRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<WorkflowView>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<WorkflowView> spec, string sortColumn, string sortOrder) =>
            {
                var sortedCompanies = workflowProfileInfoViews.AsQueryable();

                if (spec.Criteria != null)
                {
                    sortedCompanies = sortedCompanies.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    // Assuming Company has a Name property; replace logic as needed
                    sortedCompanies = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedCompanies.OrderByDescending(c => c.WorkflowName)
                        : sortedCompanies.OrderBy(c => c.WorkflowName);
                }

                var totalCount = sortedCompanies.Count();
                var paginated = sortedCompanies
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PaginatedResult<WorkflowView>.Success(paginated, totalCount, pageNumber, pageSize);
            });

        return workflowViewRepository;
    }

}