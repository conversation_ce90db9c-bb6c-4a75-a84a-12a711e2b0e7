﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Create;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Delete;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures
{
    public class FiaImpactCategoryFixture : IDisposable
    {
        public IMapper Mapper { get; }
        public List<FiaImpactCategory> FiaImpactCategories { get; set; }
        public CreateFiaImpactCategoryCommand CreateFiaImpactCategoryCommand { get; set; }
        public UpdateFiaImpactCategoryCommand UpdateFiaImpactCategoryCommand { get; set; }
        public FiaImpactCategoryCreatedEvent FiaImpactCategoryCreatedEvent { get; set; }
        public FiaImpactCategoryDeletedEvent FiaImpactCategoryDeletedEvent { get; set; }
        public FiaImpactCategoryUpdatedEvent FiaImpactCategoryUpdatedEvent { get; set; }

        public FiaImpactCategoryFixture()
        {
            FiaImpactCategories = AutoFiaImpactCategoryFixture.Create<List<FiaImpactCategory>>();

            CreateFiaImpactCategoryCommand = AutoFiaImpactCategoryFixture.Create<CreateFiaImpactCategoryCommand>();

            UpdateFiaImpactCategoryCommand = AutoFiaImpactCategoryFixture.Create<UpdateFiaImpactCategoryCommand>();

            FiaImpactCategoryCreatedEvent = AutoFiaImpactCategoryFixture.Create<FiaImpactCategoryCreatedEvent>();

            FiaImpactCategoryDeletedEvent = AutoFiaImpactCategoryFixture.Create<FiaImpactCategoryDeletedEvent>();

            FiaImpactCategoryUpdatedEvent = AutoFiaImpactCategoryFixture.Create<FiaImpactCategoryUpdatedEvent>();

            var configurationProvider = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<FiaImpactCategoryProfile>();
            });

            Mapper = configurationProvider.CreateMapper();
        }

        public Fixture AutoFiaImpactCategoryFixture
        {
            get
            {
                var fixture = new Fixture();

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateFiaImpactCategoryCommand>(p => p.Name, 10));
                fixture.Customize<CreateFiaImpactCategoryCommand>(c => c.With(b => b.Name, 0.ToString));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateFiaImpactCategoryCommand>(p => p.Name, 10));
                fixture.Customize<UpdateFiaImpactCategoryCommand>(c => c.With(b => b.Id, 0.ToString));
                fixture.Customize<Site>(c => c.With(b => b.IsActive, true));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FiaImpactCategoryCreatedEvent>(p => p.Name, 10));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FiaImpactCategoryDeletedEvent>(p => p.Name, 10));

                fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FiaImpactCategoryUpdatedEvent>(p => p.Name, 10));

                return fixture;
            }
        }
        public void Dispose()
        {

        }
    }
}
