﻿using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Create;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Update;
using ContinuityPatrol.Application.Features.DriftParameter.Events.Paginated;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftCategoryMasterModel;
using ContinuityPatrol.Domain.ViewModels.DriftImpactTypeMasterModel;
using ContinuityPatrol.Domain.ViewModels.DriftParameterModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using PCPL;

namespace ContinuityPatrol.Web.Areas.Drift.Controllers;

[Area("Drift")]
public class DriftParameterController : Controller
{
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly ILogger<DriftParameterController> _logger;
    private readonly IPublisher _publisher;

    public DriftParameterController(ILogger<DriftParameterController> logger, IDataProvider dataProvider, IMapper mapper, IPublisher publisher)
    {

        _dataProvider = dataProvider;
        _mapper = mapper;
        _publisher = publisher;
        _logger = logger;
    }
    [EventCode(EventCodes.DriftParameter.List)]
    public async Task<IActionResult> List()
    {
       await _publisher.Publish(new DriftParameterPaginatedEvent());
        _logger.LogDebug("Entering List method in DriftParameter");
        return View();
    }
    [EventCode(EventCodes.DriftParameter.Pagination)]
    public async Task<IActionResult> GetPagination(GetDriftParameterPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in DriftParameter");
        try
        {
            var timeList = await _dataProvider.DriftParameter.GetPaginatedDriftParameters(query);
            _logger.LogDebug("Successfully retrieved drift parameter paginated list on DriftParameter page");
            return Json(new { Success = true, data = timeList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }

    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.DriftParameter.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(DriftParameterViewModel parameterListVm)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in DriftParameter");
        var templateId = parameterListVm.Id;
        try
        {
            BaseResponse result;
            if (templateId.IsNullOrWhiteSpace())
            {
                var createDriftCommand = _mapper.Map<CreateDriftParameterCommand>(parameterListVm);
                result = await _dataProvider.DriftParameter.CreateAsync(createDriftCommand);
                _logger.LogDebug($"Creating DriftParameter '{createDriftCommand.Name}'");
            }
            else
            {
                var updateDriftCommand = _mapper.Map<UpdateDriftParameterCommand>(parameterListVm);
                result = await _dataProvider.DriftParameter.UpdateAsync(updateDriftCommand);
                _logger.LogDebug($"Updating DriftParameter '{updateDriftCommand.Name}'");

            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in DriftParameter, returning view.");
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on drift parameter page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [EventCode(EventCodes.DriftParameter.CplValidation)]
    public string GetCplValidation(string script)
    {
        _logger.LogDebug("Entering GetCplValidation method in DriftParameter");
        try
        {
            CPL obj = new CPL();

            var binPath = Path.Combine(Directory.GetCurrentDirectory(), "ThirdParty", "PGlobalMirror", "CPSL Grammer.cgt");

            var flag = obj.ExecuteCPL(script, binPath);

            _logger.LogDebug($"CPL script execution completed. Validation result: {flag}");

            return flag;

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while validating the cpl script.", ex);
            return "";
        }
    }
    [EventCode(EventCodes.DriftParameter.IsExist)]
    public async Task<bool> IsNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsNameExist method in DriftParameter");
        try
        {
            var isNameExits = await _dataProvider.DriftParameter.IsDriftParameterNameExist(name, id);
            _logger.LogDebug("Returning result for IsNameExist on DriftParameter");
            return isNameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on drift parameter page while checking if drift parameter name exists for : {name}.", ex);
            return false;
        }
    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.DriftParameter.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in DriftParameter");
        try
        {
            var delete = await _dataProvider.DriftParameter.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in DriftParameter");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on drift parameter.", ex);
            return ex.GetJsonException();
        }

    }

    //category 
    [EventCode(EventCodes.DriftParameter.DriftCategory)]
    public async Task<IActionResult> GetDriftCategoryList()
    {
        _logger.LogDebug("Entering GetDriftCategoryList method in DriftParameter");
        try
        {
            var driftCategoryMasterList = await _dataProvider.DriftCategoryMaster.GetDriftCategoryMasterList();
            _logger.LogDebug("Successfully retrieved drift category list in DriftParameter");
            return Json(new { Success = true, data = driftCategoryMasterList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while retrieving drift category list.", ex);
            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.DriftParameter.DriftCategoryCreateOrUpdate)]
    public async Task<IActionResult> DriftCategoryCreateOrUpdate(DriftCategoryMasterViewModel parameterListVm)
    {
        _logger.LogDebug("Entering DriftCategoryCreateOrUpdate method in DriftParameter");
        var templateId = parameterListVm.Id;
        try
        {
            BaseResponse result;
            if (templateId.IsNullOrWhiteSpace())
            {
                var createDriftCommand = _mapper.Map<CreateDriftCategoryMasterCommand>(parameterListVm);
                result = await _dataProvider.DriftCategoryMaster.CreateAsync(createDriftCommand);
                _logger.LogDebug($"Creating DriftCategory '{createDriftCommand.CategoryName}' in DriftParameter page");
            }
            else
            {
                var updateDriftCommand = _mapper.Map<UpdateDriftCategoryMasterCommand>(parameterListVm);
                result = await _dataProvider.DriftCategoryMaster.UpdateAsync(updateDriftCommand);
                _logger.LogDebug($"Updating DriftCategory '{updateDriftCommand.CategoryName}' in DriftParameter page");
            }
            _logger.LogDebug("DriftCategoryCreateOrUpdate operation completed successfully in DriftParameter, returning view.");
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error for drift category on drift management page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift management page while processing the request for drift category create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [EventCode(EventCodes.DriftParameter.IsCategory)]
    public async Task<bool> IsCategoryNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsCategoryNameExist method in DriftParameter");
        try
        {
            var isNameExits = await _dataProvider.DriftCategoryMaster.IsDriftCategoryMasterNameExist(name, id);
            _logger.LogDebug("Returning result for IsCategoryNameExist on DriftParameter");
            return isNameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on drift management page while checking drift category name exists for : {name}.", ex);
            return false;
        }
    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.DriftParameter.DriftCategoryDelete)]
    public async Task<IActionResult> DeleteDriftCategory(string id)
    {
        _logger.LogDebug("Entering DeleteDriftCategory method in DriftParameter");
        try
        {
            var delete = await _dataProvider.DriftCategoryMaster.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted drift category record in DriftManagement");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while deleting record on drift category.", ex);
            return ex.GetJsonException();
        }

    }

    //impactType 
    [EventCode(EventCodes.DriftParameter.DriftImpactType)]
    public async Task<IActionResult> GetDriftImpactTypeList()
    {
        _logger.LogDebug("Entering GetDriftImpactTypeList method in DriftParameter");
        try
        {
            var driftImpactTypeMasterList = await _dataProvider.DriftImpactTypeMaster.GetDriftImpactTypeMasterList();
            _logger.LogDebug("Successfully retrieved drift impactType list in DriftParameter");
            return Json(new { Success = true, data = driftImpactTypeMasterList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while retrieving drift impactType list.", ex);
            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.DriftParameter.DriftImpactTypeCreateOrUpdate)]
    public async Task<IActionResult> DriftImpactTypeCreateOrUpdate(DriftImpactTypeMasterViewModel parameterListVm)
    {
        _logger.LogDebug("Entering DriftImpactTypeCreateOrUpdate method in DriftParameter");
        var templateId = parameterListVm.Id;
        try
        {
            BaseResponse result;
            if (templateId.IsNullOrWhiteSpace())
            {
                var createDriftCommand = _mapper.Map<CreateDriftImpactTypeMasterCommand>(parameterListVm);
                result = await _dataProvider.DriftImpactTypeMaster.CreateAsync(createDriftCommand);
                _logger.LogDebug($"Creating DriftImpactType '{createDriftCommand.ImpactType}' in DriftParameter page");
            }
            else
            {
                var updateDriftCommand = _mapper.Map<UpdateDriftImpactTypeMasterCommand>(parameterListVm);
                result = await _dataProvider.DriftImpactTypeMaster.UpdateAsync(updateDriftCommand);
                _logger.LogDebug($"Updating DriftImpactType '{updateDriftCommand.ImpactType}' in DriftParameter page");
            }
            _logger.LogDebug("DriftImpactTypeCreateOrUpdate operation completed successfully in DriftParameter, returning view.");
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error for drift impact type on drift management page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift management page while processing the request for drift impact type create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [EventCode(EventCodes.DriftParameter.IsImpactType)]
    public async Task<bool> IsImpactTypeNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsImpactTypeNameExist method in DriftParameter");
        try
        {
            var isNameExist = await _dataProvider.DriftImpactTypeMaster.IsDriftImpactTypeMasterNameExist(name, id);
            _logger.LogDebug("Returning result for IsImpactTypeNameExist on DriftParameter");
            return isNameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on drift management page while checking drift impact type exists for : {name}.", ex);
            return false;
        }
    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.DriftParameter.DeleteImpactType)]
    public async Task<IActionResult> DeleteImpactType(string id)
    {
        _logger.LogDebug("Entering DeleteImpactType method in DriftParameter");
        try
        {
            var delete = await _dataProvider.DriftImpactTypeMaster.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted drift impactType record in DriftManagement");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while deleting record on drift impact type.", ex);
            return ex.GetJsonException();
        }

    }
}
