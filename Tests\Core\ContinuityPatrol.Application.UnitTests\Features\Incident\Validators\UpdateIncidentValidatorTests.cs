﻿using ContinuityPatrol.Application.Features.Incident.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Constants;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Validators;

public class UpdateIncidentValidatorTests : IClassFixture<IncidentFixture>
{
    private readonly Mock<IIncidentRepository> _mockIncidentRepository;

    private readonly IncidentFixture _incidentFixture;

    public UpdateIncidentValidatorTests(IncidentFixture incidentFixture)
    {
        _incidentFixture = incidentFixture;

        var incidents = new Fixture().Create<List<Domain.Entities.Incident>>();

        _mockIncidentRepository = IncidentRepositoryMocks.UpdateIncidentRepository(incidents);
    }

    //IncidentName

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_IncidentName_WithEmpty(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.IncidentName = "";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.IncidentNameRequired);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_IncidentName_IsNull(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.IncidentName = null;

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.IncidentNameRequired);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_IncidentName_MinimumRange_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.IncidentName = "AB";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.IncidentNameRange);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_IncidentName_MaximumRange_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.IncidentName = "ThisIsAVeryLongIncidentNameThatExceedsTheMaximumAllowedLength";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.IncidentNameRange);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_IncidentName_InvalidFormat_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.IncidentName = "123Invalid@Name";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.IncidentNameValid);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_IncidentName_ValidFormat_ShouldPass(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.IncidentName = "ValidIncident";
        updateIncidentCommand.BusinessFunctionName = "ValidFunction";
        updateIncidentCommand.BusinessServiceName = "ValidService";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeTrue();
    }

    //BusinessFunctionName

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessFunctionName_WithEmpty(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessFunctionName = "";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessFunctionNameRequiredUpdate);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessFunctionName_IsNull(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessFunctionName = null;

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessFunctionNameRequiredUpdate);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessFunctionName_MinimumRange_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessFunctionName = "AB";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessFunctionNameRange);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessFunctionName_MaximumRange_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessFunctionName = "ThisIsAVeryLongBusinessFunctionNameThatExceedsTheMaximumAllowedLength";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessFunctionNameRange);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessFunctionName_InvalidFormat_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessFunctionName = "123Invalid@Function";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessFunctionNameValid);
    }

    //BusinessServiceName

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessServiceName_WithEmpty(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessServiceName = "";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessServiceNameRequiredUpdate);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessServiceName_IsNull(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessServiceName = null;

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessServiceNameRequiredUpdate);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessServiceName_MinimumRange_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessServiceName = "AB";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessServiceNameRange);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessServiceName_MaximumRange_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessServiceName = "ThisIsAVeryLongBusinessServiceNameThatExceedsTheMaximumAllowedLength";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessServiceNameRange);
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Update_BusinessServiceName_InvalidFormat_Validator(UpdateIncidentCommand updateIncidentCommand)
    {
        var validator = new UpdateIncidentCommandValidator(_mockIncidentRepository.Object);

        updateIncidentCommand.BusinessServiceName = "123Invalid@Service";

        var validateResult = await validator.ValidateAsync(updateIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == ValidatorConstants.Incident.BusinessServiceNameValid);
    }
}