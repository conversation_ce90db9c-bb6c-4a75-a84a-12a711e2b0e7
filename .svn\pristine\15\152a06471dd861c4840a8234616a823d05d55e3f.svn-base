using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using System.Globalization;
using UserRole = ContinuityPatrol.Shared.Core.Enums.UserRole;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class UserActivityRepositoryTests : IClassFixture<UserActivityFixture>
{
    private readonly UserActivityFixture _userActivityFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly UserActivityRepository _repository;

    public UserActivityRepositoryTests(UserActivityFixture userActivityFixture)
    {
        _userActivityFixture = userActivityFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new UserActivityRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region Constructor Tests

    [Fact]
    public void Constructor_ShouldInitializeCorrectly()
    {
        var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = DbContextFactory.GetMockUserService();

        var repository = new UserActivityRepository(dbContext, mockUserService);

        Assert.NotNull(repository);
    }

    #endregion

    #region GetStartTimeEndTimeByUser Tests

    [Fact]
    public async Task GetStartTimeEndTimeByUser_ReturnsEmptyList_WhenNoData()
    {
        var result = await _repository.GetStartTimeEndTimeByUser("USER_123", "1-1-2024", "31-12-2024");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_ReturnsFilteredResults_WhenIsParentTrue()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        // Create test data with dates in 2024
        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Test Action 1"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 8, 20),
                IsActive = true,
                Action = "Test Action 2"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetStartTimeEndTimeByUser(
            UserActivityFixture.TestUserId,
            "1-1-2024",
            "3-7-2025");

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.TestUserId, x.UserId));
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_ReturnsFilteredResults_WhenIsParentFalse()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        // Create test data with dates in 2024
        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Test Action 1"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 8, 20),
                IsActive = true,
                Action = "Test Action 2"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetStartTimeEndTimeByUser(
            UserActivityFixture.TestUserId,
            "1-1-2024",
            "31-12-2025");

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.CompanyId, x.CompanyId));
        Assert.All(result, x => Assert.Equal(UserActivityFixture.TestUserId, x.UserId));
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_ExcludesSiteAdminUsers()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var userActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Test Action"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.SiteAdminUserId,
                LoginName = "siteadmin",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Site Admin Action"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(userActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetStartTimeEndTimeByUser(
            UserActivityFixture.TestUserId,
            "1-1-2024",
            "31-12-2025");

        Assert.NotEmpty(result);
        Assert.DoesNotContain(result, x => x.UserId == UserActivityFixture.SiteAdminUserId);
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_HandlesNullUserId()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var result = await repository.GetStartTimeEndTimeByUser(null, "1-1-2024", "31-12-2025");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_FiltersInactiveRecords()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Active Record"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = false,
                Action = "Inactive Record"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
         dbContext.SaveChanges();

        var result = await repository.GetStartTimeEndTimeByUser(
            UserActivityFixture.TestUserId,
            "1-1-2024",
            "31-12-2025");

        Assert.Single(result);
        Assert.Equal("Active Record", result.First().Action);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_ReturnsOrderedByCreatedDateDescending()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15, 8, 0, 0),
                IsActive = true,
                Action = "First Action"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15, 12, 0, 0),
                IsActive = true,
                Action = "Second Action"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15, 16, 0, 0),
                IsActive = true,
                Action = "Third Action"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetStartTimeEndTimeByUser(
            UserActivityFixture.TestUserId,
            "1-1-2024",
            "31-12-2025");

        Assert.Equal(3, result.Count);
        Assert.Equal("Third Action", result[0].Action);
        Assert.Equal("Second Action", result[1].Action);
        Assert.Equal("First Action", result[2].Action);
    }

    #endregion

    #region ListAllUserActivityAsync Tests

    [Fact]
    public async Task ListAllUserActivityAsync_ReturnsEmptyList_WhenNoData()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var result = await repository.ListAllUserActivityAsync("1-1-2024", "31-12-2024");
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllUserActivityAsync_ReturnsFilteredResults_WhenIsParentTrue()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        // Create test data with dates in 2024
        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Test Action 1"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.SiteAdminUserId,
                LoginName = "siteadmin",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Site Admin Action"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.ListAllUserActivityAsync("1-1-2024", "31-12-2025");

        Assert.NotEmpty(result);
        Assert.DoesNotContain(result, x => x.UserId == UserActivityFixture.SiteAdminUserId);
    }

    [Fact]
    public async Task ListAllUserActivityAsync_ReturnsFilteredResults_WhenIsParentFalse()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        // Create test data with dates in 2024
        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Test Action 1"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.ListAllUserActivityAsync("1-1-2024", "31-12-2025");

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.CompanyId, x.CompanyId));
    }

    #endregion

    #region GetUserActivityByLoginName Tests

    [Fact]
    public async Task GetUserActivityByLoginName_ReturnsEmptyList_WhenNoData()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var result = await repository.GetUserActivityByLoginName("nonexistent");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetUserActivityByLoginName_ReturnsFilteredResults_WhenIsParentTrue()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddDays(-1),
                IsActive = true,
                Action = "Login Action 1"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.SiteAdminUserId,
                LoginName = "siteadmin",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddDays(-1),
                IsActive = true,
                Action = "Site Admin Action"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetUserActivityByLoginName(UserActivityFixture.TestLoginName);

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.TestLoginName, x.LoginName));
        Assert.DoesNotContain(result, x => x.UserId == UserActivityFixture.SiteAdminUserId);
    }

    [Fact]
    public async Task GetUserActivityByLoginName_ReturnsFilteredResults_WhenIsParentFalse()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddDays(-1),
                IsActive = true,
                Action = "Login Action 1"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetUserActivityByLoginName(UserActivityFixture.TestLoginName);

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.CompanyId, x.CompanyId));
        Assert.All(result, x => Assert.Equal(UserActivityFixture.TestLoginName, x.LoginName));
    }

    [Fact]
    public async Task GetUserActivityByLoginName_HandlesNullLoginName()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var result = await repository.GetUserActivityByLoginName(null);
        Assert.Empty(result);
    }

    #endregion

    #region GetLoginUserActivityByLoginName Tests

    [Fact]
    public async Task GetLoginUserActivityByLoginName_ReturnsEmptyList_WhenNoData()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var result = await repository.GetLoginUserActivityByLoginName("nonexistent");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLoginUserActivityByLoginName_ReturnsFilteredResults_WhenIsParentTrue()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddHours(10),
                IsActive = true,
                Action = "Today Action 1"
            },
            new UserActivity
            {
                UserId = "USER_789",
                LoginName = UserActivityFixture.TestLoginName + "partial",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddHours(12),
                IsActive = true,
                Action = "Today Action 2"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.SiteAdminUserId,
                LoginName = "siteadmin",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddHours(14),
                IsActive = true,
                Action = "Site Admin Today Action"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetLoginUserActivityByLoginName(UserActivityFixture.TestLoginName);

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Contains(UserActivityFixture.TestLoginName, x.LoginName));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date == DateTime.Today));
        Assert.DoesNotContain(result, x => x.UserId == UserActivityFixture.SiteAdminUserId);
    }

    [Fact]
    public async Task GetLoginUserActivityByLoginName_ReturnsFilteredResults_WhenIsParentFalse()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddHours(10),
                IsActive = true,
                Action = "Today Action 1"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetLoginUserActivityByLoginName(UserActivityFixture.TestLoginName);

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.CompanyId, x.CompanyId));
        Assert.All(result, x => Assert.Contains(UserActivityFixture.TestLoginName, x.LoginName));
    }

    [Fact]
    public async Task GetLoginUserActivityByLoginName_HandlesNullLoginName()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var result = await repository.GetLoginUserActivityByLoginName(null);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLoginUserActivityByLoginName_UsesContainsForLoginNameMatching()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = "prefix_" + UserActivityFixture.TestLoginName + "_suffix",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddHours(10),
                IsActive = true,
                Action = "Partial Match Action"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetLoginUserActivityByLoginName(UserActivityFixture.TestLoginName);

        Assert.Single(result);
        Assert.Equal("Partial Match Action", result.First().Action);
        Assert.Contains(UserActivityFixture.TestLoginName, result.First().LoginName);
    }

    #endregion

    #region LoginNameUserActivityAsync Tests

    [Fact]
    public async Task LoginNameUserActivityAsync_ReturnsEmptyList_WhenNoData()
    {
        var result = await _repository.LoginNameUserActivityAsync("nonexistent", "1-1-2024", "31-12-2024");
        Assert.Empty(result);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_ReturnsEmptyList_WhenInvalidDateFormat()
    {
        await _dbContext.UserActivities.AddRangeAsync(_userActivityFixture.UserActivitiesWithSameLoginName);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.LoginNameUserActivityAsync(UserActivityFixture.TestLoginName, "invalid-date", "31-12-2024");
        Assert.Empty(result);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_ReturnsEmptyList_WhenInvalidEndDateFormat()
    {
        await _dbContext.UserActivities.AddRangeAsync(_userActivityFixture.UserActivitiesWithSameLoginName);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.LoginNameUserActivityAsync(UserActivityFixture.TestLoginName, "1-1-2024", "invalid-date");
        Assert.Empty(result);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_ReturnsFilteredResults_WhenIsParentTrue()
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new UserActivityRepository(_dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Test Action"
            }
        };

        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.LoginNameUserActivityAsync(
            UserActivityFixture.TestLoginName,
            DateTime.Today.ToString("d-M-yyyy"),
            DateTime.Today.ToString("d-M-yyyy"));

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.TestLoginName, x.LoginName));
        Assert.DoesNotContain(result, x => x.UserId == UserActivityFixture.SiteAdminUserId);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_ReturnsFilteredResults_WhenIsParentFalse()
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(_dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Test Action"
            }
        };

        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.LoginNameUserActivityAsync(
            UserActivityFixture.TestLoginName,
            DateTime.Today.ToString("d-M-yyyy"),
            DateTime.Today.ToString("d-M-yyyy"));

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.CompanyId, x.CompanyId));
        Assert.All(result, x => Assert.Equal(UserActivityFixture.TestLoginName, x.LoginName));
    }

    [Theory]
    [InlineData("1-1-2024", "31-1-2025")]
    [InlineData("01-01-2024", "31-01-2025")]
    [InlineData("1-01-2024", "31-01-2025")]
    [InlineData("01-1-2024", "31-1-2025")]
    public async Task LoginNameUserActivityAsync_HandlesVariousDateFormats(string startDate, string endDate)
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new UserActivityRepository(_dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 1, 15),
                IsActive = true,
                Action = "Test Action"
            }
        };

        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.LoginNameUserActivityAsync(UserActivityFixture.TestLoginName, startDate, endDate);

        Assert.NotEmpty(result);
        Assert.All(result, x => Assert.Equal(UserActivityFixture.TestLoginName, x.LoginName));
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_HandlesNullLoginName()
    {
        var result = await _repository.LoginNameUserActivityAsync(null, "1-1-2024", "31-12-2024");
        Assert.Empty(result);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_ExcludesSiteAdminUsers()
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new UserActivityRepository(_dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Test Action"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.SiteAdminUserId,
                LoginName = "siteadmin",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Site Admin Action"
            }
        };

        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.LoginNameUserActivityAsync(
            UserActivityFixture.TestLoginName,
            DateTime.Today.ToString("d-M-yyyy"),
            DateTime.Today.ToString("d-M-yyyy"));

        Assert.NotEmpty(result);
        Assert.DoesNotContain(result, x => x.UserId == UserActivityFixture.SiteAdminUserId);
        Assert.All(result, x => Assert.NotNull(x.UserId));
    }

    #endregion

    #region Additional Edge Case Tests

    [Fact]
    public async Task ListAllUserActivityAsync_ExcludesSiteAdminUsers()
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new UserActivityRepository(_dbContext, mockUserService.Object);

        await _dbContext.UserActivities.AddRangeAsync(_userActivityFixture.UserActivitiesWithSameLoginName);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.ListAllUserActivityAsync("1-1-2024", "31-12-2025");

        Assert.NotEmpty(result);
        Assert.DoesNotContain(result, x => x.UserId == UserActivityFixture.SiteAdminUserId);
        Assert.All(result, x => Assert.NotNull(x.UserId));
    }

    [Fact]
    public async Task ListAllUserActivityAsync_HandlesNullUserIds()
    {
        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = null,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Null UserId Action"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Valid UserId Action"
            }
        };

        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllUserActivityAsync(
            DateTime.Today.ToString("d-M-yyyy"),
            DateTime.Today.ToString("d-M-yyyy"));

        Assert.Single(result);
        Assert.Equal("Valid UserId Action", result.First().Action);
        Assert.NotNull(result.First().UserId);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_FiltersCorrectDateRange()
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new UserActivityRepository(_dbContext, mockUserService.Object);
        //var additionalUsers = new List<User>
        //{
        //    new User
        //    {
        //        ReferenceId = "SITE_ADMIN_2",
        //        LoginName = "siteadmin2",
        //        Role = UserRole.SiteAdmin.ToString(),
        //        CompanyId = UserActivityFixture.CompanyId,
        //        IsActive = true
        //    }
        //};
        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 1, 5), // Before range
                IsActive = true,
                Action = "Before Range"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 1, 15), // In range
                IsActive = true,
                Action = "In Range"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 2, 5), // After range
                IsActive = true,
                Action = "After Range"
            }
        };
        //await _dbContext.Users.AddRangeAsync(additionalUsers);
        // _dbContext.SaveChanges();
        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.LoginNameUserActivityAsync(
            UserActivityFixture.TestLoginName,
            "10-1-2024",
            "31-1-2025");

        Assert.Single(result);
        Assert.Equal("In Range", result.First().Action);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_HandlesExactDateMatching()
    {
        var testDate = new DateTime(2024, 6, 15, 14, 30, 0);
        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = testDate,
                IsActive = true,
                Action = "Exact Date Action"
            }
        };

        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.LoginNameUserActivityAsync(
            UserActivityFixture.TestLoginName,
            "15-6-2024",
            "15-7-2025");

        Assert.Single(result);
        Assert.Equal("Exact Date Action", result.First().Action);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_HandlesEndDatePlusOneDay()
    {
        var testDate = new DateTime(2024, 6, 15, 23, 59, 59);
        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = testDate,
                IsActive = true,
                Action = "End of Day Action"
            }
        };

        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
         _dbContext.SaveChanges();

        var result = await _repository.LoginNameUserActivityAsync(
            UserActivityFixture.TestLoginName,
            "15-6-2024",
            "15-6-2025");

        Assert.Single(result);
        Assert.Equal("End of Day Action", result.First().Action);
    }

    [Fact]
    public async Task GetUserActivityByLoginName_HandlesMultipleSiteAdmins()
    {
        var additionalUsers = new List<User>
        {
            new User
            {
                ReferenceId = "SITE_ADMIN_2",
                LoginName = "siteadmin2",
                Role = UserRole.SiteAdmin.ToString(),
                CompanyId = UserActivityFixture.CompanyId,
                IsActive = true
            }
        };

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Regular User Action"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.SiteAdminUserId,
                LoginName = "siteadmin",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Site Admin 1 Action"
            },
            new UserActivity
            {
                UserId = "SITE_ADMIN_2",
                LoginName = "siteadmin2",
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today,
                IsActive = true,
                Action = "Site Admin 2 Action"
            }
        };

        await _dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await _dbContext.Users.AddRangeAsync(additionalUsers);
        await _dbContext.UserActivities.AddRangeAsync(testActivities);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetUserActivityByLoginName(UserActivityFixture.TestLoginName);

        Assert.Single(result);
        Assert.Equal("Regular User Action", result.First().Action);
        Assert.DoesNotContain(result, x => x.UserId == UserActivityFixture.SiteAdminUserId);
        Assert.DoesNotContain(result, x => x.UserId == "SITE_ADMIN_2");
    }

    //[Fact]
    //public async Task GetStartTimeEndTimeByUser_HandlesInvalidDateFormat()
    //{
    //    using var dbContext = DbContextFactory.CreateInMemoryDbContext();
    //    var mockUserService = new Mock<ILoggedInUserService>();
    //    mockUserService.Setup(x => x.IsParent).Returns(true);
    //    mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

    //    var repository = new UserActivityRepository(dbContext, mockUserService.Object);

    //    // This should handle gracefully when ToDateTime() fails
    //    var result = await repository.GetStartTimeEndTimeByUser(
    //        UserActivityFixture.TestUserId,
    //        "invalid-date-format",
    //        "31-12-2024");

    //    Assert.Empty(result);
    //}

    //[Fact]
    //public async Task ListAllUserActivityAsync_HandlesInvalidDateFormat()
    //{
    //    using var dbContext = DbContextFactory.CreateInMemoryDbContext();
    //    var mockUserService = new Mock<ILoggedInUserService>();
    //    mockUserService.Setup(x => x.IsParent).Returns(true);
    //    mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

    //    var repository = new UserActivityRepository(dbContext, mockUserService.Object);

    //    // This should handle gracefully when ToDateTime() fails
    //    var result = await repository.ListAllUserActivityAsync("invalid-date-format", "31-12-2024");

    //    Assert.Empty(result);
    //}

    [Fact]
    public async Task GetUserActivityByLoginName_HandlesNullCompanyId()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = null, // Null CompanyId
                CreatedDate = DateTime.Today.AddDays(-1),
                IsActive = true,
                Action = "Null CompanyId Action"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddDays(-1),
                IsActive = true,
                Action = "Valid CompanyId Action"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetUserActivityByLoginName(UserActivityFixture.TestLoginName);

        Assert.Single(result);
        Assert.Equal("Valid CompanyId Action", result.First().Action);
        Assert.NotNull(result.First().CompanyId);
    }

    [Fact]
    public async Task GetLoginUserActivityByLoginName_HandlesUsersWithNullRoles()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var usersWithNullRoles = new List<User>
        {
            new User
            {
                ReferenceId = "USER_NULL_ROLE",
                LoginName = "nullroleuser",
                Role = null, // Null role
                CompanyId = UserActivityFixture.CompanyId,
                IsActive = true
            }
        };

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = "USER_NULL_ROLE",
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = DateTime.Today.AddHours(10),
                IsActive = true,
                Action = "Null Role User Action"
            }
        };

        await dbContext.Users.AddRangeAsync(usersWithNullRoles);
        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.SaveChangesAsync();

        var result = await repository.GetLoginUserActivityByLoginName(UserActivityFixture.TestLoginName);

        Assert.Single(result);
        Assert.Equal("Null Role User Action", result.First().Action);
    }

    [Fact]
    public async Task LoginNameUserActivityAsync_HandlesNullCompanyIdInQuery()
    {
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(UserActivityFixture.CompanyId);

        var repository = new UserActivityRepository(dbContext, mockUserService.Object);

        var testActivities = new List<UserActivity>
        {
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = null, // Null CompanyId should be filtered out
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Null CompanyId Action"
            },
            new UserActivity
            {
                UserId = UserActivityFixture.TestUserId,
                LoginName = UserActivityFixture.TestLoginName,
                CompanyId = UserActivityFixture.CompanyId,
                CreatedDate = new DateTime(2024, 6, 15),
                IsActive = true,
                Action = "Valid CompanyId Action"
            }
        };

        await dbContext.UserActivities.AddRangeAsync(testActivities);
        await dbContext.Users.AddRangeAsync(_userActivityFixture.UserList);
        await dbContext.SaveChangesAsync();

        var result = await repository.LoginNameUserActivityAsync(
            UserActivityFixture.TestLoginName,
            "1-6-2024",
            "4-7-2025");

        Assert.Single(result);
        Assert.Equal("Valid CompanyId Action", result.First().Action);
        Assert.NotNull(result.First().CompanyId);
    }

    #endregion
}
