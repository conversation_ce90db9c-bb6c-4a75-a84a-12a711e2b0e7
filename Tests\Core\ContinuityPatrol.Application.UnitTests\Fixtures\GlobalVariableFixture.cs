using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalVariable.Events.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Events.Delete;
using ContinuityPatrol.Application.Features.GlobalVariable.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class GlobalVariableFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<GlobalVariable> GlobalVariables { get; set; }

    public List<GlobalVariable> InvalidGlobalVariables { get; set; }

    public CreateGlobalVariableCommand CreateGlobalVariableCommand { get; set; }

    public UpdateGlobalVariableCommand UpdateGlobalVariableCommand { get; set; }

    public GlobalVariableCreatedEvent GlobalVariableCreatedEvent { get; set; }

    public GlobalVariableDeletedEvent GlobalVariableDeletedEvent { get; set; }

    public GlobalVariableUpdatedEvent GlobalVariableUpdatedEvent { get; set; }

    public GlobalVariableFixture()
    {
        GlobalVariables = AddGlobalVariableBusinessLogic(AutoGlobalVariableFixture.Create<List<GlobalVariable>>());

        InvalidGlobalVariables = AddInvalidGlobalVariableBusinessLogic(AutoGlobalVariableFixture.CreateMany<GlobalVariable>(5).ToList());

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<GlobalVariableProfile>();
        });
        Mapper = configurationProvider.CreateMapper();

        CreateGlobalVariableCommand = AutoGlobalVariableFixture.Create<CreateGlobalVariableCommand>();

        UpdateGlobalVariableCommand = AutoGlobalVariableFixture.Create<UpdateGlobalVariableCommand>();

        GlobalVariableCreatedEvent = AutoGlobalVariableFixture.Create<GlobalVariableCreatedEvent>();

        GlobalVariableDeletedEvent = AutoGlobalVariableFixture.Create<GlobalVariableDeletedEvent>();

        GlobalVariableUpdatedEvent = AutoGlobalVariableFixture.Create<GlobalVariableUpdatedEvent>();
    }

    private List<GlobalVariable> AddInvalidGlobalVariableBusinessLogic(List<GlobalVariable> globalVariables)
    {
        var globalVariableList = new List<GlobalVariable>();

        for (var index = 0; index < globalVariables.Count; index++)
        {
            var globalVariable = globalVariables[index];

            // Set required properties for all invalid global variables
            globalVariable.IsActive = true;
            globalVariable.CreatedDate = DateTime.UtcNow;
            globalVariable.ReferenceId = Guid.NewGuid().ToString();

            if (index == 0)
            {
                globalVariable.VariableName = string.Empty; // Invalid empty name
                globalVariableList.Add(globalVariable);
                continue;
            }

            if (index == 1)
            {
                globalVariable.VariableName = new string('A', 101); // Invalid name too long
                globalVariableList.Add(globalVariable);
                continue;
            }

            if (index == 2)
            {
                globalVariable.VariableValue = string.Empty; // Invalid empty value
                globalVariableList.Add(globalVariable);
                continue;
            }

            if (index == 3)
            {
                globalVariable.VariableValue = new string('V', 501); // Invalid value too long
                globalVariableList.Add(globalVariable);
                continue;
            }

            if (index == 4)
            {
                globalVariable.Type = new string('T', 101); // Invalid type too long
                globalVariableList.Add(globalVariable);
                continue;
            }

            globalVariableList.Add(globalVariable);
        }

        return globalVariableList;
    }

    private List<GlobalVariable> AddGlobalVariableBusinessLogic(List<GlobalVariable> globalVariables)
    {
        var globalVariableList = new List<GlobalVariable>();

        foreach (var globalVariable in globalVariables)
        {
            globalVariable.IsActive = true;
            globalVariable.CreatedDate = DateTime.UtcNow;
            globalVariable.ReferenceId = Guid.NewGuid().ToString();

            globalVariableList.Add(globalVariable);
        }

        return globalVariableList;
    }

    public Fixture AutoGlobalVariableFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateGlobalVariableCommand>(p => p.VariableName, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateGlobalVariableCommand>(p => p.VariableValue, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateGlobalVariableCommand>(p => p.Type, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateGlobalVariableCommand>(p => p.VariableName, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateGlobalVariableCommand>(p => p.VariableValue, 10));

            fixture.Customize<GlobalVariable>(c => c.With(b => b.IsActive, true));
            fixture.Customize<GlobalVariable>(c => c.With(b => b.CreatedDate, DateTime.UtcNow));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<GlobalVariableCreatedEvent>(p => p.Name, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<GlobalVariableDeletedEvent>(p => p.Name, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<GlobalVariableUpdatedEvent>(p => p.Name, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
