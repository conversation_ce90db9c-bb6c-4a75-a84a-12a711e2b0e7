﻿using ContinuityPatrol.Application.Features.FiaTemplate.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaTemplate.Queries;

public class GetFiaTemplateNameUniqueQueryHandlerTests : IClassFixture<FiaTemplateFixture>
{
    private readonly FiaTemplateFixture _fiaTemplateFixture;
    private readonly Mock<IFiaTemplateRepository> _mockFiaTemplateRepository;
    private readonly GetFiaTemplateNameUniqueQueryHandler _handler;

    public GetFiaTemplateNameUniqueQueryHandlerTests(FiaTemplateFixture fiaTemplateFixture)
    {
        _fiaTemplateFixture = fiaTemplateFixture;

        _mockFiaTemplateRepository = FiaTemplateRepositoryMocks.GetFiaTemplateNameUniqueRepository(_fiaTemplateFixture.FiaTemplates);

        _handler = new GetFiaTemplateNameUniqueQueryHandler(_mockFiaTemplateRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_When_NameExists()
    {
        var existingName = _fiaTemplateFixture.FiaTemplates.First().Name;

        var query = new GetFiaTemplateNameUniqueQuery { Name = existingName };

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Return_False_When_NameDoesNotExist()
    {
        var nonExistingName = "NonExistingFiaTemplateName";

        var query = new GetFiaTemplateNameUniqueQuery { Name = nonExistingName };

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_Call_IsNameExistMethod_OnlyOnce()
    {
        var query = new GetFiaTemplateNameUniqueQuery { Name = "TestName" };

        await _handler.Handle(query, CancellationToken.None);

        _mockFiaTemplateRepository.Verify(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_When_NameExistsButSameId()
    {
        var existingFiaTemplate = _fiaTemplateFixture.FiaTemplates.First();

        var query = new GetFiaTemplateNameUniqueQuery
        {
            Name = existingFiaTemplate.Name,
            Id = existingFiaTemplate.ReferenceId
        };

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_Return_True_When_NameExistsWithDifferentId()
    {
        var existingFiaTemplate = _fiaTemplateFixture.FiaTemplates.First();

        var query = new GetFiaTemplateNameUniqueQuery
        {
            Name = existingFiaTemplate.Name,
            Id = Guid.NewGuid().ToString()
        };

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Return_False_When_EmptyName()
    {
        var query = new GetFiaTemplateNameUniqueQuery { Name = string.Empty };

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_Return_False_When_NullName()
    {
        var query = new GetFiaTemplateNameUniqueQuery { Name = null };

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_Should_Not_Throw_Exception_When_ValidQuery()
    {
        var query = new GetFiaTemplateNameUniqueQuery { Name = "TestName" };

        var exception = await Record.ExceptionAsync(async () =>
            await _handler.Handle(query, CancellationToken.None));

        exception.ShouldBeNull();
    }
}
