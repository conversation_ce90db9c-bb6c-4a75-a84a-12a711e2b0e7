﻿using ContinuityPatrol.Web.Areas.Report.Controllers;
using System.Data;
using System.Drawing;
using Newtonsoft.Json;
using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using ContinuityPatrol.Web.Areas.ITAutomation;
using DevExpress.XtraCharts;
using System.Runtime.Versioning;
using ContinuityPatrol.Web.Areas.ITAutomation.Controllers;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate;

public partial class ScheduledJobWorkflowReport : DevExpress.XtraReports.UI.XtraReport
{
    private ILogger<WorkflowScheduleExecutionHistoryController> _logger;
    public SchedulerWorkflowActionResultsVm ReportData = new SchedulerWorkflowActionResultsVm();
    public ScheduledJobWorkflowReport(string reportData)
    {
        try
        {
            _logger = WorkflowScheduleExecutionHistoryController.Logger;
            InitializeComponent();
            ReportData = JsonConvert.DeserializeObject<SchedulerWorkflowActionResultsVm>(reportData);

            DetailReport.DataSource = ReportData.ScheduleWorkflowActionResultsReportVms;
            _userName.Text = "Report Generated By : " + ReportData.ReportGeneratedBy;
            lblWorkflowName.Text = ReportData.ScheduleWorkflowActionResultsReportVms.FirstOrDefault().WorkflowName;

            var success = ReportData.ScheduleWorkflowActionResultsReportVms.Where(x => x.Status.ToLower().Equals("success"));
            var error = ReportData.ScheduleWorkflowActionResultsReportVms.Where(x => x.Status.ToLower().Equals("error"));

            lblSuccessCount.Text = success.Count().ToString();
            lblErrorCount.Text = error.Count().ToString();
            lblTotalCount.Text = ReportData.ScheduleWorkflowActionResultsReportVms.Count().ToString();
            _logger.LogInformation("Scheduled job workflow report loaded successfully");
        }
        catch(Exception ex)
        {
            _logger.LogInformation($"Scheduled job workflow report throw exception {ex.Message}");
        }
    }

    private int serialNumber = 1;

    private void xrSerialNumber_BeforePrint(object sender, CancelEventArgs e)
    {
        XRTableCell cell = (XRTableCell)sender;

        cell.Text = serialNumber.ToString();
        serialNumber++;
    }
    private void xrChart4_BeforePrint(object sender, System.EventArgs e)
    {
        try
        {
            var chartValues = ReportData.ScheduleWorkflowActionResultsReportVms;
           
            var success = chartValues.Where(x =>x.Status.ToLower().Equals("success"));
            var error = chartValues.Where(x => x.Status.ToLower().Equals("error"));

            Series series = new Series("Series1", ViewType.Bar);
            xrChart4.Series.Add(series);
            xrChart4.WidthF = 225f;
            xrChart4.HeightF = 200f;
            series.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            series.DataSource = CreateChartData(success.Count(), error.Count());
            series.ArgumentScaleType = ScaleType.Auto;
            series.ArgumentDataMember = "Argument";
            series.ValueScaleType = ScaleType.Numerical;
            series.ValueDataMembers.AddRange(new string[] { "Value" });
            series.View.Colorizer = new ColorObjectColorizer();
            BarSeriesView view = (BarSeriesView)series.View;
            view.BarWidth = 0.4;
            view.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
            view.Pane.BorderVisible = false;
            xrChart4.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            // X Axis Font Size
            AxisBase axisX = ((XYDiagram)xrChart4.Diagram).AxisX;
            if(OperatingSystem.IsWindows() && OperatingSystem.IsWindowsVersionAtLeast(6, 1))
            {
                axisX.Label.Font = new Font(axisX.Label.Font.FontFamily, 6f);
            }
            //((XYDiagram)xrChart4.Diagram).AxisY.GridLines.Visible = false;
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Scheduled Job Workflow Report's barchart. The error message : " + ex.Message); throw; }
    }
    private DataTable CreateChartData(Int64 Success, Int64 Error)
    {
        DataTable table = new DataTable("Table1");

        table.Columns.Add("Argument", typeof(string));
        table.Columns.Add("Value", typeof(Int64));

        Random rnd = new Random();

        table.Rows.Add("Success", Success);
        table.Rows.Add("Error", Error);

        return table;
    }
    private void _version_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Scheduled Job Workflow Report's CP Version. The error message : " + ex.Message); throw; }
    }

    [SupportedOSPlatform("windows")]
    private static Image LoadImageFromFile(MemoryStream path)
    {
        return Image.FromStream(path);
    }
    public void ClientCompanyLogo()
    {
        try
        {
            string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "-" : PreBuildReportController.CompanyLogo;
            if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "-")
            {
                prperpetuuitiLogo.Visible = false;
                if (imgbase64String.Contains(","))
                {
                    imgbase64String = imgbase64String.Split(',')[1];
                }
                byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    if (OperatingSystem.IsWindows())
                    {
                        prClientLogo.Image = LoadImageFromFile(ms);
                    }
                    else
                    {
                        throw new PlatformNotSupportedException("Image loading only works on Windows in this context.");
                    }
                }
            }
            else
            {
                prClientLogo.Visible = false;
                prperpetuuitiLogo.Visible = true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("Error occured while display the customer logo in Scheduled Job Workflow Report" + ex.Message.ToString());
        }
    }
}
