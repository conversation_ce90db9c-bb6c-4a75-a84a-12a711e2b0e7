﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Delete;
using ContinuityPatrol.Application.Features.WorkflowTemp.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowTemp.Events
{
    public class WorkflowTempDeletedEventHandlerTests : IClassFixture<WorkflowTempFixture>
    {
        private readonly WorkflowTempFixture _workflowTempFixture;

        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

        private readonly WorkflowTempDeletedEventHandler _handler;

        public WorkflowTempDeletedEventHandlerTests(WorkflowTempFixture workflowProfileInfoFixture)
        {
            _workflowTempFixture = workflowProfileInfoFixture;

            var mockLoggedInUserService = new Mock<ILoggedInUserService>();

            var mockWorkflowTempEventLogger = new Mock<ILogger<WorkflowTempDeletedEventHandler>>();

            _mockUserActivityRepository = WorkflowTempRepositoryMocks.CreateWorkflowTempEventRepository(_workflowTempFixture.UserActivities);

            _handler = new WorkflowTempDeletedEventHandler(mockLoggedInUserService.Object, mockWorkflowTempEventLogger.Object, _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_IncreaseUserActivityCount_When_DeleteWorkflowTempEventDeleted()
        {
            _workflowTempFixture.UserActivities[0].LoginName = "Test";

            var result = _handler.Handle(_workflowTempFixture.WorkflowTempDeletedEvent, CancellationToken.None);

            result.Equals(_workflowTempFixture.UserActivities[0].LoginName);

            await Task.CompletedTask;
        }

        [Fact]
        public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
        {
            await _handler.Handle(_workflowTempFixture.WorkflowTempDeletedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_UserActivityCount_When_DeleteWorkflowTempEventDeleted()
        {
            _workflowTempFixture.UserActivities[0].LoginName = "Test";

            var result = _handler.Handle(_workflowTempFixture.WorkflowTempDeletedEvent, CancellationToken.None);

            result.Equals(_workflowTempFixture.UserActivities[0].Id);

            result.Equals(_workflowTempFixture.WorkflowTempDeletedEvent.Name);

            await Task.CompletedTask;
        }
    }
}
