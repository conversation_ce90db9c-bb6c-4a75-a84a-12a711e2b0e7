using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserGroupFixture : IDisposable
{
    public List<UserGroup> UserGroupPaginationList { get; set; }
    public List<UserGroup> UserGroupList { get; set; }
    public UserGroup UserGroupDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public const string TestCompanyId = "COMPANY_123";
    public const string TestGroupName = "Test Group";
    public const string TestUserId = "USER_123";

    public UserGroupFixture()
    {
        var fixture = new Fixture();

        UserGroupList = fixture.Create<List<UserGroup>>();

        UserGroupPaginationList = fixture.CreateMany<UserGroup>(20).ToList();

        UserGroupDto = fixture.Create<UserGroup>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public UserGroup CreateUserGroup(
        string groupName = TestGroupName,
        string groupDescription = "Test Description",
        string userId = TestUserId,
        string userNames = "Test User",
        string userProperties = TestUserId,
        bool isActive = true,
        string referenceId = null)
    {
        return new UserGroup
        {
            ReferenceId = referenceId ?? Guid.NewGuid().ToString(),
            GroupName = groupName,
            GroupDescription = groupDescription,
            UserId = userId,
            UserNames = userNames,
            UserProperties = userProperties,
            IsActive = isActive
        };
    }

    public List<UserGroup> CreateMultipleUserGroups(
        int count,
        bool isActive = true)
    {
        var userGroups = new List<UserGroup>();
        for (int i = 1; i <= count; i++)
        {
            userGroups.Add(CreateUserGroup(
                groupName: $"Group {i:D2}",
                groupDescription: $"Description {i}",
                userId: $"USER_{i}",
                userNames: $"User {i}",
                userProperties: $"USER_{i}",
                isActive: isActive
            ));
        }
        return userGroups;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
