using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGap.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Commands;

public class DeleteCyberAirGapTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<ICyberAirGapRepository> _mockCyberAirGapRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly Mock<ICyberJobManagementRepository> _mockCyberJobManagement;
    private readonly DeleteCyberAirGapCommandHandler _handler;

    public DeleteCyberAirGapTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockCyberAirGapRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(_cyberAirGapFixture.CyberAirGaps);
        _mockPublisher = new Mock<IPublisher>();
        _mockCyberJobManagement = new Mock<ICyberJobManagementRepository>();

        _handler = new DeleteCyberAirGapCommandHandler(
            _mockCyberAirGapRepository.Object,
            _mockPublisher.Object,
            _mockCyberJobManagement.Object);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGap_When_NotInUse()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        // Setup: No cyber jobs associated with this air gap
        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<CyberJobManagement>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteCyberAirGapResponse>();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("Airgap");
        result.Message.ShouldContain(existingAirGap.Name);
    }

    [Fact]
    public async Task Handle_CallGetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<CyberJobManagement>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockCyberAirGapRepository.Verify(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_CallUpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<CyberJobManagement>());

        _mockCyberAirGapRepository
     .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
     .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockCyberAirGapRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGap>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishCyberAirGapDeletedEvent_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
         .ReturnsAsync(new List<CyberJobManagement>());

        _mockCyberAirGapRepository
     .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
     .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteCyberAirGapCommand { Id = nonExistentId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapIsInactive()
    {
        // Arrange
        var inactiveAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        inactiveAirGap.IsActive = false;
        var command = new DeleteCyberAirGapCommand { Id = inactiveAirGap.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowEntityAssociatedException_When_AirGapInUse()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        
        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
    .ReturnsAsync(new List<CyberJobManagement>() );

        // Act & Assert
        //var exception = await Should.ThrowAsync<EntityAssociatedException>(async () =>
        //    await _handler.Handle(command, CancellationToken.None));
        
        //exception.Message.ShouldContain("currently in use");
    }

    [Fact]
    public async Task Handle_VerifyEventData_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<CyberJobManagement>());

        CyberAirGapDeletedEvent publishedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
        .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(existingAirGap.Name);
    }

    [Fact]
    public async Task Handle_VerifySoftDelete_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
             .ReturnsAsync(new List<CyberJobManagement>());

        Domain.Entities.CyberAirGap updatedEntity = null;
        _mockCyberAirGapRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGap>()))
            .Callback<Domain.Entities.CyberAirGap>(entity => updatedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAirGap entity) => entity);

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
        .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        updatedEntity.ShouldNotBeNull();
        updatedEntity.IsActive.ShouldBeFalse();
        updatedEntity.ReferenceId.ShouldBe(existingAirGap.ReferenceId);
    }

    [Fact]
    public async Task Handle_CallCyberJobManagement_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<CyberJobManagement>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
       .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockCyberJobManagement.Verify(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        //await Should.ThrowAsync<OperationCanceledException>(async () =>
        //    await _handler.Handle(command, cts.Token));
    }

    //[Fact]
    //public async Task Handle_DeleteMultipleAirGaps_When_ValidCommands()
    //{
    //    // Arrange
    //    var airGapsToDelete = _cyberAirGapFixture.CyberAirGaps.Take(2).ToList();
        
    //    foreach (var airGap in airGapsToDelete)
    //    {
    //        //_mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(airGap.ReferenceId))
    //        //    .ReturnsAsync(new List<object>());
    //    }

    //    // Act & Assert
    //    foreach (var airGap in airGapsToDelete)
    //    {
    //        var command = new DeleteCyberAirGapCommand { Id = airGap.ReferenceId };
    //        var result = await _handler.Handle(command, CancellationToken.None);
            
    //        result.ShouldNotBeNull();
    //        result.IsActive.ShouldBeFalse();
    //        result.Message.ShouldContain(airGap.Name);
    //    }

    //    // Verify all deletions were processed
    //    _mockCyberAirGapRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGap>()), Times.Exactly(2));
    //    _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    //}

    [Fact]
    public async Task Handle_DeleteAirGap_When_NullCyberJobList()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new DeleteCyberAirGapCommand { Id = existingAirGap.ReferenceId };

        // Setup: Null cyber job list
        _mockCyberJobManagement.Setup(x => x.GetCyberJobByAirGapId(existingAirGap.ReferenceId))
      .ReturnsAsync(new List<CyberJobManagement>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
     .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain(existingAirGap.Name);
    }
}
