﻿using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Application.Features.Company.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Application.Features.Company.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;


namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class CompanyController : BaseController
{
    private readonly ILogger<CompanyController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;

    public CompanyController(I<PERSON>apper mapper, ILogger<CompanyController> logger, IPublisher publisher, IDataProvider dataProvider)
    {
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _publisher = publisher;
    }


    [AntiXss]
    [EventCode(EventCodes.Company.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Company");

        await _publisher.Publish(new CompanyPaginatedEvent());

        return View();
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [EventCode(EventCodes.Company.CreateOrUpdate)]
    public async Task<JsonResult> CreateOrUpdate(CompanyViewModel company)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Company");
        try
        {
            if (!WebHelper.UserSession.IsParent)
            {
                _logger.LogDebug("Access restricted for non-parent company user in Company.");

                return Json(new { success = false, message = "Access restricted to child company user" });
            }

            var companyId = Request.Form["Id"].ToString();

            company.CompanyLogo ??= string.Empty;

            _logger.LogDebug($"Company Logo '{company.CompanyLogo}' is set in Company");

            if (companyId.IsNullOrWhiteSpace())
            {
                company.ParentId = LoggedInUserCompanyId;

                var companyCommand = _mapper.Map<CreateCompanyCommand>(company);

                _logger.LogDebug($"Creating Company '{company.Name}'");

                var result = await _dataProvider.Company.CreateAsync(companyCommand);

                _logger.LogDebug("CreateOrUpdate operation completed successfully in Company, returning view.");

                return Json(new { success = true, data = result });
            }
            else
            {
                company.ParentId ??= string.Empty;

                var companyCommand = _mapper.Map<UpdateCompanyCommand>(company);

                _logger.LogDebug($"Updating Company '{company.Name}'");

               var result = await _dataProvider.Company.UpdateAsync(companyCommand);

                _logger.LogDebug("CreateOrUpdate operation completed successfully in Company, returning view.");

                return Json(new { success = true, data = result });
            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on company page while processing the request for create or update.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpDelete]
    [EventCode(EventCodes.Company.Delete)]
    public async Task<JsonResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in company");      

        try
        {
            _logger.LogDebug($"Deleting Company Details by Id '{id}'");

            if (!WebHelper.UserSession.IsParent)
            {
                return Json(new { success = false, message = "Access restricted to child company user" });
            }

            var result = await _dataProvider.Company.DeleteAsync(id);

            return Json(new { Success = true, data = result});
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on company.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Company.IsCompanyNameExist)]
    public async Task<JsonResult> IsCompanyNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsCompanyNameExist method in Company");
        try
        {
            _logger.LogDebug("Returning result for IsCompanyNameExist on company");

            var result = await _dataProvider.Company.IsCompanyNameExist(name, id);

            return Json(new { Success = true, data = result });

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on company while checking if company name exists for : {name}.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Company.IsCompanyDisplayNameExist)]
    public async Task<JsonResult> IsCompanyDisplayNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsCompanyDisplayNameExist method in Company");
        try
        {
            _logger.LogDebug("Returning result for IsCompanyDisplayNameExist on company");

            var result = await _dataProvider.Company.IsCompanyDisplayNameExist(name, id);

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on company while checking if display name exists for : {name}.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Company.GetCompanies)]
    public async Task<JsonResult> GetCompanies()
    {
        _logger.LogDebug("Entering GetCompanies method in Company");
        try
        {
            _logger.LogDebug("Successfully retrieved company names in Company");

            var result = await _dataProvider.Company.GetCompanyNamesOnLogin();

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on company page while retrieving company names.", ex);

            return ex.GetJsonException();

        }
    }

    [HttpGet]
    [EventCode(EventCodes.Company.GetPagination)]
    public async Task<JsonResult> GetPagination(GetCompanyPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in Company");
        try
        {
            _logger.LogDebug("Successfully retrieved company paginated list on company page");

            var result = await _dataProvider.Company.GetPaginatedCompanies(query);

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on company page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Company.GetByReferenceId)]
    public async Task<JsonResult> GetCompanyById(string id)
    {
        _logger.LogDebug("Entering GetCompanyById method in Company");
        try
        {
            _logger.LogDebug($"Successfully retrieved company details for Id :{id} in company page");

            var result =  await _dataProvider.Company.GetCompanyById(id);

            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on company page while retrieving the company details by id.", ex);

            return ex.GetJsonException();
        }
    }
}