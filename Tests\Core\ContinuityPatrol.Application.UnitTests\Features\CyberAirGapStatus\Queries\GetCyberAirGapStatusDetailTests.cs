using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Queries;


public class GetCyberAirGapStatusDetailTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<ICyberAirGapStatusRepository> _mockCyberAirGapStatusRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetCyberAirGapStatusDetailsQueryHandler _handler;

    public GetCyberAirGapStatusDetailTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockCyberAirGapStatusRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(_cyberAirGapStatusFixture.CyberAirGapStatuses);
        _mockMapper = new Mock<IMapper>();

        _handler = new GetCyberAirGapStatusDetailsQueryHandler(
            _mockMapper.Object,
            _mockCyberAirGapStatusRepository.Object);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_ValidId()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = existingEntity.ReferenceId
        };

        var expectedVm = _cyberAirGapStatusFixture.CyberAirGapStatusDetailVm;
        expectedVm.Id = existingEntity.ReferenceId;
        expectedVm.AirGapName = existingEntity.AirGapName;

        _mockCyberAirGapStatusRepository
            .Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
            .ReturnsAsync(existingEntity);

        _mockMapper.Setup(x => x.Map<CyberAirGapStatusDetailVm>(existingEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CyberAirGapStatusDetailVm>();
        result.Id.ShouldBe(existingEntity.ReferenceId);
        result.AirGapName.ShouldBe(existingEntity.AirGapName);

        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(query.Id), Times.Once);
        _mockMapper.Verify(x => x.Map<CyberAirGapStatusDetailVm>(existingEntity), Times.Once);

    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_EntityNotFound()
    {
        // Arrange
        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = "non-existent-id"
        };


    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_EntityInactive()
    {
        // Arrange
        var inactiveEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        inactiveEntity.IsActive = false;

        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = inactiveEntity.ReferenceId
        };

        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(query.Id))
            .ReturnsAsync(inactiveEntity);

    }

    /// <summary>
    /// Test: Get cyber air gap log detail with cancellation token
    /// Expected: Respects cancellation and throws OperationCanceledException
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_CancellationRequested()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = existingEntity.ReferenceId
        };
        var cancellationToken = new CancellationToken(true);


    }

    /// <summary>
    /// Test: Get cyber air gap log detail when repository fails
    /// Expected: Throws exception
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_RepositoryFails()
    {
        // Arrange
        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = "test-id"
        };

        var mockFailingRepository = CyberAirGapStatusRepositoryMocks.CreateFailingCyberAirGapStatusRepository();
        var handler = new GetCyberAirGapStatusDetailsQueryHandler(
            _mockMapper.Object,
            mockFailingRepository.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(query, CancellationToken.None));

        exception.Message.ShouldBe("Query operation failed");
        _mockMapper.Verify(x => x.Map<CyberAirGapStatusDetailVm>(It.IsAny<Domain.Entities.CyberAirGapLog>()), Times.Never);
    }

    /// <summary>
    /// Test: Get cyber air gap log detail with mapper integration
    /// Expected: Correctly maps entity to view model
    /// </summary>
    //[Fact]
    //public async Task Handle_GetCyberAirGapLogDetail_When_MapperIntegration()
    //{
    //    // Arrange
    //    var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
    //    var query = new GetCyberAirGapStatusDetailQuery
    //    {
    //        Id = existingEntity.ReferenceId
    //    };

    //    var expectedVm = new CyberAirGapStatusDetailVm
    //    {
    //        Id = existingEntity.ReferenceId,
    //        AirGapId = existingEntity.AirGapId,
    //        AirGapName = existingEntity.AirGapName,
    //        SourceSiteName = existingEntity.SourceSiteName,
    //        TargetSiteName = existingEntity.TargetSiteName,
    //        Port = existingEntity.Port,
    //        Description = existingEntity.Description,
    //        Source = existingEntity.Source,
    //        Target = existingEntity.Target,
    //        Status = existingEntity.Status,
    //        IsFileTransfered = existingEntity.IsFileTransfered
    //    };

    //    _mockCyberAirGapStatusRepository
    //   .Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
    //   .ReturnsAsync(existingEntity);

    //    _mockMapper.Setup(x => x.Map<CyberAirGapStatusDetailVm>(existingEntity))
    //        .Returns(expectedVm);

    //    // Act
    //    var result = await _handler.Handle(query, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //    result.Id.ShouldBe(expectedVm.Id);
    //    result.AirGapId.ShouldBe(expectedVm.AirGapId);
    //    result.AirGapName.ShouldBe(expectedVm.AirGapName);
    //    result.SourceSiteName.ShouldBe(expectedVm.SourceSiteName);
    //    result.TargetSiteName.ShouldBe(expectedVm.TargetSiteName);
    //    result.Port.ShouldBe(expectedVm.Port);
    //    result.Description.ShouldBe(expectedVm.Description);
    //    result.Source.ShouldBe(expectedVm.Source);
    //    result.Target.ShouldBe(expectedVm.Target);
    //    result.Status.ShouldBe(expectedVm.Status);
    //    result.IsFileTransfered.ShouldBe(expectedVm.IsFileTransfered);

    //    _mockMapper.Verify(x => x.Map<CyberAirGapStatusDetailVm>(existingEntity), Times.Once);
    //}

    [Theory]
    [InlineData("Database", "Production Database Replication")]
    [InlineData("File", "File System Synchronization")]
    [InlineData("Archive", "Long-term Archive Storage")]
    [InlineData("Backup", "Backup Data Transfer")]
    [InlineData("Network", "Network Configuration Sync")]
    public async Task Handle_GetCyberAirGapLogDetail_When_DifferentAirGapTypes(string airGapType, string description)
    {
        // Arrange
        var testEntity = new Domain.Entities.CyberAirGapStatus
        {
            ReferenceId = $"test-{airGapType.ToLower()}-001",
            AirGapId = $"airgap-{airGapType.ToLower()}-001",
            AirGapName = $"{airGapType} Air Gap System",
            Description = description,
            SourceSiteName = $"{airGapType} Source Site",
            TargetSiteName = $"{airGapType} Target Site",
            IsActive = true
        };

        _cyberAirGapStatusFixture.CyberAirGapStatuses.Add(testEntity);

        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = testEntity.ReferenceId
        };

        var expectedVm = new CyberAirGapStatusDetailVm
        {
            Id = testEntity.ReferenceId,
            AirGapName = testEntity.AirGapName,
            Description = testEntity.Description
        };

        _mockMapper.Setup(x => x.Map<CyberAirGapStatusDetailVm>(testEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.AirGapName.ShouldContain(airGapType);
        result.Description.ShouldBe(description);

        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_ComplexProperties()
    {
        // Arrange
        var complexEntity = new Domain.Entities.CyberAirGapStatus
        {
            ReferenceId = "complex-test-001",
            AirGapName = "Complex Enterprise Air Gap",
            Source = @"{
                ""type"": ""database"",
                ""cluster"": {
                    ""name"": ""PROD-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""PROD-DB-01"", ""role"": ""Primary""},
                        {""server"": ""PROD-DB-02"", ""role"": ""Secondary""}
                    ]
                }
            }",
            Target = @"{
                ""type"": ""database"",
                ""cluster"": {
                    ""name"": ""DR-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""DR-DB-01"", ""role"": ""Primary""},
                        {""server"": ""DR-DB-02"", ""role"": ""Secondary""}
                    ]
                }
            }",
            IsActive = true
        };

        _cyberAirGapStatusFixture.CyberAirGapStatuses.Add(complexEntity);

        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = complexEntity.ReferenceId
        };

        var expectedVm = new CyberAirGapStatusDetailVm
        {
            Id = complexEntity.ReferenceId,
            AirGapName = complexEntity.AirGapName,
            Source = complexEntity.Source,
            Target = complexEntity.Target
        };

        _mockMapper.Setup(x => x.Map<CyberAirGapStatusDetailVm>(complexEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Source.ShouldContain("PROD-CLUSTER-01");
        result.Target.ShouldContain("DR-CLUSTER-01");
        result.Source.ShouldContain("PROD-DB-01");
        result.Target.ShouldContain("DR-DB-01");

        _mockMapper.Verify(x => x.Map<CyberAirGapStatusDetailVm>(complexEntity), Times.Once);
    }


    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_ValidatingResponse()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = existingEntity.ReferenceId
        };

        var expectedVm = new CyberAirGapStatusDetailVm
        {
            Id = existingEntity.ReferenceId,
            AirGapId = existingEntity.AirGapId,
            AirGapName = existingEntity.AirGapName,
            SourceSiteId = existingEntity.SourceSiteId,
            SourceSiteName = existingEntity.SourceSiteName,
            TargetSiteId = existingEntity.TargetSiteId,
            TargetSiteName = existingEntity.TargetSiteName,
            Port = existingEntity.Port,
            Description = existingEntity.Description,
            Source = existingEntity.Source,
            Target = existingEntity.Target,
            SourceComponentId = existingEntity.SourceComponentId,
            SourceComponentName = existingEntity.SourceComponentName,
            TargetComponentId = existingEntity.TargetComponentId,
            TargetComponentName = existingEntity.TargetComponentName,
            EnableWorkflowId = existingEntity.EnableWorkflowId,
            DisableWorkflowId = existingEntity.DisableWorkflowId,
            ErrorMessage = existingEntity.ErrorMessage,
            WorkflowStatus = existingEntity.WorkflowStatus,
            StartTime = existingEntity.StartTime,
            EndTime = existingEntity.EndTime,
            RPO = existingEntity.RPO,
            Status = existingEntity.Status,
            IsFileTransfered = existingEntity.IsFileTransfered
        };

        _mockMapper.Setup(x => x.Map<CyberAirGapStatusDetailVm>(existingEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CyberAirGapStatusDetailVm>();

        // Validate all properties are mapped correctly
        result.Id.ShouldBe(expectedVm.Id);
        result.AirGapId.ShouldBe(expectedVm.AirGapId);
        result.AirGapName.ShouldBe(expectedVm.AirGapName);
        result.SourceSiteId.ShouldBe(expectedVm.SourceSiteId);
        result.SourceSiteName.ShouldBe(expectedVm.SourceSiteName);
        result.TargetSiteId.ShouldBe(expectedVm.TargetSiteId);
        result.TargetSiteName.ShouldBe(expectedVm.TargetSiteName);
        result.Port.ShouldBe(expectedVm.Port);
        result.Description.ShouldBe(expectedVm.Description);
        result.Source.ShouldBe(expectedVm.Source);
        result.Target.ShouldBe(expectedVm.Target);
        result.SourceComponentId.ShouldBe(expectedVm.SourceComponentId);
        result.SourceComponentName.ShouldBe(expectedVm.SourceComponentName);
        result.TargetComponentId.ShouldBe(expectedVm.TargetComponentId);
        result.TargetComponentName.ShouldBe(expectedVm.TargetComponentName);
        result.EnableWorkflowId.ShouldBe(expectedVm.EnableWorkflowId);
        result.DisableWorkflowId.ShouldBe(expectedVm.DisableWorkflowId);
        result.ErrorMessage.ShouldBe(expectedVm.ErrorMessage);
        result.WorkflowStatus.ShouldBe(expectedVm.WorkflowStatus);
        result.StartTime.ShouldBe(expectedVm.StartTime);
        result.EndTime.ShouldBe(expectedVm.EndTime);
        result.RPO.ShouldBe(expectedVm.RPO);
        result.Status.ShouldBe(expectedVm.Status);
        result.IsFileTransfered.ShouldBe(expectedVm.IsFileTransfered);
    }

    /// <summary>
    /// Test: Get cyber air gap log detail repository verification
    /// Expected: Repository is called exactly once with correct parameters
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_RepositoryVerification()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var query = new GetCyberAirGapStatusDetailQuery
        {
            Id = existingEntity.ReferenceId
        };

        var expectedVm = _cyberAirGapStatusFixture.CyberAirGapStatusDetailVm;
        _mockMapper.Setup(x => x.Map<CyberAirGapStatusDetailVm>(existingEntity))
            .Returns(expectedVm);

        _mockCyberAirGapStatusRepository
            .Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
            .ReturnsAsync(existingEntity);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();

        // Verify repository was called exactly once with the correct ID
        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(query.Id), Times.Once);

        // Verify mapper was called exactly once with the correct entity
        _mockMapper.Verify(x => x.Map<CyberAirGapStatusDetailVm>(existingEntity), Times.Once);

    }
}


