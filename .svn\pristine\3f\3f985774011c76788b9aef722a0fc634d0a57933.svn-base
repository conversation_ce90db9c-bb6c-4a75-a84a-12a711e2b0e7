﻿using System.Data;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Report.ReportTemplate;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Domain.ViewModels.ServerSubTypeModel;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using NPOI.HSSF.UserModel;
using NPOI.SS.Util;
using NPOI.SS.UserModel;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using Newtonsoft.Json;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Application.Contracts.Persistence;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class BulkImportInputController : BaseController
{
    private readonly ILogger<BulkImportInputController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IFormRepository _formRepository;
    public static List<string> ServerValues = new List<string>();
    public static List<string> CommonValues = new List<string>();
    public static List<string> ServerDrValues = new List<string>();
    public static List<string> DatabaseValues = new List<string>();
    public static List<string> DatabaseDrValues = new List<string>();
    public static List<string> ReplicationValues = new List<string>();
    public static List<string> ReplicationDrValues = new List<string>();
    public static List<string> InfraObjectValues = new List<string>();
    public static List<string> WorkflowValues = new List<string>();
    public static List<string> propertyNames = new List<string>();
    public static List<string> propertyCount = new List<string>();
    public static string TemplateName { get; set; }
    public static string ReportGeneratedName { get; set; }
    public BulkImportInputController(ILoggedInUserService loggedInUserService, ILogger<BulkImportInputController> logger, IDataProvider dataProvider, IFormRepository formRepository)
    {
        _logger = logger;
        _dataProvider = dataProvider;
        _loggedInUserService = loggedInUserService;
        _formRepository = formRepository;

    }


    [HttpGet]
    public IActionResult List()
    {
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Download_Template(string templateType)
    {
        var reportsDirectoryInfra = "";
        var templateName = string.Empty;
        templateName = templateType switch
        {
            "MssqlNLS" => "MS-SQL2KX",
            "MSSQLMirroring" or "MSSQLAlWaysOn" => "MSSQL",
            "OracleRac" => "Oracle-Rac",
            "Postgres" => "PostgreSQL",
            "Mongo" => "MongoDB",
            _ => templateType
        };

        try
        {
            var businessServiceNames = await _dataProvider.BusinessService.GetBusinessServiceNames();
            //  var servers = await _dataProvider.Server.GetServerNames();
            var dbTypesFormTyprList = await _dataProvider.FormMapping.GetFormMappingList();
            var dbTypes = dbTypesFormTyprList.Where(x => x.Name.ToLower() == "database" && x.FormTypeName.ToLower().Equals(templateName.ToLower())).ToList();
            List<string> DbVersion = new List<string>();
            foreach (var value in dbTypes)
            {
                var result = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(value.Version);
                List<string> versions = result["Version"];
                for (int i = 0; i < versions.Count; i++)
                {
                    DbVersion.Add($"{value.FormTypeName} {versions[i]}");
                }
            }
            // var databases = await _dataProvider.Database.GetDatabaseNames();
            // var databasesType = await _dataProvider.Database.GetByType(null);
            var site = await _dataProvider.Site.GetSites();

            var serverRole = await _dataProvider.ServerType.GetServerTypeList();
            var serverType = await _dataProvider.ServerSubType.GetServerSubTypeList();

            var licenseNames = await _dataProvider.LicenseManager.GetAllPoNumbers();

            var formTyprList = await _dataProvider.FormMapping.GetFormMappingList();
            var osType = formTyprList.Where(x => x.Name.ToLower() == "server").ToList();
            List<string> OSVersion = new List<string>();
            List<string> winrmPort = new List<string>();
            List<string> proxyAccessType = new List<string>();
            foreach (var value in osType)
            {
                if (!string.IsNullOrEmpty(value.FormTypeName) && value.FormTypeName.ToLower().Equals("windows"))
                {
                    var winProperties = await _formRepository.GetByReferenceIdAsync(value.FormId);
                    var data = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(winProperties.Properties);

                    foreach (var keyValuePair in data)
                    {
                        if (keyValuePair.Key.Equals("fields", StringComparison.OrdinalIgnoreCase))
                        {
                            var config = keyValuePair.Value;
                            foreach (var fieldKeyValuePair in config)
                            {
                                var fieldConfig = fieldKeyValuePair.Value;
                                var label = fieldConfig.config?.label?.ToString();
                                if (!string.IsNullOrEmpty(label) && label.Contains("WinRM Port"))
                                {
                                    if (fieldConfig.options != null)
                                    {
                                        foreach (var option in fieldConfig.options)
                                        {
                                            if (option.value != null)
                                            {
                                                winrmPort.Add(option.value.ToString());
                                            }
                                        }
                                    }
                                }
                                label = fieldConfig.config?.label?.ToString();
                                if (!string.IsNullOrEmpty(label) && label.Contains("Proxy Access Type"))
                                {
                                    if (fieldConfig.options != null)
                                    {
                                        foreach (var option in fieldConfig.options)
                                        {
                                            if (option.value != null)
                                            {
                                                proxyAccessType.Add(option.value.ToString());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                var result = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(value.Version);
                List<string> versions = result["Version"];
                for (int i = 0; i < versions.Count; i++)
                {
                    OSVersion.Add($"{value.FormTypeName} {versions[i]}");
                }
            }
            winrmPort?.RemoveAt(0);
            proxyAccessType?.RemoveAt(0);
            //  var replicationNames = await _dataProvider.Replication.GetReplicationList();

            var operationalFunctionName = await _dataProvider.BusinessFunction.GetBusinessFunctionNames();
            var replicationList = await _dataProvider.FormMapping.GetFormMappingList();
            var replicationTypeLists = replicationList.Where(x => x?.Name?.ToLower() == "replication").ToList();
            var replicationCategories = await _dataProvider.ReplicationMaster.GetReplicationMasterNames();
            var replicationcatagory = replicationCategories.GroupBy(c => c.Name).Select(g => g.First()).ToList();
            var InfraList = await _dataProvider.InfraObject.GetInfraObjectList();
            var roboCopyPropertiesOption = await _dataProvider.RoboCopy.GetRoboCopyList();

            ServerValues.Clear();
            DatabaseValues.Clear();
            ReplicationValues.Clear();
            InfraObjectValues.Clear();
            WorkflowValues.Clear();
            ServerDrValues.Clear();
            DatabaseDrValues.Clear();
            ReplicationDrValues.Clear();
            CommonValues.Clear();
            CommonValues.AddRange(new[] { "Operational Service", "License Key" });
            #region PR Server Configuration
            ServerValues.AddRange(new[] { "Name", "Site Name", "Server Role", "Server Type", "OS Type", "OS Version" });
            if (templateType == "Oracle")
            {
                ServerValues.AddRange(new[] { "IpAddress", "HostName", "ConnectViaHostName", "Port",
             "AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword",
                 "SubstituteAuthentication","SubstituteAuthenticationType","SubstituteAuthenticationPath","SubstituteAuthenticationUser",
                 "SubstituteAuthenticationPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","ShellPrompt"});
            }
            if (templateType == "MssqlNLS")
            {
                ServerValues.AddRange(new[] { "ThisisaPartofCluster", "VirtualIPAddress", "IpAddress", "HostName",
               "ConnectViaHostName", "Port", "VirtualGuestOS", "AuthenticationType", "SSOEnabled", "singlesignon_name", "signonprofile","SSHUser","SSHPassword",
                "SSHKeyUser","SSHKeyPath","SSHKeyPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","ShellPrompt",
                "isASMGrid","ASMInstance"});
            }
            if (templateType == "Mongo")
            {
                ServerValues.AddRange(new[]{"ThisisaPartofCluster","VirtualIPAddress","IpAddress","HostName","ConnectViaHostName","Port","VirtualGuestOS","AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser",
                "SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword","SubstituteAuthentication","SubstituteAuthenticationType","ConfigureSubstituteAuthentication","ShellPrompt","isASMGrid","ASMInstance"});

            }
            if (templateType == "Mysql")
            {
                ServerValues.AddRange(new[]{"ThisisaPartofCluster","VirtualIPAddress","IpAddress","HostName","ConnectViaHostName","Port","VirtualGuestOS","AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser",
                "SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword","SubstituteAuthentication","SubstituteAuthenticationType","ConfigureSubstituteAuthentication","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","ShellPrompt","isASMGrid","ASMInstance"});

            }
            if (templateType == "MSSQLMirroring")
            {
                ServerValues.AddRange(new[] { "ThisisaPartofCluster", "VirtualIPAddress", "IpAddress", "HostName", "ConnectViaHostName", "Port", "VirtualGuestOS", "AuthenticationType",
                "SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort",
                "ProxyAccessType","ShellPrompt","isASMGrid","ASMInstance"});
            }
            if (templateType == "OracleRac")
            {
                ServerValues.AddRange(new[]{"ThisisaPartofCluster","VirtualIPAddress","IpAddress","HostName","ConnectViaHostName","Port","VirtualGuestOS",
                 "AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword",
                "SubstituteAuthentication", "SubstituteAuthenticationType", "SubstituteAuthenticationPath",
                "SubstituteAuthenticationUser", "SubstituteAuthenticationPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType", "ShellPrompt", "isASMGrid", "ASMInstance"});
            }

            if (templateType == "RoboCopy")
            {
                ServerValues.AddRange(new[]{"IpAddress","Host Name","ConnectViaHostName","Port","Virtual Guest OS","Authentication Type","SSOEnabled",
                "SSH User","SSH Password","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","Shell Prompt","IsASM Grid"});
            }
            if (templateType == "Postgres")
            {
                ServerValues.AddRange(new[]{"IpAddress","Host Name","ConnectViaHostName","Port","Virtual Guest OS","AuthenticationType","SSH User",
                "SSH Password","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","Shell Prompt"});
            }
            if (templateType == "SRM")
            {
                ServerValues.AddRange(new[] { "ThisisaPartofCluster","VirtualIPAddress","IpAddress", "HostName", "ConnectViaHostName", "Port","VirtualGuestOS",
             "AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword",
                 "WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL", "WinRMPort","ProxyAccessType","ShellPrompt","isASMGrid","ASMInstance"});
            }
            if (templateType == "MSSQLAlWaysOn")
            {
                ServerValues.AddRange(new[] { "ThisisaPartofCluster", "VirtualIPAddress", "IpAddress", "HostName", "ConnectViaHostName", "Port", "VirtualGuestOS", "AuthenticationType",
                "SSOEnabled", "singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL", "WinRMPort",
                "ProxyAccessType","ShellPrompt","isASMGrid","ASMInstance"});
            }
            if (templateType == "IBM DB2")
            {
                ServerValues.AddRange(new[]{"IpAddress","HostName","ConnectViaHostName","Port", "AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword",
                "SubstituteAuthentication", "SubstituteAuthenticationType", "SubstituteAuthenticationPath",
                "SubstituteAuthenticationUser", "SubstituteAuthenticationPassword","PortChannel","ShellPrompt","EnableSSHParameters","KeyExchangeCiphers","HostKeyAlgorithms","EncryptionCiphers","MAC Ciphers"});
            }
            #endregion
            //DR Server Configuration
            #region DR Server Configuration
            ServerDrValues.AddRange(new[] { "Name", "Site Name", "Server Role", "Server Type", "OS Type", "OS Version" });
            if (templateType == "Oracle")
            {
                ServerDrValues.AddRange(new[] { "IpAddress", "HostName", "ConnectViaHostName", "Port",
             "AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword",
                 "SubstituteAuthentication","SubstituteAuthenticationType","SubstituteAuthenticationPath","SubstituteAuthenticationUser",
                 "SubstituteAuthenticationPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","ShellPrompt"});
            }
            if (templateType == "MssqlNLS")
            {
                ServerDrValues.AddRange(new[] { "ThisisaPartofCluster", "VirtualIPAddress", "IpAddress", "HostName",
               "ConnectViaHostName", "Port", "VirtualGuestOS", "AuthenticationType", "SSOEnabled", "singlesignon_name", "signonprofile","SSHUser","SSHPassword",
                "SSHKeyUser","SSHKeyPath","SSHKeyPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","ShellPrompt",
                "isASMGrid","ASMInstance"});
            }
            if (templateType == "Mongo")
            {
                ServerDrValues.AddRange(new[]{"ThisisaPartofCluster","VirtualIPAddress","IpAddress","HostName","ConnectViaHostName","Port","VirtualGuestOS","AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser",
                "SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword","SubstituteAuthentication","SubstituteAuthenticationType","ConfigureSubstituteAuthentication","ShellPrompt","isASMGrid","ASMInstance"});
            }
            if (templateType == "Mysql")
            {
                ServerDrValues.AddRange(new[]{"ThisisaPartofCluster","VirtualIPAddress","IpAddress","HostName","ConnectViaHostName","Port","VirtualGuestOS","AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser",
                "SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword","SubstituteAuthentication","SubstituteAuthenticationType","ConfigureSubstituteAuthentication","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","ShellPrompt","isASMGrid","ASMInstance"});

            }
            if (templateType == "MSSQLMirroring")
            {
                ServerDrValues.AddRange(new[] { "ThisisaPartofCluster", "VirtualIPAddress", "IpAddress", "HostName", "ConnectViaHostName", "Port", "VirtualGuestOS", "AuthenticationType",
                "SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL", "WinRMPort",
                "ProxyAccessType","ShellPrompt","isASMGrid","ASMInstance"});
            }
            if (templateType == "OracleRac")
            {
                ServerDrValues.AddRange(new[]{"ThisisaPartofCluster","VirtualIPAddress","IpAddress","HostName","ConnectViaHostName","Port","VirtualGuestOS",
                 "AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword",
                "SubstituteAuthentication", "SubstituteAuthenticationType", "ConfigureSubstituteAuthentication", "SubstituteAuthenticationType", "SubstituteAuthenticationPath",
                "SubstituteAuthenticationUser", "SubstituteAuthenticationPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType", "ShellPrompt", "isASMGrid", "ASMInstance"});
            }

            if (templateType == "RoboCopy")
            {
                ServerDrValues.AddRange(new[]{"IpAddress","Host Name","ConnectViaHostName","Port","Virtual Guest OS","Authentication Type","SSOEnabled",
                "SSH User","SSH Password","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","Shell Prompt","IsASM Grid"});
            }
            if (templateType == "Postgres")
            {
                ServerDrValues.AddRange(new[]{"IpAddress","Host Name","ConnectViaHostName","Port","Virtual Guest OS","AuthenticationType","SSH User",
                "SSH Password","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","Shell Prompt"});
            }
            if (templateType == "SRM")
            {
                ServerDrValues.AddRange(new[] { "ThisisaPartofCluster","VirtualIPAddress","IpAddress", "HostName", "ConnectViaHostName", "Port","VirtualGuestOS",
             "AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword",
                 "WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL","WinRMPort","ProxyAccessType","ShellPrompt","isASMGrid","ASMInstance"});
            }
            if (templateType == "MSSQLAlWaysOn")
            {
                ServerDrValues.AddRange(new[] { "ThisisaPartofCluster", "VirtualIPAddress", "IpAddress", "HostName", "ConnectViaHostName", "Port", "VirtualGuestOS", "AuthenticationType",
                "SSOEnabled", "singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword","WMIUser","WMIPassword","PowerShellUser","PowerShellPassword","UseSSL", "WinRMPort",
                "ProxyAccessType","ShellPrompt","isASMGrid","ASMInstance"});
            }
            if (templateType == "IBM DB2")
            {
                ServerDrValues.AddRange(new[]{"IpAddress","HostName","ConnectViaHostName","Port", "AuthenticationType","SSOEnabled","singlesignon_name","signonprofile","SSHUser","SSHPassword","SSHKeyUser","SSHKeyPath","SSHKeyPassword",
                "SubstituteAuthentication", "SubstituteAuthenticationType", "SubstituteAuthenticationPath",
                "SubstituteAuthenticationUser", "SubstituteAuthenticationPassword","PortChannel","ShellPrompt","EnableSSHParameters","KeyExchangeCiphers","HostKeyAlgorithms","EncryptionCiphers","MAC Ciphers"});
            }
            #endregion
            // PRDatabase Configuration
            #region PR Database Configuration
            if (templateType != "RoboCopy" && templateType != "SRM")
            {

                DatabaseValues.AddRange(new[] { "Name", "Database Type", "Database Version"/*,"Server Name"*/});
                if (templateType == "Oracle")
                {
                    DatabaseValues.AddRange(new[]{"DatabaseConnectivity", "ThisisaPartofCluster", "OracleSID","InstanceName", "SSOEnabled",
                    "singlesignon_name","signonprofile","UserName", "Password", "Port","ArchivePath", "RedoPath", "OracleHomePath", "isASMGrid","ASMInstanceName", "ASMUserName",
                    "ASMPassword", "ASMGridPath","EnvVariable", "Envvariable", "DatabaseAuthentication","SQLPlus", "Authentication", "Role"});
                }
                if (templateType == "MssqlNLS")
                {
                    DatabaseValues.AddRange(new[]{ "ThisisaPartofCluster", "DatabaseSID", "UserName", "SSOEnabled", "singlesignon_name", "Password", "Port",
                    "AuthenticationMode", "configureinstancename", "InstanceName", "DatafileLocation", "TransactionLogsLocation", "UndoFileLocation", "BackupRestorePath", "NetworkSharedPath" });
                }
                if (templateType == "Mysql")
                {
                    DatabaseValues.AddRange(new[]{"ThisisaPartofCluster", "DatabaseName", "DatabaseUserName","DatabasePassword", "Port",
                    "ServicePath", "ServiceName","MySQLDBBinLocation"});
                }
                if (templateType == "MSSQLMirroring")
                {
                    DatabaseValues.AddRange(new[]{ "ThisisaPartofCluster","DatabaseSID","UserName","SSOEnabled", "singlesignon_name", "signonprofile", "Password", "Port", "AuthenticationMode", "configureinstancename",
                    "InstanceName", "DatafileLocation", "TransactionLogsLocation","UndoFileLocation","BackupRestorePath","NetworkSharedPath"});
                }
                if (templateType == "Mongo")
                {
                    DatabaseValues.AddRange(new[] { "Installation Path", "Instance Name", "Port", "User Name", "Password", "Mongo DBBinary Location" });
                }
                if (templateType == "OracleRac")
                {
                    DatabaseValues.AddRange(new[]{"DatabaseConnectivity", "ThisisaPartofCluster", "IsRac", "UniqueName","OracleSID", "InstanceName", "SSOEnabled",
                    "singlesignon_name", "signonprofile", "UserName", "Password", "Port","ArchivePath", "RedoPath", "OracleHomePath","isASMGrid",
                    "ASMInstanceName", "ASMUserName","ASMPassword", "ASMGridPath", "EnvVariable","Envvariable", "DatabaseAuthentication", "SQLPlus", "Authentication",
                    "Role"});
                }
                if (templateType == "Postgres")
                {
                    DatabaseValues.AddRange(new[]{"This is a Part of Cluster", "Database Name", "User Name", "Password", "Port", "Database Data Directory",
                   "Database Bin Directory", "SU Login (Optional)","Service Name (Optional)"});
                }
                if (templateType == "MSSQLAlWaysOn")
                {
                    DatabaseValues.AddRange(new[]{ "ThisisaPartofCluster", "DatabaseName", "UserName", "SSOEnabled", "singlesignon_name", "Password", "Port",
                    "AuthenticationMode", "configureinstancename", "InstanceName", "DatafileLocation", "TransactionLogsLocation", "UndoFileLocation", "BackupRestorePath", "NetworkSharedPath" });
                }
                if (templateType == "IBM DB2")
                {
                    DatabaseValues.AddRange(new[] { "ThisIsaPartofCluster", "DatabaseName", "UserName", "password", "Port", "InstanceName", "Envinorment Variable" });
                }

            }
            #endregion
            //DR DatabaseConfiguration
            #region DR Database Configuration
            if (templateType != "RoboCopy" && templateType != "SRM")
            {

                DatabaseDrValues.AddRange(new[] { "Name", "Database Type", "Database Version"/*, "Server Name"*/ });
                if (templateType == "Oracle")
                {
                    DatabaseDrValues.AddRange(new[]{"DatabaseConnectivity", "ThisisaPartofCluster", "OracleSID","InstanceName", "SSOEnabled",
                    "singlesignon_name","signonprofile","UserName", "Password", "Port","ArchivePath", "RedoPath", "OracleHomePath", "isASMGrid","ASMInstanceName", "ASMUserName",
                    "ASMPassword", "ASMGridPath","EnvVariable", "Envvariable", "DatabaseAuthentication","SQLPlus", "Authentication", "Role"});
                }
                if (templateType == "MssqlNLS")
                {
                    DatabaseDrValues.AddRange(new[]{ "ThisisaPartofCluster", "DatabaseSID", "UserName", "SSOEnabled", "singlesignon_name", "Password", "Port",
                    "AuthenticationMode", "configureinstancename", "InstanceName", "DatafileLocation", "TransactionLogsLocation", "UndoFileLocation", "BackupRestorePath", "NetworkSharedPath" });
                }
                if (templateType == "Mysql")
                {
                    DatabaseDrValues.AddRange(new[]{"ThisisaPartofCluster", "DatabaseName", "DatabaseUserName","DatabasePassword", "Port",
                    "ServicePath", "ServiceName","MySQLDBBinLocation"});
                }
                if (templateType == "MSSQLMirroring")
                {
                    DatabaseDrValues.AddRange(new[]{ "ThisisaPartofCluster","DatabaseSID","UserName","SSOEnabled", "singlesignon_name", "signonprofile", "Password", "Port", "AuthenticationMode", "configureinstancename",
                    "InstanceName", "DatafileLocation", "TransactionLogsLocation","UndoFileLocation","BackupRestorePath","NetworkSharedPath"});
                }
                if (templateType == "Mongo")
                {
                    DatabaseDrValues.AddRange(new[] { "Installation Path", "Instance Name", "Port", "User Name", "Password", "Mongo DBBinary Location" });
                }
                if (templateType == "OracleRac")
                {
                    DatabaseDrValues.AddRange(new[]{"DatabaseConnectivity", "ThisisaPartofCluster", "IsRac", "UniqueName","OracleSID", "InstanceName", "SSOEnabled",
                    "singlesignon_name", "signonprofile", "UserName", "Password", "Port","ArchivePath", "RedoPath", "OracleHomePath","isASMGrid",
                    "ASMInstanceName", "ASMUserName","ASMPassword", "ASMGridPath", "EnvVariable","Envvariable", "DatabaseAuthentication", "SQLPlus", "Authentication",
                    "Role"});
                }
                if (templateType == "Postgres")
                {
                    DatabaseDrValues.AddRange(new[]{"This is a Part of Cluster", "Database Name", "User Name", "Password", "Port", "Database Data Directory",
                   "Database Bin Directory", "SU Login (Optional)","Service Name (Optional)"});
                }
                if (templateType == "MSSQLAlWaysOn")
                {
                    DatabaseDrValues.AddRange(new[]{ "ThisisaPartofCluster", "DatabaseName", "UserName", "SSOEnabled", "singlesignon_name", "Password", "Port",
                    "AuthenticationMode", "configureinstancename", "InstanceName", "DatafileLocation", "TransactionLogsLocation", "UndoFileLocation", "BackupRestorePath", "NetworkSharedPath" });
                }
                if (templateType == "IBM DB2")
                {
                    DatabaseDrValues.AddRange(new[] { "ThisIsaPartofCluster", "DatabaseName", "UserName", "password", "Port", "InstanceName", "Envinorment Variable" });
                }

            }
            #endregion
            //PR Replication Configuration
            #region PR Replication Configuration
            if (templateType != "Postgres" && templateType != "Mongo" && templateType != "IBM DB2")
            {
                ReplicationValues.AddRange(new[] { "Name", "Site Name", "Replication Type" });
                if (templateType == "Oracle" || templateType == "SRM")
                {
                    ReplicationValues.AddRange(new[] { "ReplicationMode", "ServiceName", "ProtectionMode" });
                }
                if (templateType == "MssqlNLS")
                {
                    ReplicationValues.AddRange(new[] { "BackupJobName", "CopyJobName", "RestoreJobName", "ProdNetworkPath", "DRNetworkPath", "ProdLocalPath", "DRLocalPath", "SQL CMD Path" });
                }
                if (templateType == "Mysql")
                {
                    ReplicationValues.AddRange(new[] { "ProductionServer", "DRServer" });
                }
                if (templateType == "MSSQLMirroring")
                {
                    ReplicationValues.AddRange(new[] { "PRServerNetworkAddress", "DRServerNetworkAddress" });
                }
                //if (templateType == "Mongo")
                //{
                //    ReplicationValues.AddRange(new[]{ "HP3PAR Storage Server(PR)", "IP Address(PR)", "Username(PR)", "Password(Optional)(PR)", "HP3PAR Storage Server(DR)",
                //"IP Address(DR)", "Username(DR)", "Password(Optional)(DR)","HP3PAR Storage IP Address(PR)", "HP3PAR Storage Name(PR)", "Remote Copy Name(PR)", "Volume Name(PR)",
                //"HP3PAR Storage IP Address(DR)", "HP3PAR Storage Name(DR)", "Remote Copy Name(DR)", "Volume Name(DR)"});
                //}
                if (templateType == "OracleRac")
                {
                    ReplicationValues.AddRange(new[] { "ReplicationMode", "ServiceName", "ProtectionMode" });
                }
                if (templateType == "RoboCopy")
                {
                    ReplicationValues.AddRange(new[] { "OS Platform", "Time Interval", "Minutes/Hours/Days", "Source Directory", "Destination Directory", "RoboCopy Properties" });
                }
                if (templateType == "MSSQLAlWaysOn")
                {
                    ReplicationValues.AddRange(new[] { "AvailabilityGroupName", "groupRole" });
                }
            }
            #endregion
            //DR Replication configuration     
            #region DR Replication Configuration
            if (templateType != "Postgres" && templateType != "Mongo" && templateType != "IBM DB2")
            {
                ReplicationDrValues.AddRange(new[] { "Name", "Site Name", "Replication Type" });
                if (templateType == "Oracle" || templateType == "SRM")
                {
                    ReplicationDrValues.AddRange(new[] { "ReplicationMode", "ServiceName", "ProtectionMode" });
                }
                if (templateType == "MssqlNLS")
                {
                    ReplicationDrValues.AddRange(new[] { "BackupJobName", "CopyJobName", "RestoreJobName", "ProdNetworkPath", "DRNetworkPath", "ProdLocalPath", "DRLocalPath", "SQL CMD Path" });
                }
                if (templateType == "Mysql")
                {
                    ReplicationDrValues.AddRange(new[] { "ProductionServer", "DRServer" });
                }
                if (templateType == "MSSQLMirroring")
                {
                    ReplicationDrValues.AddRange(new[] { "PRServerNetworkAddress", "DRServerNetworkAddress" });
                }
                //if (templateType == "Mongo")
                //{
                //    ReplicationDrValues.AddRange(new[]{ "HP3PAR Storage Server(PR)", "IP Address(PR)", "Username(PR)", "Password(Optional)(PR)", "HP3PAR Storage Server(DR)",
                //"IP Address(DR)", "Username(DR)", "Password(Optional)(DR)","HP3PAR Storage IP Address(PR)", "HP3PAR Storage Name(PR)", "Remote Copy Name(PR)", "Volume Name(PR)",
                //"HP3PAR Storage IP Address(DR)", "HP3PAR Storage Name(DR)", "Remote Copy Name(DR)", "Volume Name(DR)"});
                //}
                if (templateType == "OracleRac")
                {
                    ReplicationDrValues.AddRange(new[] { "ReplicationMode", "ServiceName", "ProtectionMode" });
                }
                if (templateType == "RoboCopy")
                {
                    ReplicationDrValues.AddRange(new[] { "OS Platform", "Time Interval", "Minutes/Hours/Days", "Source Directory", "Destination Directory", "RoboCopy Properties" });
                }
                if (templateType == "MSSQLAlWaysOn")
                {
                    ReplicationDrValues.AddRange(new[] { "AvailabilityGroupName", "groupRole" });
                }
            }
            #endregion
            //InfraObject Configuration
            #region InfraObject Configuration
            InfraObjectValues.AddRange(new[]{ "Name", "Description", "Operational Function", "Activity Type", "Database Type", "Site Type PR","Site Type DR", "Replication Category Type",
            "Replication Type", "isPair", "isAssociate", "Pair InfraObject ID", "Associate InfraObject ID","Priority"});

            #endregion
            #region Workflow
            WorkflowValues.AddRange(new[] { "IsSwitchOver", "IsFailOver", "IsSwitchBack", "IsFailBack" });
            #endregion

            propertyNames.Clear();
            propertyCount.Clear();
            propertyNames.AddRange(new[]{CommonValues, ServerValues, ServerDrValues,DatabaseValues, DatabaseDrValues,ReplicationValues,
            ReplicationDrValues, InfraObjectValues,WorkflowValues}.SelectMany(x => x));
            propertyCount.Add("Common Configuration:" + CommonValues.Count);
            propertyCount.Add("PRServer Configuration:" + ServerValues.Count);
            propertyCount.Add("DRServer Configuration:" + ServerDrValues.Count);
            if (templateType != "RoboCopy" && templateType != "SRM")
            {
                propertyCount.Add("PRDatabase Configuration:" + DatabaseValues.Count);
                propertyCount.Add("DRDatabase Configuration:" + DatabaseDrValues.Count);
            }
            if (templateType != "Postgres" && templateType != "Mongo" && templateType != "IBM DB2")
            {
                propertyCount.Add("PRReplication Configuration:" + ReplicationValues.Count);
                propertyCount.Add("DRReplication Configuration:" + ReplicationDrValues.Count);
            }
            propertyCount.Add("Infraobject Configuration:" + InfraObjectValues.Count);
            propertyCount.Add("Workflow Template:" + WorkflowValues.Count);

            XtraReport reportInfra = new BulkImport();
            var filenameSuffixInfra = DateTime.Now.ToString("MMddyyyyhhmmsstt");
            var fileNameInfra = "BulkImportTemplate_" + templateType + "_" + filenameSuffixInfra + ".xls";
            reportsDirectoryInfra = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileNameInfra);
            reportInfra.ExportToXls(reportsDirectoryInfra);
            AddComboBoxToExcel(reportsDirectoryInfra, businessServiceNames, operationalFunctionName,
                //servers, databases,replicationNames, 
                site, serverRole, serverType, licenseNames, osType, OSVersion, dbTypes, DbVersion, replicationTypeLists, replicationcatagory, InfraList, roboCopyPropertiesOption, winrmPort, proxyAccessType);
            var fileBytes = System.IO.File.ReadAllBytes(reportsDirectoryInfra);
            return File(fileBytes, "application/vnd.ms-excel", fileNameInfra);
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred: {ex.Message}");
            return Content($"{ex.Message.ToString()}");
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectoryInfra))
            {
                System.IO.File.Delete(reportsDirectoryInfra);
            }
        }
    }

    public void AddComboBoxToExcel(string filePath, List<BusinessServiceNameVm> businessServices, List<BusinessFunctionNameVm> operationalFunctionName,
     //List<ServerNameVm> serverName, List<DatabaseNameVm> databaseNames, List<ReplicationListVm> replicationNames,
     List<SiteListVm> sites, List<ServerTypeListVm> serverRoles, List<ServerSubTypeListVm> serverTypes,
     List<LicenseManagerNameVm> licenseNames, List<FormTypeCategoryListVm> osTypes, List<string> OSVersion, List<FormTypeCategoryListVm> dbTypes, List<string> DbVersion,
     List<FormTypeCategoryListVm> replicationTypeList, List<ReplicationMasterNameVm> replicationcatagory, List<InfraObjectListVm> InfraList, List<RoboCopyListVm> RobocopyProp,
     List<string> winrmPort, List<string> proxyAccessType)
    {
        try
        {
            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite))
            {
                var workbook = new HSSFWorkbook(fileStream);
                var sheet = workbook.GetSheetAt(0);
                var columnEnd = FindCellsWithText(sheet, "IsFailBack");
                // Define the custom range A2:CT103
                int customStartRow = 2; int customEndRow = 501;
                int customStartCol = 0; int customEndCol = columnEnd[0].ColumnIndex;

                // Create an unlocked style
                ICellStyle unlockedStyle = workbook.CreateCellStyle();
                unlockedStyle.IsLocked = false;

                // Create a font for SF UI Text
                IFont sfFont = workbook.CreateFont();
                sfFont.FontName = "SF UI Text";

                // Create a custom style for the range A2:CT103
                ICellStyle customStyle = workbook.CreateCellStyle();
                customStyle.IsLocked = false;
                customStyle.SetFont(sfFont);
                customStyle.BorderLeft = BorderStyle.Thin; customStyle.BorderRight = BorderStyle.Thin; customStyle.BorderTop = BorderStyle.Thin; customStyle.BorderBottom = BorderStyle.Thin; customStyle.WrapText = true;
                customStyle.Alignment = HorizontalAlignment.Left;
                customStyle.VerticalAlignment = VerticalAlignment.Center;
                // Apply styles to the range  A2:CT103
                for (int rowIndex = customStartRow; rowIndex <= customEndRow; rowIndex++)
                {
                    IRow row = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);
                    row.HeightInPoints = 25F;
                    for (int colIndex = customStartCol; colIndex <= customEndCol; colIndex++)
                    {
                        ICell cell = row.GetCell(colIndex) ?? row.CreateCell(colIndex);
                        cell.CellStyle = customStyle; // Apply custom style to the cell
                    }
                }

                // Protect the sheet
                sheet.ProtectSheet("Ptechno");


                var dropdownData = new (string headerText, List<string> items, string namedRange, string[] asignHeaderText)[]
                {
                (null, businessServices.Select(bs => $"{bs.Name}${bs.Id}").ToList(), "OperationalServiceList", new[] { "Operational Service" }),
                (null, operationalFunctionName.Select(of => $"{of.Name}${of.Id}").ToList(), "OperationalFunctionList", new[] { "Operational Function" }),
                //("PRServer Configuration", serverName.Select(s => $"{s.Name}${s.Id}").ToList(), "ServerList", new[] { "Server Name", "PRServer Name", "DRServer Name" }),
                //("PRDatabase Configuration", databaseNames.Select(d => $"{d.Name}${d.Id}").ToList(), "DatabaseList", new[] { "PRDatabase Name", "DRDatabase Name" }),
                //("PRReplication Config", replicationNames.Select(r => $"{r.Name}${r.Id}").ToList(), "ReplicationList", new[] { "PRReplication Name", "DRReplication Name" }),
                (null, new List<string> { "High", "Low", "Medium" }, "CriticalAndPriorityList", new[] { "Critical Level", "Priority" }),
                (null, new List<string> { "true", "false" }, "checkBoxOptions", new[] { "UseSSL","isASMGrid", "Resiliency Ready", "SSOEnabled", "isPair", "isAssociate", "ConnectViaHostName",
                                         "VirtualGuestOS", "ThisisaPartofCluster", "SubstituteAuthentication","EnvVariable","DatabaseAuthentication","IsSwitchOver", "IsFailOver", "IsSwitchBack", "IsFailBack","configureinstancename", "IsRac"}),
                (null, new List<string> { "Application", "DB", "Virtual" }, "ActivityTypeOptions", new[] { "Activity Type" }),
                (null, new List<string> { "SshPassword", "SshKey","WMI","PowerShell" }, "AuthenticationType", new[] { "AuthenticationType" }),
                (null, new List<string> { "SQLServer", "Windows" }, "AuthenticationMode", new[] { "AuthenticationMode" }),
                (null, new List<string> { "ASYNC", "SYNC" }, "ReplicationMode", new[] { "ReplicationMode" }),
                (null, new List<string> { "DB User Credential" }, "Authentication", new[] { "Authentication" }),
                 (null, new List<string> { "Maximum Performance", "Maximum Protection","Maximum Availability" }, "ProtectionMode", new[] { "ProtectionMode" }),
                (null, sites?.Select(s => s.Name).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string> { "Enter Site name" }, "SiteList", new[] { "Site Name","Site Type PR","Site Type DR" }),
                (null, serverRoles?.Select(s => s.Name).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string> { "enter Server Role" }, "ServerRoleList", new[] { "Server Role" }),
                (null, serverTypes?.Select(c => c.Name).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string> { "Enter Server Type" }, "ServerTypeList", new[] { "Server Type" }),
                (null, licenseNames?.Select(c => c.PoNumber).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string> { "Enter LicenseKey" }, "LicenseKeyList", new[] { "License Key" }),
                (null, osTypes?.Select(c => c.FormTypeName).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string> { "Enter OSType" }, "OSTypeList", new[] { "OS Type" }),
                (null,OSVersion?.Where(version => !string.IsNullOrEmpty(version)).ToList()?? new List<string> { "Enter OSVersion" },"OSVersionList",new[] { "OS Version" }),
                (null, dbTypes?.Where(c => !string.IsNullOrEmpty(c.FormTypeName)).Select(c => c.FormTypeName).ToList() ?? new List<string> { "Enter Database Type Name" }, "DatabaseType", new[] { "Database Type" }),
                (null,DbVersion?.Where(version => !string.IsNullOrEmpty(version)).ToList()?? new List<string> { "Enter DbVersion" },"DbVersionList",new[] { "Database Version" }),
                (null, replicationTypeList?.Where(r => !string.IsNullOrEmpty(r.FormTypeName)).Select(r => r.FormTypeName).ToList() ?? new List<string> { "Enter Replication Type Name" }, "ReplicationType", new[] { "Replication Type" }),
                (null, replicationcatagory?.Where(r => !string.IsNullOrEmpty(r.Name)).Select(r => r.Name).ToList() ?? new List<string> { "Enter Replication Catagory Type Name" }, "ReplicationCategory", new[] { "Replication Category Type" }),
                 (null, InfraList?.Where(i => !string.IsNullOrEmpty(i.Name)).Select(i => i.Name).ToList() ?? new List<string> { "Enter InfraObject Name" }, "InfraObjectName", new[] { "Pair InfraObject ID", "Associate InfraObject ID" }),
                 (null, RobocopyProp?.Where(i => !string.IsNullOrEmpty(i.Name)).Select(i => i.Name).ToList() ?? new List<string> { "Enter RoboCopy Properties" }, "RoboCopyProp", new[] { "RoboCopy Properties" }),
                (null, winrmPort?.Where(winrmPorts => !string.IsNullOrEmpty(winrmPorts)).ToList() ?? new List<string> { "Enter WIN RM Port" }, "winrmPort", new[] { "WinRMPort" }),
                (null, proxyAccessType?.Where(proxyAccessTypes => !string.IsNullOrEmpty(proxyAccessTypes)).ToList() ?? new List<string> { "Enter Proxy Access Type" }, "proxyAccessType", new[] { "ProxyAccessType" }),
                (null, new List<string> { "via SSH", "Direct" }, "DatabaseConnectivity", new[] { "DatabaseConnectivity" }),
                (null, new List<string> { "sudo su", "su", "asu", "sudo", "privrun", "ispasswordless", "other" }, "SubstituteAuthenticationType", new[] { "SubstituteAuthenticationType" })
                };

                foreach (var data in dropdownData)
                {
                    AddDropdown(sheet, workbook, data.headerText, data.items, data.namedRange, data.asignHeaderText);
                }
                var labels = new[]
               {
                            "Port",
                            //"OperationalService Name",
                            //"OperationalFunction Name",
                            "Name",
                            "IpAddress",
                            "SSHUser",
                            "ShellPrompt"
                            //"Database Name",
                            //"Replication Name",
                            //"InfraObject Name"

                        };  // Validations

                foreach (var label in labels)
                {
                    var cells = FindCellsWithText(sheet, label);
                    if (cells.Any())
                    {
                        // Determine validation type based on label
                        ValidationType validationType;

                        if (label == "IpAddress")
                        {
                            validationType = ValidationType.IpAddress;
                        }
                        else if (label == "Port")
                        {
                            validationType = ValidationType.Port;
                        }
                        else if (label == "SSHUser" || label == "ShellPrompt")
                        {
                            validationType = ValidationType.SSHUser;
                        }
                        else
                        {
                            validationType = ValidationType.Alphanumeric;
                        }
                        ApplyValidationcells(sheet, cells, validationType);
                    }
                }

                using (var outStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(outStream);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred AddComboBoxToExcel: {ex.Message}");
            throw;
        }
    }
    private void AddDropdown(ISheet sheet, HSSFWorkbook workbook, string headerText, List<string> items, string namedRange, string[] asignHeaderText)
    {
        var hiddenSheetName = $"{headerText ?? namedRange}Options";
        hiddenSheetName = hiddenSheetName.Length > 31 ? hiddenSheetName[..31] : hiddenSheetName;
        var hiddenSheet = workbook.GetSheet(hiddenSheetName) ?? workbook.CreateSheet(hiddenSheetName);
        workbook.SetSheetHidden(workbook.GetSheetIndex(hiddenSheet), true);

        int rowIndex = 0;

        // Add items that may contain "$" to hidden sheet
        if (items.Any(i => i.Contains("$")))
        {
            foreach (var item in items.Where(i => !string.IsNullOrEmpty(i)))
            {
                var splitItem = item.Split('$');

                // Create a single row and populate both cells in that row
                var row = hiddenSheet.CreateRow(rowIndex++);
                row.CreateCell(0).SetCellValue(splitItem[0]);
                row.CreateCell(1).SetCellValue(splitItem[1]);
            }
        }
        else
        {
            foreach (var item in items.Where(i => !string.IsNullOrEmpty(i)))
                hiddenSheet.CreateRow(rowIndex++).CreateCell(0).SetCellValue(item);
        }

        if (headerText != null)
        {
            List<ICell> PrHeaderCells = null;
            List<ICell> DrHeaderCells = null;

            // Determine which headers to search for based on headerText
            switch (headerText)
            {
                case "PRServer Configuration":
                    PrHeaderCells = FindCellsWithText(sheet, "PRServer Configuration");
                    DrHeaderCells = FindCellsWithText(sheet, "DRServer Configuration");
                    break;
                case "PRDatabase Configuration":
                    PrHeaderCells = FindCellsWithText(sheet, "PRDatabase Configuration");
                    DrHeaderCells = FindCellsWithText(sheet, "DRDatabase Configuration");
                    break;
                case "PRReplication Config":
                    PrHeaderCells = FindCellsWithText(sheet, "PRReplication Configuration");
                    DrHeaderCells = FindCellsWithText(sheet, "DRReplication Configuration");
                    break;
            }

            if (PrHeaderCells != null && PrHeaderCells.Count > 0)
            {
                var startRow = PrHeaderCells[0].RowIndex + 2;
                var endRow = startRow + 500;
                var column1 = PrHeaderCells[0].ColumnIndex;

                var startRowDr = DrHeaderCells[0].RowIndex + 2;
                var endRowDr = startRowDr + 500;
                var column2 = DrHeaderCells[0].ColumnIndex;

                for (int rowNum = startRow; rowNum <= endRow; rowNum++)
                {
                    var row = sheet.GetRow(rowNum);

                    // Get values from both columns
                    var cell1 = row?.GetCell(column1);
                    var cell2 = row?.GetCell(column2);

                    // Add the value from Column A to the hidden sheet
                    if (cell1?.CellType == CellType.String && !string.IsNullOrEmpty(cell1.StringCellValue))
                    {
                        var hiddenRow = hiddenSheet.CreateRow(rowIndex++);
                        hiddenRow.CreateCell(0).SetCellFormula($"TRIM(Sheet!{cell1.Address.FormatAsString()})");
                    }

                    // Add the value from Column B to the hidden sheet
                    if (cell2?.CellType == CellType.String && !string.IsNullOrEmpty(cell2.StringCellValue))
                    {
                        var hiddenRow = hiddenSheet.CreateRow(rowIndex++);
                        hiddenRow.CreateCell(0).SetCellFormula($"TRIM(Sheet!{cell2.Address.FormatAsString()})");
                    }
                }
            }
        }


        // Create named range for dropdown that includes all entries in Column A
        var namedRangeDef = workbook.CreateName();
        namedRangeDef.NameName = namedRange;
        if (rowIndex != 0)
        {
            namedRangeDef.RefersToFormula = $"'{hiddenSheetName}'!$A$1:$A${rowIndex}";
        }
        else
        {
            namedRangeDef.RefersToFormula = $"'{hiddenSheetName}'!$A$1:$A$4";
        }

        // Apply dropdown to header cells
        foreach (var asignHeader in asignHeaderText)
        {
            var headerCellsForDropdown = FindCellsWithText(sheet, asignHeader);
            foreach (var headerCell in headerCellsForDropdown)
            {
                var cellRange = new CellRangeAddressList(headerCell.RowIndex + 1, headerCell.RowIndex + 500, headerCell.ColumnIndex, headerCell.ColumnIndex);
                var dvConstraint = new HSSFDataValidationHelper((HSSFSheet)sheet).CreateFormulaListConstraint(namedRange);
                var validation = new HSSFDataValidationHelper((HSSFSheet)sheet).CreateValidation(dvConstraint, cellRange);
                validation.EmptyCellAllowed = false;
                validation.ShowErrorBox = true;
                sheet.AddValidationData(validation);
            }
        }
    }
    private List<ICell> FindCellsWithText(ISheet sheet, string text)
    {
        var cells = new List<ICell>();
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                for (int cellIndex = 0; cellIndex < row.LastCellNum; cellIndex++)
                {
                    var cell = row.GetCell(cellIndex);
                    if (cell?.CellType == CellType.String && cell.StringCellValue.Equals(text, StringComparison.OrdinalIgnoreCase))
                    {
                        cells.Add(cell);
                    }
                }
            }
        }
        return cells;
    }
    private enum ValidationType
    {
        Alphanumeric,
        IpAddress,
        Port,
        SSHUser
    }
    private void ApplyValidationcells(ISheet sheet, IEnumerable<ICell> startCells, ValidationType validationType)
    {
        var dataValidationHelper = new HSSFDataValidationHelper((HSSFSheet)sheet);

        foreach (var startCell in startCells)
        {
            int startRow;
            int endRow = sheet.LastRowNum;
            int startColumn;
            bool isInMergedRegion = false;

            var mergedRegions = sheet.MergedRegions;
            var mergedRegion = mergedRegions.FirstOrDefault(r => r.IsInRange(startCell.RowIndex, startCell.ColumnIndex));

            if (mergedRegion != null)
            {
                isInMergedRegion = true;
                startRow = mergedRegion.LastRow + 1;
                startColumn = mergedRegion.FirstColumn;
            }
            else
            {
                startRow = startCell.RowIndex;
                startColumn = startCell.ColumnIndex;
            }

            var cellRangeAddressList = new CellRangeAddressList(startRow, endRow, startColumn, startColumn);
            string columnLetter = CellReference.ConvertNumToColString(startColumn);
            IDataValidationConstraint dvConstraint;
            string errorMessage;

            switch (validationType)
            {
                case ValidationType.IpAddress:
                    string ipAddressFormula =
                         "AND(" +
                         "A1=TRIM(A1), " +
                         "LEN(A1) = LEN(SUBSTITUTE(A1, \" \", \"\")), " +
                         "LEN(A1) - LEN(SUBSTITUTE(A1, \".\", \"\")) = 3, " +
                         "ISNUMBER(1*MID(A1,1,FIND(\".\",A1)-1)), " +
                         "1*MID(A1,1,FIND(\".\",A1)-1) >= 0, " +
                         "1*MID(A1,1,FIND(\".\",A1)-1) <= 255, " +
                         "ISNUMBER(1*MID(A1,FIND(\".\",A1)+1,FIND(\".\",A1,FIND(\".\",A1)+1)-FIND(\".\",A1)-1)), " +
                         "1*MID(A1,FIND(\".\",A1)+1,FIND(\".\",A1,FIND(\".\",A1)+1)-FIND(\".\",A1)-1) >= 0, " +
                         "1*MID(A1,FIND(\".\",A1)+1,FIND(\".\",A1,FIND(\".\",A1)+1)-FIND(\".\",A1)-1) <= 255, " +
                         "ISNUMBER(1*MID(A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1,FIND(\".\",A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1)-FIND(\".\",A1,FIND(\".\",A1)+1)-1)), " +
                         "1*MID(A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1,FIND(\".\",A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1)-FIND(\".\",A1,FIND(\".\",A1)+1)-1) >= 0, " +
                         "1*MID(A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1,FIND(\".\",A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1)-FIND(\".\",A1,FIND(\".\",A1)+1)-1) <= 255, " +
                         "ISNUMBER(1*RIGHT(A1,LEN(A1)-FIND(\".\",A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1))), " +
                         "1*RIGHT(A1,LEN(A1)-FIND(\".\",A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1)) >= 0, " +
                         "1*RIGHT(A1,LEN(A1)-FIND(\".\",A1,FIND(\".\",A1,FIND(\".\",A1)+1)+1)) <= 255" +
                         ")";

                    dvConstraint = dataValidationHelper.CreateCustomConstraint(ipAddressFormula);
                    errorMessage = "Please enter a valid IP address.";
                    break;
                case ValidationType.Port:
                    string portFormula =
                    "AND(" +
                    "LEN(A1)=SUMPRODUCT(--ISNUMBER(FIND(MID(A1,ROW(INDIRECT(\"1:\"&LEN(A1))),1),\"0123456789\"))), " +
                    "LEN(A1)<=5, " +
                    "A1 >= 0, " +
                    "A1 <= 65535, " +
                    "A1 = ROUND(A1, 0)" +
                    ")";
                    dvConstraint = dataValidationHelper.CreateCustomConstraint(portFormula);
                    errorMessage = "Please enter a valid port.";
                    break;
                case ValidationType.SSHUser:
                    string SSHFormula =
                    "AND(" +
                    "LEN(A1) = LEN(TRIM(A1)), " +
                    "TRIM(A1) = A1" +
                    ")";
                    dvConstraint = dataValidationHelper.CreateCustomConstraint(SSHFormula);
                    errorMessage = "Not allow starting and ending white space.";
                    break;
                case ValidationType.Alphanumeric:
                default:
                    string alphanumericFormula = $"AND( LEN(A1) > 2,NOT(ISNUMBER(LEFT(A1, 1) * 1)),LEN(A1) - 1 >= 2,   RIGHT(A1, 1) <> \"_\", LEN(A1) = SUMPRODUCT(--ISNUMBER(FIND(MID(A1, ROW(INDIRECT(\"1:\" & LEN(A1))), 1), \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_\"))), TRIM(INDIRECT(ADDRESS(ROW() - 1, COLUMN()))) <> \"\", COUNTIF(${columnLetter}$3:${columnLetter}${endRow}, A1) <= 1)";
                    dvConstraint = dataValidationHelper.CreateCustomConstraint(alphanumericFormula);
                    errorMessage = "Enter Valid Inputs Only.";
                    break;
            }
            var validation = dataValidationHelper.CreateValidation(dvConstraint, cellRangeAddressList);
            validation.CreateErrorBox("Invalid Input", errorMessage);
            validation.ShowErrorBox = true;

            sheet.AddValidationData(validation);
            sheet.ForceFormulaRecalculation = true;
        }
    }

}
