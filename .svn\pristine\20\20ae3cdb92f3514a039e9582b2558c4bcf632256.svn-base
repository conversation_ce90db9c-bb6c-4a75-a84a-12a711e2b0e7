<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Operation Type.js QUnit Tests</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script>console.log("Runner HTml loaded")</script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.2.0/sinon.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.19.4.js"></script>
    <script type="text/javascript">
        var RootUrl = '@Url.Content("~/")';
    </script>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
        <!-- Test elements will be added here dynamically -->
    </div>
    <script src="/js/Common/common.js"></script>
    <script src="/js/SiteAdmin/Form/ServerMapping/ServerMapping.js"></script>
    <script src="/js/SiteAdmin/Form/ServerMapping/ServerMappingTest.js"></script>
    <script>console.log("Runner HTml loaded")</script>
</body>
</html>