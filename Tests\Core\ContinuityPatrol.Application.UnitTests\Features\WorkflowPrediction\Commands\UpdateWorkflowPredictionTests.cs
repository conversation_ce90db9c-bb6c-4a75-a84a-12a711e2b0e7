﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Commands;

public class UpdateWorkflowPredictionTests
{
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IWorkflowPredictionRepository> _mockWorkflowPredictionRepository;
    private readonly UpdateWorkflowPredictionCommandHandler _handler;

    public UpdateWorkflowPredictionTests()
    {
        _mockMapper = new Mock<IMapper>();
        Mock<IPublisher> mockPublisher = new();
        _mockWorkflowPredictionRepository = new Mock<IWorkflowPredictionRepository>();

        _handler = new UpdateWorkflowPredictionCommandHandler(
            _mockMapper.Object,
            _mockWorkflowPredictionRepository.Object,
            mockPublisher.Object
        );
    }

    [Fact]
    public async Task Handle_ReturnsCorrectResponse_WhenWorkflowPredictionUpdatedSuccessfully()
    {
        var actionId = Guid.NewGuid().ToString();
        var nextPossibleId = Guid.NewGuid().ToString();
        var nodeId = Guid.NewGuid().ToString();

        var command = new UpdateWorkflowPredictionCommand
        {
            Id = Guid.NewGuid().ToString(),
            ActionId = actionId,
            ActionName = "Updated Action",
            Count = 20,
            NextPossibleId = nextPossibleId,
            NextPossibleActionName = "Updated Next Action",
            NodeId = nodeId
        };
        var workflowPrediction = new Domain.Entities.WorkflowPrediction
        {
            ReferenceId = command.Id,
            ActionId = actionId,
            ActionName = "Updated Action",
            Count = 20,
            NextPossibleId = nextPossibleId,
            NextPossibleActionName = "Updated Next Action",
            NodeId = nodeId
        };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(workflowPrediction);

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.UpdateAsync(workflowPrediction))
            .ReturnsAsync(workflowPrediction);

        _mockMapper
            .Setup(mapper => mapper.Map(command, workflowPrediction, typeof(UpdateWorkflowPredictionCommand), typeof(Domain.Entities.WorkflowPrediction)));

        var expectedMessage = $" WorkflowPrediction '{workflowPrediction.ActionId}' has been updated successfully";

        var result = await _handler.Handle(command, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(expectedMessage, result.Message);
        Assert.Equal(command.Id, result.WorkflowPredictionId);

        Assert.Equal(workflowPrediction.ActionId, command.ActionId);
        Assert.Equal(workflowPrediction.ActionName, command.ActionName);
        Assert.Equal(workflowPrediction.Count, command.Count);
        Assert.Equal(workflowPrediction.NextPossibleActionName, command.NextPossibleActionName);
        Assert.Equal(workflowPrediction.NextPossibleId, command.NextPossibleId);
        Assert.Equal(workflowPrediction.NodeId, command.NodeId);

        _mockWorkflowPredictionRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockWorkflowPredictionRepository.Verify(repo => repo.UpdateAsync(workflowPrediction), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map(command, workflowPrediction, typeof(UpdateWorkflowPredictionCommand), typeof(Domain.Entities.WorkflowPrediction)), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowsNotFoundException_WhenWorkflowPredictionNotFound()
    {
        var command = new UpdateWorkflowPredictionCommand
        {
            Id = Guid.NewGuid().ToString(),
        };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync((Domain.Entities.WorkflowPrediction)null!);

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));

        _mockWorkflowPredictionRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockWorkflowPredictionRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.WorkflowPrediction>()), Times.Never);
        _mockMapper.Verify(mapper => mapper.Map(It.IsAny<UpdateWorkflowPredictionCommand>(), It.IsAny<Domain.Entities.WorkflowPrediction>(), typeof(UpdateWorkflowPredictionCommand), typeof(Domain.Entities.WorkflowPrediction)), Times.Never);
    }
}