﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfile.Events.Create;
using ContinuityPatrol.Application.Features.WorkflowProfile.Events.Delete;
using ContinuityPatrol.Application.Features.WorkflowProfile.Events.Update;
using ContinuityPatrol.Application.Features.WorkflowProfile.Events.UpdatePassword;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;
using WorkflowProfile = ContinuityPatrol.Domain.Entities.WorkflowProfile;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class WorkflowProfileFixture : IDisposable
{
    public IMapper Mapper { get; }
    public List<WorkflowProfile> WorkflowProfiles { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateWorkflowProfileCommand CreateWorkflowProfileCommand { get; set; }
    public UpdateWorkflowProfileCommand UpdateWorkflowProfileCommand { get; set; }
    public WorkflowProfileCreatedEvent WorkflowProfileCreatedEvent { get; set; }
    public WorkflowProfileDeletedEvent WorkflowProfileDeletedEvent { get; set; }
    public WorkflowProfileUpdatedEvent WorkflowProfileUpdatedEvent { get; set; }
    public UpdatePasswordEvent UpdatePasswordEvent { get; set; }

    public WorkflowProfileFixture()
    {
        WorkflowProfiles = AutoWorkflowProfileFixture.Create<List<WorkflowProfile>>();

        UserActivities = AutoWorkflowProfileFixture.Create<List<UserActivity>>();

        CreateWorkflowProfileCommand = AutoWorkflowProfileFixture.Create<CreateWorkflowProfileCommand>();

        UpdateWorkflowProfileCommand = AutoWorkflowProfileFixture.Create<UpdateWorkflowProfileCommand>();

        WorkflowProfileCreatedEvent = AutoWorkflowProfileFixture.Create<WorkflowProfileCreatedEvent>();

        WorkflowProfileDeletedEvent = AutoWorkflowProfileFixture.Create<WorkflowProfileDeletedEvent>();

        WorkflowProfileUpdatedEvent = AutoWorkflowProfileFixture.Create<WorkflowProfileUpdatedEvent>();
        UpdatePasswordEvent = AutoWorkflowProfileFixture.Create<UpdatePasswordEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<WorkflowProfileProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoWorkflowProfileFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateWorkflowProfileCommand>(p => p.Name, 10));
            fixture.Customize<CreateWorkflowProfileCommand>(c => c.With(b => b.Name, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowProfileCommand>(p => p.Name, 10));
            fixture.Customize<UpdateWorkflowProfileCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<WorkflowProfile>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateWorkflowProfileCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<WorkflowProfile>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowProfileCreatedEvent>(p => p.ProfileName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowProfileDeletedEvent>(p => p.ProfileName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowProfileUpdatedEvent>(p => p.ProfileName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}