﻿using ContinuityPatrol.Application.Features.Incident.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Events;

public class UpdateIncidentEventTests : IClassFixture<IncidentFixture>, IClassFixture<UserActivityFixture>
{
    private readonly IncidentFixture _incidentFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly IncidentUpdatedEventHanlder _handler;

    public UpdateIncidentEventTests(IncidentFixture incidentFixture, UserActivityFixture userActivityFixture)
    {
        _incidentFixture = incidentFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockIncidentEventLogger = new Mock<ILogger<IncidentUpdatedEventHanlder>>();

        _mockUserActivityRepository = CompanyRepositoryMocks.CreateCompanyEventRepository(_userActivityFixture.UserActivities);

        _handler = new IncidentUpdatedEventHanlder(_mockUserActivityRepository.Object, mockIncidentEventLogger.Object, mockLoggedInUserService.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateIncidentEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_incidentFixture.IncidentUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_incidentFixture.IncidentUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}