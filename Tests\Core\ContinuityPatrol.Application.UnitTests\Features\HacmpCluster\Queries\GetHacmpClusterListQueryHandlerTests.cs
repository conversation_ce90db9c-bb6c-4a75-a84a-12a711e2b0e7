﻿using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Queries;

public class GetHacmpClusterListQueryHandlerTests : IClassFixture<HacmpClusterFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;
    private Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;
    private readonly GetHacmpClusterListQueryHandler _handler;

    public GetHacmpClusterListQueryHandlerTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.GetHacmpClusterRepository(_hacmpClusterFixture.HacmpClusters);

        _handler = new GetHacmpClusterListQueryHandler(_hacmpClusterFixture.Mapper, _mockHacmpClusterRepository.Object);

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
    }

    [Fact]
    public async Task Handle_Return_Valid_HacmpClustersDetail()
    {
        var result = await _handler.Handle(new GetHacmpClusterListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<HacmpClusterListVm>>();

        result[0].Id.ShouldBe(_hacmpClusterFixture.HacmpClusters[0].ReferenceId);
        result[0].Name.ShouldBe(_hacmpClusterFixture.HacmpClusters[0].Name);
        result[0].ServerId.ShouldBe(_hacmpClusterFixture.HacmpClusters[0].ServerId);
        result[0].ServerName.ShouldBe(_hacmpClusterFixture.HacmpClusters[0].ServerName);
        result[0].LSSRCPath.ShouldBe(_hacmpClusterFixture.HacmpClusters[0].LSSRCPath);
        result[0].CLRGInfoPath.ShouldBe(_hacmpClusterFixture.HacmpClusters[0].CLRGInfoPath);
        result[0].ResourceGroupName.ShouldBe(_hacmpClusterFixture.HacmpClusters[0].ResourceGroupName);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.GetHacmpClusterEmptyRepository();
        var handler = new GetHacmpClusterListQueryHandler(_hacmpClusterFixture.Mapper, _mockHacmpClusterRepository.Object);
        var result = await handler.Handle(new GetHacmpClusterListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetHacmpClusterListQuery(), CancellationToken.None);
        _mockHacmpClusterRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public void GetHacmpClusterListQuery_CanBeInstantiated()
    {
        var query = new GetHacmpClusterListQuery();
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Return_AllHacmpClusters_When_MultipleRecords()
    {
        var result = await _handler.Handle(new GetHacmpClusterListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<HacmpClusterListVm>>();
        result.Count.ShouldBe(_hacmpClusterFixture.HacmpClusters.Count);

        foreach (var hacmpCluster in result)
        {
            hacmpCluster.Id.ShouldNotBeNullOrEmpty();
            hacmpCluster.Name.ShouldNotBeNullOrEmpty();
            hacmpCluster.ServerId.ShouldNotBeNullOrEmpty();
            hacmpCluster.ServerName.ShouldNotBeNullOrEmpty();
        }
    }

    [Fact]
    public async Task Handle_Return_MappedProperties_When_ValidHacmpClusters()
    {
        var result = await _handler.Handle(new GetHacmpClusterListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<HacmpClusterListVm>>();
        result.ShouldNotBeEmpty();

        var firstHacmpCluster = result.First();
        firstHacmpCluster.Id.ShouldNotBeNullOrEmpty();
        firstHacmpCluster.Name.ShouldNotBeNullOrEmpty();
        firstHacmpCluster.ServerId.ShouldNotBeNullOrEmpty();
        firstHacmpCluster.ServerName.ShouldNotBeNullOrEmpty();
        firstHacmpCluster.LSSRCPath.ShouldNotBeNullOrEmpty();
        firstHacmpCluster.CLRGInfoPath.ShouldNotBeNullOrEmpty();
        firstHacmpCluster.ResourceGroupName.ShouldNotBeNullOrEmpty();
    }
}