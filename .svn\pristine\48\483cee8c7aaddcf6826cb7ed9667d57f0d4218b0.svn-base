﻿<div class="modal-lg modal-dialog modal-dialog-scrollabel modal-dialog-centered">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-generate"></i><span>Generate Workflow</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>
        <div class="modal-body">
           @*  <div>
                <div class="form-label mb-1 p-1">Generate Type</div>
                <div class="form-group d-flex">
                    <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input generateType" name="solution" value="solution" checked/>
                        <label class='form-check-label'>From Template</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input generateType" name="files" value="files" />
                        <label class='form-check-label'>From File</label>
                    </div>
                </div>
            </div> *@

            <div class="wizard-content" id="workflowActionCreate">
                <form id="example-form" action="#" class="tab-wizard wizard-circle wizard clearfix example-form">
                    <h6>
                        <span class="step">
                            <i class="cp-user-role"></i>
                        </span>
                        <span class="step_title">
                            Workflow Creation
                        </span>
                    </h6>
                    <section id="solutionContainer">
                        <div class="form-group">
                            <div class="form-label">Replication Type</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-replication-type"></i></span>
                                <select class="form-select-modal" data-live-search="true" id="WFReplicationList" name="replicationId" data-placeholder="Select Replication Type">
                                </select>
                            </div>
                            <span id="replicationRestore-error"></span>
                        </div>

                        <div class="form-group">
                            <div class="form-label">Operation Type </div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-workflow-type"></i></span>
                                <select class="form-select-modal" data-live-search="true" id="WFOperationList" name="operationId" data-placeholder="Select Operation Type">
                                </select>
                            </div>
                            <span id="operationRestore-error"></span>
                        </div>

                        <div class="form-group" id="workflowNameContainer">
                            <div class="form-label">Workflow Template </div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-template-store"></i></span>
                                <select class="form-select-modal" data-live-search="true" id="WFWorkflowNameCont" name="workflowNameCont" data-placeholder="Select Workflow Template">
                                </select>
                            </div>
                            <span id="workflowNameCont-error"></span>
                        </div>

                        <div class="form-group d-flex">
                            <div class="form-check form-check-inline">
                                <input type="radio" class="form-check-input restore-check" id="WFInfraNameList" name="restore" value="infraobject" checked />
                                <label class='form-check-label'>InfraObject</label>

                            </div>

                            <div class="form-check form-check-inline">
                                <input type="radio" class="form-check-input restore-check" name="restore" value="custom" />
                                <label class='form-check-label'>Custom</label>

                            </div>
                        </div>

                        <div class="form-group" id="infraContainer">
                            <div class="form-label"> InfraObject Name </div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-infra-object"></i></span>
                                <select class="form-select-modal" id="WFInfraNameSelectList" name="infraNameCont" data-placeholder="Select InfraObject" data-live-search="true">
                                </select>
                            </div>
                            <span id="infraNameList-error"></span>
                        </div>

                        <div class="d-flex flex-row gap-3">
                            <div class="d-flex align-items-center mb-3" id="generateparentFindReplaceCheck">
                                <input type="checkbox" id="generateFindReplaceCheckBox" class="form-check" /> <span class="ms-2" id="generateFindReplaceLabel">Find and Replace</span>
                            </div>
                            <div class="d-flex align-items-center mb-3" id="generateparentCheck">
                                <input type="checkbox" id="generateAppendCheckBox" class="form-check" /> <span class="ms-2" id="generateAppendLabel">Append</span>
                            </div>
                        </div>

                        <div class="d-flex gap-2 d-none" id="generateFindAndReplaceContainer">
                            <div class="form-group w-100">
                                <div class="form-label">Find</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-search"></i></span>
                                    <input type="text" class="form-control" name="FindGenerateName" id="findGenerateValue"
                                           placeholder="Find Value" onchange="handleSaveAsChange(event, 'findGenerateValue-error')" />
                                </div>
                                <span id="findGenerateValue-error"></span>
                            </div>
                            <div class="form-group w-100">
                                <div class="form-label">Replace</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-replace"></i></span>
                                    <input type="text" class="form-control" name="replaceGenerateName" id="replaceGenerateValue"
                                           placeholder="Replace Value" onchange="handleSaveAsChange(event, 'replaceGenerateValue-error')" />
                                </div>
                                <span id="replaceGenerateValue-error"></span>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <div class="form-group w-100" id="positionGenerateContainer">
                                <div class="form-label">Position</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-activity-type"></i></span>
                                    <select class="form-select-modal select_actions" id="TextGenerateposition" name="position" onchange="handleSaveAsChange(event, 'TextGenerateposition-error')">
                                        <option value="prefix">Prefix</option>
                                        <option value="suffix">Suffix</option>
                                    </select>
                                </div>
                                <span id="TextGenerateposition-error"></span>
                            </div>
                            <div class="form-group w-100" id="textGenerateContainer">
                                <div class="form-label">Text</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-text"></i></span>
                                    <input type="text" class="form-control" name="Text" placeholder="Text" id="appendGenerateText"
                                           onchange="handleSaveAsChange(event, 'appendGenerateText-error')" />
                                </div>
                                <span id="appendGenerateText-error"></span>
                            </div>
                        </div>

                        <div id="fileContainer" class="d-none">
                            <div class="form-group">
                                <div class="form-label">Import Template File</div>
                                <div class="input-group">
                                    <input type="file" class="form-control" name="importTemplate" id="" accept="application/json" placeholder="File Name" />
                                </div>
                                <span id="importTemplate-error"></span>
                            </div>
                        </div>

                    </section>
                    <h6>
                        <span class="step">
                            <i class="cp-users-rectangle"></i>
                        </span>
                        <span class="step_title">
                            Component Mapping
                        </span>
                    </h6>
                    <section>
                        <div>
                            <div class="text-primary fw-semibold mb-1 d-none" id='templateComponent'>Infra Components</div>
                            <section class="restoreData p-2 mb-2"></section>
                        </div>
              
                    </section>
                </form>
            </div>
        </div>

        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary">
                <i class="cp-note me-1"></i>Note: All fields are mandatory
                except optional
            </small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm btnCancel" data-bs-dismiss="modal">Cancel</button>
                <a class="btn btn-primary prev_btn btn-sm" href="javascript:void(0)" role="menuitem" id="btnRestorePrevious">Previous</a>
                <a class="btn btn-primary next_btn btn-sm" href="javascript:void(0)" role="menuitem" id="btnRestoreNext">
                    Generate
                </a>
                <a class="btn btn-primary finish_btn btn-sm" href="javascript:void(0)" role="menuitem" id="saveRestore">Generate</a>
            </div>
        </div>

    </div>
</div>
