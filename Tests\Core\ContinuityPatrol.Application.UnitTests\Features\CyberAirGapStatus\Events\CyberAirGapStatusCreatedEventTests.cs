using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Events;

public class CyberAirGapStatusCreatedEventTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<CyberAirGapStatusCreatedEventHandler>> _mockLogger;
    private readonly CyberAirGapStatusCreatedEventHandler _handler;

    private readonly Mock<ILoggedInUserService> _userService;
    private readonly Mock<ILogger<CyberAirGapStatusCreatedEventHandler>> _logger;
    private readonly Mock<IUserActivityRepository> _userActivityRepository;

    public CyberAirGapStatusCreatedEventTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockUserActivityRepository = CyberAirGapStatusRepositoryMocks.CreateUserActivityRepository();
        _mockUserService = CyberAirGapStatusRepositoryMocks.CreateUserService();
        _mockLogger = new Mock<ILogger<CyberAirGapStatusCreatedEventHandler>>();

        _userActivityRepository = new Mock<IUserActivityRepository>();
        _userService = new Mock<ILoggedInUserService>();
        _logger = new Mock<ILogger<CyberAirGapStatusCreatedEventHandler>>();
        _handler = new CyberAirGapStatusCreatedEventHandler(
            _userService.Object,
            _logger.Object,
            _userActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_ValidData()
    {
        // Arrange
        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = "Enterprise Air Gap Status System"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_NullName()
    {
        // Arrange
        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = null
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldNotBeNull();
       
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_EmptyName()
    {
        // Arrange
        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = string.Empty
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_SpecialCharacters()
    {
        // Arrange
        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = "Special Characters & <script>alert('xss')</script> 🚀💻📊 测试数据"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldNotBeNull();
    }

    
    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_CancellationRequested()
    {
        // Arrange
        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = "Cancelled Event Test"
        };
        var cancellationToken = new CancellationToken(true);

    }

   
    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_RepositoryFails()
    {
        // Arrange
        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = "Repository Failure Test"
        };

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act & Assert
       
    }

    
    [Theory]
    [InlineData("User001", "<EMAIL>", "Company001", "*************", "/api/v1/cyberairgapstatus")]
    [InlineData("User002", "<EMAIL>", "Company002", "*********", "/api/v2/cyberairgapstatus")]
    [InlineData("AdminUser", "<EMAIL>", "AdminCompany", "***********", "/admin/cyberairgapstatus")]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_DifferentUserContexts(
        string userId, string loginName, string companyId, string ipAddress, string requestUrl)
    {
        // Arrange
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns(userId);
        mockUserService.Setup(x => x.LoginName).Returns(loginName);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);
        mockUserService.Setup(x => x.IpAddress).Returns(ipAddress);
        mockUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);

        var handler = new CyberAirGapStatusCreatedEventHandler(
             _userService.Object,
            _logger.Object,
            _userActivityRepository.Object);

        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = "User Context Test"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldNotBeNull();
        
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_NullUserServiceValues()
    {
        // Arrange
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns((string)null);
        mockUserService.Setup(x => x.LoginName).Returns((string)null);
        mockUserService.Setup(x => x.CompanyId).Returns((string)null);
        mockUserService.Setup(x => x.IpAddress).Returns((string)null);
        mockUserService.Setup(x => x.RequestedUrl).Returns((string)null);

        var handler = new CyberAirGapStatusCreatedEventHandler(
           _userService.Object,
            _logger.Object,
            _userActivityRepository.Object);

        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = "Null User Service Test"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldNotBeNull();
        
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_MultipleEvents()
    {
        // Arrange
        var events = new[]
        {
            new CyberAirGapStatusCreatedEvent { Name = "Air Gap Status 1" },
            new CyberAirGapStatusCreatedEvent { Name = "Air Gap Status 2" },
            new CyberAirGapStatusCreatedEvent { Name = "Air Gap Status 3" }
        };

        var capturedActivities = new List<Domain.Entities.UserActivity>();

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivities.Add(activity))
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        foreach (var evt in events)
        {
            await _handler.Handle(evt, CancellationToken.None);
        }

        // Assert
        capturedActivities.ShouldNotBeNull();    
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusCreatedEvent_When_ValidatingActivityDetails()
    {
        // Arrange
        var createdEvent = new CyberAirGapStatusCreatedEvent
        {
            Name = "Validation Test Air Gap Status"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        createdEvent.ShouldNotBeNull();
    }
}
