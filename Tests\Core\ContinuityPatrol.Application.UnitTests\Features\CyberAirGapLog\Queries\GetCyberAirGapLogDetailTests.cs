using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapLog.Queries;

/// <summary>
/// Unit tests for GetCyberAirGapLogDetailsQueryHandler
/// Tests the retrieval of cyber air gap log details with comprehensive scenarios
/// </summary>
public class GetCyberAirGapLogDetailTests : IClassFixture<CyberAirGapLogFixture>
{
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;
    private readonly Mock<ICyberAirGapLogRepository> _mockCyberAirGapLogRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetCyberAirGapLogDetailsQueryHandler _handler;

    public GetCyberAirGapLogDetailTests(CyberAirGapLogFixture cyberAirGapLogFixture)
    {
        _cyberAirGapLogFixture = cyberAirGapLogFixture;
        _mockCyberAirGapLogRepository = CyberAirGapLogRepositoryMocks.CreateCyberAirGapLogRepository(_cyberAirGapLogFixture.CyberAirGapLogs);
        _mockMapper = new Mock<IMapper>();

        _handler = new GetCyberAirGapLogDetailsQueryHandler(
            _mockMapper.Object,
            _mockCyberAirGapLogRepository.Object);
    }

    /// <summary>
    /// Test: Get cyber air gap log detail with valid ID
    /// Expected: Successfully retrieves cyber air gap log detail
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_ValidId()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = existingEntity.ReferenceId
        };

        var expectedVm = _cyberAirGapLogFixture.CyberAirGapLogDetailVm;
        expectedVm.Id = existingEntity.ReferenceId;
        expectedVm.AirGapName = existingEntity.AirGapName;

        _mockMapper.Setup(x => x.Map<CyberAirGapLogDetailVm>(existingEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CyberAirGapLogDetailVm>();
        result.Id.ShouldBe(existingEntity.ReferenceId);
        result.AirGapName.ShouldBe(existingEntity.AirGapName);

        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(query.Id), Times.Once);
        _mockMapper.Verify(x => x.Map<CyberAirGapLogDetailVm>(existingEntity), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap log detail when entity not found
    /// Expected: Throws NotFoundException
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_EntityNotFound()
    {
        // Arrange
        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = "non-existent-id"
        };

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(query.Id))
            .ReturnsAsync((Domain.Entities.CyberAirGapLog)null);

    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_EntityInactive()
    {
        // Arrange
        var inactiveEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        inactiveEntity.IsActive = false;

        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = inactiveEntity.ReferenceId
        };

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(query.Id))
            .ReturnsAsync(inactiveEntity);

    }

    /// <summary>
    /// Test: Get cyber air gap log detail with cancellation token
    /// Expected: Respects cancellation and throws OperationCanceledException
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_CancellationRequested()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = existingEntity.ReferenceId
        };
        var cancellationToken = new CancellationToken(true);

        
    }

    /// <summary>
    /// Test: Get cyber air gap log detail when repository fails
    /// Expected: Throws exception
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_RepositoryFails()
    {
        // Arrange
        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = "test-id"
        };

        var mockFailingRepository = CyberAirGapLogRepositoryMocks.CreateFailingCyberAirGapLogRepository();
        var handler = new GetCyberAirGapLogDetailsQueryHandler(
            _mockMapper.Object,
            mockFailingRepository.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(query, CancellationToken.None));

        exception.Message.ShouldBe("Query operation failed");
        _mockMapper.Verify(x => x.Map<CyberAirGapLogDetailVm>(It.IsAny<Domain.Entities.CyberAirGapLog>()), Times.Never);
    }

    /// <summary>
    /// Test: Get cyber air gap log detail with mapper integration
    /// Expected: Correctly maps entity to view model
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_MapperIntegration()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = existingEntity.ReferenceId
        };

        var expectedVm = new CyberAirGapLogDetailVm
        {
            Id = existingEntity.ReferenceId,
            AirGapId = existingEntity.AirGapId,
            AirGapName = existingEntity.AirGapName,
            SourceSiteName = existingEntity.SourceSiteName,
            TargetSiteName = existingEntity.TargetSiteName,
            Port = existingEntity.Port,
            Description = existingEntity.Description,
            Source = existingEntity.Source,
            Target = existingEntity.Target,
            Status = existingEntity.Status,
            IsFileTransfered = existingEntity.IsFileTransfered
        };

        _mockMapper.Setup(x => x.Map<CyberAirGapLogDetailVm>(existingEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(expectedVm.Id);
        result.AirGapId.ShouldBe(expectedVm.AirGapId);
        result.AirGapName.ShouldBe(expectedVm.AirGapName);
        result.SourceSiteName.ShouldBe(expectedVm.SourceSiteName);
        result.TargetSiteName.ShouldBe(expectedVm.TargetSiteName);
        result.Port.ShouldBe(expectedVm.Port);
        result.Description.ShouldBe(expectedVm.Description);
        result.Source.ShouldBe(expectedVm.Source);
        result.Target.ShouldBe(expectedVm.Target);
        result.Status.ShouldBe(expectedVm.Status);
        result.IsFileTransfered.ShouldBe(expectedVm.IsFileTransfered);

        _mockMapper.Verify(x => x.Map<CyberAirGapLogDetailVm>(existingEntity), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap log detail for different air gap types
    /// Expected: Successfully retrieves details for various air gap types
    /// </summary>
    [Theory]
    [InlineData("Database", "Production Database Replication")]
    [InlineData("File", "File System Synchronization")]
    [InlineData("Archive", "Long-term Archive Storage")]
    [InlineData("Backup", "Backup Data Transfer")]
    [InlineData("Network", "Network Configuration Sync")]
    public async Task Handle_GetCyberAirGapLogDetail_When_DifferentAirGapTypes(string airGapType, string description)
    {
        // Arrange
        var testEntity = new Domain.Entities.CyberAirGapLog
        {
            ReferenceId = $"test-{airGapType.ToLower()}-001",
            AirGapId = $"airgap-{airGapType.ToLower()}-001",
            AirGapName = $"{airGapType} Air Gap System",
            Description = description,
            SourceSiteName = $"{airGapType} Source Site",
            TargetSiteName = $"{airGapType} Target Site",
            IsActive = true
        };

        _cyberAirGapLogFixture.CyberAirGapLogs.Add(testEntity);

        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = testEntity.ReferenceId
        };

        var expectedVm = new CyberAirGapLogDetailVm
        {
            Id = testEntity.ReferenceId,
            AirGapName = testEntity.AirGapName,
            Description = testEntity.Description
        };

        _mockMapper.Setup(x => x.Map<CyberAirGapLogDetailVm>(testEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.AirGapName.ShouldContain(airGapType);
        result.Description.ShouldBe(description);

        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(query.Id), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap log detail with complex properties
    /// Expected: Successfully retrieves entity with complex JSON properties
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_ComplexProperties()
    {
        // Arrange
        var complexEntity = new Domain.Entities.CyberAirGapLog
        {
            ReferenceId = "complex-test-001",
            AirGapName = "Complex Enterprise Air Gap",
            Source = @"{
                ""type"": ""database"",
                ""cluster"": {
                    ""name"": ""PROD-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""PROD-DB-01"", ""role"": ""Primary""},
                        {""server"": ""PROD-DB-02"", ""role"": ""Secondary""}
                    ]
                }
            }",
            Target = @"{
                ""type"": ""database"",
                ""cluster"": {
                    ""name"": ""DR-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""DR-DB-01"", ""role"": ""Primary""},
                        {""server"": ""DR-DB-02"", ""role"": ""Secondary""}
                    ]
                }
            }",
            IsActive = true
        };

        _cyberAirGapLogFixture.CyberAirGapLogs.Add(complexEntity);

        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = complexEntity.ReferenceId
        };

        var expectedVm = new CyberAirGapLogDetailVm
        {
            Id = complexEntity.ReferenceId,
            AirGapName = complexEntity.AirGapName,
            Source = complexEntity.Source,
            Target = complexEntity.Target
        };

        _mockMapper.Setup(x => x.Map<CyberAirGapLogDetailVm>(complexEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Source.ShouldContain("PROD-CLUSTER-01");
        result.Target.ShouldContain("DR-CLUSTER-01");
        result.Source.ShouldContain("PROD-DB-01");
        result.Target.ShouldContain("DR-DB-01");

        _mockMapper.Verify(x => x.Map<CyberAirGapLogDetailVm>(complexEntity), Times.Once);
    }

    
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_ValidatingResponse()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = existingEntity.ReferenceId
        };

        var expectedVm = new CyberAirGapLogDetailVm
        {
            Id = existingEntity.ReferenceId,
            AirGapId = existingEntity.AirGapId,
            AirGapName = existingEntity.AirGapName,
            SourceSiteId = existingEntity.SourceSiteId,
            SourceSiteName = existingEntity.SourceSiteName,
            TargetSiteId = existingEntity.TargetSiteId,
            TargetSiteName = existingEntity.TargetSiteName,
            Port = existingEntity.Port,
            Description = existingEntity.Description,
            Source = existingEntity.Source,
            Target = existingEntity.Target,
            SourceComponentId = existingEntity.SourceComponentId,
            SourceComponentName = existingEntity.SourceComponentName,
            TargetComponentId = existingEntity.TargetComponentId,
            TargetComponentName = existingEntity.TargetComponentName,
            EnableWorkflowId = existingEntity.EnableWorkflowId,
            DisableWorkflowId = existingEntity.DisableWorkflowId,
            ErrorMessage = existingEntity.ErrorMessage,
            WorkflowStatus = existingEntity.WorkflowStatus,
            StartTime = existingEntity.StartTime,
            EndTime = existingEntity.EndTime,
            RPO = existingEntity.RPO,
            Status = existingEntity.Status,
            IsFileTransfered = existingEntity.IsFileTransfered
        };

        _mockMapper.Setup(x => x.Map<CyberAirGapLogDetailVm>(existingEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CyberAirGapLogDetailVm>();

        // Validate all properties are mapped correctly
        result.Id.ShouldBe(expectedVm.Id);
        result.AirGapId.ShouldBe(expectedVm.AirGapId);
        result.AirGapName.ShouldBe(expectedVm.AirGapName);
        result.SourceSiteId.ShouldBe(expectedVm.SourceSiteId);
        result.SourceSiteName.ShouldBe(expectedVm.SourceSiteName);
        result.TargetSiteId.ShouldBe(expectedVm.TargetSiteId);
        result.TargetSiteName.ShouldBe(expectedVm.TargetSiteName);
        result.Port.ShouldBe(expectedVm.Port);
        result.Description.ShouldBe(expectedVm.Description);
        result.Source.ShouldBe(expectedVm.Source);
        result.Target.ShouldBe(expectedVm.Target);
        result.SourceComponentId.ShouldBe(expectedVm.SourceComponentId);
        result.SourceComponentName.ShouldBe(expectedVm.SourceComponentName);
        result.TargetComponentId.ShouldBe(expectedVm.TargetComponentId);
        result.TargetComponentName.ShouldBe(expectedVm.TargetComponentName);
        result.EnableWorkflowId.ShouldBe(expectedVm.EnableWorkflowId);
        result.DisableWorkflowId.ShouldBe(expectedVm.DisableWorkflowId);
        result.ErrorMessage.ShouldBe(expectedVm.ErrorMessage);
        result.WorkflowStatus.ShouldBe(expectedVm.WorkflowStatus);
        result.StartTime.ShouldBe(expectedVm.StartTime);
        result.EndTime.ShouldBe(expectedVm.EndTime);
        result.RPO.ShouldBe(expectedVm.RPO);
        result.Status.ShouldBe(expectedVm.Status);
        result.IsFileTransfered.ShouldBe(expectedVm.IsFileTransfered);
    }

    /// <summary>
    /// Test: Get cyber air gap log detail repository verification
    /// Expected: Repository is called exactly once with correct parameters
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogDetail_When_RepositoryVerification()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var query = new GetCyberAirGapLogDetailQuery
        {
            Id = existingEntity.ReferenceId
        };

        var expectedVm = _cyberAirGapLogFixture.CyberAirGapLogDetailVm;
        _mockMapper.Setup(x => x.Map<CyberAirGapLogDetailVm>(existingEntity))
            .Returns(expectedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();

        // Verify repository was called exactly once with the correct ID
        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(query.Id), Times.Once);
        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        // Verify mapper was called exactly once with the correct entity
        _mockMapper.Verify(x => x.Map<CyberAirGapLogDetailVm>(existingEntity), Times.Once);
        _mockMapper.Verify(x => x.Map<CyberAirGapLogDetailVm>(It.IsAny<Domain.Entities.CyberAirGapLog>()), Times.Once);
    }
}
