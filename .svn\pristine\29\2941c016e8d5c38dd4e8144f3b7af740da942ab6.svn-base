using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberAirGapRepository : BaseRepository<CyberAirGap>, ICyberAirGapRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberAirGapRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<CyberAirGap>> ListAllAsync()
    {
        return await MapCyberAirGap(base.QueryAll(x => x.IsActive)).ToListAsync();
    }

    public override async Task<CyberAirGap> GetByReferenceIdAsync(string id)
    {
        var query = base.GetByReferenceId(id, x => x.ReferenceId == id);

        return await MapCyberAirGap(query).FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<CyberAirGap>>PaginatedListAllAsync(int pageNumber,int pageSize,Specification<CyberAirGap> specification, string sortColumn, string sortOrder)
    { 
       return await MapCyberAirGap(Entities.Specify(specification)).DescOrderById().ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);
    }
    public override IQueryable<CyberAirGap> GetPaginatedQuery()
    {
        return MapCyberAirGap(base.QueryAll(x => x.IsActive))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }
    public async Task<bool> IsNameExist(string name, string id)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            throw new ArgumentException("Name must be provided", nameof(name));
        }
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.Name == name);
        }
        var matchingItems = await Entities
            .Where(e => e.Name == name)
            .ToListAsync();

        return matchingItems.Unique(id);
    }

    public async Task<List<CyberAirGap>> GetAirGapBySiteId(string id)
    {
        return await MapCyberAirGap(
                base.FilterBy(x => x.SourceSiteId == id || x.TargetSiteId == id))
            .ToListAsync();
    }
    public async Task<List<CyberAirGap>> GetAirGapByServerId(string id)
    {
        return await MapCyberAirGap(
                base.FilterBy(x => x.Source == id || x.Target == id))
            .ToListAsync();
    }
    public async Task<List<CyberAirGap>> GetAirGapByComponentId(string id)
    {
        return await MapCyberAirGap(base.FilterBy(x => x.SourceComponentId == id || x.TargetComponentId == id))
            .ToListAsync();
    }
    private IQueryable<CyberAirGap> MapCyberAirGap(IQueryable<CyberAirGap> cyberAirGaps)
    {
        return cyberAirGaps.Select(x => new
        {
            SourceSite = _dbContext.Sites.FirstOrDefault(s => s.ReferenceId == x.SourceSiteId),
            TargetSite = _dbContext.Sites.FirstOrDefault(s => s.ReferenceId == x.TargetSiteId),
            SourceComponent = _dbContext.CyberComponents.FirstOrDefault(c => c.ReferenceId == x.SourceComponentId),
            TargetComponent = _dbContext.CyberComponents.FirstOrDefault(c => c.ReferenceId == x.TargetComponentId),
            AirGap = x
        })
        .Select(res => new CyberAirGap
        {
            Id= res.AirGap.Id,
            ReferenceId=res.AirGap.ReferenceId,
            Name = res.AirGap.Name,
            SourceSiteId=res.SourceSite.ReferenceId ?? res.AirGap.SourceSiteId,
            SourceSiteName=res.SourceSite.Name ?? res.AirGap.SourceSiteName,
            TargetSiteId=res.TargetSite.ReferenceId ?? res.AirGap.TargetSiteId,
            TargetSiteName = res.TargetSite.Name?? res.AirGap.TargetSiteName,
            Port=res.AirGap.Port,
            Description=res.AirGap.Description,
            Source = res.AirGap.Source,
            SourceComponentId=res.SourceComponent.ReferenceId ?? res.AirGap.SourceComponentId,
            SourceComponentName=res.SourceComponent.Name ?? res.AirGap.SourceComponentName,
            Target=res.AirGap.Target,
            TargetComponentId=res.TargetComponent.ReferenceId ?? res.AirGap.TargetComponentId,
            TargetComponentName=res.TargetComponent.Name ?? res.AirGap.TargetComponentName,
            EnableWorkflowId=res.AirGap.EnableWorkflowId,
            DisableWorkflowId=res.AirGap.DisableWorkflowId,
            WorkflowStatus=res.AirGap.WorkflowStatus,
            StartTime=res.AirGap.StartTime,
            EndTime=res.AirGap.EndTime,
            RPO=res.AirGap.RPO,
            IsAttached=res.AirGap.IsAttached,
            Status= res.AirGap.Status,
            ErrorMessage=res.AirGap.ErrorMessage,
            IsActive=res.AirGap.IsActive,
            CreatedBy=res.AirGap.CreatedBy,
            CreatedDate=res.AirGap.CreatedDate,
            LastModifiedBy=res.AirGap.LastModifiedBy,
            LastModifiedDate=res.AirGap.LastModifiedDate

        });
    }
}
