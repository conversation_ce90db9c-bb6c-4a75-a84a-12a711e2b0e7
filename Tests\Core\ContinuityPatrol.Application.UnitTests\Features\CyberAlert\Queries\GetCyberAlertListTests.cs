using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Queries;

public class GetCyberAlertListTests : IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
    private readonly Mock<ILogger<GetCyberAlertListQueryHandler>> _mockLogger;
    private readonly GetCyberAlertListQueryHandler _handler;
    private readonly Mock<IMapper> _mapper;
    public GetCyberAlertListTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);
        _mockLogger = new Mock<ILogger<GetCyberAlertListQueryHandler>>();

        _mapper = new Mock<IMapper>();
        _handler = new GetCyberAlertListQueryHandler(_mapper.Object ,
            _mockCyberAlertRepository.Object);
    }

    [Fact]
    public async Task Handle_GetCyberAlertList_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnOnlyActiveAlerts_When_ValidQuery()
    {
        var inactiveAlert = new Domain.Entities.CyberAlert
        {
            ReferenceId = "inactive-alert-test",
            IsActive = false
        };
        _cyberAlertFixture.CyberAlerts.Add(inactiveAlert);

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

    }

    /// <summary>
    /// Test: Query handler returns empty list when no alerts exist
    /// Expected: Empty list is returned successfully
    /// </summary>
    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoAlertsExist()
    {
        // Arrange
        _cyberAlertFixture.CyberAlerts.Clear();
        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockCyberAlertRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var query = new GetCyberAlertListQuery();

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
       
    }

    [Fact]
    public async Task Handle_ReturnAlertsWithEssentialProperties_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        //// Assert
        //result.Success.ShouldBeTrue();
        //result.Data.ShouldNotBeNull();
        //result.Data.ShouldNotBeEmpty();

        //foreach (var alert in result.Data)
        //{
        //    alert.Id.ShouldNotBeNullOrEmpty();
        //    alert.AlertId.ShouldNotBeNullOrEmpty();
        //    alert.Title.ShouldNotBeNullOrEmpty();
        //    alert.Severity.ShouldNotBeNullOrEmpty();
        //    alert.Priority.ShouldNotBeNullOrEmpty();
        //    alert.Status.ShouldNotBeNullOrEmpty();
        //    alert.DetectedAt.ShouldNotBeNull();
        //    alert.IsActive.ShouldBeTrue();
        //}
    }

    /// <summary>
    /// Test: Query handler returns alerts with different severity levels
    /// Expected: Alerts of all severity levels are included in the list
    /// </summary>
    [Fact]
    public async Task Handle_ReturnAlertsWithDifferentSeverities_When_ValidQuery()
    {
        // Arrange
        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(severityLevels.Length).ToList();

        for (int i = 0; i < alerts.Count && i < severityLevels.Length; i++)
        {
            alerts[i].Severity = severityLevels[i];
        }

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        //result.Success.ShouldBeTrue();
        //result.Data.ShouldNotBeNull();
        
        //foreach (var severity in severityLevels)
        //{
        //    result.Data.ShouldContain(alert => alert.Severity == severity);
        //}
    }

    /// <summary>
    /// Test: Query handler returns alerts with different statuses
    /// Expected: Alerts of all statuses are included in the list
    /// </summary>
    [Fact]
    public async Task Handle_ReturnAlertsWithDifferentStatuses_When_ValidQuery()
    {
        // Arrange
        var statuses = new[] { "Open", "Acknowledged", "In Progress", "Resolved", "Closed" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(statuses.Length).ToList();

        for (int i = 0; i < alerts.Count && i < statuses.Length; i++)
        {
           // alerts[i].Status = statuses[i];
        }

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        //result.Success.ShouldBeTrue();
        //result.Data.ShouldNotBeNull();
        
        //foreach (var status in statuses)
        //{
        //    result.Data.ShouldContain(alert => alert.Status == status);
        //}
    }

    [Fact]
    public async Task Handle_ReturnAlertsWithDifferentTypes_When_ValidQuery()
    {
        // Arrange
        var alertTypes = new[] { "Intrusion", "Malware", "DDoS", "Phishing", "Vulnerability" };
        var alerts = _cyberAlertFixture.CyberAlerts.Take(alertTypes.Length).ToList();

        for (int i = 0; i < alerts.Count && i < alertTypes.Length; i++)
        {
            alerts[i].Type = alertTypes[i];
        }

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        //result.Success.ShouldBeTrue();
        //result.Data.ShouldNotBeNull();
        
        //foreach (var alertType in alertTypes)
        //{
        //    result.Data.ShouldContain(alert => alert.Type == alertType);
        //}
    }

    [Fact]
    public async Task Handle_ReturnAlertsOrderedByDetectionDate_When_ValidQuery()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlerts.Take(5).ToList();
        for (int i = 0; i < alerts.Count; i++)
        {
           // alerts[i].DetectedAt = DateTime.UtcNow.AddHours(-i);
        }

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        //result.Success.ShouldBeTrue();
        //result.Data.ShouldNotBeNull();
        //result.Data.Count.ShouldBeGreaterThanOrEqualTo(5);

        //// Check if alerts are ordered by DetectedAt descending
        //for (int i = 0; i < result.Data.Count - 1; i++)
        //{
        //    result.Data[i].DetectedAt.ShouldBeGreaterThanOrEqualTo(result.Data[i + 1].DetectedAt);
        //}
    }

    [Fact]
    public async Task Handle_HandleLargeDataset_When_ManyAlertsExist()
    {
        // Arrange
        var additionalAlerts = _cyberAlertFixture.AutoCyberAlertFixture.CreateMany<Domain.Entities.CyberAlert>(100).ToList();
        _cyberAlertFixture.CyberAlerts.AddRange(additionalAlerts);

        var query = new GetCyberAlertListQuery();

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await _handler.Handle(query, CancellationToken.None);
        stopwatch.Stop();

        // Assert
       
        stopwatch.ElapsedMilliseconds.ShouldBeLessThan(1000); // Should complete within 1 second
    }

    [Fact]
    public async Task Handle_ReturnConsistentResults_When_MultipleCalls()
    {
        // Arrange
        var query = new GetCyberAlertListQuery();

        // Act
        var result1 = await _handler.Handle(query, CancellationToken.None);
        var result2 = await _handler.Handle(query, CancellationToken.None);

    }
    [Fact]
    public async Task Handle_IncludeAcknowledgmentInformation_When_ValidQuery()
    {
        // Arrange
        var acknowledgedAlert = _cyberAlertFixture.CyberAlerts.First();

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert

        //var acknowledgedAlertInList = result.Data.FirstOrDefault(a => a.Id == acknowledgedAlert.ReferenceId);
        //acknowledgedAlertInList.ShouldNotBeNull();
        //acknowledgedAlertInList.Status.ShouldBe("Acknowledged");
        //acknowledgedAlertInList.AcknowledgedBy.ShouldBe("SecurityAnalyst123");
        //acknowledgedAlertInList.AcknowledgedAt.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_IncludeResolutionInformation_When_ValidQuery()
    {
        // Arrange
        var resolvedAlert = _cyberAlertFixture.CyberAlerts.First();
       

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
      

        
    }

    [Fact]
    public async Task Handle_ProcessRapidListQueries_When_MultipleQueries()
    {
        // Arrange
        var queries = Enumerable.Range(1, 10).Select(_ => new GetCyberAlertListQuery()).ToList();

        // Act
        var tasks = queries.Select(query => _handler.Handle(query, CancellationToken.None));
        var results = await Task.WhenAll(tasks);

        // Assert
        
        results.Length.ShouldBe(10);
        _mockCyberAlertRepository.Verify(x => x.ListAllAsync(), Times.Exactly(10));
    }

    [Fact]
    public async Task Handle_IncludeSourceAndTargetInformation_When_ValidQuery()
    {
       
        // Arrange
        if (!_cyberAlertFixture.CyberAlerts.Any())
        {
            _cyberAlertFixture.CyberAlerts.Add(new Domain.Entities.CyberAlert
            {
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                // Set other required properties for your test
            });
        }

        var alertWithSourceTarget = _cyberAlertFixture.CyberAlerts.First();

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);



    }

    /// <summary>
    /// Test: Query handler returns alerts with rule information
    /// Expected: Detection rule details are included
    /// </summary>
    [Fact]
    public async Task Handle_IncludeRuleInformation_When_ValidQuery()
    {
        // Arrange
        var alertWithRule = _cyberAlertFixture.CyberAlerts.First();
        

        var query = new GetCyberAlertListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
     
    }
}
