﻿using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Validators;

public class UpdateHacmpClusterValidatorTests : IClassFixture<HacmpClusterFixture>
{
    private readonly Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;

    private readonly HacmpClusterFixture _hacmpClusterFixture;

    public UpdateHacmpClusterValidatorTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        var hacmpClusters = new Fixture().Create<List<Domain.Entities.HacmpCluster>>();

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.UpdateHacmpClusterRepository(hacmpClusters);
    }

    [Theory]
    [AutoHacmpClusterData]
    public async Task Verify_Create_ValidCommand_ShouldPass(UpdateHacmpClusterCommand updateHacmpClusterCommand)
    {
        var validator = new UpdateHacmpClusterCommandValidator(_mockHacmpClusterRepository.Object);

        updateHacmpClusterCommand.Name = "TestCluster";
        updateHacmpClusterCommand.ServerId = "TestServerId";
        updateHacmpClusterCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateHacmpClusterCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeTrue();
    }
}
