using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Database.Queries.GetNames;
using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Create;
using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Delete;
using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Update;
using ContinuityPatrol.Application.Features.EscalationMatrix.Queries.GetDetail;
using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Queries.GetNameUnique;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrix;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class EscalationMatrixControllerTests : IDisposable
{
    private readonly EscalationMatrixController _controller;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly EscalationMatrixFixture _escalationMatrixFixture;

    public EscalationMatrixControllerTests()
    {
        _escalationMatrixFixture = new EscalationMatrixFixture();
        var testBuilder = new ControllerTestBuilder<EscalationMatrixController>();
        _controller = testBuilder.CreateController(
            _ => new EscalationMatrixController(),
            out _mediatorMock);
    }

    public void Dispose()
    {
        _escalationMatrixFixture?.Dispose();
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateEscalationmatrix_WithValidCommand_ReturnsBaseResponse()
    {
        // Arrange
        var command = _escalationMatrixFixture.CreateEscalationMatrixCommand;
        var expectedResponse = _escalationMatrixFixture.CreateEscalationMatrixResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateEscalationmatrix(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<BaseResponse>>(result);
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<CreateEscalationMatrixResponse>(createdAtActionResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.Equal("Enterprise Escalation Matrix", returnedResponse.EscMatName);
    }

    [Fact]
    public async Task Update_WithValidCommand_ReturnsUpdateResponse()
    {
        // Arrange
        var command = _escalationMatrixFixture.UpdateEscalationMatrixCommand;
        var expectedResponse = _escalationMatrixFixture.UpdateEscalationMatrixResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.Update(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<UpdateEscalationMatrixResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<UpdateEscalationMatrixResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task Delete_WithValidId_ReturnsDeleteResponse()
    {
        // Arrange
        var escalationMatrixId = Guid.NewGuid().ToString();
        var expectedResponse = _escalationMatrixFixture.DeleteEscalationMatrixResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteEscalationMatrixCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.Delete(escalationMatrixId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<DeleteEscalationMatrixResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<DeleteEscalationMatrixResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task GetById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var escalationMatrixId = Guid.NewGuid().ToString();
        var expectedDetail = _escalationMatrixFixture.EscalationMatrixDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetEscalationMatrixDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetById(escalationMatrixId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<EscalationMatrixListVm>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedDetail = Assert.IsType<EscalationMatrixListVm>(okResult.Value);
        Assert.NotNull(returnedDetail.Id);
        Assert.Equal("ESC_MATRIX_001", returnedDetail.EscMatCode);
        Assert.Equal("Enterprise Critical System Escalation Matrix", returnedDetail.EscMatDesc);
    }

    [Fact]
    public async Task GetNames_ReturnsEscalationMatrixNamesList()
    {
        // Arrange
        var expectedNames = new List<DatabaseNameVm>
        {
            new() { Id = Guid.NewGuid().ToString(), Name = "Enterprise Escalation Matrix 1" },
            new() { Id = Guid.NewGuid().ToString(), Name = "Enterprise Escalation Matrix 2" },
            new() { Id = Guid.NewGuid().ToString(), Name = "Enterprise Escalation Matrix 3" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDatabaseNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedNames);

        // Act
        var result = await _controller.GetNames();

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<EscalationMatrixNameVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedNames = Assert.IsType<List<DatabaseNameVm>>(okResult.Value);
        Assert.Equal(3, returnedNames.Count);
        Assert.All(returnedNames, name => Assert.Contains("Enterprise Escalation Matrix", name.Name));
    }

    [Fact]
    public async Task GetPaginatedList_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _escalationMatrixFixture.GetEscalationMatrixPaginatedListQuery;
        var expectedResult = PaginatedResult<EscalationMatrixListVm>.Success(
            _escalationMatrixFixture.EscalationMatrixListVm,
            _escalationMatrixFixture.EscalationMatrixListVm.Count,
            1,
            10);

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedList(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<EscalationMatrixListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResult = Assert.IsType<PaginatedResult<EscalationMatrixListVm>>(okResult.Value);
        Assert.Equal(5, returnedResult.Data.Count);
        Assert.All(returnedResult.Data, matrix => Assert.Contains("Enterprise Escalation Matrix", matrix.EscMatDesc));
    }

    [Fact]
    public async Task IsEscalationNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var escalationMatrixName = "Enterprise Escalation Matrix";
        var escalationMatrixId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetEscalationMatrixLevelNameUniqueQuery>(q => 
                q.Name == escalationMatrixName && q.Id == escalationMatrixId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsEscalationNameExist(escalationMatrixName, escalationMatrixId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Arrange & Act & Assert
        Assert.NotNull(_controller);
        
        var method = typeof(EscalationMatrixController).GetMethod("ClearDataCache");
        Assert.NotNull(method);
        Assert.True(method.IsPublic);
        
        var nonActionAttribute = method.GetCustomAttributes(typeof(Microsoft.AspNetCore.Mvc.NonActionAttribute), false);
        Assert.NotEmpty(nonActionAttribute);
    }

    [Fact]
    public async Task CreateEscalationmatrix_CallsClearDataCache()
    {
        // Arrange
        var command = _escalationMatrixFixture.CreateEscalationMatrixCommand;
        var expectedResponse = _escalationMatrixFixture.CreateEscalationMatrixResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateEscalationmatrix(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task Update_CallsClearDataCache()
    {
        // Arrange
        var command = _escalationMatrixFixture.UpdateEscalationMatrixCommand;
        var expectedResponse = _escalationMatrixFixture.UpdateEscalationMatrixResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.Update(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task Delete_CallsClearDataCache()
    {
        // Arrange
        var escalationMatrixId = Guid.NewGuid().ToString();
        var expectedResponse = _escalationMatrixFixture.DeleteEscalationMatrixResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteEscalationMatrixCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.Delete(escalationMatrixId);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.IsAny<DeleteEscalationMatrixCommand>(), default), Times.Once);
    }

    #endregion

    #region Additional Test Cases

    [Fact]
    public async Task CreateEscalationmatrix_WithInvalidCommand_ReturnsErrorResponse()
    {
        // Arrange
        var command = _escalationMatrixFixture.CreateEscalationMatrixCommand;
        command.EscMatCode = null; // Invalid data
        var errorResponse = new CreateEscalationMatrixResponse
        {
            Success = false,
            Message = "Escalation Matrix Code is required"
        };

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(errorResponse);

        // Act
        var result = await _controller.CreateEscalationmatrix(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<BaseResponse>>(result);
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<CreateEscalationMatrixResponse>(createdAtActionResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("required", returnedResponse.Message);
    }

    [Fact]
    public async Task Update_WithNonExistentId_ReturnsErrorResponse()
    {
        // Arrange
        var command = _escalationMatrixFixture.UpdateEscalationMatrixCommand;
        command.Id = Guid.NewGuid().ToString(); // Non-existent ID
        var errorResponse = new UpdateEscalationMatrixResponse
        {
            Success = false,
            Message = "Escalation Matrix not found"
        };

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(errorResponse);

        // Act
        var result = await _controller.Update(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<UpdateEscalationMatrixResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<UpdateEscalationMatrixResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("not found", returnedResponse.Message);
    }

    [Fact]
    public async Task Delete_WithNonExistentId_ReturnsErrorResponse()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var errorResponse = new DeleteEscalationMatrixResponse
        {
            Success = false,
            Message = "Escalation Matrix not found"
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteEscalationMatrixCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(errorResponse);

        // Act
        var result = await _controller.Delete(nonExistentId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<DeleteEscalationMatrixResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<DeleteEscalationMatrixResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("not found", returnedResponse.Message);
    }

    [Fact]
    public async Task GetById_WithNullId_ReturnsEmptyResult()
    {
        // Arrange
        string? nullId = null;
        var emptyDetail = new EscalationMatrixListVm();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetEscalationMatrixDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyDetail);

        // Act
        var result = await _controller.GetById(nullId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<EscalationMatrixListVm>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedDetail = Assert.IsType<EscalationMatrixListVm>(okResult.Value);
        Assert.NotNull(returnedDetail);
    }

    [Fact]
    public async Task GetNames_WithNoData_ReturnsEmptyList()
    {
        // Arrange
        var emptyList = new List<DatabaseNameVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDatabaseNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetNames();

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<EscalationMatrixNameVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedNames = Assert.IsType<List<DatabaseNameVm>>(okResult.Value);
        Assert.Empty(returnedNames);
    }

    [Fact]
    public async Task GetPaginatedList_WithSearchFilter_ReturnsFilteredResults()
    {
        // Arrange
        var query = _escalationMatrixFixture.GetEscalationMatrixPaginatedListQuery;
        query.SearchString = "Critical";
        var filteredData = _escalationMatrixFixture.EscalationMatrixListVm
            .Where(x => x.EscMatDesc.Contains("Critical")).ToList();
        var filteredResult = PaginatedResult<EscalationMatrixListVm>.Success(
            filteredData,
            filteredData.Count,
            1,
            10);

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedList(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<EscalationMatrixListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResult = Assert.IsType<PaginatedResult<EscalationMatrixListVm>>(okResult.Value);
        Assert.All(returnedResult.Data, matrix => Assert.Contains("Critical", matrix.EscMatDesc));
    }

    [Fact]
    public async Task IsEscalationNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var nonExistingName = "Non-Existing Escalation Matrix";
        var escalationMatrixId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetEscalationMatrixLevelNameUniqueQuery>(q =>
                q.Name == nonExistingName && q.Id == escalationMatrixId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsEscalationNameExist(nonExistingName, escalationMatrixId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsEscalationNameExist_WithNullId_ReturnsCorrectResult()
    {
        // Arrange
        var escalationMatrixName = "New Escalation Matrix";
        string? nullId = null;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetEscalationMatrixLevelNameUniqueQuery>(q =>
                q.Name == escalationMatrixName && q.Id == nullId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsEscalationNameExist(escalationMatrixName, nullId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    #endregion
}
