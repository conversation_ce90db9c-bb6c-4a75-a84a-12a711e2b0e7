using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapLog.Commands;

public class DeleteCyberAirGapLogTests : IClassFixture<CyberAirGapLogFixture>
{
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;
    private readonly Mock<ICyberAirGapLogRepository> _mockCyberAirGapLogRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteCyberAirGapLogCommandHandler _handler;

    public DeleteCyberAirGapLogTests(CyberAirGapLogFixture cyberAirGapLogFixture)
    {
        _cyberAirGapLogFixture = cyberAirGapLogFixture;
        _mockCyberAirGapLogRepository = CyberAirGapLogRepositoryMocks.CreateCyberAirGapLogRepository(_cyberAirGapLogFixture.CyberAirGapLogs);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new DeleteCyberAirGapLogCommandHandler(
            _mockCyberAirGapLogRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_ValidCommand()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };

        CyberAirGapLogDeletedEvent publishedEvent = null;

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapLogDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
       .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteCyberAirGapLogResponse>();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();

        existingEntity.IsActive.ShouldBeFalse();

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(existingEntity.AirGapName);

        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_EntityNotFound()
    {
        // Arrange
        var command = new DeleteCyberAirGapLogCommand
        {
            Id = "non-existent-id"
        };

        //_mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
        //    .ReturnsAsync((CyberAirGapLog)null);

        //// Act & Assert
        //var exception = await Should.ThrowAsync<NotFoundException>(
        //    async () => await _handler.Handle(command, CancellationToken.None));

        //exception.Message.ShouldContain("CyberAirGapLog");
        //exception.Message.ShouldContain(command.Id);

        //_mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.Once);
        //_mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAirGapLog>()), Times.Never);
        //_mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_CancellationRequested()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };
        var cancellationToken = new CancellationToken(true);

    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_RepositoryFails()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };

        var mockFailingRepository = CyberAirGapLogRepositoryMocks.CreateFailingCyberAirGapLogRepository();
        mockFailingRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(existingEntity);

        var handler = new DeleteCyberAirGapLogCommandHandler(
            mockFailingRepository.Object,
            _mockPublisher.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe("Update operation failed");
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_MultipleDeletes()
    {
        // Arrange
        var entities = _cyberAirGapLogFixture.CyberAirGapLogs.Take(2).ToList();
        var commands = entities.Select(e => new DeleteCyberAirGapLogCommand { Id = e.ReferenceId }).ToList();

        // Act
        var results = new List<DeleteCyberAirGapLogResponse>();
        foreach (var command in commands)
        {
            var result = await _handler.Handle(command, CancellationToken.None);
            results.Add(result);
        }

        // Assert
        results.ShouldAllBe(r => r.Success);
        results.ShouldAllBe(r => !r.IsActive);
        entities.ShouldAllBe(e => !e.IsActive);

        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Exactly(2));
     //   _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAirGapLog>()), Times.Exactly(2));
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_AlreadyInactive()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        existingEntity.IsActive = false; // Set as already inactive

        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
       .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();

        existingEntity.IsActive.ShouldBeFalse();

        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_SpecialCharactersInName()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        existingEntity.AirGapName = "Special Characters & <script>alert('xss')</script> 🚀💻📊 测试数据";

        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };

        CyberAirGapLogDeletedEvent publishedEvent = null;

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapLogDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
       .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        publishedEvent.ShouldNotBeNull();
    }

    
    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_NullAirGapName()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        existingEntity.AirGapName = null;

        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };

        CyberAirGapLogDeletedEvent publishedEvent = null;

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapLogDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
       .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBeNull();

        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_EmptyAirGapName()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        existingEntity.AirGapName = string.Empty;

        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };

        CyberAirGapLogDeletedEvent publishedEvent = null;

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapLogDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapLogDeletedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
       .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(string.Empty);

        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_ValidatingResponse()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
       .ReturnsAsync(existingEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteCyberAirGapLogResponse>();
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("CyberAirGapLog");
        result.Message.ShouldContain("deleted successfully");
        result.Message.ShouldContain(existingEntity.AirGapName);
    }

   
    [Fact]
    public async Task Handle_DeleteCyberAirGapLog_When_ConcurrentOperations()
    {
        // Arrange
        var existingEntity = _cyberAirGapLogFixture.CyberAirGapLogs.First();
        var command = new DeleteCyberAirGapLogCommand
        {
            Id = existingEntity.ReferenceId
        };

        _mockCyberAirGapLogRepository.Setup(x => x.GetByReferenceIdAsync(existingEntity.ReferenceId))
        .ReturnsAsync(existingEntity);
        var tasks = Enumerable.Range(1, 3).Select(async i =>
        {
            try
            {
                return await _handler.Handle(command, CancellationToken.None);
            }
            catch (Exception)
            {
                return null; // Some operations might fail due to concurrency
            }
        });

        var results = await Task.WhenAll(tasks);

        // Assert
        var successfulResults = results.Where(r => r != null).ToList();
        successfulResults.ShouldNotBeEmpty();


        // Verify that repository operations were called
        _mockCyberAirGapLogRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.AtLeastOnce);
        _mockCyberAirGapLogRepository.Verify(x => x.UpdateAsync(existingEntity), Times.AtLeastOnce);
    }
}
