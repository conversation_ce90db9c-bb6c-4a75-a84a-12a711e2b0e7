﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Paginated;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Events;

public class PaginatedWorkflowProfileInfoEventTests : IClassFixture<WorkflowProfileInfoFixture>
{
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;
    private readonly Mock<ILogger<WorkflowProfileInfoPaginatedEventHandler>> _mockLogger;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly WorkflowProfileInfoPaginatedEventHandler _handler;

    public PaginatedWorkflowProfileInfoEventTests(WorkflowProfileInfoFixture workflowProfileInfoFixture)
    {
        _workflowProfileInfoFixture = workflowProfileInfoFixture;
        _mockLogger = new Mock<ILogger<WorkflowProfileInfoPaginatedEventHandler>>();
        _mockUserActivityRepository = new Mock<IUserActivityRepository>();
        _mockUserService = new Mock<ILoggedInUserService>();

        _mockUserService.SetupGet(us => us.UserId).Returns("testUserId");
        _mockUserService.SetupGet(us => us.LoginName).Returns("testLoginName");
        _mockUserService.SetupGet(us => us.RequestedUrl).Returns("/test/url");
        _mockUserService.SetupGet(us => us.CompanyId).Returns("testCompanyId");
        _mockUserService.SetupGet(us => us.IpAddress).Returns("127.0.0.1");

        _handler = new WorkflowProfileInfoPaginatedEventHandler(
            _mockLogger.Object,
            _mockUserActivityRepository.Object,
            _mockUserService.Object
        );
    }

    [Fact]
    public async Task Handle_ShouldAddUserActivityToRepository()
    {
        var notification = new WorkflowProfileInfoPaginatedEvent {ProfileName = "PTS"};

        var cancellationToken = CancellationToken.None;

        await _handler.Handle(notification, cancellationToken);

        _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == "testUserId" &&
            ua.LoginName == "testLoginName" &&
            ua.RequestUrl == "/test/url" &&
            ua.CompanyId == "testCompanyId" &&
            ua.HostAddress == "127.0.0.1" &&
            ua.Entity == "WorkflowProfileInfo" &&
            ua.Action == "View WorkflowProfileInfo" &&
            ua.ActivityType == "View" &&
            ua.ActivityDetails == "Workflow Profile Management viewed"
        )), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldLogInformation()
    {
        var notification = new WorkflowProfileInfoPaginatedEvent { ProfileName = "PTS" };
        var cancellationToken = CancellationToken.None;

        await _handler.Handle(notification, cancellationToken);

        _mockUserActivityRepository.Verify(
            repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(
                activity => activity.UserId == "testUserId" &&
                            activity.LoginName == "testLoginName" &&
                            activity.RequestUrl == "/test/url" &&
                            activity.CompanyId == "testCompanyId" &&
                            activity.HostAddress == "127.0.0.1" &&
                            activity.Entity == Modules.WorkflowProfileInfo.ToString() &&
                            activity.Action == $"{ActivityType.View} {Modules.WorkflowProfileInfo}" &&
                            activity.ActivityType == ActivityType.View.ToString() &&
                            activity.ActivityDetails == "Workflow Profile Management viewed")),
            Times.Once);

        _mockLogger.Verify(
            logger => logger.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<object>(v => v.ToString()!.Contains("Workflow Profile Management viewed")),
                It.IsAny<Exception>(),
                ((Func<object, Exception, string>)It.IsAny<object>())!),
            Times.Once);
    }
    [Fact]
    public async Task Handle_Return_UserActivityCount_When_PaginatedWorkflowProfileInfoEventCreated()
    {
        _workflowProfileInfoFixture.UserActivities[0].LoginName = "Test";

        var notification = new WorkflowProfileInfoPaginatedEvent { ProfileName = _workflowProfileInfoFixture.WorkflowProfileInfoPaginatedEvent.ProfileName };

        var result = _handler.Handle(notification, CancellationToken.None);

        result.Equals(_workflowProfileInfoFixture.UserActivities[0].Id);

        result.Equals(_workflowProfileInfoFixture.WorkflowProfileInfoPaginatedEvent.ProfileName);

        await Task.CompletedTask;
    }
}