using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using Moq;
using Newtonsoft.Json;
using Xunit;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowInfraObjectRepositoryTests : IClassFixture<WorkflowInfraObjectFixture>,IClassFixture<InfraObjectFixture>,IClassFixture<WorkflowFixture>
    {
        private readonly WorkflowInfraObjectFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowInfraObjectRepository _repositoryParent;
        private readonly WorkflowInfraObjectRepository _repositoryIsNotParent;
        private readonly WorkflowInfraObjectRepository _repositoryAllInfra;
        private readonly InfraObjectFixture _infraObjectFixture;
        private readonly WorkflowFixture _workflowFixture;

        public WorkflowInfraObjectRepositoryTests(WorkflowInfraObjectFixture fixture, InfraObjectFixture infraObjectFixture, WorkflowFixture workflowFixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repositoryParent = new WorkflowInfraObjectRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repositoryIsNotParent = new WorkflowInfraObjectRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
            _repositoryAllInfra = new WorkflowInfraObjectRepository(_dbContext, DbContextFactory.GetMockUserService());
            _infraObjectFixture = infraObjectFixture;
            _workflowFixture = workflowFixture;
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAll_WhenIsAllInfra()
        {
            await _dbContext.WorkflowInfraObjects.AddRangeAsync(_fixture.WorkflowInfraObjectList);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.ListAllAsync();

            Assert.Equal(_fixture.WorkflowInfraObjectList.Count, result.Count);
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAssigned_WhenNotAllInfra()
        {
            await _dbContext.WorkflowInfraObjects.AddRangeAsync(_fixture.WorkflowInfraObjectList);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.ListAllAsync();

            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenIsParent()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenNotParent()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "ChHILD_COMPANY_123";
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetInfraObjectIdAttachByWorkflowId_ReturnsEntity_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);  
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.IsAttach = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetInfraObjectIdAttachByWorkflowId(entity.WorkflowId, entity.InfraObjectId, entity.ActionType);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
            Assert.Equal(entity.InfraObjectId, result.InfraObjectId);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectFromInfraObjectId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;

            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectFromInfraObjectId(entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetResilienceWorkflowByInfraObjectId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.ActionType = "Resiliency Ready";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetResilienceWorkflowByInfraObjectId(entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectDetailByInfraObjectId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectDetailByInfraObjectId(entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetInfraObjectFromWorkflowId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetInfraObjectFromWorkflowId(entity.WorkflowId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowId, x.WorkflowId));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectFromWorkflowId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();
            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectFromWorkflowId(entity.WorkflowId, entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowId, x.WorkflowId));
            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByWorkflowIdAsync_ReturnsEntity_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.IsAttach = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectByWorkflowIdAsync(entity.WorkflowId);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
        }

        [Fact]
        public async Task GetWorkflowIdAttachByActionType_ReturnsEntity_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.IsAttach = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowIdAttachByActionType(entity.WorkflowId, entity.ActionType);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
            Assert.Equal(entity.ActionType, result.ActionType);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByInfraObjectIdAndActionType_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.ActionType = "TestType";
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectByInfraObjectIdAndActionType(entity.InfraObjectId, "TestType");

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
            Assert.All(result, x => Assert.Equal("TestType", x.ActionType));
        }

        [Fact]
        public async Task WorkflowIdAndInfraObjectIdUnique_ReturnsTrue_WhenExists()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.IsAttach = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.WorkflowIdAndInfraObjectIdUnique(entity.WorkflowId, entity.InfraObjectId, entity.ActionType);

            Assert.True(result);
        }

        [Fact]
        public async Task WorkflowIdAndInfraObjectIdUnique_ReturnsFalse_WhenNotExists()
        {
            var result = await _repositoryAllInfra.WorkflowIdAndInfraObjectIdUnique("non-existent", "non-existent", "non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowIdUnique_ReturnsTrue_WhenExists()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.IsWorkflowIdUnique(entity.WorkflowId);

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowIdUnique_ReturnsFalse_WhenNotExists()
        {
            var result = await _repositoryAllInfra.IsWorkflowIdUnique("non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task IsInfraObjectIdUnique_ReturnsTrue_WhenExists()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.IsInfraObjectIdUnique(entity.InfraObjectId);

            Assert.True(result);
        }

        [Fact]
        public async Task IsInfraObjectIdUnique_ReturnsFalse_WhenNotExists()
        {
            var result = await _repositoryAllInfra.IsInfraObjectIdUnique("non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByWorkflowIdForWorkflowList_ReturnsEntity_WhenExists()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            entity.IsActive = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectByWorkflowIdForWorkflowList(entity.WorkflowId);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByWorkflowIdForWorkflowList_ReturnsNull_WhenNotExists()
        {
            var result = await _repositoryAllInfra.GetWorkflowInfraObjectByWorkflowIdForWorkflowList("non-existent");

            Assert.Null(result);
        }

        #region AssignedInfraObjects Method Tests

        [Fact]
        public async Task ListAllAsync_ReturnsAssignedInfraObjects_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Setup AssignedEntity with business services and infra objects
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" },
                                    new AssignedInfraObjects { Id = "INFRA_002", Name = "Infra Object 2" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity1 = _fixture.WorkflowInfraObjectDto;
            entity1.CompanyId = "COMPANY_123";
            entity1.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity1.InfraObjectId = "INFRA_001"; // This matches assigned infra object

            var entity2 = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = "INFRA_003", // This doesn't match assigned infra object
                ActionType = "Test Action",
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            await _dbContext.WorkflowInfraObjects.AddRangeAsync(entity1, entity2);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.ListAllAsync();

            // Assert
            Assert.NotNull(result);
            // Should only return entities with assigned infra objects
            Assert.All(result, x => Assert.Contains(x.InfraObjectId, new[] { "INFRA_001", "INFRA_002" }));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectFromInfraObjectId_ReturnsAssignedInfraObjects_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetWorkflowInfraObjectFromInfraObjectId("INFRA_001");

            // Assert
            Assert.NotNull(result);
            Assert.All(result, x => Assert.Equal("INFRA_001", x.InfraObjectId));
        }

        [Fact]
        public async Task GetResilienceWorkflowByInfraObjectId_ReturnsAssignedInfraObjects_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";
            entity.ActionType = "Resiliency Ready";

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetResilienceWorkflowByInfraObjectId("INFRA_001");

            // Assert
            Assert.NotNull(result);
            Assert.All(result, x => Assert.Equal("INFRA_001", x.InfraObjectId));
        }

        #endregion

        #region GetInfraObjectById Method Tests

        [Fact]
        public async Task GetInfraObjectIdAttachByWorkflowId_ReturnsFilteredInfraObject_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";
            entity.IsAttach = true;

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetInfraObjectIdAttachByWorkflowId(entity.WorkflowId, "INFRA_001", entity.ActionType);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("INFRA_001", result.InfraObjectId);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByWorkflowIdAsync_ReturnsFilteredInfraObject_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";
            entity.IsAttach = true;

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetWorkflowInfraObjectByWorkflowIdAsync(entity.WorkflowId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
        }

        [Fact]
        public async Task GetWorkflowIdAttachByActionType_ReturnsFilteredInfraObject_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";
            entity.IsAttach = true;

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetWorkflowIdAttachByActionType(entity.WorkflowId, entity.ActionType);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
            Assert.Equal(entity.ActionType, result.ActionType);
        }

        #endregion

        #region Additional Coverage Tests for Non-Parent Scenarios

        [Fact]
        public async Task GetWorkflowInfraObjectDetailByInfraObjectId_ReturnsAssignedInfraObjects_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetWorkflowInfraObjectDetailByInfraObjectId("INFRA_001");

            // Assert
            Assert.NotNull(result);
            Assert.All(result, x => Assert.Equal("INFRA_001", x.InfraObjectId));
        }

        [Fact]
        public async Task GetInfraObjectFromWorkflowId_ReturnsAssignedInfraObjects_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetInfraObjectFromWorkflowId(entity.WorkflowId);

            // Assert
            Assert.NotNull(result);
            Assert.All(result, x => Assert.Equal(entity.WorkflowId, x.WorkflowId));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectFromWorkflowId_ReturnsAssignedInfraObjects_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetWorkflowInfraObjectFromWorkflowId(entity.WorkflowId, "INFRA_001");

            // Assert
            Assert.NotNull(result);
            Assert.All(result, x => Assert.Equal(entity.WorkflowId, x.WorkflowId));
            Assert.All(result, x => Assert.Equal("INFRA_001", x.InfraObjectId));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByInfraObjectIdAndActionType_ReturnsAssignedInfraObjects_WhenNotAllInfra()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001";
            entity.ActionType = "TestType";

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetWorkflowInfraObjectByInfraObjectIdAndActionType("INFRA_001", "TestType");

            // Assert
            Assert.NotNull(result);
            Assert.All(result, x => Assert.Equal("INFRA_001", x.InfraObjectId));
            Assert.All(result, x => Assert.Equal("TestType", x.ActionType));
        }

        #endregion

        #region Edge Cases and Empty Assigned Business Services Tests

        [Fact]
        public async Task AssignedInfraObjects_ReturnsEmpty_WhenNoAssignedBusinessServices()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>() // Empty list
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.ListAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result); // Should be empty when no assigned business services
        }

        [Fact]
        public async Task AssignedInfraObjects_ReturnsEmpty_WhenNoAssignedBusinessFunctions()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>() // Empty list
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.ListAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result); // Should be empty when no assigned business functions
        }

        [Fact]
        public async Task AssignedInfraObjects_ReturnsEmpty_WhenNoAssignedInfraObjects()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>() // Empty list
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.ListAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result); // Should be empty when no assigned infra objects
        }

        [Fact]
        public async Task GetInfraObjectById_ReturnsNull_WhenInfraObjectNotAssigned()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_002", Name = "Infra Object 2" } // Different ID
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "COMPANY_123";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = "INFRA_001"; // Not in assigned list
            entity.IsAttach = true;

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetInfraObjectIdAttachByWorkflowId(entity.WorkflowId, "INFRA_001", entity.ActionType);

            // Assert
            Assert.Null(result); // Should be null when infra object is not assigned
        }

        #endregion

        #region Complete AssignedInfraObjects Coverage Tests

        [Fact]
        public async Task AssignedInfraObjects_CoversAllBranches_WhenAssignedBusinessServicesCountGreaterThanZero()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Test the specific branch: if (AssignedEntity.AssignedBusinessServices.Count > 0)
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" },
                                    new AssignedInfraObjects { Id = "INFRA_002", Name = "Infra Object 2" }
                                }
                            },
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_002",
                                Name = "Business Function 2",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_003", Name = "Infra Object 3" }
                                }
                            }
                        }
                    },
                    new AssignedBusinessServices
                    {
                        Id = "BS_002",
                        Name = "Business Service 2",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_003",
                                Name = "Business Function 3",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_004", Name = "Infra Object 4" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            // Create multiple entities to test the foreach loops
            var entity1 = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = "INFRA_001", // Matches assigned
                ActionType = "Test Action 1",
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            var entity2 = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = "INFRA_003", // Matches assigned
                ActionType = "Test Action 2",
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            var entity3 = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = "INFRA_999", // Does NOT match assigned
                ActionType = "Test Action 3",
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            await _dbContext.WorkflowInfraObjects.AddRangeAsync(entity1, entity2, entity3);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.ListAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Only entity1 and entity2 should be returned
            Assert.Contains(result, x => x.InfraObjectId == "INFRA_001");
            Assert.Contains(result, x => x.InfraObjectId == "INFRA_003");
            Assert.DoesNotContain(result, x => x.InfraObjectId == "INFRA_999");
        }

        [Fact]
        public async Task AssignedInfraObjects_CoversAssignedBusinessFunctionsCountGreaterThanZero()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Test the specific branch: if (assignedBusinessFunctions.Count > 0)
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = "INFRA_001",
                ActionType = "Test Action",
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.ListAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("INFRA_001", result.First().InfraObjectId);
        }

        [Fact]
        public async Task AssignedInfraObjects_CoversAssignedBusinessInfraObjectsCountGreaterThanZero()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Test the specific branch: if (assignedBusinessInfraObjects.Count > 0)
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" },
                                    new AssignedInfraObjects { Id = "INFRA_002", Name = "Infra Object 2" },
                                    new AssignedInfraObjects { Id = "INFRA_003", Name = "Infra Object 3" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            // Create multiple entities to test the foreach infraObject loop and the LINQ query
            var entities = new List<WorkflowInfraObject>();
            for (int i = 1; i <= 5; i++)
            {
                entities.Add(new WorkflowInfraObject
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    CompanyId = "COMPANY_123",
                    WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                    InfraObjectId = $"INFRA_{i:D3}",
                    ActionType = $"Test Action {i}",
                    IsActive = true,
                    CreatedBy = "Test User",
                    CreatedDate = DateTime.UtcNow
                });
            }

            await _dbContext.WorkflowInfraObjects.AddRangeAsync(entities);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.ListAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count); // Only INFRA_001, INFRA_002, INFRA_003 should be returned
            Assert.Contains(result, x => x.InfraObjectId == "INFRA_001");
            Assert.Contains(result, x => x.InfraObjectId == "INFRA_002");
            Assert.Contains(result, x => x.InfraObjectId == "INFRA_003");
            Assert.DoesNotContain(result, x => x.InfraObjectId == "INFRA_004");
            Assert.DoesNotContain(result, x => x.InfraObjectId == "INFRA_005");
        }

        #endregion

        #region Complete GetInfraObjectById Coverage Tests

        [Fact]
        public async Task GetInfraObjectById_CoversSelectManyChain_WhenInfraObjectMatches()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Test the SelectMany chain: AssignedBusinessServices -> AssignedBusinessFunctions -> AssignedInfraObjects
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" },
                                    new AssignedInfraObjects { Id = "INFRA_002", Name = "Infra Object 2" }
                                }
                            },
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_002",
                                Name = "Business Function 2",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_003", Name = "Infra Object 3" }
                                }
                            }
                        }
                    },
                    new AssignedBusinessServices
                    {
                        Id = "BS_002",
                        Name = "Business Service 2",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_003",
                                Name = "Business Function 3",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_004", Name = "Infra Object 4" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = "INFRA_003", // This should match in the SelectMany chain
                ActionType = "Test Action",
                IsAttach = true,
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act - This will call GetInfraObjectById internally
            var result = await repository.GetInfraObjectIdAttachByWorkflowId(entity.WorkflowId, "INFRA_003", entity.ActionType);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("INFRA_003", result.InfraObjectId);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
        }

        [Fact]
        public async Task GetInfraObjectById_ReturnsNull_WhenInfraObjectIdDoesNotMatch()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" },
                                    new AssignedInfraObjects { Id = "INFRA_002", Name = "Infra Object 2" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = "INFRA_999", // This does NOT match any assigned infra object
                ActionType = "Test Action",
                IsAttach = true,
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act - This will call GetInfraObjectById internally
            var result = await repository.GetInfraObjectIdAttachByWorkflowId(entity.WorkflowId, "INFRA_999", entity.ActionType);

            // Assert
            Assert.Null(result); // Should be null because INFRA_999 is not in assigned list
        }

        [Fact]
        public async Task GetInfraObjectById_HandlesSingleOrDefault_WhenMultipleMatches()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Create scenario where the same infra object appears in multiple business functions
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            },
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_002",
                                Name = "Business Function 2",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" } // Same infra object
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = "INFRA_001",
                ActionType = "Test Action",
                IsAttach = true,
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act - This will call GetInfraObjectById internally
            var result = await repository.GetInfraObjectIdAttachByWorkflowId(entity.WorkflowId, "INFRA_001", entity.ActionType);

            // Assert
            Assert.NotNull(result); // Should return the first match due to SingleOrDefault
            Assert.Equal("INFRA_001", result.InfraObjectId);
        }

        [Fact]
        public async Task GetInfraObjectById_HandlesNullInfraObject_Parameter()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Act - Call method that will result in null being passed to GetInfraObjectById
            var result = await repository.GetInfraObjectIdAttachByWorkflowId("NON_EXISTENT_WORKFLOW", "INFRA_001", "TestAction");

            // Assert
            Assert.Null(result); // Should be null when no workflow infra object is found
        }

        #endregion

        #region Additional Coverage for All Method Combinations

        [Fact]
        public async Task GetWorkflowInfraObjectFromInfraObjectId_CoversAssignedInfraObjectsWithMultipleBusinessServices()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Test multiple business services with multiple business functions each
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    },
                    new AssignedBusinessServices
                    {
                        Id = "BS_002",
                        Name = "Business Service 2",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_002",
                                Name = "Business Function 2",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_002", Name = "Infra Object 2" }
                                }
                            }
                        }
                    },
                    new AssignedBusinessServices
                    {
                        Id = "BS_003",
                        Name = "Business Service 3",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_003",
                                Name = "Business Function 3",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_003", Name = "Infra Object 3" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            // Create entities for each assigned infra object
            var entities = new List<WorkflowInfraObject>();
            for (int i = 1; i <= 5; i++)
            {
                entities.Add(new WorkflowInfraObject
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    CompanyId = "COMPANY_123",
                    WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                    InfraObjectId = $"INFRA_{i:D3}",
                    ActionType = $"Test Action {i}",
                    IsActive = true,
                    CreatedBy = "Test User",
                    CreatedDate = DateTime.UtcNow
                });
            }

            await _dbContext.WorkflowInfraObjects.AddRangeAsync(entities);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetWorkflowInfraObjectFromInfraObjectId("INFRA_002");

            // Assert
            Assert.NotNull(result);
            Assert.Single(result); // Only INFRA_002 should be returned
            Assert.Equal("INFRA_002", result.First().InfraObjectId);
        }

        [Fact]
        public async Task GetResilienceWorkflowByInfraObjectId_CoversComplexAssignedInfraObjectsScenario()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Test complex nested structure
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" },
                                    new AssignedInfraObjects { Id = "INFRA_002", Name = "Infra Object 2" },
                                    new AssignedInfraObjects { Id = "INFRA_003", Name = "Infra Object 3" }
                                }
                            },
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_002",
                                Name = "Business Function 2",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_004", Name = "Infra Object 4" },
                                    new AssignedInfraObjects { Id = "INFRA_005", Name = "Infra Object 5" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            // Create multiple entities with "resiliency ready" action type
            var entities = new List<WorkflowInfraObject>();
            for (int i = 1; i <= 7; i++)
            {
                entities.Add(new WorkflowInfraObject
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    CompanyId = "COMPANY_123",
                    WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                    InfraObjectId = $"INFRA_{i:D3}",
                    ActionType = "Resiliency Ready",
                    IsActive = true,
                    CreatedBy = "Test User",
                    CreatedDate = DateTime.UtcNow
                });
            }

            await _dbContext.WorkflowInfraObjects.AddRangeAsync(entities);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await repository.GetResilienceWorkflowByInfraObjectId("INFRA_003");

            // Assert
            Assert.NotNull(result);
            Assert.Single(result); // Only INFRA_003 should be returned
            Assert.Equal("INFRA_003", result.First().InfraObjectId);
            Assert.Equal("Resiliency Ready", result.First().ActionType);
        }

        [Fact]
        public async Task AssignedInfraObjects_CoversEmptyInfraObjectsListScenario()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            // Test scenario where infraObjects parameter is empty
            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data but no WorkflowInfraObjects (empty infraObjects parameter to AssignedInfraObjects)
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            // Act - No WorkflowInfraObjects added, so infraObjects will be empty
            var result = await repository.ListAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result); // Should be empty when no infraObjects exist
        }

        [Fact]
        public async Task GetInfraObjectById_CoversWhereClauseWithNullInfraObjectId()
        {
            // Arrange
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();
            mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

            var assignedEntity = new AssignedEntity
            {
                IsAll = false,
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_001",
                        Name = "Business Service 1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_001",
                                Name = "Business Function 1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>
                                {
                                    new AssignedInfraObjects { Id = "INFRA_001", Name = "Infra Object 1" }
                                }
                            }
                        }
                    }
                }
            };

            mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

            var repository = new WorkflowInfraObjectRepository(_dbContext, mockLoggedInUserService.Object);

            // Add test data
            await _dbContext.Workflows.AddAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            // Create entity with null InfraObjectId to test the Where clause: infraObject?.InfraObjectId == assignedInfraObjects.Id
            var entity = new WorkflowInfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                WorkflowId = _workflowFixture.WorkflowDto.ReferenceId,
                InfraObjectId = null, // This will test the null check in Where clause
                ActionType = "Test Action",
                IsAttach = true,
                IsActive = true,
                CreatedBy = "Test User",
                CreatedDate = DateTime.UtcNow
            };

            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            // Act - This will call GetInfraObjectById internally with null InfraObjectId
            var result = await repository.GetInfraObjectIdAttachByWorkflowId(entity.WorkflowId, null, entity.ActionType);

            // Assert
            Assert.Null(result); // Should be null when InfraObjectId is null
        }

        #endregion
    }
}