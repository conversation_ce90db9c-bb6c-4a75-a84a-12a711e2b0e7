using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.CyberAirGap.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Events;

public class DeleteCyberAirGapEventTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<CyberAirGapDeletedEventHandler>> _mockLogger;
    private readonly CyberAirGapDeletedEventHandler _handler;

    public DeleteCyberAirGapEventTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockUserActivityRepository = CyberAirGapRepositoryMocks.CreateUserActivityRepository(_cyberAirGapFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<CyberAirGapDeletedEventHandler>>();

        // Setup default user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");

        _handler = new CyberAirGapDeletedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_ProcessCyberAirGapDeletedEvent_When_ValidEvent()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "TestAirGap_Deleted"
        };

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivityWithCorrectDeleteProperties_When_ValidEvent()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "TestAirGap_DeleteUserActivity"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "TestAirGap_DeleteCancellation"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

       
    }

    [Fact]
    public async Task Handle_ProcessMultipleDeleteEvents_When_ValidEvents()
    {
        // Arrange
        var events = new[]
        {
            new CyberAirGapDeletedEvent
            {
                Name = "TestAirGap_DeleteMultiple_1",
            },
            new CyberAirGapDeletedEvent
            {
                Name = "TestAirGap_DeleteMultiple_2"
            },
            new CyberAirGapDeletedEvent
            {
                Name = "TestAirGap_DeleteMultiple_3"
            }
        };

        // Act
        foreach (var eventItem in events)
        {
            await _handler.Handle(eventItem, CancellationToken.None);
        }

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(3));
    }

    [Fact]
    public async Task Handle_CreateCompleteDeleteUserActivity_When_EventWithAllProperties()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "CompleteDeleteTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    /// <summary>
    /// Test: Event with soft delete indication
    /// Expected: Soft delete is reflected in user activity details
    /// </summary>
    [Fact]
    public async Task Handle_HandleSoftDelete_When_EventIndicatesSoftDelete()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "TestAirGap_SoftDelete"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain("TestAirGap_SoftDelete");
        createdActivity.ActivityDetails.ShouldContain("deleted");
        createdActivity.UserId.ShouldBe("TestUser123");
    }

    [Fact]
    public async Task Handle_HandleWorkflowStatusAtDeletion_When_EventWithFinalStatus()
    {
        // Arrange
        var finalStatuses = new[] { "Active", "Inactive", "Error", "Completed" };

        foreach (var status in finalStatuses)
        {
            var cyberAirGapEvent = new CyberAirGapDeletedEvent
            {
                Name = $"TestAirGap_FinalStatus_{status}"
            };

            Domain.Entities.UserActivity createdActivity = null;
            _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
                .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

            // Act
            await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

            // Assert
            createdActivity.ShouldNotBeNull();
            createdActivity.ActivityDetails.ShouldContain($"TestAirGap_FinalStatus_{status}");
            createdActivity.UserId.ShouldBe("TestUser123");
        }
    }

    [Fact]
    public async Task Handle_HandleComponentInfoAtDeletion_When_EventWithComponentDetails()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "TestAirGap_ComponentInfoDelete"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain("TestAirGap_ComponentInfoDelete");
        createdActivity.UserId.ShouldBe("TestUser123");
    }

    [Fact]
    public async Task Handle_HandleSiteInfoAtDeletion_When_EventWithSiteDetails()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "TestAirGap_SiteInfoDelete"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain("TestAirGap_SiteInfoDelete");
        createdActivity.UserId.ShouldBe("TestUser123");
    }

    [Fact]
    public async Task Handle_HandleFinalJsonConfig_When_EventWithFinalSourceTarget()
    {
        // Arrange
        var finalSource = "{\"serverId\":\"final-srv-001\",\"componentId\":\"final-comp-001\",\"status\":\"deleted\"}";
        var finalTarget = "{\"serverId\":\"final-srv-002\",\"componentId\":\"final-comp-002\",\"status\":\"deleted\"}";

        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "TestAirGap_FinalJsonDelete"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain("TestAirGap_FinalJsonDelete");
        createdActivity.UserId.ShouldBe("TestUser123");
    }

    [Fact]
    public async Task Handle_UseLoggedInUserService_When_ValidEvent()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "UserServiceDeleteTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();

        // Verify user service was called
        _mockUserService.Verify(x => x.UserId, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.LoginName, Times.AtLeastOnce);
    }

    /// <summary>
    /// Test: Event processing performance with rapid succession
    /// Expected: Multiple rapid delete events are processed correctly
    /// </summary>
    [Fact]
    public async Task Handle_ProcessRapidDeleteEvents_When_MultipleEventsInSuccession()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new CyberAirGapDeletedEvent
        {
            Name = $"RapidDeleteTestAirGap_{i:00}"
        }).ToList();

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(10));
    }

    /// <summary>
    /// Test: Event with entity reference logging
    /// Expected: Activity details contain air gap ID for reference
    /// </summary>
    [Fact]
    public async Task Handle_LogActivityWithEntityReference_When_ValidEvent()
    {
        // Arrange
        var airGapId = "entity-reference-delete-airgap-id";
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "EntityReferenceDeleteTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleSpecialCharsInDeletedName_When_EventWithSpecialChars()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapDeletedEvent
        {
            Name = "Special-DeletedAirGap_Test@123!"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }
}
