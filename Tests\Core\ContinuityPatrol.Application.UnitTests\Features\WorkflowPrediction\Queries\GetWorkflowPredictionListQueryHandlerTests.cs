﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetList;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Queries;

public class GetWorkflowPredictionListQueryHandlerTests
{
    private readonly Mock<IWorkflowPredictionRepository> _mockWorkflowPredictionRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetWorkflowPredictionListQueryHandler _handler;

    public GetWorkflowPredictionListQueryHandlerTests()
    {
        _mockWorkflowPredictionRepository = new Mock<IWorkflowPredictionRepository>();
        _mockMapper = new Mock<IMapper>();
        _handler = new GetWorkflowPredictionListQueryHandler(_mockMapper.Object, _mockWorkflowPredictionRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenNoPredictionsExist()
    {
        var workflowPredictions = new List<Domain.Entities.WorkflowPrediction>();

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(workflowPredictions);

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowPredictionListVm>>(workflowPredictions))
            .Returns(new List<WorkflowPredictionListVm>());

        var result = await _handler.Handle(new GetWorkflowPredictionListQuery(), CancellationToken.None);

        Assert.NotNull(result);
        Assert.Empty(result);
        _mockWorkflowPredictionRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map<List<WorkflowPredictionListVm>>(workflowPredictions), Times.Never);
    }

    [Fact]
    public async Task Handle_ReturnsMappedWorkflowPredictions_WhenPredictionsExist()
    {
        // Arrange
        var testDate = DateTime.UtcNow;
        var workflowPredictions = new List<Domain.Entities.WorkflowPrediction>
        {
            new()
            {
                ReferenceId = "prediction-1",
                ActionId = "action-1",
                ActionName = "Test Action 1",
                Count = 5,
                NextPossibleId = "next-1",
                NextPossibleActionName = "Next Action 1",
                NodeId = "node-1",
                LastModifiedDate = testDate
            },
            new()
            {
                ReferenceId = "prediction-2",
                ActionId = "action-2",
                ActionName = "Test Action 2",
                Count = 3,
                NextPossibleId = "next-2",
                NextPossibleActionName = "Next Action 2",
                NodeId = "node-2",
                LastModifiedDate = testDate.AddDays(-1)
            }
        };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(workflowPredictions);

        var workflowPredictionVms = new List<WorkflowPredictionListVm>
        {
            new()
            {
                Id = "prediction-1",
                ActionId = "action-1",
                ActionName = "Test Action 1",
                Count = 5,
                NextPossibleId = "next-1",
                NextPossibleActionName = "Next Action 1",
                NodeId = "node-1"
            },
            new()
            {
                Id = "prediction-2",
                ActionId = "action-2",
                ActionName = "Test Action 2",
                Count = 3,
                NextPossibleId = "next-2",
                NextPossibleActionName = "Next Action 2",
                NodeId = "node-2"
            }
        };

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowPredictionListVm>>(workflowPredictions))
            .Returns(workflowPredictionVms);

        // Act
        var result = await _handler.Handle(new GetWorkflowPredictionListQuery(), CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

        // Verify first item
        var firstResult = result[0];
        Assert.Equal("prediction-1", firstResult.Id);
        Assert.Equal("action-1", firstResult.ActionId);
        Assert.Equal("Test Action 1", firstResult.ActionName);
        Assert.Equal(5, firstResult.Count);
        Assert.Equal("next-1", firstResult.NextPossibleId);
        Assert.Equal("Next Action 1", firstResult.NextPossibleActionName);
        Assert.Equal("node-1", firstResult.NodeId);

        // Verify second item
        var secondResult = result[1];
        Assert.Equal("prediction-2", secondResult.Id);
        Assert.Equal("action-2", secondResult.ActionId);
        Assert.Equal("Test Action 2", secondResult.ActionName);
        Assert.Equal(3, secondResult.Count);
        Assert.Equal("next-2", secondResult.NextPossibleId);
        Assert.Equal("Next Action 2", secondResult.NextPossibleActionName);
        Assert.Equal("node-2", secondResult.NodeId);

        _mockWorkflowPredictionRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map<List<WorkflowPredictionListVm>>(workflowPredictions), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldHandleNullProperties_WhenMappingToViewModel()
    {
        // Arrange
        var workflowPredictions = new List<Domain.Entities.WorkflowPrediction>
        {
            new()
            {
                Id = 0,
                ActionId = null,
                ActionName = null,
                Count = 0,
                NextPossibleId = null,
                NextPossibleActionName = null,
                NodeId = null
            }
        };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(workflowPredictions);

        var workflowPredictionVms = new List<WorkflowPredictionListVm>
        {
            new WorkflowPredictionListVm
            {
                Id = null,
                ActionId = null,
                ActionName = null,
                Count = 0,
                NextPossibleId = null,
                NextPossibleActionName = null,
                NodeId = null
            }
        };

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowPredictionListVm>>(workflowPredictions))
            .Returns(workflowPredictionVms);

        // Act
        var result = await _handler.Handle(new GetWorkflowPredictionListQuery(), CancellationToken.None);

        // Assert
        var actualVm = result.First();
        Assert.Null(actualVm.Id);
        Assert.Null(actualVm.ActionId);
        Assert.Null(actualVm.ActionName);
        Assert.Equal(0, actualVm.Count);
        Assert.Null(actualVm.NextPossibleId);
        Assert.Null(actualVm.NextPossibleActionName);
        Assert.Null(actualVm.NodeId);
    }
}