using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Events;

public class CyberAirGapStatusUpdatedEventTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<CyberAirGapStatusUpdatedEventHandler>> _mockLogger;
    private readonly CyberAirGapStatusUpdatedEventHandler _handler;

    private readonly Mock<ILoggedInUserService> _userService;
    private readonly Mock<ILogger<CyberAirGapStatusUpdatedEventHandler>> _logger;
    private readonly Mock<IUserActivityRepository> _userActivityRepository;
    public CyberAirGapStatusUpdatedEventTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockUserActivityRepository = CyberAirGapStatusRepositoryMocks.CreateUserActivityRepository();
        _mockUserService = CyberAirGapStatusRepositoryMocks.CreateUserService();
        _mockLogger = new Mock<ILogger<CyberAirGapStatusUpdatedEventHandler>>();

        _logger = new Mock<ILogger<CyberAirGapStatusUpdatedEventHandler>>();
        _userService = new Mock<ILoggedInUserService>();
        _userActivityRepository = new Mock<IUserActivityRepository>();
        _handler = new CyberAirGapStatusUpdatedEventHandler(
            _userService.Object,
            _logger.Object,
            _userActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_ValidData()
    {
        // Arrange
        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = "Updated Enterprise Air Gap Status System"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_NullName()
    {
        // Arrange
        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = null
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_EmptyName()
    {
        // Arrange
        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = string.Empty
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
        
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_SpecialCharacters()
    {
        // Arrange
        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = "Updated Special Characters & <script>alert('xss')</script> 🔄💻📊 更新数据"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_CancellationRequested()
    {
        // Arrange
        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = "Cancelled Update Event Test"
        };
        var cancellationToken = new CancellationToken(true);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Never);
    }

    /// <summary>
    /// Test: Handle cyber air gap status updated event when repository fails
    /// Expected: Throws exception but still logs
    /// </summary>
    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_RepositoryFails()
    {
        // Arrange
        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = "Repository Failure Update Test"
        };

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act & Assert
       
    }

    /// <summary>
    /// Test: Handle cyber air gap status updated event with different user contexts
    /// Expected: Uses correct user information from service
    /// </summary>
    [Theory]
    [InlineData("UpdateUser001", "<EMAIL>", "*************", "/api/v1/cyberairgapstatus/update")]
    [InlineData("UpdateUser002", "<EMAIL>", "*********", "/api/v2/cyberairgapstatus/update")]
    [InlineData("UpdateAdmin", "<EMAIL>", "***********", "/admin/cyberairgapstatus/update")]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_DifferentUserContexts(
        string userId, string loginName, string ipAddress, string requestUrl)
    {
        // Arrange
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns(userId);
        mockUserService.Setup(x => x.LoginName).Returns(loginName);
        mockUserService.Setup(x => x.IpAddress).Returns(ipAddress);
        mockUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);

        var handler = new CyberAirGapStatusUpdatedEventHandler(
           _userService.Object,
            _logger.Object,
            _userActivityRepository.Object);

        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = "User Context Update Test"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
       
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_NullUserServiceValues()
    {
        // Arrange
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns((string)null);
        mockUserService.Setup(x => x.LoginName).Returns((string)null);
        mockUserService.Setup(x => x.IpAddress).Returns((string)null);
        mockUserService.Setup(x => x.RequestedUrl).Returns((string)null);

        var handler = new CyberAirGapStatusUpdatedEventHandler(
            mockUserService.Object,
            _logger.Object,
            _userActivityRepository.Object);

        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = "Null User Service Update Test"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await handler.Handle(updatedEvent, CancellationToken.None);

        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_MultipleEvents()
    {
        // Arrange
        var events = new[]
        {
            new CyberAirGapStatusUpdatedEvent { Name = "Updated Air Gap Status 1" },
            new CyberAirGapStatusUpdatedEvent { Name = "Updated Air Gap Status 2" },
            new CyberAirGapStatusUpdatedEvent { Name = "Updated Air Gap Status 3" }
        };

        var capturedActivities = new List<Domain.Entities.UserActivity>();

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivities.Add(activity))
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        foreach (var evt in events)
        {
            await _handler.Handle(evt, CancellationToken.None);
        }

    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_ValidatingActivityDetails()
    {
        // Arrange
        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = "Validation Update Test Air Gap Status"
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();

    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_LongName()
    {
        // Arrange
        var longName = new string('A', 500) + " Updated Air Gap Status System";
        var updatedEvent = new CyberAirGapStatusUpdatedEvent
        {
            Name = longName
        };

        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CyberAirGapStatusUpdatedEvent_When_ConcurrentOperations()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new CyberAirGapStatusUpdatedEvent
        {
            Name = $"Concurrent Update Test {i}"
        }).ToArray();

        var capturedActivities = new List<Domain.Entities.UserActivity>();
        var lockObject = new object();

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity =>
            {
                lock (lockObject)
                {
                    capturedActivities.Add(activity);
                }
            })
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

        // Assert
        tasks.ShouldNotBeNull();
    }
}
