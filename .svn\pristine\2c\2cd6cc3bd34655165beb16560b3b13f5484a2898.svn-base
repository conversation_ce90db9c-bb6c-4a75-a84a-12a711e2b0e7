﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormTypeCategory.Events;

public class CreateFormTypeCategoryEventTests : IClassFixture<FormTypeCategoryFixture>, IClassFixture<UserActivityFixture>
{
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly FormTypeCategoryCreatedEventHandler _handler;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    public CreateFormTypeCategoryEventTests(FormTypeCategoryFixture formTypeCategoryFixture, UserActivityFixture userActivityFixture)
    {
        _formTypeCategoryFixture = formTypeCategoryFixture;
        _userActivityFixture = userActivityFixture;
        var loggedInUserService=new Mock<ILoggedInUserService>();
        var logger = new Mock<ILogger<FormTypeCategoryCreatedEventHandler>>();
        _mockUserActivityRepository = UserActivityRepositoryMocks.CreateUserActivityRepository(_userActivityFixture.UserActivities);
        _handler = new FormTypeCategoryCreatedEventHandler(loggedInUserService.Object, logger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_ValidEvent_CompletesSuccessfully()
    {
        // Act & Assert
        await Should.NotThrowAsync(async () =>
            await _handler.Handle(_formTypeCategoryFixture.FormTypeCategoryCreatedEvent, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateFormTypeCategoryEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_formTypeCategoryFixture.FormTypeCategoryCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_formTypeCategoryFixture.FormTypeCategoryCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}