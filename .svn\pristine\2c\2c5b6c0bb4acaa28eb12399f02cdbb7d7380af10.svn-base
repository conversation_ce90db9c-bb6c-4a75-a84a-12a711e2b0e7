using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberComponentGroupRepository : BaseRepository<CyberComponentGroup>, ICyberComponentGroupRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberComponentGroupRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<CyberComponentGroup>> ListAllAsync()
    {
        return await MapCyberComponentGroup(base.QueryAll(x => x.IsActive)).ToListAsync();
    }
    public override async Task<CyberComponentGroup> GetByReferenceIdAsync(string id)
    {
        var query = base.GetByReferenceId(id, x => x.ReferenceId == id);
        return await MapCyberComponentGroup(query).FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<CyberComponentGroup>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<CyberComponentGroup> specification, string sortColumn, string sortOrder)
    {
        var query = MapCyberComponentGroup(Entities.Specify(specification).DescOrderById());
        return await query.ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<CyberComponentGroup> GetPaginatedQuery()
    {
        return MapCyberComponentGroup(base.QueryAll(x => x.IsActive))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }

    public async Task<List<CyberComponentGroup>> GetCyberComponentGroupsBySiteId(string id)
    {
        return await MapCyberComponentGroup(base.FilterBy(x => x.SiteId == id)).ToListAsync();
    }
    public async Task<List<CyberComponentGroup>> GetComponentGroupsByComponentId(string id)
    {
        return await MapCyberComponentGroup(base.FilterBy(x => x.ComponentProperties.Contains(id))).ToListAsync();
    }
    public async Task<bool> IsNameExist(string name, string id)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            throw new ArgumentException("Group name must be provided", nameof(name));
        }
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.GroupName == name);
        }

        var matchingItems = await Entities
            .Where(e => e.GroupName == name)
            .ToListAsync();

        return matchingItems.Unique(id);
    }

    private IQueryable<CyberComponentGroup> MapCyberComponentGroup(IQueryable<CyberComponentGroup> cyberComponentGroups)
    {
        return cyberComponentGroups.Select(x => new
        {
            Site = _dbContext.Sites.FirstOrDefault(s => s.ReferenceId == x.SiteId),
            CyberComponentGroup = x
        })
         .Select(res => new CyberComponentGroup
         {
             Id = res.CyberComponentGroup.Id,
             ReferenceId = res.CyberComponentGroup.ReferenceId,
             GroupName = res.CyberComponentGroup.GroupName,
             ComponentProperties = res.CyberComponentGroup.ComponentProperties,
             SiteId = res.Site.ReferenceId ?? res.CyberComponentGroup.SiteId,
             SiteName = res.Site.Name ?? res.CyberComponentGroup.SiteName,
             IsActive = res.CyberComponentGroup.IsActive,
             CreatedBy = res.CyberComponentGroup.CreatedBy,
             CreatedDate = res.CyberComponentGroup.CreatedDate,
             LastModifiedBy = res.CyberComponentGroup.LastModifiedBy,
             LastModifiedDate = res.CyberComponentGroup.LastModifiedDate
         });
    }
}
