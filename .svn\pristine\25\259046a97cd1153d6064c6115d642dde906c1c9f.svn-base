function layoutcarddraggable() {

    $(function () {
        $(".layoutcard").draggable({
            containment: ".PB_pageLayoutContent",
            drag: function (event, ui) {
                var current = $(this);
                var siblings = $(".layoutcard").not(current);
                var revert = false;
                if (siblings.length != 0) {
                    siblings.each(function () {
                        if (isColliding(current, $(this))) {
                            revert = true;
                        }
                    });
                }
                else {
                    revert = true;
                }

                if (revert) {
                    current.draggable('option', 'revert', true);
                } else {
                    current.draggable('option', 'revert', false);
                }
                
            }
        }).resizable()

        function isColliding($div1, $div2) {
            var d1Offset = $div1.offset();
            var d1Height = $div1.outerHeight(true);
            var d1Width = $div1.outerWidth(true);
            var d1Top = d1Offset.top;
            var d1Left = d1Offset.left;
            var d1Bottom = d1Top + d1Height;
            var d1Right = d1Left + d1Width;

            var d2Offset = $div2.offset();
            var d2Height = $div2.outerHeight(true);
            var d2Width = $div2.outerWidth(true);
            var d2Top = d2Offset.top;
            var d2Left = d2Offset.left;
            var d2Bottom = d2Top + d2Height;
            var d2Right = d2Left + d2Width;



            return !(d1Bottom < d2Top ||
                d1Top > d2Bottom ||
                d1Right < d2Left ||
                d1Left > d2Right);
        }

        LayoutHeightDiv()
    });


}

