using ContinuityPatrol.Application.Features.BackUp.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUp.Events;

public class BackUpDeletedEventTests : IClassFixture<BackUpFixture>
{
    private readonly BackUpFixture _backUpFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<BackUpDeletedEventHandler>> _mockLogger;
    private readonly BackUpDeletedEventHandler _handler;

    public BackUpDeletedEventTests(BackUpFixture backUpFixture)
    {
        _backUpFixture = backUpFixture;
        _mockUserActivityRepository = BackUpRepositoryMocks.CreateUserActivityRepository(_backUpFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BackUpDeletedEventHandler>>();

        // Setup default user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");
        _mockUserService.Setup(x => x.RequestedUrl).Returns("/api/backup/delete");
        _mockUserService.Setup(x => x.IpAddress).Returns("*************");

        _handler = new BackUpDeletedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_ProcessBackUpDeletedEvent_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = "TestDatabase_Deleted"
        };

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivityWithCorrectDeleteProperties_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = "ProductionDatabase_Deleted"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("Delete BackUp");
        createdActivity.ActivityType.ShouldBe("Delete");
        createdActivity.ActivityDetails.ShouldBe("BackUp 'ProductionDatabase_Deleted' deleted successfully.");
        createdActivity.UserId.ShouldBe("TestUser123");
        createdActivity.LoginName.ShouldBe("TestUser123");
        createdActivity.RequestUrl.ShouldBe("/api/backup/delete");
        createdActivity.HostAddress.ShouldBe("*************");
        createdActivity.IsActive.ShouldBeTrue();
    }
  

    [Fact]
    public async Task Handle_ProcessMultipleDeleteEvents_When_ValidEvents()
    {
        // Arrange
        var events = new[]
        {
            new BackUpDeletedEvent { Name = "Database1_Deleted" },
            new BackUpDeletedEvent { Name = "Database2_Deleted" },
            new BackUpDeletedEvent { Name = "Database3_Deleted" }
        };

        // Act
        foreach (var eventItem in events)
        {
            await _handler.Handle(eventItem, CancellationToken.None);
        }

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(3));
    }

    /// <summary>
    /// Test: Event with different database names
    /// Expected: All database names are handled correctly in events
    /// </summary>
    [Fact]
    public async Task Handle_HandleDifferentDatabaseNames_When_ValidEvents()
    {
        // Arrange
        var databaseNames = new[] { "ProductionDB", "StagingDB", "TestDB", "DevDB", "BackupDB" };

        foreach (var dbName in databaseNames)
        {
            var backUpEvent = new BackUpDeletedEvent
            {
                Name = $"{dbName}_Deleted"
            };

            Domain.Entities.UserActivity createdActivity = null;
            _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
                .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

            // Act
            await _handler.Handle(backUpEvent, CancellationToken.None);

            // Assert
            createdActivity.ShouldNotBeNull();
            createdActivity.ActivityDetails.ShouldContain($"{dbName}_Deleted");
            createdActivity.ActivityDetails.ShouldContain("deleted successfully");
        }
    }

    /// <summary>
    /// Test: Event with long database name
    /// Expected: Long database names are handled correctly
    /// </summary>
    [Fact]
    public async Task Handle_HandleLongDatabaseName_When_ValidEvent()
    {
        // Arrange
        var longDatabaseName = "VeryLongDatabaseNameThatExceedsNormalLengthLimitsForTestingPurposesAndShouldBeHandledGracefullyByTheEventHandlerWithoutAnyIssuesOrTruncationProblems_Deleted";
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = longDatabaseName
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain(longDatabaseName);
        createdActivity.ActivityDetails.ShouldContain("deleted successfully");
    }

    /// <summary>
    /// Test: Event with special characters in database name
    /// Expected: Special characters are handled correctly in events
    /// </summary>
    [Fact]
    public async Task Handle_HandleSpecialCharsInDatabaseName_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = "Special-Database@123!_Deleted & <script>alert('xss')</script>"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityDetails.ShouldContain("Special-Database@123!_Deleted");
        createdActivity.ActivityDetails.ShouldContain("deleted successfully");
        createdActivity.UserId.ShouldBe("TestUser123");
    }

    /// <summary>
    /// Test: Event with null or empty database name
    /// Expected: Null/empty database names are handled gracefully
    /// </summary>
    [Fact]
    public async Task Handle_HandleNullDatabaseName_When_EventWithNullName()
    {
        // Arrange
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = null
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("Delete BackUp");
        createdActivity.ActivityDetails.ShouldContain("deleted successfully");
        // Name handling should be graceful even when null
    }

    /// <summary>
    /// Test: Event processing performance with rapid succession
    /// Expected: Multiple rapid delete events are processed correctly
    /// </summary>
    [Fact]
    public async Task Handle_ProcessRapidDeleteEvents_When_MultipleEventsInSuccession()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new BackUpDeletedEvent
        {
            Name = $"RapidDelete_Database_{i:00}"
        }).ToList();

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(10));
    }

    /// <summary>
    /// Test: User service integration
    /// Expected: User service is called to get current user information
    /// </summary>
    [Fact]
    public async Task Handle_UseUserServiceForUserInfo_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = "UserServiceIntegration_Database_Deleted"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.UserId.ShouldBe("TestUser123");
        createdActivity.LoginName.ShouldBe("TestUser123");
        createdActivity.RequestUrl.ShouldBe("/api/backup/delete");
        createdActivity.HostAddress.ShouldBe("*************");
        
        // Verify user service was called
        _mockUserService.Verify(x => x.UserId, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.LoginName, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.RequestedUrl, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.IpAddress, Times.AtLeastOnce);
    }

    /// <summary>
    /// Test: Logger integration
    /// Expected: Logger is used to log event processing
    /// </summary>
    [Fact]
    public async Task Handle_LogEventProcessing_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = "LoggingIntegration_Database_Deleted"
        };

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        
        
        _mockLogger.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_HandleEmptyDatabaseName_When_EventWithEmptyName()
    {
        // Arrange
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = string.Empty
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("Delete BackUp");
        createdActivity.ActivityDetails.ShouldContain("deleted successfully");
    }

    /// <summary>
    /// Test: Event with whitespace-only database name
    /// Expected: Whitespace-only database names are handled gracefully
    /// </summary>
    [Fact]
    public async Task Handle_HandleWhitespaceDatabaseName_When_EventWithWhitespaceName()
    {
        // Arrange
        var backUpEvent = new BackUpDeletedEvent
        {
            Name = "   \t\n\r   "
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("Delete BackUp");
        createdActivity.ActivityDetails.ShouldContain("deleted successfully");
    }
}
