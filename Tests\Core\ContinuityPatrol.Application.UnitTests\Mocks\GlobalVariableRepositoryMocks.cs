using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using MockQueryable.Moq;
using Moq;
using System.Collections;
using System.Linq.Expressions;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class GlobalVariableRepositoryMocks
{
    public static Mock<IGlobalVariableRepository> CreateGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        globalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        globalVariableRepository.Setup(repo => repo.AddAsync(It.IsAny<GlobalVariable>())).ReturnsAsync(
            (GlobalVariable globalVariable) =>
            {
                globalVariable.Id = new Fixture().Create<int>();
                globalVariable.ReferenceId = new Fixture().Create<Guid>().ToString();
                globalVariable.CreatedDate = DateTime.UtcNow;
                globalVariable.IsActive = true;

                globalVariables.Add(globalVariable);

                return globalVariable;
            });

        return globalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> UpdateGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var mockGlobalVariableRepository = new Mock<IGlobalVariableRepository>();

        mockGlobalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        mockGlobalVariableRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => globalVariables.SingleOrDefault(x => x.ReferenceId == i));

        mockGlobalVariableRepository.Setup(repo => repo.UpdateAsync(It.IsAny<GlobalVariable>())).ReturnsAsync((GlobalVariable globalVariable) =>
        {
            var index = globalVariables.FindIndex(item => item.ReferenceId == globalVariable.ReferenceId);

            globalVariables[index] = globalVariable;

            return globalVariable;
        });

        return mockGlobalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> DeleteGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var mockGlobalVariableRepository = new Mock<IGlobalVariableRepository>();

        mockGlobalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        mockGlobalVariableRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => globalVariables.SingleOrDefault(x => x.ReferenceId == i));

        mockGlobalVariableRepository.Setup(repo => repo.UpdateAsync(It.IsAny<GlobalVariable>())).ReturnsAsync((GlobalVariable globalVariable) =>
        {
            var index = globalVariables.FindIndex(item => item.ReferenceId == globalVariable.ReferenceId);

            globalVariable.IsActive = false;

            globalVariables[index] = globalVariable;

            return globalVariable;
        });

        return mockGlobalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> GetGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        globalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        globalVariableRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => globalVariables.SingleOrDefault(x => x.ReferenceId == i));

        return globalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> GetPaginatedGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        globalVariableRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<GlobalVariable>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<GlobalVariable> spec, string sortColumn, string sortOrder) =>
            {
                var sortedGlobalVariables = globalVariables.AsQueryable();

                if (spec.Criteria != null)
                {
                    sortedGlobalVariables = sortedGlobalVariables.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    sortedGlobalVariables = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedGlobalVariables.OrderByDescending(c => c.VariableName)
                        : sortedGlobalVariables.OrderBy(c => c.VariableName);
                }

                var totalCount = sortedGlobalVariables.Count();
                var paginated = sortedGlobalVariables
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PaginatedResult<GlobalVariable>.Success(paginated, totalCount, pageNumber, pageSize);
            });

        return globalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> GetGlobalVariableNamesRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableNamesRepository = new Mock<IGlobalVariableRepository>();

        globalVariableNamesRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        return globalVariableNamesRepository;
    }

    public static Mock<IGlobalVariableRepository> GetNameUniqueRepository(List<GlobalVariable> globalVariables)
    {
        var nameUniqueRepository = new Mock<IGlobalVariableRepository>();

        nameUniqueRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(
            (string i, string j) =>
            {
                return j == 0.ToString() ? globalVariables.Exists(x => x.VariableName == i) : globalVariables.Exists(x => x.VariableName == i && x.ReferenceId == j);
            }
        );

        return nameUniqueRepository;
    }

    public static Mock<IGlobalVariableRepository> GetGlobalVariableEmptyRepository()
    {
        var mockGlobalVariableRepository = new Mock<IGlobalVariableRepository>();

        mockGlobalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<GlobalVariable>());
        mockGlobalVariableRepository.Setup(repo => repo.GetByVariableName(It.IsAny<string>())).ReturnsAsync(new List<GlobalVariable>());
        mockGlobalVariableRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(false);

        return mockGlobalVariableRepository;
    }

    //Events
    public static Mock<IUserActivityRepository> CreateGlobalVariableEventRepository(List<UserActivity> userActivities)
    {
        var globalVariableEventRepository = new Mock<IUserActivityRepository>();

        globalVariableEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        globalVariableEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return globalVariableEventRepository;
    }

    public static Mock<IGlobalVariableRepository> GetGlobalVariableByNameRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        globalVariableRepository.Setup(repo => repo.GetByVariableName(It.IsAny<string>())).ReturnsAsync((string name) =>
            globalVariables.Where(x => x.VariableName.ToLower().Contains(name?.ToLower() ?? "")).ToList());

        return globalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> GetPaginatedGlobalVariableQueryRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        var queryableGlobalVariable = globalVariables.BuildMock();

        globalVariableRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableGlobalVariable);

        return globalVariableRepository;
    }
}

// Test-specific queryable that handles GlobalVariableFilterSpecification for in-memory data
public class TestGlobalVariableQueryable : IQueryable<GlobalVariable>
{
    private readonly List<GlobalVariable> _data;
    private readonly IQueryable<GlobalVariable> _queryable;

    public TestGlobalVariableQueryable(List<GlobalVariable> data)
    {
        _data = data;
        _queryable = data.AsQueryable();
    }

    public IEnumerator<GlobalVariable> GetEnumerator() => _queryable.GetEnumerator();
    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    public Expression Expression => _queryable.Expression;
    public Type ElementType => _queryable.ElementType;
    public IQueryProvider Provider => new TestGlobalVariableQueryProvider(_queryable.Provider, _data);
}

// Test-specific query provider that handles the Specify extension method for in-memory data
public class TestGlobalVariableQueryProvider : IQueryProvider
{
    private readonly IQueryProvider _provider;
    private readonly List<GlobalVariable> _data;

    public TestGlobalVariableQueryProvider(IQueryProvider provider, List<GlobalVariable> data)
    {
        _provider = provider;
        _data = data;
    }

    public IQueryable CreateQuery(Expression expression) => _provider.CreateQuery(expression);

    public IQueryable<TElement> CreateQuery<TElement>(Expression expression)
    {
        // Check if this is a Specify method call
        if (IsSpecifyMethodCall(expression))
        {
            return HandleSpecifyMethodCall<TElement>(expression);
        }
        return _provider.CreateQuery<TElement>(expression);
    }

    public object Execute(Expression expression) => _provider.Execute(expression);
    public TResult Execute<TResult>(Expression expression) => _provider.Execute<TResult>(expression);

    private bool IsSpecifyMethodCall(Expression expression)
    {
        return expression is MethodCallExpression methodCall &&
               methodCall.Method.Name == "Specify" &&
               methodCall.Method.DeclaringType?.Name == "QueryableExtensions";
    }

    private IQueryable<TElement> HandleSpecifyMethodCall<TElement>(Expression expression)
    {
        if (expression is MethodCallExpression methodCall && methodCall.Arguments.Count >= 2)
        {
            // Get the source queryable
            var sourceQuery = _provider.CreateQuery<TElement>(methodCall.Arguments[0]);

            // Get the specification argument
            var specificationArg = methodCall.Arguments[1];

            // If it's a GlobalVariableFilterSpecification, handle it manually
            if (specificationArg is ConstantExpression constantExpr &&
                constantExpr.Value is GlobalVariableFilterSpecification spec &&
                typeof(TElement) == typeof(GlobalVariable))
            {
                return ApplyGlobalVariableFilter(sourceQuery, spec) as IQueryable<TElement>;
            }
        }

        return _provider.CreateQuery<TElement>(expression);
    }

    private IQueryable<GlobalVariable> ApplyGlobalVariableFilter(IQueryable sourceQuery, GlobalVariableFilterSpecification spec)
    {
        var globalVariables = _data.AsQueryable();

        // Apply the same filtering logic as GlobalVariableFilterSpecification but for in-memory data
        if (!string.IsNullOrEmpty(spec.SearchString))
        {
            var searchString = spec.SearchString;

            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');
                var filteredQuery = globalVariables.Where(p => false); // Start with empty result

                foreach (var stringItem in stringArray)
                {
                    if (stringItem.Contains("variablename=", StringComparison.OrdinalIgnoreCase))
                    {
                        var value = stringItem.Replace("variablename=", "", StringComparison.OrdinalIgnoreCase);
                        filteredQuery = filteredQuery.Union(globalVariables.Where(p => p.VariableName.Contains(value)));
                    }
                    else if (stringItem.Contains("variablevalue=", StringComparison.OrdinalIgnoreCase))
                    {
                        var value = stringItem.Replace("variablevalue=", "", StringComparison.OrdinalIgnoreCase);
                        filteredQuery = filteredQuery.Union(globalVariables.Where(p => p.VariableValue.Contains(value)));
                    }
                }
                return filteredQuery;
            }
            else
            {
                return globalVariables.Where(p => p.VariableName.Contains(searchString) || p.VariableValue.Contains(searchString));
            }
        }
        else
        {
            return globalVariables.Where(p => p.VariableName != null);
        }
    }
}
