using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using MockQueryable.Moq;
using Moq;
using System.Collections;
using System.Linq.Expressions;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class GlobalVariableRepositoryMocks
{
    public static Mock<IGlobalVariableRepository> CreateGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        globalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        globalVariableRepository.Setup(repo => repo.AddAsync(It.IsAny<GlobalVariable>())).ReturnsAsync(
            (GlobalVariable globalVariable) =>
            {
                globalVariable.Id = new Fixture().Create<int>();
                globalVariable.ReferenceId = new Fixture().Create<Guid>().ToString();
                globalVariable.CreatedDate = DateTime.UtcNow;
                globalVariable.IsActive = true;

                globalVariables.Add(globalVariable);

                return globalVariable;
            });

        return globalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> UpdateGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var mockGlobalVariableRepository = new Mock<IGlobalVariableRepository>();

        mockGlobalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        mockGlobalVariableRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => globalVariables.SingleOrDefault(x => x.ReferenceId == i));

        mockGlobalVariableRepository.Setup(repo => repo.UpdateAsync(It.IsAny<GlobalVariable>())).ReturnsAsync((GlobalVariable globalVariable) =>
        {
            var index = globalVariables.FindIndex(item => item.ReferenceId == globalVariable.ReferenceId);

            globalVariables[index] = globalVariable;

            return globalVariable;
        });

        return mockGlobalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> DeleteGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var mockGlobalVariableRepository = new Mock<IGlobalVariableRepository>();

        mockGlobalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        mockGlobalVariableRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => globalVariables.SingleOrDefault(x => x.ReferenceId == i));

        mockGlobalVariableRepository.Setup(repo => repo.UpdateAsync(It.IsAny<GlobalVariable>())).ReturnsAsync((GlobalVariable globalVariable) =>
        {
            var index = globalVariables.FindIndex(item => item.ReferenceId == globalVariable.ReferenceId);

            globalVariable.IsActive = false;

            globalVariables[index] = globalVariable;

            return globalVariable;
        });

        return mockGlobalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> GetGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        globalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        globalVariableRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => globalVariables.SingleOrDefault(x => x.ReferenceId == i));

        return globalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> GetPaginatedGlobalVariableRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        globalVariableRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<GlobalVariable>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<GlobalVariable> spec, string sortColumn, string sortOrder) =>
            {
                var sortedGlobalVariables = globalVariables.AsQueryable();

                if (spec.Criteria != null)
                {
                    sortedGlobalVariables = sortedGlobalVariables.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    sortedGlobalVariables = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedGlobalVariables.OrderByDescending(c => c.VariableName)
                        : sortedGlobalVariables.OrderBy(c => c.VariableName);
                }

                var totalCount = sortedGlobalVariables.Count();
                var paginated = sortedGlobalVariables
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PaginatedResult<GlobalVariable>.Success(paginated, totalCount, pageNumber, pageSize);
            });

        return globalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> GetGlobalVariableNamesRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableNamesRepository = new Mock<IGlobalVariableRepository>();

        globalVariableNamesRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(globalVariables);

        return globalVariableNamesRepository;
    }

    public static Mock<IGlobalVariableRepository> GetNameUniqueRepository(List<GlobalVariable> globalVariables)
    {
        var nameUniqueRepository = new Mock<IGlobalVariableRepository>();

        nameUniqueRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(
            (string i, string j) =>
            {
                return j == 0.ToString() ? globalVariables.Exists(x => x.VariableName == i) : globalVariables.Exists(x => x.VariableName == i && x.ReferenceId == j);
            }
        );

        return nameUniqueRepository;
    }

    public static Mock<IGlobalVariableRepository> GetGlobalVariableEmptyRepository()
    {
        var mockGlobalVariableRepository = new Mock<IGlobalVariableRepository>();

        mockGlobalVariableRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<GlobalVariable>());
        mockGlobalVariableRepository.Setup(repo => repo.GetByVariableName(It.IsAny<string>())).ReturnsAsync(new List<GlobalVariable>());
        mockGlobalVariableRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(false);

        return mockGlobalVariableRepository;
    }

    //Events
    public static Mock<IUserActivityRepository> CreateGlobalVariableEventRepository(List<UserActivity> userActivities)
    {
        var globalVariableEventRepository = new Mock<IUserActivityRepository>();

        globalVariableEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        globalVariableEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return globalVariableEventRepository;
    }

    public static Mock<IGlobalVariableRepository> GetGlobalVariableByNameRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        globalVariableRepository.Setup(repo => repo.GetByVariableName(It.IsAny<string>())).ReturnsAsync((string name) =>
            globalVariables.Where(x => x.VariableName.ToLower().Contains(name?.ToLower() ?? "")).ToList());

        return globalVariableRepository;
    }

    public static Mock<IGlobalVariableRepository> GetPaginatedGlobalVariableQueryRepository(List<GlobalVariable> globalVariables)
    {
        var globalVariableRepository = new Mock<IGlobalVariableRepository>();

        var queryableGlobalVariable = globalVariables.BuildMock();

        globalVariableRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableGlobalVariable);

        return globalVariableRepository;
    }
}