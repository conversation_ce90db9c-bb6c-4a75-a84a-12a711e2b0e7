﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Queries
{
   public class GetWorkflowDrCalenderNameUniqueQueryHandlerTests : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly WorkflowDrCalenderFixture _workflowDrCalenderFixture;

        private Mock<IWorkflowDrCalenderRepository> _mockWorkflowDrCalenderRepository;

        private readonly GetWorkflowDrCalenderNameUniqueQueryHandler _handler;

        public GetWorkflowDrCalenderNameUniqueQueryHandlerTests(WorkflowDrCalenderFixture workflowDrCalenderFixture)
        {
            _workflowDrCalenderFixture = workflowDrCalenderFixture;

            _mockWorkflowDrCalenderRepository = WorkflowDrCalenderRepositoryMocks.GetWorkflowDrcalenderNameUniqueRepository(_workflowDrCalenderFixture.WorkflowDrCalenderInfos);

            _handler = new GetWorkflowDrCalenderNameUniqueQueryHandler(_mockWorkflowDrCalenderRepository.Object);
        }

        [Fact]
        public async Task Handle_Return_True_WorkflowDrCalenderName_Exist()
        {
            _workflowDrCalenderFixture.WorkflowDrCalenderInfos[0].ProfileName = "WorkflowProfile";
            _workflowDrCalenderFixture.WorkflowDrCalenderInfos[0].IsActive = true;

            var result = await _handler.Handle(new GetWorkflowDrCalenderNameUniqueQuery { Id = _workflowDrCalenderFixture.WorkflowDrCalenderInfos[0].ReferenceId, Name = _workflowDrCalenderFixture.WorkflowDrCalenderInfos[0].ProfileName }, CancellationToken.None);

            result.ShouldBeTrue();
        }

        [Fact]
        public async Task Handle_Return_False_WorkflowDrcalenderNameAndId_NotMatch()
        {
            var result = await _handler.Handle(new GetWorkflowDrCalenderNameUniqueQuery { Id = 1.ToString(), Name = "Testing" }, CancellationToken.None);

            result.ShouldBeFalse();
        }

        [Fact]
        public async Task Handle_Return_False_WorkflowDrCalender_Name_NotMatch()
        {
            var result = await _handler.Handle(new GetWorkflowDrCalenderNameUniqueQuery { Id = 0.ToString(), Name = "Demo" }, CancellationToken.None);

            result.ShouldBeFalse();
        }

        [Fact]
        public async Task Handle_Call_IsWorkflowDrCalenderNameExist_OneTime()
        {
            await _handler.Handle(new GetWorkflowDrCalenderNameUniqueQuery(), CancellationToken.None);

            _mockWorkflowDrCalenderRepository.Verify(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_EmptyList_When_NoRecords()
        {
            _mockWorkflowDrCalenderRepository = WorkflowDrCalenderRepositoryMocks.GetWorkflowDrEmptyRepository();

            var result = await _handler.Handle(new GetWorkflowDrCalenderNameUniqueQuery(), CancellationToken.None);

            result.ShouldBe(false);
        }
    }
}
