using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberAlertRepository : BaseRepository<CyberAlert>, ICyberAlertRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberAlertRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    
    public  async Task<PaginatedResult<CyberAlert>>GetPaginatedListBySeverity(int pageNumber,int pageSize ,Specification<CyberAlert> specification,string severity,string sortColumn,string sortOrder )
    {
        if (string.IsNullOrWhiteSpace(severity))
        {
            throw new ArgumentException("Severity must be provided", nameof(severity));
        }

        var query = Entities
            .Specify(specification)
            .Where(e => e.Severity.ToLower() == severity.ToLower())
            .DescOrderById();

        return await query.ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public  IQueryable<CyberAlert> GetPaginatedListBySeverity(string severity)
    {
        if (string.IsNullOrWhiteSpace(severity))
        {
            throw new ArgumentException("Severity must be provided", nameof(severity));
        }

        return _dbContext.CyberAlerts
            .Active()
            .AsNoTracking()
            .Where(e => e.Severity.ToLower() == severity.ToLower())
            .OrderByDescending(e => e.Id);
    }
}
