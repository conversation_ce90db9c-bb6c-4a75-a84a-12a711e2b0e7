﻿using ContinuityPatrol.Application.Features.Incident.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Events;

public class DeleteIncidentEventTests : IClassFixture<IncidentFixture>, IClassFixture<UserActivityFixture>
{
    private readonly IncidentFixture _incidentFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly IncidentDeletedEventHanlder _handler;

    public DeleteIncidentEventTests(IncidentFixture incidentFixture, UserActivityFixture userActivityFixture)
    {
        _incidentFixture = incidentFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockIncidentEventLogger = new Mock<ILogger<IncidentDeletedEventHanlder>>();

        _mockUserActivityRepository = CompanyRepositoryMocks.CreateCompanyEventRepository(_userActivityFixture.UserActivities);

        _handler = new IncidentDeletedEventHanlder(mockIncidentEventLogger.Object, mockLoggedInUserService.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateIncidentEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_incidentFixture.IncidentDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_incidentFixture.IncidentDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}