using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

/// <summary>
/// Mock repository implementations for CyberAirGapLog unit testing
/// Provides comprehensive mock setups for all repository operations
/// </summary>
public static class CyberAirGapLogRepositoryMocks
{
    /// <summary>
    /// Creates a mock CyberAirGapLog repository with standard CRUD operations
    /// </summary>
    /// <param name="cyberAirGapLogs">Test data for the repository</param>
    /// <returns>Configured mock repository</returns>
    public static Mock<ICyberAirGapLogRepository> CreateCyberAirGapLogRepository(List<CyberAirGapLog> cyberAirGapLogs)
    {
        var mockRepository = new Mock<ICyberAirGapLogRepository>();

        // Setup ListAllAsync - returns all active cyber air gap logs
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberAirGapLogs.Where(x => x.IsActive).ToList());

        // Setup GetByReferenceIdAsync - returns cyber air gap log by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberAirGapLogs.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        // Setup AddAsync - adds new cyber air gap log and returns it
        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAirGapLog>()))
            .ReturnsAsync((CyberAirGapLog cyberAirGapLog) =>
            {
                cyberAirGapLog.ReferenceId = Guid.NewGuid().ToString();
                cyberAirGapLog.CreatedDate = DateTime.Now;
                cyberAirGapLog.IsActive = true;
                cyberAirGapLogs.Add(cyberAirGapLog);
                return cyberAirGapLog;
            });

        // Setup UpdateAsync - updates existing cyber air gap log
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAirGapLog>()))
            .ReturnsAsync((CyberAirGapLog cyberAirGapLog) =>
            {
                var existingLog = cyberAirGapLogs.FirstOrDefault(x => x.ReferenceId == cyberAirGapLog.ReferenceId);
                if (existingLog != null)
                {
                    existingLog.AirGapName = cyberAirGapLog.AirGapName;
                    existingLog.Description = cyberAirGapLog.Description;
                    existingLog.SourceSiteName = cyberAirGapLog.SourceSiteName;
                    existingLog.TargetSiteName = cyberAirGapLog.TargetSiteName;
                    existingLog.Port = cyberAirGapLog.Port;
                    existingLog.Status = cyberAirGapLog.Status;
                    existingLog.LastModifiedDate = DateTime.Now;
                    existingLog.IsActive = cyberAirGapLog.IsActive;
                    return existingLog;
                }
                return cyberAirGapLog;
            });

        // Setup DeleteAsync - soft delete (sets IsActive to false)
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberAirGapLog>()))
            .ReturnsAsync((CyberAirGapLog cyberAirGapLog) =>
            {
                var existingLog = cyberAirGapLogs.FirstOrDefault(x => x.ReferenceId == cyberAirGapLog.ReferenceId);
                if (existingLog != null)
                {
                    existingLog.IsActive = false;
                    existingLog.LastModifiedDate = DateTime.Now;
                }
                return existingLog ?? cyberAirGapLog;
            });

        // Setup IsNameExist - checks if air gap name already exists
        mockRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) =>
            {
                if (string.IsNullOrEmpty(id))
                {
                    return cyberAirGapLogs.Any(x => x.AirGapName.Equals(name, StringComparison.OrdinalIgnoreCase) && x.IsActive);
                }
                return cyberAirGapLogs.Any(x => x.AirGapName.Equals(name, StringComparison.OrdinalIgnoreCase) && 
                                              x.ReferenceId != id && x.IsActive);
            });

        // Setup GetCyberAirGapLogByAirGabId - gets logs by air gap ID within date range
        mockRepository.Setup(repo => repo.GetCyberAirGapLogByAirGabId(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string startDate, string endDate, string airGapId) =>
            {
                var start = DateTime.TryParse(startDate, out var startDateTime) ? startDateTime : DateTime.MinValue;
                var end = DateTime.TryParse(endDate, out var endDateTime) ? endDateTime : DateTime.MaxValue;
                
                return cyberAirGapLogs.Where(x => x.AirGapId == airGapId && 
                                                 x.IsActive &&
                                                 x.CreatedDate >= start && 
                                                 x.CreatedDate <= end).ToList();
            });

        
        //mockRepository.Setup(repo => repo.GetAirGaplist(It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((string startDate, string endDate) =>
        //    {
        //        var start = DateTime.TryParse(startDate, out var startDateTime) ? startDateTime : DateTime.MinValue;
        //        var end = DateTime.TryParse(endDate, out var endDateTime) ? endDateTime : DateTime.MaxValue;
                
        //        return cyberAirGapLogs.Where(x => x.IsActive &&
        //                                         x.CreatedDate >= start && 
        //                                         x.CreatedDate <= end).ToList();
        //    });

        // Setup PaginatedListAllAsync - returns paginated results
        //mockRepository.Setup(repo => repo.PaginatedListAllAsync(
        //        It.IsAny<int>(), It.IsAny<int>(), It.IsAny<object>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, object filterSpec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredLogs = cyberAirGapLogs.Where(x => x.IsActive).ToList();
                
        //        // Apply sorting
        //        if (!string.IsNullOrEmpty(sortColumn))
        //        {
        //            switch (sortColumn.ToLower())
        //            {
        //                case "airgapname":
        //                    filteredLogs = sortOrder?.ToLower() == "desc" 
        //                        ? filteredLogs.OrderByDescending(x => x.AirGapName).ToList()
        //                        : filteredLogs.OrderBy(x => x.AirGapName).ToList();
        //                    break;
        //                case "status":
        //                    filteredLogs = sortOrder?.ToLower() == "desc" 
        //                        ? filteredLogs.OrderByDescending(x => x.Status).ToList()
        //                        : filteredLogs.OrderBy(x => x.Status).ToList();
        //                    break;
        //                case "createddate":
        //                    filteredLogs = sortOrder?.ToLower() == "desc" 
        //                        ? filteredLogs.OrderByDescending(x => x.CreatedDate).ToList()
        //                        : filteredLogs.OrderBy(x => x.CreatedDate).ToList();
        //                    break;
        //                default:
        //                    filteredLogs = filteredLogs.OrderBy(x => x.AirGapName).ToList();
        //                    break;
        //            }
        //        }

        //        // Apply pagination
        //        var totalCount = filteredLogs.Count;
        //        var pagedLogs = filteredLogs.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();

        //        return new PaginatedResult<CyberAirGapLog>
        //        {
        //            Data = pagedLogs,
        //            PageSize = pageSize,
        //            TotalCount = totalCount,
        //            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        //        };
        //    });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository that throws exceptions for testing error scenarios
    /// </summary>
    /// <returns>Mock repository configured to throw exceptions</returns>
    public static Mock<ICyberAirGapLogRepository> CreateFailingCyberAirGapLogRepository()
    {
        var mockRepository = new Mock<ICyberAirGapLogRepository>();

        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAirGapLog>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAirGapLog>()))
            .ThrowsAsync(new InvalidOperationException("Update operation failed"));

        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ThrowsAsync(new InvalidOperationException("Query operation failed"));

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ThrowsAsync(new InvalidOperationException("List operation failed"));

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock user service for testing
    /// </summary>
    /// <returns>Configured mock user service</returns>
    public static Mock<ILoggedInUserService> CreateUserService()
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        
        mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        mockUserService.Setup(x => x.LoginName).Returns("TestUser123");
        mockUserService.Setup(x => x.CompanyId).Returns("TestCompany123");
        mockUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");
        mockUserService.Setup(x => x.RequestedUrl).Returns("/api/test");

        return mockUserService;
    }

    /// <summary>
    /// Creates a mock user activity repository for testing
    /// </summary>
    /// <returns>Configured mock user activity repository</returns>
    public static Mock<IUserActivityRepository> CreateUserActivityRepository()
    {
        var mockRepository = new Mock<IUserActivityRepository>();

        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity activity) =>
            {
                activity.ReferenceId = Guid.NewGuid().ToString();
                activity.CreatedDate = DateTime.Now;
                activity.IsActive = true;
                return activity;
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock job scheduler for testing
    /// </summary>
    /// <returns>Configured mock job scheduler</returns>
    public static Mock<IJobScheduler> CreateJobScheduler()
    {
        var mockJobScheduler = new Mock<IJobScheduler>();

        //mockJobScheduler.Setup(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .Returns(Task.CompletedTask);

        //mockJobScheduler.Setup(x => x.DeleteJob(It.IsAny<string>()))
        //    .Returns(Task.CompletedTask);

        return mockJobScheduler;
    }

    /// <summary>
    /// Creates an empty mock repository for testing scenarios with no data
    /// </summary>
    /// <returns>Mock repository with empty data</returns>
    public static Mock<ICyberAirGapLogRepository> CreateEmptyCyberAirGapLogRepository()
    {
        return CreateCyberAirGapLogRepository(new List<CyberAirGapLog>());
    }

    /// <summary>
    /// Creates a mock repository with large dataset for performance testing
    /// </summary>
    /// <param name="count">Number of test records to create</param>
    /// <returns>Mock repository with large dataset</returns>
    public static Mock<ICyberAirGapLogRepository> CreateLargeCyberAirGapLogRepository(int count = 1000)
    {
        var cyberAirGapLogs = new List<CyberAirGapLog>();
        
        for (int i = 1; i <= count; i++)
        {
            cyberAirGapLogs.Add(new CyberAirGapLog
            {
                ReferenceId = $"cyberairgaplog-{i:0000}",
                AirGapId = $"airgap-{i:0000}",
                AirGapName = $"Performance Test Air Gap {i:0000}",
                SourceSiteName = $"Source Site {i:000}",
                TargetSiteName = $"Target Site {i:000}",
                Port = 8000 + (i % 1000),
                Status = i % 3 == 0 ? "Active" : i % 3 == 1 ? "Warning" : "Error",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-i % 30),
                CreatedBy = "PerformanceTestUser"
            });
        }

        return CreateCyberAirGapLogRepository(cyberAirGapLogs);
    }
}
