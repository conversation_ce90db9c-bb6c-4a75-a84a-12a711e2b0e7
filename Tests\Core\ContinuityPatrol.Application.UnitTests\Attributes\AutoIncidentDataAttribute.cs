using ContinuityPatrol.Application.Features.Incident.Commands.Create;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoIncidentDataAttribute : AutoDataAttribute
{
    public AutoIncidentDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateIncidentCommand>(p => p.IncidentName, 10));
            fixture.Customize<CreateIncidentCommand>(c => c.With(b => b.IncidentName, "TestIncident"));
            fixture.Customize<CreateIncidentCommand>(c => c.With(b => b.IncidentNumber, "INC-001"));
            fixture.Customize<CreateIncidentCommand>(c => c.With(b => b.Description, "Test Description"));
            fixture.Customize<CreateIncidentCommand>(c => c.With(b => b.BusinessFunctionName, "TestFunction"));
            fixture.Customize<CreateIncidentCommand>(c => c.With(b => b.BusinessServiceName, "TestService"));

            return fixture;
        })
    {

    }
}
