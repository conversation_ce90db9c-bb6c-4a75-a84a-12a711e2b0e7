﻿using ContinuityPatrol.Application.Features.Replication.Events.PaginatedView;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Create;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Update;
using ContinuityPatrol.Application.Features.SingleSignOn.Events.PaginatedView;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class SingleSignOnController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<SingleSignOnController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public SingleSignOnController(IPublisher publisher,ILogger<SingleSignOnController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    [EventCode(EventCodes.SingleSignOn.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in SingleSignOn");
        await _publisher.Publish(new SingleSignOnPaginatedEvent());
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    [AntiXss]
    [EventCode(EventCodes.SingleSignOn.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(SingleSignOnViewModel singleSignOnViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in SingleSignOn");
        var ssoId = Request.Form["id"].ToString();
        try
        {
            if (ssoId.IsNullOrWhiteSpace())
            {               
                var ssoCreateCommand = _mapper.Map<CreateSingleSignOnCommand>(singleSignOnViewModel);
                _logger.LogDebug($"Creating SingleSignOn '{singleSignOnViewModel.ProfileName}'");
                var response = await _dataProvider.SingleSignOn.CreateAsync(ssoCreateCommand);
                _logger.LogDebug("Create operation completed successfully in SingleSignOn, returning view.");
                return Json(new { success = true, data = response });
            }
            else
            {               
                var ssoUpdateCommand = _mapper.Map<UpdateSingleSignOnCommand>(singleSignOnViewModel);
                _logger.LogDebug($"Updating SingleSignOn '{singleSignOnViewModel.ProfileName}'");
                var response = await _dataProvider.SingleSignOn.UpdateAsync(ssoUpdateCommand);
                _logger.LogDebug("Update operation completed successfully in SingleSignOn, returning view.");
                return Json(new { success = true, data = response });
            }
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on SingleSignOn page: {ex.ValidationErrors.FirstOrDefault()}");
            return ex.GetJsonException();
        }
        catch (Exception ex)        {
            _logger.Exception("An error occurred on SingleSignOn option page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }        
    }

    [Authorize(Policy = Permissions.Configuration.Delete)]
    [EventCode(EventCodes.SingleSignOn.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in SingleSignOn");
        try
        {
            var response = await _dataProvider.SingleSignOn.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in SingleSignOn");
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on SingleSignOn.", ex);
            return ex.GetJsonException();
        }        
    }

    [HttpGet]
    [EventCode(EventCodes.SingleSignOn.GetPagination)]
    public async Task<JsonResult> GetPagination(GetSingleSignOnPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in SingleSignOn");
        try
        {
            var result = await _dataProvider.SingleSignOn.GetPaginatedSingleSignOns(query);
            _logger.LogDebug("Successfully retrieved SingleSignOn paginated list on SingleSignOn page");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SingleSignOn page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.SingleSignOn.GetSingleSignOnList)]
    public async Task<JsonResult> GetSingleSignOnList()
    {
        _logger.LogDebug("Entering GetSingleSignOnList method in SingleSignOn");
        try
        {
            var result = await _dataProvider.SingleSignOn.GetSingleSignOnList();
            _logger.LogDebug("Successfully retrieved SingleSignOn List.");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SingleSignOn page while retrieving SingleSignOn List.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [EventCode(EventCodes.SingleSignOn.GetSingleSignOnByType)]
    public async Task<JsonResult> GetSingleSignOnByType(string type)
    {
        _logger.LogDebug("Entering GetSingleSignOnByType method in SingleSignOn");
        try
        {
            var result = await _dataProvider.SingleSignOn.GetSingleSignOnByType(type);
            _logger.LogDebug($"Successfully retrieved Get SingleSignOn by type '{type}'.");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on SingleSignOn page while retrieving SingleSignOn by type '{type}' .", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.SingleSignOn.IsSingleSignOnNameExist)]
    public async Task<bool> IsSingleSignOnNameExist(string singleSignOnName, string id)
    {
        _logger.LogDebug("Entering IsInfraObjectNameExist method in SingleSignOn");
        try
        {
            var nameExist = await _dataProvider.SingleSignOn.IsSingleSignOnProfileNameExist(singleSignOnName, id);
            _logger.LogDebug("Returning result for IsSingleSignOnNameExist on SingleSignOn");
            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on SingleSignOn page while checking if SingleSignOn name exists for : {singleSignOnName}.", ex);
            return false;
        }
    }

    [HttpGet]
    [EventCode(EventCodes.SingleSignOn.GetByReferenceId)]
    public async Task<JsonResult> GetSingleSignOnById(string id)
    {
        _logger.LogDebug("Entering GetSingleSignOnById method in SingleSignOn");
        try
        {
            var signOnDetailVm = await _dataProvider.SingleSignOn.GetSingleSignOnById(id);
            _logger.LogDebug($"Successfully retrieved SingleSignOn details by id '{id}'");
            return Json(new { success = true, data = signOnDetailVm });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SingleSignOn page while retrieving the single sign on by id.", ex);
            return ex.GetJsonException();
        }
    }
}
