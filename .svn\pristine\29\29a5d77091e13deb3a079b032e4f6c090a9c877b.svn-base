﻿using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator.Helper;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;

public class CreateBulkImportValidatorCommandValidator : AbstractValidator<BulkImportValidatorCommand>
{
    public CreateBulkImportValidatorCommandValidator(IServerRepository serverRepository,
        IDatabaseRepository databaseRepository,
        IReplicationRepository replicationRepository, IInfraObjectRepository infraObjectRepository)

    {
        When(x => x.IsServer, () =>
        {
            RuleForEach(x => x.ServerList)
                .NotEmpty()
                .NotNull()
                .SetValidator(new CreateBulkImportComponentServerValidator(serverRepository));
        });


        When(x => x.IsDatabase, () =>
        {
            RuleForEach(x => x.DatabaseList)
                .NotEmpty()
                .NotNull()
                .SetValidator(new CreateBulkImportComponentDataBaseValidator(databaseRepository));
        });

        When(x => x.IsReplication, () =>
        {
            RuleForEach(x => x.ReplicationList)
            .NotEmpty()
            .NotNull()
            .SetValidator(new CreateBulkImportComponentReplicationValidator(replicationRepository));
        });

        When(x => x.IsInfraObject, () =>
        {
            RuleFor(x => x.InfraObject)
                .NotEmpty()
                .NotNull()
                .SetValidator(new CreateBulkImportComponentInfraObjectValidator(infraObjectRepository));
        });
    }
}