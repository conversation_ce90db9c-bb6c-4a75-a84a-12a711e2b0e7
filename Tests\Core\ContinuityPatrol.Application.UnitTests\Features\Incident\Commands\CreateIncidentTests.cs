﻿using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Commands;

public class CreateGlobalVariableTests : IClassFixture<GlobalVariableFixture>
{
    private readonly GlobalVariableFixture _gobalVaraibleFixture;
    private readonly Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateGlobalVariableCommandHandler _handler;

    public CreateFiaIntervalTests(FiaIntervalFixture fiaIntervalFixture)
    {
        _fiaIntervalFixture = fiaIntervalFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockFiaIntervalRepository = FiaIntervalRepositoryMocks.CreateFiaIntervalRepository(_fiaIntervalFixture.FiaIntervals);

        _handler = new CreateFiaIntervalCommandHandler(_fiaIntervalFixture.Mapper, _mockFiaIntervalRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseFiaIntervalCount_When_FiaIntervalCreated()
    {
        var initialCount = _fiaIntervalFixture.FiaIntervals.Count;

        await _handler.Handle(_fiaIntervalFixture.CreateFiaIntervalCommand, CancellationToken.None);

        var allFiaIntervals = await _mockFiaIntervalRepository.Object.ListAllAsync();

        allFiaIntervals.Count.ShouldBe(initialCount + 1);
    }

    [Fact]
    public async Task Handle_Return_CreateFiaIntervalResponse_When_FiaIntervalCreated()
    {
        var result = await _handler.Handle(_fiaIntervalFixture.CreateFiaIntervalCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateFiaIntervalResponse));

        result.Id.ShouldNotBeNullOrEmpty();

        result.Message.ShouldContain("Time interval created successfully");

        result.Success.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_fiaIntervalFixture.CreateFiaIntervalCommand, CancellationToken.None);

        _mockFiaIntervalRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.FiaInterval>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_FiaIntervalCreated()
    {
        await _handler.Handle(_fiaIntervalFixture.CreateFiaIntervalCommand, CancellationToken.None);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<FiaIntervalCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_MapCommand_ToEntity_Correctly()
    {
        var command = _fiaIntervalFixture.CreateFiaIntervalCommand;

        await _handler.Handle(command, CancellationToken.None);

        _mockFiaIntervalRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.FiaInterval>(
            entity => entity.MinTime == command.MinTime &&
                     entity.MaxTime == command.MaxTime &&
                     entity.MinTimeUnit == command.MinTimeUnit &&
                     entity.MaxTimeUnit == command.MaxTimeUnit)), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_CorrectMessage_When_FiaIntervalCreated()
    {
        var result = await _handler.Handle(_fiaIntervalFixture.CreateFiaIntervalCommand, CancellationToken.None);

        result.Message.ShouldBe("Time interval created successfully");
    }

    [Fact]
    public async Task Handle_Return_EntityReferenceId_When_FiaIntervalCreated()
    {
        var result = await _handler.Handle(_fiaIntervalFixture.CreateFiaIntervalCommand, CancellationToken.None);

        result.Id.ShouldNotBeNullOrEmpty();

        // The result ID should be the newly created entity's ReferenceId
        var allFiaIntervals = await _mockFiaIntervalRepository.Object.ListAllAsync();
        var createdFiaInterval = allFiaIntervals.Last();
        result.Id.ShouldBe(createdFiaInterval.ReferenceId);
    }

    [Fact]
    public async Task Handle_PublishEvent_WithCorrectEventName()
    {
        await _handler.Handle(_fiaIntervalFixture.CreateFiaIntervalCommand, CancellationToken.None);

        _mockPublisher.Verify(x => x.Publish(It.Is<FiaIntervalCreatedEvent>(
            evt => evt.Name == "Time interval"), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallMapper_ToMapCommandToEntity()
    {
        var command = _fiaIntervalFixture.CreateFiaIntervalCommand;

        await _handler.Handle(command, CancellationToken.None);

        // Verify mapper was called to map command to entity
        _mockFiaIntervalRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.FiaInterval>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetSuccessResponse_When_FiaIntervalCreated()
    {
        var result = await _handler.Handle(_fiaIntervalFixture.CreateFiaIntervalCommand, CancellationToken.None);

        result.Success.ShouldBe(true);
        result.Message.ShouldNotBeNullOrEmpty();
        result.Id.ShouldNotBeNullOrEmpty();
    }
}