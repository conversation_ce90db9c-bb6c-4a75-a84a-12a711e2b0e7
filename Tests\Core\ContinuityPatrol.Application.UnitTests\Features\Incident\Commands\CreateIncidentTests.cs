﻿using ContinuityPatrol.Application.Features.Incident.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Constants;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Commands;

public class CreateIncidentTests : IClassFixture<IncidentFixture>
{
    private readonly IncidentFixture _incidentFixture;
    private readonly Mock<IIncidentRepository> _mockIncidentRepository;
    private readonly CreateIncidentCommandHanlder _handler;

    public CreateIncidentTests(IncidentFixture incidentFixture)
    {
        _incidentFixture = incidentFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockIncidentRepository = IncidentRepositoryMocks.CreateIncidentRepository(_incidentFixture.Incidents);

        _handler = new CreateIncidentCommandHanlder(_mockIncidentRepository.Object, _incidentFixture.Mapper, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseIncidentCount_When_IncidentCreated()
    {
        await _handler.Handle(_incidentFixture.CreateIncidentCommand, CancellationToken.None);

        var allIncidents = await _mockIncidentRepository.Object.ListAllAsync();

        allIncidents.Count.ShouldBe(_incidentFixture.Incidents.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateIncidentResponse_When_IncidentCreated()
    {
        var result = await _handler.Handle(_incidentFixture.CreateIncidentCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateIncidentCommandResponse));

        result.IncidentId.ShouldNotBeNullOrEmpty();

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);

        result.Success.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_incidentFixture.CreateIncidentCommand, CancellationToken.None);

        _mockIncidentRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.Incident>()), Times.Once);
    }

}