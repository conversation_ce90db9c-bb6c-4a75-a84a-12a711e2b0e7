﻿using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactType.Queries
{
    public class GetFiaImpactTypeNameUniqueQueryHandlerTests
    {
        private readonly Mock<IFiaImpactTypeRepository> _mockFiaImpactTypeRepository;
        private readonly GetFiaImpactTypeNameUniqueQueryHandler _handler;
        private readonly List<Domain.Entities.FiaImpactType> _fiaImpactTypes;
        private readonly Fixture _fixture;

        public GetFiaImpactTypeNameUniqueQueryHandlerTests()
        {
            _fixture = new Fixture();

            _fiaImpactTypes = new List<Domain.Entities.FiaImpactType>
            {
                new Domain.Entities.FiaImpactType { Id = 1, ReferenceId = "ref-001", Name = "High" },
                new Domain.Entities.FiaImpactType { Id = 2, ReferenceId = "ref-002", Name = "Medium" }
            };

            _mockFiaImpactTypeRepository = FiaImpactTypeRepositoryMocks.GetFiaImpactTypeNameUniqueRepository(_fiaImpactTypes);
            _handler = new GetFiaImpactTypeNameUniqueQueryHandler(_mockFiaImpactTypeRepository.Object);
        }

        [Fact]
        public async Task Handle_Should_Return_True_When_NameExists_WithMatchingId()
        {
            // Arrange
            var query = new GetFiaImpactTypeNameUniqueQuery
            {
                Name = "High",
                Id = "ref-001"
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result);
            _mockFiaImpactTypeRepository.Verify(repo => repo.IsNameExist("High", "ref-001"), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Return_False_When_NameDoesNotExist()
        {
            // Arrange
            var query = new GetFiaImpactTypeNameUniqueQuery
            {
                Name = "Low",
                Id = "ref-999"
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result);
            _mockFiaImpactTypeRepository.Verify(repo => repo.IsNameExist("Low", "ref-999"), Times.Once);
        }
    }
}
