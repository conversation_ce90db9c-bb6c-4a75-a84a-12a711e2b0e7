﻿using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Delete;
using ContinuityPatrol.Application.Features.FiaTemplate.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaTemplate.Commands;

public class DeleteFiaTemplateTests : IClassFixture<FiaTemplateFixture>
{
    private readonly FiaTemplateFixture _fiaTemplateFixture;
    private readonly Mock<IFiaTemplateRepository> _mockFiaTemplateRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteFiaTemplateCommandHandler _handler;

    public DeleteFiaTemplateTests(FiaTemplateFixture fiaTemplateFixture)
    {
        _fiaTemplateFixture = fiaTemplateFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockFiaTemplateRepository = FiaTemplateRepositoryMocks.DeleteFiaTemplateRepository(_fiaTemplateFixture.FiaTemplates);

        _handler = new DeleteFiaTemplateCommandHandler(_mockFiaTemplateRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_DeleteFiaTemplateResponse_When_FiaTemplateDeleted()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteFiaTemplateResponse));

        result.Message.ShouldContain("FIA Template");

        result.Success.ShouldBe(true);

        result.IsActive.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        _mockFiaTemplateRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        _mockFiaTemplateRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.FiaTemplate>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_PublishMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<FiaTemplateDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_CorrectMessage_When_FiaTemplateDeleted()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.Message.ShouldContain("FIA Template");
        result.Message.ShouldContain(_fiaTemplateFixture.FiaTemplates[0].Name);
    }

    [Fact]
    public async Task Handle_Set_IsActive_False_When_FiaTemplateDeleted()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_PublishEvent_With_CorrectName()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        _mockPublisher.Verify(x => x.Publish(
            It.Is<FiaTemplateDeletedEvent>(e => e.Name == _fiaTemplateFixture.FiaTemplates[0].Name),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_Success_True()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.Success.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Should_Not_Throw_Exception_When_ValidCommand()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        var exception = await Record.ExceptionAsync(async () =>
            await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None));

        exception.ShouldBeNull();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_FiaTemplateNotFound()
    {
        var invalidCommand = new DeleteFiaTemplateCommand { Id = Guid.NewGuid().ToString() };

        var exception = await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(invalidCommand, CancellationToken.None));

        exception.ShouldNotBeNull();
        exception.Message.ShouldContain("FiaTemplate");
    }

    [Fact]
    public async Task Handle_Throw_ArgumentException_When_InvalidGuid()
    {
        var invalidCommand = new DeleteFiaTemplateCommand { Id = "invalid-guid" };

        var exception = await Should.ThrowAsync<InvalidArgumentException>(async () =>
            await _handler.Handle(invalidCommand, CancellationToken.None));

        exception.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Throw_ArgumentException_When_EmptyId()
    {
        var invalidCommand = new DeleteFiaTemplateCommand { Id = string.Empty };

        var exception = await Should.ThrowAsync<InvalidArgumentException>(async () =>
            await _handler.Handle(invalidCommand, CancellationToken.None));

        exception.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Return_CorrectIsActiveStatus_When_FiaTemplateDeleted()
    {
        var validGuid = Guid.NewGuid();
        _fiaTemplateFixture.FiaTemplates[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteFiaTemplateCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBe(false);
        result.Success.ShouldBe(true);
    }
}