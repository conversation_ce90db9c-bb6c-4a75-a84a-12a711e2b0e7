﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactCategory.Queries
{
    public class GetFiaImpactCategoryDetailsQueryHandlerTests
    {
        private readonly Mock<IFiaImpactCategoryRepository> _mockRepository;
        private readonly IMapper _mapper;
        private readonly GetFiaImpactCategoryDetailsQueryHandler _handler;
        private readonly List<Domain.Entities.FiaImpactCategory> _fiaImpactCategories;
        private readonly Fixture _fixture;

        public GetFiaImpactCategoryDetailsQueryHandlerTests()
        {
            _fixture = new Fixture();

            // Setup data
            _fiaImpactCategories = _fixture.CreateMany<Domain.Entities.FiaImpactCategory>(3).ToList();
            _fiaImpactCategories[0].IsActive = true; // Ensure active for valid test

            // Setup mapper
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<FiaImpactCategoryProfile>();
            });
            _mapper = config.CreateMapper();

            // Setup mock repo
            _mockRepository = FiaImpactCategoryRepositoryMocks
                .GetFiaImpactCategoryRepository(_fiaImpactCategories);

            _handler = new GetFiaImpactCategoryDetailsQueryHandler(_mapper, _mockRepository.Object);
        }

        [Fact(DisplayName = "Handle_Should_Return_DetailVm_When_Valid_ReferenceId")]
        public async Task Handle_Should_Return_DetailVm_When_Valid_ReferenceId()
        {
            // Arrange
            var validId = _fiaImpactCategories[0].ReferenceId;
            var query = new GetFiaImpactCategoryDetailQuery { Id = validId };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<FiaImpactCategoryDetailVm>(result);
            Assert.Equal(_fiaImpactCategories[0].Name, result.Name);
        }

        [Fact(DisplayName = "Handle_Should_Throw_NotFoundException_When_Not_Exists")]
        public async Task Handle_Should_Throw_NotFoundException_When_Not_Exists()
        {
            // Arrange
            var invalidId = Guid.NewGuid().ToString();
            var query = new GetFiaImpactCategoryDetailQuery { Id = invalidId };

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));
        }

        [Fact(DisplayName = "Handle_Should_Throw_NotFoundException_When_Deactivated")]
        public async Task Handle_Should_Throw_NotFoundException_When_Deactivated()
        {
            // Arrange
            var deactivated = _fiaImpactCategories.First();
            deactivated.IsActive = false;
            var query = new GetFiaImpactCategoryDetailQuery { Id = deactivated.ReferenceId };

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));
        }
    }
}
