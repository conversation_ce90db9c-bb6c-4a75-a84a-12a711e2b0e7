﻿using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Commands;

public class UpdateGlobalVariableTests : IClassFixture<GlobalVariableFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;
    private readonly Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateGlobalVariableCommandHandler _handler;

    public UpdateGlobalVariableTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.UpdateGlobalVariableRepository(_globalVariableFixture.GlobalVariables);

        _handler = new UpdateGlobalVariableCommandHandler(_globalVariableFixture.Mapper, _mockGlobalVariableRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidGlobalVariable_UpdateToGlobalVariablesRepo()
    {
        _globalVariableFixture.UpdateGlobalVariableCommand.Id = _globalVariableFixture.GlobalVariables[0].ReferenceId;

        var result = await _handler.Handle(_globalVariableFixture.UpdateGlobalVariableCommand, CancellationToken.None);

        var globalVariable = await _mockGlobalVariableRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_globalVariableFixture.UpdateGlobalVariableCommand.VariableName, globalVariable.VariableName);
    }

    [Fact]
    public async Task Handle_Return_UpdateGlobalVariableResponse_When_GlobalVariableUpdated()
    {
        _globalVariableFixture.UpdateGlobalVariableCommand.Id = _globalVariableFixture.GlobalVariables[0].ReferenceId;

        var result = await _handler.Handle(_globalVariableFixture.UpdateGlobalVariableCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateGlobalVariableResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_globalVariableFixture.UpdateGlobalVariableCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidGlobalVariableId()
    {
        _globalVariableFixture.UpdateGlobalVariableCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_globalVariableFixture.UpdateGlobalVariableCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var handler = new UpdateGlobalVariableCommandHandler(_globalVariableFixture.Mapper, _mockGlobalVariableRepository.Object, _mockPublisher.Object);

        _globalVariableFixture.UpdateGlobalVariableCommand.Id = _globalVariableFixture.GlobalVariables[0].ReferenceId;

        await handler.Handle(_globalVariableFixture.UpdateGlobalVariableCommand, CancellationToken.None);

        _mockGlobalVariableRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockGlobalVariableRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.GlobalVariable>()), Times.Once);
    }
}