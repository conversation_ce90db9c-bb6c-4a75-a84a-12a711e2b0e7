﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactCategory.Events
{
    public class FiaImpactCategoryUpdatedEventHandlerTests : IClassFixture<FiaImpactCategoryFixture>
    {
        private readonly FiaImpactCategoryFixture _fixture;
        private readonly Mock<ILogger<FiaImpactCategoryUpdatedEventHandler>> _mockLogger;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly FiaImpactCategoryUpdatedEventHandler _handler;
        private readonly List<Domain.Entities.UserActivity> _userActivities;

        public FiaImpactCategoryUpdatedEventHandlerTests(FiaImpactCategoryFixture fixture)
        {
            _fixture = fixture;

            _mockLogger = new Mock<ILogger<FiaImpactCategoryUpdatedEventHandler>>();
            _mockUserService = new Mock<ILoggedInUserService>();
            _userActivities = new List<Domain.Entities.UserActivity>();

            _mockUserActivityRepository = FiaImpactCategoryRepositoryMocks.CreateFiaImpactCategoryEventRepository(_userActivities);

            _mockUserService.Setup(u => u.UserId).Returns("user-456");
            _mockUserService.Setup(u => u.LoginName).Returns("updatedUser");
            _mockUserService.Setup(u => u.RequestedUrl).Returns("/api/fia-impact-category/update");
            _mockUserService.Setup(u => u.IpAddress).Returns("***********");

            _handler = new FiaImpactCategoryUpdatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact(DisplayName = "Handle_Should_Log_And_Create_UserActivity_When_ValidUpdateEventReceived")]
        public async Task Handle_Should_Log_And_Create_UserActivity_When_ValidUpdateEventReceived()
        {
            var updatedEvent = _fixture.FiaImpactCategoryUpdatedEvent;

            await _handler.Handle(updatedEvent, CancellationToken.None);

            Assert.Single(_userActivities);

            var activity = _userActivities.First();
            Assert.Equal("user-456", activity.UserId);
            Assert.Equal("updatedUser", activity.LoginName);
            Assert.Equal("/api/fia-impact-category/update", activity.RequestUrl);
            Assert.Equal("***********", activity.HostAddress);
            Assert.Equal("Update FiaImpactCategory", activity.Action);
            Assert.Equal("FiaImpactCategory", activity.Entity);
            Assert.Equal("Update", activity.ActivityType);
            Assert.Contains(updatedEvent.Name, activity.ActivityDetails);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            //_mockLogger.Verify(log => log.LogInformation($"FiaImpactCategory '{updatedEvent.Name}' updated successfully."), Times.Once);
            _mockLogger.Verify(log => log.Log(LogLevel.Information, It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString() == $"FiaImpactCategory '{updatedEvent.Name}' updated successfully."),
                It.IsAny<Exception>(), It.IsAny<Func<It.IsAnyType, Exception, string>>()!), Times.Once);
        }
    }
}
