﻿using ContinuityPatrol.Application.Features.FiaImpactType.Events.Update;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactType.Events
{
    public class FiaImpactTypeUpdatedEventHandlerTests
    {
        private readonly Mock<ILogger<FiaImpactTypeUpdatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly FiaImpactTypeUpdatedEventHandler _handler;
        private readonly List<Domain.Entities.UserActivity> _userActivityList;

        public FiaImpactTypeUpdatedEventHandlerTests()
        {
            _mockLogger = new Mock<ILogger<FiaImpactTypeUpdatedEventHandler>>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _userActivityList = new List<Domain.Entities.UserActivity>();
            _mockUserActivityRepository = FiaImpactTypeRepositoryMocks.CreateFiaImpactTypeEventRepository(_userActivityList);

            _mockUserService.Setup(x => x.UserId).Returns("TestUserId");
            _mockUserService.Setup(x => x.LoginName).Returns("TestLogin");
            _mockUserService.Setup(x => x.IpAddress).Returns("***********");
            _mockUserService.Setup(x => x.RequestedUrl).Returns("/api/fia-impact-type");

            _handler = new FiaImpactTypeUpdatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_Should_Log_And_Save_UserActivity_When_Updated()
        {
            // Arrange
            var updateEvent = new FiaImpactTypeUpdatedEvent
            {
                Name = "UpdatedImpact"
            };

            // Act
            await _handler.Handle(updateEvent, CancellationToken.None);

            // Assert
            Assert.Single(_userActivityList);
            var activity = _userActivityList.First();

            Assert.Equal("TestUserId", activity.UserId);
            Assert.Equal("TestLogin", activity.LoginName);
            Assert.Equal("***********", activity.HostAddress);
            Assert.Equal("/api/fia-impact-type", activity.RequestUrl);
            Assert.Equal("Update FiaImpactType", activity.Action);
            Assert.Equal("FiaImpactType", activity.Entity);
            Assert.Equal(ActivityType.Update.ToString(), activity.ActivityType);
            Assert.Contains("UpdatedImpact", activity.ActivityDetails);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _mockLogger.Verify(
                log => log.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("UpdatedImpact")),
                    It.IsAny<Exception>(),
                    (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()
                ),
                Times.Once
            );
        }
    }
}
