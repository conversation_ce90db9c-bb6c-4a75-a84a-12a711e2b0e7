﻿@using ContinuityPatrol.Domain.ViewModels.PageWidgetModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<link href="~/css/pagebuilder.css" rel="stylesheet" />
<style>
    .accordion-button::after {
        background-size: 0.9rem !important;
    }
</style>

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-platform-name"></i><span id="WidgetMainTitle">Widget</span></h6>
            <div class="btn-group gap-2">
                <div class="input-group hideshowwidget">
                    <input type="search" id="search-widget" class="form-control" placeholder="Search" autocomplete="off">
                </div>
                <button type="button" id="backblankpage" class="btn btn-primary btn-sm rounded-2"><i class="cp-add me-1"></i>Create</button>
            </div>
        </div>
        <div class="card-body pt-0" style="height:calc(100vh - 120px); overflow:auto;">
            <div class="Choose-Template">
                <div class="row" id="homepageModal">
                   
                </div>
            </div>
            <div id="createpageModal" class="h-100">
                <div class="row h-100">
                    <div class="col-3">
                        <div class="">
                            <h6><i class="cp-custom me-1"></i>Custom</h6>
                            <ul class="drag nav">
                              
                                <li class="widget_btnDrag customChartDropdown" name="customChartDropdown" draggable="true" ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-circle-downarrow"></span>Dropdown</button>
                                </li>
                                <li class="widget_btnDrag customTable" name="customTable" draggable="true" ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-table fs-5"></span>Table</button>
                                </li>
                                <li class="widget_btnDrag customList" name="customList" draggable="true" ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-form-list fs-5"></span>List</button>
                                </li>
                                <li class="widget_btnDrag customCard" name="customCard" draggable="true" ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-card fs-5"></span>Card</button>
                                </li>
                                <li class="widget_btnDrag customChart" name="customChart" draggable="true" ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-donut-chart"></span>Chart</button>
                                </li>
                                <li class="widget_btnDrag customDiagram" name="customDiagram" draggable="true"  ondragstart="dragg(event)">
                                        <button class="drag-button" role="button"><span class="cp-solution fs-5"></span>Diagram</button>
                                </li>
                            @*     <li class="widget_btnDrag customChartCalender" name="customChartCalender" draggable="true" ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-calendar"></span>Calendar</button>
                                </li>
                                <li class="widget_btnDrag customChartTabs" name="customChartTabs" draggable="true" ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-generate"></span>Tabs</button>
                                </li> *@
                            </ul>
                        </div>
                        <hr />
                        <div>
                            <h6><i class="cp-table me-1"></i>Prebuild</h6>
                            <ul class="drag nav">
                                <li class="widget_btnDrag preBuildTable" name="preBuildTable" draggable="true" htmlPropertics='<div class="card Card_Design_None mb-0" id="mssqlserver"><div class="card-header card-title d-flex align-items-center justify-content-between"><span title=" Services ">Services</span></div><div class="card-body pt-0 p-2"><table class="table mb-0" id="tableCluster" style="table-layout:fixed"><thead><tr><th title="Service / Process / Workflow Name">Service / Process / Workflow Name</th><th class="text-primary" title="Primary">Server IP/HostName</th><th class="text-info" title="DR">Status</th></tr></thead><tbody id="mssqlserverbody"> <tr><td><i class="text-secondary cp-monitoring-services me-1 fs-6"></i><span>WF_MS_Success</span></td><td><i class="text-secondary cp-ip-address me-1 fs-6"></i>***********</td><td><i class="text-danger cp-fail-back me-1 fs-6"></i>Error</td></tr><tr><td><i class="text-secondary cp-monitoring-services me-1 fs-6"></i><span>WF_MS_Fail</span></td><td><i class="text-secondary cp-ip-address me-1 fs-6"></i>***********</td><td><i class="text-danger cp-fail-back me-1 fs-6"></i>Error</td></tr><tr><td><i class="text-secondary cp-monitoring-services me-1 fs-6"></i><span>server</span></td><td><i class="text-secondary cp-ip-address me-1 fs-6"></i>***********</td><td><i class="text-success cp-reload cp-animate me-1 fs-6"></i>Running</td></tr></tbody></table></div></div>' ondragstart="dragg(event)">
                                    <button class="drag-button p-2" role="button"><span class="cp-table fs-5"></span>Services Table</button>
                                </li>
                                <li class="widget_btnDrag preBuildTablePluggable" name="preBuildTablePluggable" draggable="true" htmlPropertics='<div class="col-12 d-grid"><div class="card Card_Design_None mb-0"><div class="card-header card-title" style="font-size:15px" title="Pluggable Databases">Pluggable Databases</div><div class="card-body pt-0" id="noPluggableimg"><table class="table mb-0" style="table-layout:fixed"><thead><tr><th title="Replication Details">Replication Details</th><th class="text-primary" title="Primary">Primary</th><th class="text-info" title="DR">DR</th></tr></thead><tbody><tr><td class="fw-semibold text-truncate" title="PDB Name"><i class="text-secondary cp-replication-connect me-1 fs-6"></i>PDB Name</td><td class="text-truncate" id="PR_PDB_Name"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td><td class="text-truncate" id="DR_PDB_Name"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td></tr><tr><td class="fw-semibold text-truncate" title="CONNECTION ID"><i class="text-secondary cp-name me-1 fs-6"></i>CONNECTION ID</td><td class="text-truncate" id="PR_CONNECTION_ID"><span><i class="text-success cp-circle-switch me-1 fs-6"></i></span></td><td class="text-truncate" id="DR_CONNECTION_ID"><span><i class="text-success cp-circle-switch me-1 fs-6"></i></span></td></tr><tr><td class="fw-semibold text-truncate" title="PDB ID"><i class="text-secondary cp-replication-connect me-1 fs-6"></i>PDB ID</td><td class="text-truncate" id="PR_PDB_ID"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td><td class="text-truncate" id="DR_PDB_ID"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td></tr><tr><td class="fw-semibold text-truncate" title="PDB MODE"><i class="text-secondary cp-replication-connect me-1 fs-6"></i>PDB MODE</td><td class="text-truncate" id="PR_PDB_MODE"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td><td class="text-truncate" id="DR_PDB_MODE"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td></tr><tr><td class="fw-semibold text-truncate" title="LOGGING"><i class="text-secondary cp-log-file-name me-1 fs-6"></i>LOGGING</td><td class="text-truncate" id="PR_LOGGING"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td><td class="text-truncate" id="DR_LOGGING"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td></tr><tr><td class="fw-semibold text-truncate" title="FORCE_LOGGING"><i class="text-secondary cp-log-file-name me-1 fs-6"></i>FORCE_LOGGING</td><td class="text-truncate" id="PR_FORCE_LOGGING"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td><td class="text-truncate" id="DR_FORCE_LOGGING"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td></tr><tr><td class="fw-semibold text-truncate" title="RECOVERY_STATUS"><i class="text-secondary cp-time me-1 fs-6"></i>RECOVERY_STATUS</td><td class="text-truncate" id="PR_RECOVERY_STATUS"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td><td class="text-truncate" id="DR_RECOVERY_STATUS"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td></tr><tr><td class="fw-semibold text-truncate" title="PDB SIZE"><i class="text-secondary cp-replication-connect me-1 fs-6"></i>PDB SIZE</td><td class="text-truncate" id="PR_PDB_SIZE"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td><td class="text-truncate" id="DR_PDB_SIZE"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td></tr></tbody></table></div></div></div>' ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-plugin-manager fs-5"></span>Pluggable</button>
                                </li>
                                <li class="widget_btnDrag preBuildTableASM" name="preBuildTableASM" draggable="true" htmlPropertics='<div class="col-12 d-grid pageBuilderInfraId"><div class="card Card_Design_None mb-2"><div class="card-header card-title" title="ASM Details">ASM Details </div><div class="card-body pt-0 p-2"><table class="table mb-0"><thead><tr><th title="ASM">ASM</th><th class="text-primary" title="Primary">Primary</th><th class="text-info" title="DR">DR</th></tr></thead><tbody><tr><td class="fw-semibold text-truncate" title="Services">Services</td><td class="text-truncate"><table class="table mb-0" id="asmPrimaryData" style="table-layout:fixed"><thead><tr><th>#</th><th title="Name">Name</th><th title="State">State</th><th title="Type">Type</th></tr></thead><tbody id="prASMData"></tbody></table></td><td class="text-truncate"><table class="table mb-0" id="asmDRData" style="table-layout:fixed"><thead><tr><th>#</th><th title="Name">Name</th><th title="State">State</th><th title="Type">Type</th></tr></thead><tbody id="drASMData"></tbody></table></td></tr></tbody></table></div></div></div>' ondragstart="dragg(event)">
                                    <button class="drag-button" role="button"><span class="cp-asm fs-5"></span>ASM</button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-6 d-grid border rounded widgetCreationCardmenu px-0" id="widgetCreationCard" ondrop="drop(event)" ondragover="allowDrop(event)">
                    </div>
                    <div class="col-3 collapse " id="collapseExample">
                        <div class="card border mb-0">
                            <div class="card-header card-title p-2 pb-0">
                                <div class="nav nav-pills flex-column flex-sm-row" id="myTab" role="tablist">
                                    <button class="flex-sm-fill text-sm-center nav-link active fw-normal" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane" type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">
                                        Design
                                    </button>
                                    <button class="flex-sm-fill text-sm-center nav-link fw-normal" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane" type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false">
                                        Content
                                    </button>
                                    <button class="flex-sm-fill text-sm-center nav-link fw-normal" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact-tab-pane" type="button" role="tab" aria-controls="contact-tab-pane" aria-selected="false">
                                        Interaction
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-2" style="height: calc(100vh - 215px); overflow-y: auto;">
                                <div class="tab-content" id="myTabContent">
                                    <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">
                                        <div class="accordion accordion-flush CreateWidget_accordion" id="accordionFlushExample">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseOne">
                                                        Basic Items
                                                    </button>
                                                </h2>
                                                <div id="flush-collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                    <div class="accordion-body p-3">
                                                        <div class="d-flex gap-2">
                                                            <div class="w-50">
                                                                <div class="form-group">
                                                                    <label class="form-label">Width</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-file-size"></i></span>
                                                                        <select class="form-select" data-placeholder="Select Width">
                                                                            <option selected>Col-1</option>
                                                                            <option>Col-2</option>
                                                                            <option>Col-3</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="w-50">
                                                                <div class="form-group">
                                                                    <label class="form-label">Height</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-file-size"></i></span>
                                                                        <select class="form-select" data-placeholder="Select Height">
                                                                            <option selected>Auto</option>
                                                                            <option>100%</option>
                                                                            <option>50%</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-item tableDetails">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed buttonName" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                                        List
                                                    </button>
                                                </h2>
                                                <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                    <div class="accordion-body p-3">
                                                        <div class="d-flex justify-content-between">
                                                            <div class="form-inline rowGroup">
                                                                <label class="form-label rowlabel">Row</label>
                                                                <span class="cp-circle-plus text-primary mx-2" role="button" id="rowAdd"></span>
                                                                <span class="cp-circle-minus text-secondary" role="button" id="rowMinus"></span>
                                                            </div>
                                                            <div class="form-inline columnGroup">
                                                                <label class="form-label">Column</label>
                                                                <span class="cp-circle-plus text-primary mx-2" role="button" id="columnAdd"></span>
                                                                <span class="cp-circle-minus text-secondary " role="button" id="columnMinus"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseTwo">
                                                        Data
                                                    </button>
                                                </h2>
                                                <div id="flush-collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                    <div class="accordion-body p-3">
                                                        <div class="month" id="widgetChartData">
                                                            <ul class="list-group border-secondary-subtle mb-2 chartListData" role="button">
                                                                <li class="list-group-item d-flex justify-content-between align-items-center chartDetails" id="ArchiveLogHour" widgetTitle="Archive Log Generation Hourly Last 24Hrs(Count)" name="ArchiveLogHour">
                                                                    Archive Log Generation Hourly
                                                                    <span class="cp-note text-primary fs-5"></span>
                                                                </li>
                                                                <li class="list-group-item d-flex justify-content-between align-items-center chartDetails" widgetTitle="Archive Log Generation Last 24Hrs(Size)" name="ArchiveLogDay">
                                                                    Archive Log Generation Days
                                                                    <span class="cp-note text-primary fs-5"></span>
                                                                </li>
                                                                <li class="list-group-item d-flex justify-content-between align-items-center chartDetails" widgetTitle="Archive Log Generation Weekly (Size)" name="ArchiveLogWeek">
                                                                    Archive Log Generation Weekly
                                                                    <span class="cp-note text-primary fs-5"></span>
                                                                </li>
                                                                <li class="list-group-item d-flex justify-content-between align-items-center chartDetails" name="bar">
                                                                    Bar chart
                                                                    <span class="cp-note text-primary fs-5"></span>
                                                                </li>
                                                                <li class="list-group-item  d-flex justify-content-between align-items-center chartDetails" name="line">
                                                                    Line chart
                                                                    <span class="cp-note text-primary fs-5"></span>
                                                                </li>
                                                                <li class="list-group-item d-flex justify-content-between align-items-center chartDetails" name="pie">
                                                                    Pie chart
                                                                    <span class="cp-note text-primary fs-5"></span>
                                                                </li>
                                                                <li class="list-group-item d-flex justify-content-between align-items-center chartDetails" name="donut">
                                                                    Donut chart
                                                                    <span class="cp-note text-primary fs-5"></span>
                                                                </li>
                                                               
                                                                <li class="list-group-item d-flex justify-content-between align-items-center chartDetails" name="bubble">
                                                                    Bubble chart
                                                                    <span class="cp-note text-primary fs-5"></span>
                                                                </li>

                                                            </ul>
                                                            <div class="form-group datasetModal">
                                                                <label class="form-label">Dataset</label>
                                                                <div class="input-group">
                                                                    <span class="input-group-text"><i class="cp-dataset-form"></i></span>
                                                                    <select class="form-select selectDataset" id="ddlColumnName" data-placeholder="Select Dataset">
                                                                    </select>
                                                                </div>
                                                            </div>
                                                     
                                                            <div class="form-group x-axisgroup">
                                                                <label class="form-label" title="x-axis name">x-axis name</label>
                                                                <div class="input-group">
                                                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                                                    <select class="form-select selectxaxis" id="xaxisvalue" data-placeholder="Enter x-axis name">
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="form-group y-axisgroup">
                                                                <label class="form-label" title="y-axis name">
                                                                    y-axis name
                                                                </label>
                                                                <div class="input-group">
                                                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                                                    <select class="form-select selectyaxis" id="yaxisvalue" data-placeholder="Enter y-axis name">
                                                                    </select>
                                                                </div>
                                                            </div>
                                                          
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseChart" aria-expanded="false" aria-controls="flush-collapseOne">
                                                        Chart Settings
                                                    </button>
                                                </h2>
                                                <div id="flush-collapseChart" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                    <div class="accordion-body p-3">
                                                        <div class="d-flex gap-3">
                                                            <div class="w-50">
                                                                <div class="form-group">
                                                                    <label class="form-label">X Axis</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-dataset-form"></i></span>
                                                                        <select class="form-select" data-placeholder="Select X Axis">
                                                                            <option selected>100</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="w-50">
                                                                <div class="form-group">
                                                                    <label class="form-label">Y Axis</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-dataset-form"></i></span>
                                                                        <select class="form-select" data-placeholder="Select Y Axis">
                                                                            <option selected>100</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="d-flex gap-3">
                                                            <div class="w-25">
                                                                <div class="form-group">
                                                                    <label class="form-label">Column 01</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-colour-picker"></i></span>
                                                                        <input type="color" class="form-control form-control-color" id="exampleColorInput" value="#41c200" title="Choose your color">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="w-25">
                                                                <div class="form-group">
                                                                    <label class="form-label">Column 02</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-colour-picker"></i></span>
                                                                        <input type="color" class="form-control form-control-color" id="exampleColorInput" value="#ff9c08" title="Choose your color">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="w-25">
                                                                <div class="form-group">
                                                                    <label class="form-label">Scroll Bar</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-dataset-form"></i></span>
                                                                       @*  <select class="form-select" data-placeholder="Select Total Bar">
                                                                            <option selected>06</option>
                                                                        </select> *@
                                                                        <input type="text" class="form-control form-control-color" id="scrolbarTextVH">
                                                                    </div>
                                                                </div>

                                                            </div>
                                                            <div class="w-25">
                                                                <div class="form-group">
                                                                    <label class="form-label"></label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-dataset-form"></i></span>
                                                                        @*  <select class="form-select" data-placeholder="Select Total Bar">
                                                                            <option selected>06</option>
                                                                        </select> *@
                                                                        <input type="text" class="form-control form-control-color" id="scrolbarText">
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" value="" id="reverseCheck1">
                                                            <label class="form-check-label" for="reverseCheck1">Show legend</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
                                        <div class="form-group">
                                            <label class="form-label">Data Model</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cp-configure-dataset"></i></span>
                                                <input class="form-control" type="date" placeholder="Select Date" />
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Data Model</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cp-configure-dataset"></i></span>
                                                <select class="form-select" data-placeholder="Select Dataset">
                                                    <option selected>
                                                        Select Dataset
                                                    </option>
                                                    <option>
                                                        Dataset _ 01 _ lorem_demo
                                                    </option>
                                                    <option>
                                                        Dataset _ 01 _ lorem_demo
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        @* <div class="card Card_Design_None">
                                            <div class="card-header header pt-0 p-2"><span>Filter</span><a href="#" class="text-decoration-none">Add Action +</a></div>
                                            <div class="card-body border p-2">
                                                <ul class="list-group list-group-flush">
                                                    <li class="list-group-item px-0">
                                                        <div class="header mb-2">
                                                            <span>Action</span>
                                                            <div class="d-flex gap-2">
                                                                <div role="button"><i class="cp-checks"></i></div>
                                                                <div role="button"><i class="cp-Delete"></i></div>
                                                            </div>
                                                        </div>
                                                        <div class="d-flex gap-3">
                                                            <input class="form-control border border-light-subtle" type="text" placeholder="Equal to" />
                                                            <input class="form-control border border-light-subtle" type="text" placeholder="Equal to" />
                                                            <input class="form-control border border-light-subtle" type="text" placeholder="Success / Failed" />
                                                        </div>
                                                    </li>
                                                    <li class="list-group-item px-0">
                                                        <div class="header mb-2">
                                                            <span>Action</span>
                                                            <div class="d-flex gap-2">
                                                                <div role="button"><i class="cp-checks"></i></div>
                                                                <div role="button"><i class="cp-Delete"></i></div>
                                                            </div>
                                                        </div>
                                                        <div class="d-flex gap-3">
                                                            <input class="form-control border border-light-subtle" type="text" placeholder="Equal to" />
                                                            <input class="form-control border border-light-subtle" type="text" placeholder="Equal to" />
                                                            <input class="form-control border border-light-subtle" type="text" placeholder="Success / Failed" />
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div> *@
                                    </div>
                                    <div class="tab-pane fade" id="contact-tab-pane" role="tabpanel" aria-labelledby="contact-tab" tabindex="0">
                                        <button class="btn btn-sm btn-primary rounded-1 float-end mb-2 ConditionSettingsModal" data-bs-toggle="modal" data-bs-target="#ConditionSettingsModal" type="button">Add Condition +</button>
                                        <div class="accordion accordion-flush CreateWidget_accordion addconditionData" id="accordionFlushExample">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="text-end">
                                    <button class="btn btn-sm btn-primary btn_preview" type="button">Preview</button>
                                    <button class="btn btn-sm btn-secondary btn_cancel" type="button">Cancel</button>
                                    <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="contextMenu dropdown" id="contextMenu">
    <ul class="UlContextBtn dropdown-menu dropdown-menu-lg-end fs-8" style="display:block;">
        <li id="btnEdit">
            <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                <span><i class="cp-cut me-1  fs-7"></i>Edit</span>
            </a>
        </li>
        <li id="btnDelete">
            <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                <span><i class="cp-Delete  me-1 fs-7"></i>Delete</span>
            </a>
        </li>
    </ul>
</div>

<!-- Business Tree Overview Modal -->
<div class="modal fade" id="previewZoom" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-business-service"></i><span>Preview</span></h6>
                <button type="button" class="btn-close ms-2 " data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body py-2">
                <div class="card-body pt-0 pb-1" style="height:600px;">
                    <div class="preview-chart" id="wrapper1"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="TitleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-grid"></i><span>Widget Title</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <div class="form-label">Widget Title</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input class="form-control" id="widgetTitle" type="text" placeholder="Enter Widget Title" />
                        </div>
                        <span id="widgetTitleError"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="widgetBuilderApply">Apply</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="CardModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Company Configuration"><i class="cp-grid"></i><span>Card Title</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>

                    <div class="form-group">
                        <label class="form-label">Card</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                            <select class="form-select-modal" id="selectCardType" data-live-search="true" data-placeholder="Select Type">
                                <option value="">Select</option>
                                <option value="DatabaseSize">Database Size</option>
                                <option value="LogSequence">Log Sequence</option>
                                <option value="CurrentSCN">Current SCN</option>
                                <option value="ReplicationConnectState">Replication Connect State</option>
                                <option value="DataLag">DataLag</option>
                                <option value="TNSService">TNS Service Details</option>
                            </select>

                            <span id="componentTypeError"></span>
                        </div>

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="widgetCardApply">Apply</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="ConditionalModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Company Configuration"><i class="cp-grid"></i><span>Card Title</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>

                    <div class="form-group">
                        <label class="form-label">Card</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                            <select class="form-select-modal" id="selectCardType" data-live-search="true" data-placeholder="Select Type">
                                <option value="">Select</option>
                                <option value="DatabaseSize">Database Size</option>
                                <option value="LogSequence">Log Sequence</option>
                                <option value="CurrentSCN">Current SCN</option>
                                <option value="ReplicationConnectState">Replication Connect State</option>
                                <option value="DataLag">DataLag</option>
                                <option value="TNSService">TNS Service Details</option>
                            </select>

                            <span id="componentTypeError"></span>
                        </div>

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="widgetCardApply">Apply</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="solutionCardModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Company Configuration"><i class="cp-grid"></i><span>Solution </span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label class="form-label">Solution Type</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                            <select class="form-select-modal selectsolutionType" id="selectsolutionType" data-live-search="true" data-placeholder="Select Type">
                                <option value="">Select</option>
                                <option value="Mysql">Mysql</option>
                                <option value="MssqlNLS">Mssql</option>
                                <option value="mssqldbmirroring">Mssql DB Mirroring</option>
                                <option value="MssqlAlwaysOn">MssqlAlwaysOn</option>
                                <option value="Oracle">Oracle</option>
                                <option value="Postgres">Postgres</option>
                                <option value="HyperV">HyperV</option>
                                <option value="OracleRac">OracleRac</option>
                                <option value="RSync">RSync</option>
                                <option value="RSyncAppReplication">RSyncAppReplication</option>
                                <option value="RoboCopy">RoboCopy</option>
                                <option value="DB2HADR">DB2HADR</option>
                                <option value="MongoDB">MongoDB</option>
                                <option value="DataSyncAppReplication">DataSyncAppReplication</option>
                                <option value="SybaseRSHADR">SybaseRSHADR</option>

                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">InfraObject Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                            <select class="form-select-modal selectInfraObject" id="selectInfraObject" data-live-search="true" data-placeholder="Select InfraObject Name">
                                

                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="solutionCardApply">Apply</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="headingModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-grid"></i><span>Title Editing</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <div class="form-label">Label Title</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input class="form-control" id="labelTitle" type="text" placeholder="Enter Label Name" />
                        </div>
                        <span id="pagelabelTitle_error"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="widgetLabelApply">Update</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="iconModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-body">
                <div class="row mb-2">
                    <div class="col-7">
                        <label class="fw-semibold">Image Builder</label>
                    </div>
                    <div class="col-5">
                        <div class="d-flex align-items-center">
                            <label>Color</label>
                            <div class="input-group">
                                <select class="form-select-modal selectColor" id="selectColor" data-live-search="true" data-placeholder="Select Color">
                                    <option value="select">Select Color</option>
                                    <option value="text-primary">primary</option>
                                    <option value="text-secondary">secondary</option>
                                    <option value="text-success">success</option>
                                    <option value="text-danger">danger</option>
                                    <option value="text-warning">warning</option>
                                    <option value="text-info">info</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <table class="table table-bordered cellActive">
                    <tbody>
                          <tr class="text-start"><th colspan="14">Common</th></tr>
                        <tr>
                            <td class="imagepicker Active" icon="cp-up-linearrow"><i title="Line Arrow" class="cp-up-linearrow custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-reload"><i title="Reload" class="cp-reload custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-unique-name"><i title="Database Uniquename" class="cp-database-unique-name custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-relationship-state"><i title="Relationship State" class="cp-relationship-state custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-health"><i title="Health" class="cp-health custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-online"><i title="online" class="cp-online custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-disable"><i title="Disable" class="cp-disable custom-cursor-on-hover"></i></td>  
                            <td class="imagepicker " icon="cp-replication-source"><i title="Replication Source" class="cp-replication-source custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-drill-action-type"><i title="CP Drill Action Type" class="cp-drill-action-type custom-cursor-on-hover"></i></td>
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-id"><i title="Reload" class="cp-id custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-priority"><i title="Priority" class="cp-priority custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-pending"><i title="Pending" class="cp-pending custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-report-path"><i title="Report path" class="cp-report-path custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-files"><i title="Files" class="cp-files custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-connected"><i title="Connected" class="cp-connected custom-cursor-on-hover"></i></td>
                            <td class="imagepicker " icon="cp-location"><i title="Location" class="cp-location custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-zip-file"><i title="Zip File" class="cp-zip-file custom-cursor-on-hover"></i></td>
                        </tr>
                        <tr class="text-start"><th colspan="14">Mysql</th></tr>
                        <tr>

                            <td class="imagepicker" icon="cp-control-file-type"><i title="Control Filetype" class="cp-control-file-type custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-control-file-name"><i title="Control Filename" class="cp-control-file-name custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-cluster-database"><i title="Cluster Database" class="cp-cluster-database custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-reset-log-change"><i title="Reset Logchange" class="cp-reset-log-change custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-parameter-file"><i title="Parameter File" class="cp-parameter-file custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-platform-name"><i title="Platform name" class="cp-platform-name custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-database-page"><i title="Database page" class="cp-database-page custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-folder-file"><i title="Files" class="cp-folder-file custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-unique"><i title="database" class="cp-database-unique custom-cursor-on-hover"></i></td>
                            
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-db-create-online"><i title="Create online" class="cp-db-create-online custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-file-location"><i title="File-location" class="cp-file-location custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-roate-settings"><i title="Roate settings" class="cp-roate-settings custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-generate"><i title="generate" class="cp-generate custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-warning"><i title="Warning" class="cp-warning custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-name"><i title="name" class="cp-name custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-synbase-backup-server"><i title="Synbase Backup Server" class="cp-synbase-backup-server custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-active-dg-enable"><i title="Active DG Enable" class="cp-active-dg-enable custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-affirm"><i title="Affirm" class="cp-affirm custom-cursor-on-hover"></i></td>
                        </tr>
                        <tr class="text-start"><th colspan="14">MssqlNLS</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-remote-login"><i title="Remote Login" class="cp-remote-login" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-hp"><i title="HP" class="cp-hp custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-refresh-clock"><i title="Refresh Clock" class="cp-refresh-clock custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-time-one"><i title="Time one" class="cp-time-one custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-Veeam"><i title="Veeam" class="cp-Veeam custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-stand-storage"><i title="Storage" class="cp-stand-storage custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-hds"><i title="hds" class="cp-hds custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-netapp"><i title="Netapp" class="cp-netapp" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-transaction"><i title="Transaction" class="cp-transaction" cursorshover="true"></i></td>
                            
                        </tr>
                        <tr>
                          
                            <td class="imagepicker" icon="cp-system-management-tool"><i title="systemmanagementtool" class="cp-system-management-tool" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-protection-mode"><i title="Protection Mode" class="cp-protection-mode custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-right-left"><i title="Right Left" class="cp-right-left custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-veritas-cluster"><i title="veritas cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-virtualization"><i title="virtualization" class="cp-virtualization custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-aix"><i title="Aix" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-oracle-solaris"><i title="solaris" class="cp-oracle-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-fal-client"><i title="Fal client" class="cp-fal-client custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-enables"><i title="Enable" class="cp-enables custom-cursor-on-hover"></i></td>
                            
                        </tr>
                        <tr class="text-start"><th colspan="14">MssqlAlwaysOn</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-web"><i title="web" class="cp-web custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-power-cli"><i title="power-cli" class="cp-power-cli custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-workflow"><i title="Workflow" class="cp-workflow custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-replication-rotate"><i title="Replication Rotate" class="cp-replication-rotate custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-replication-type"><i title="Replication Type" class="cp-replication-type custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-web"><i title="Web" class="cp-web"></i> </td>
                            <td class="imagepicker" icon="cp-exchange"><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-oracle-ops"><i title="Sunllom" class="cp-oracle-ops custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td class="imagepicker" icon="cp-apply-finish-time"><i title="Apply FinishTime" class="cp-apply-finish-time custom-cursor-on-hover" cursorshover="true"></i></td>
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-soft-layer"><i title="Infoblox" class="cp-soft-layer custom-cursor-on-hover"></i> </td>
                            <td class="imagepicker" icon="cp-solution"><i title="Solution" class="cp-solution custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-token"><i title="Token" class="cp-token custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-general"><i title="General" class="cp-general custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-create-Logger"><i title="Create-Logger" class="cp-create-Logger custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-conditional"><i title="Conditional" class="cp-conditional custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-loop"><i title="Loop" class="cp-loop custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-up-linearrow"><i title="Up Linearrow" class="cp-up-linearrow custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-server-times"><i title="Server Times" class="cp-server-times custom-cursor-on-hover"></i></td>
                            

                        </tr>
                        <tr class="text-start"><th colspan="14">Oracle</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-log-archive-config"><i title="log archive config" class="cp-log-archive-config custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-if"><i title="If" class="cp-if custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-circle-switch"><i title="circle-switch" class="cp-circle-switch custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-data-source"><i title="data-source" class="cp-data-source custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-rule"><i title="Rule" class="cp-rule custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-wait"><i title="Wait" class="cp-wait custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-common"><i title="Common" class="cp-common custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-security"><i title="Security" class="cp-security custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-backup_data"><i title="Backup Data" class="cp-backup_data custom-cursor-on-hover"></i></td>
                            
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-powershell"><i title="Powershell" class="cp-powershell custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-conversion"><i title="Conversion" class="cp-conversion custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-script"><i title="Script" class="cp-script custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-SSH"><i title="SSH" class="cp-SSH custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-connect"><i title="Connect " class="cp-connect custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-error-handing"><i title="Error-handing " class="cp-error-handing custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-stringutility"><i title="Stringutility" class="cp-stringutility custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-wmi"><i title="Wmi" class="cp-wmi custom-cursor-on-hover"></i></td>
                           
                        </tr>
                        <tr class="text-start"><th colspan="14">Postgres</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-log-file-name"><i title="Log Files" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-configure-settings"><i title="Configure Settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-prsite"><i title="prsite" class="cp-prsite custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-mongo-db"><i title="Mongo DB" class="cp-mongo-db"></i></td>
                            <td class="imagepicker" icon="cp-nutanix"><i title="Nutanix" class="cp-nutanix custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-IBM-AIX"><i title="IBM-AIX" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-fal-server"><i title="Mainframe" class="cp-fal-server custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-ebdr"><i title="EBDR" class="cp-ebdr custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-Idle"><i title="Idle" class="cp-Idle custom-cursor-on-hover"></i></td>
                            
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-double-take"><i title="Double Take" class="cp-double-take custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-H3PAR"><i title="H3PAR" class="cp-H3PAR custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-instance-id"><i title="instance id" class="cp-instance-id custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-type"><i title="database type" class="cp-database-type custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-server-cloud"><i title="Mainframe" class="cp-server-cloud custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-server"><i title="Server" class="cp-server custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database"><i title="Database" class="cp-database custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-cloud"><i title="Cloud" class="cp-cloud custom-cursor-on-hover"></i></td>

                        </tr>
                        <tr class="text-start"><th colspan="14">HyperV</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-server-role"><i title="Server Role" class="cp-server-role custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-dataguard-status"><i title="Dataguard Status" class="cp-dataguard-status custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-switch-over"><i title="Switch Over" class="cp-switch-over custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-configure-settings"><i title="configure settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-business-service"><i title="Business Service" class="cp-business-service custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-success"><i title="Database Success" class="cp-database-success custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-instance-name"><i title="Instance Name" class="cp-instance-name custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-time"><i title="time" class="cp-time custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-disables"><i title="Disables" class="cp-disables custom-cursor-on-hover"></i></td>
                            
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-left-right"><i title="Left Right" class="cp-left-right custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-cluster-database"><i title="Cluster Database" class="cp-cluster-database custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-sid"><i title="Database Sid" class="cp-database-sid custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-endpoint-port-number"><i title="Endpoint Portnumber" class="cp-endpoint-port-number custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-hcl"><i title="HCL" class="cp-hcl custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-role"><i title="Database Role" class="cp-database-role custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-log-file-name"><i title="Logfile Name" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-refresh"><i title="refresh" class="cp-refresh custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-updatability"><i title="Database Updatability" class="cp-database-updatability custom-cursor-on-hover"></i></td>
                            

                        </tr>
                        <tr class="text-start"><th colspan="14">OracleRac</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-Storage-chain"><i title="Storage Chain" class="cp-Storage-chain custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-mysql-data"><i title="Mysql Data" class="cp-mysql-data custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-time"><i title="Database Time" class="cp-database-time custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-last-copied-transaction"><i title="Last Copied Transaction" class="cp-last-copied-transaction custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-table-clock"><i title="Table Clock" class="cp-table-clock custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-open-mode"><i title="Open mode" class="cp-open-mode custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-apply-lag"><i title="Apply Lag" class="cp-apply-lag custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-disable"><i title="Disable" class="cp-disable custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-file-copy-job-execution"><i title="File Copy Job Execution" class="cp-file-copy-job-execution custom-cursor-on-hover"></i></td>
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-reload"><i title="Reload" class="cp-reload custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-ip-address"><i title="Ip-Address" class="cp-ip-address custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-monitoring-services"><i title="Monitoring Services" class="cp-monitoring-services custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-fail-back"><i title="Fail Back" class="cp-fail-back custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-archive-mode"><i title="Archive mode" class="cp-archive-mode custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-instant-name"><i title="Instant name" class="cp-instant-name custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-custom-server-4"><i title="Custom Server4" class="cp-custom-server-4 custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-custom-server-3"><i title="Custom Server3" class="cp-custom-server-3 custom-cursor-on-hover"></i></td>


                        </tr>
                        <tr class="text-start"><th colspan="14">RSync</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-file-size"><i title="File size" class="cp-file-size custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-last-backup"><i title="Last backup" class="cp-last-backup custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-form-name"><i title="Form Name" class="cp-form-name custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-user-role"><i title="User Role" class="cp-user-role custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-dr"><i title="Dr" class="cp-dr custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-standby-file"><i title="Standby-File" class="cp-standby-file custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-standby-redo-logs"><i title="Redo logs" class="cp-standby-redo-logs custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-success"><i title="Success" class="cp-success custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-download"><i title="Download" class="cp-download custom-cursor-on-hover"></i></td>
                            
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-physical-drsite"><i title="Physical Drsite" class="cp-physical-drsite custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-list-prsite"><i title="List Prsite" class="cp-list-prsite custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-file-edits"><i title="File Edits" class="cp-file-edits custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-error"><i title="Error" class="cp-error custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-warning"><i title="Database Warning" class="cp-database-warning custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-folder-server"><i title="folder server" class="cp-folder-server custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-retry"><i title="retry" class="cp-retry custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-calendar"><i title="calendar" class="cp-calendar custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-file-calender"><i title="File Calender" class="cp-file-calender custom-cursor-on-hover"></i></td>
                            
                        </tr>
                        <tr>
                          
                            <td class="imagepicker" icon="cp-estimated-time"><i title="Estimated Time" class="cp-estimated-time custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-warning"><i title="Database Warning" class="cp-database-warning custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-arrow-page"><i title="arrow page" class="cp-arrow-page custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-location-down"><i title="location down" class="cp-location-down custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-estimated-time"><i title="Estimated Time" class="cp-estimated-time custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-apply-finish-time"><i title="Finish Time" class="cp-apply-finish-time custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-folder-file"><i title="Folder File" class="cp-folder-file custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-disk-controller"><i title="Disk Controller" class="cp-disk-controller custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-primary"><i title="Primary" class="cp-primary custom-cursor-on-hover"></i></td>

                        </tr>
                        <tr class="text-start"><th colspan="14">RSyncAppReplication</th></tr>
                        <tr>
                           
                            <td class="imagepicker" icon="cp-replication-on"><i title="Replication On" class="cp-replication-on custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-disconnected"><i title="Disconnected" class="cp-disconnected custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-asynchronous"><i title="Asynchronous" class="cp-asynchronous custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-transport-lag"><i title="transport" class="cp-transport-lag custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-folder-file"><i title="Folder File" class="cp-folder-file custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-disk-controller"><i title="Disk Controller" class="cp-disk-controller custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-replication-on"><i title="Replication On" class="cp-replication-on custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-disconnected"><i title="Disconnected" class="cp-disconnected custom-cursor-on-hover"></i></td>
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-asynchronous"><i title="Asynchronous" class="cp-asynchronous custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-transport-lag"><i title="transport" class="cp-transport-lag custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-server-cloud"><i title="Server Cloud" class="cp-server-cloud custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-Storage-three"><i title="Storage Three" class="cp-Storage-three custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-location"><i title="location" class="cp-location custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-Storage-chain"><i title="Storage chain" class="cp-Storage-chain custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-settings"><i title="Settings" class="cp-settings custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-cluster-database"><i title="Cluster Database" class="cp-cluster-database custom-cursor-on-hover"></i></td>
                        </tr>
                        <tr class="text-start"><th colspan="14">RoboCopy</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-online"><i title="Online" class="cp-online custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-health-success"><i title="Health Success" class="cp-health-success custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-play-resume"><i title="Play Resume" class="cp-play-resume custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-stand-server"><i title="Stand Server" class="cp-stand-server custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-version"><i title="Version" class="cp-version custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-active-dg-enable"><i title="active enable" class="cp-active-dg-enable custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-agree"><i title="agree" class="cp-agree custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-replication-connect"><i title="replication connect" class="cp-replication-connect custom-cursor-on-hover"></i></td>
                        </tr>
                        <tr class="text-start"><th colspan="14">AzureStorage</th></tr>
                        <tr>
                            <td class="imagepicker" icon="cp-datas"><i title="datas" class="cp-datas custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-report"><i title="Report" class="cp-report custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-stand-server"><i title="Stand Server" class="cp-stand-server custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-version"><i title="Version" class="cp-version custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-active-dg-enable"><i title="active enable" class="cp-active-dg-enable custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-agree"><i title="agree" class="cp-agree custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-replication-connect"><i title="replication connect" class="cp-replication-connect custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-datalog"><i title="replication connect" class="cp-datalog custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-database-sizes"><i title="Database Sizes" class="cp-database-sizes custom-cursor-on-hover"></i></td>
                            
                        </tr>
                        <tr>
                            <td class="imagepicker" icon="cp-success-rate"><i title="Success Rate" class="cp-success-rate custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-delay"><i title="Stand Server" class="cp-delay custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-timer-meter"><i title="Version" class="cp-timer-meter custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-data-lag"><i title="active enable" class="cp-data-lag custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-agree"><i title="agree" class="cp-agree custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-replication-connect"><i title="replication connect" class="cp-replication-connect custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-datalog"><i title="replication connect" class="cp-datalog custom-cursor-on-hover"></i></td>
                            <td class="imagepicker" icon="cp-table-date"><i title="Table Date" class="cp-table-date custom-cursor-on-hover"></i></td>
                            
                        </tr>
                      
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked">
                    <label class="form-check-label" for="flexCheckChecked">
                        No Image
                    </label>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm ConditionSettingsModal" data-bs-target="#ConditionSettingsModal" data-bs-toggle="modal">Close</button>
                    <button type="button" class="btn btn-primary btn-sm" id="widgetImageApply">Apply</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" model="new PageWidgetViewModel()" />
</div>

<!--Condition Settings Modal-->
<div class="modal fade" id="ConditionSettingsModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-business-service"></i><span>Condition Settings</span></h6>
                <div class="d-flex align-items-center gap-2" style="width:195px">
                    <div class="w-100">
                        <select class="form-select-lg w-100 p-1 border border-secondary-subtle" id="selectCommonName" style="font-size:12px">
                            <option value="select">Select</option> 
                        </select>
                        
                    </div>
                <div class="border">
                    <i class="cp-web" id="commonIcon" onclick="iconadd(this)" data-bs-toggle="modal" data-bs-target="#iconModal" role="button" aria-expanded="false" aria-controls="iconModal"></i>
            </div>
                    <button type="button" class="btn-close ms-2 add_condition_cancel" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
              </div>
            </div>
            <div class="modal-body">
                <table class="table align-middle">
                    <tbody class="add_modal_table">

                    </tbody>
                </table>
     
            </div>
            <div class="modal-footer justify-content-between">
              
                    <div >
                        <a href="#" class="text-decoration-none add_condition_modal">Add Condition +</a>
                    </div>
               
          <div>
                <button type="button" class="btn btn-secondary add_condition_cancel" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary add_condition_save">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Condition Settings Modal-->
<div class="modal fade" id="ParameterSettingsModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-business-service"></i><span>Parameter Settings</span></h6>
                <div class="d-flex align-items-center gap-2">
                  
                    <button type="button" class="btn-close ms-2 add_condition_cancel" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <select class="form-select-lg w-100 p-1 border border-secondary-subtle" id="selectParameter" style="font-size:12px">
                    <option value="select">Select</option>
                </select>
            </div>
            <div class="modal-footer justify-content-end">
                <div>
                    <button type="button" class="btn btn-secondary addParameterCancel" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary addParameterSave">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>




@section Scripts
{
    <partial name="_ValidationScriptsPartial" />
}

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/lib/apexchart/apexchart.js"></script>
<script src="~/js/DashBoardBuilder/dashboardchart.js"></script>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/PageBuilder/WidgetBuilder.js"></script>
<script src="~/js/PageBuilder/MonitorSolution.js"></script>
<script src="~/lib/canvas/canvas.js"></script>
<script src="~/lib/jquery-ui/jquery-ui.min.js"></script>

