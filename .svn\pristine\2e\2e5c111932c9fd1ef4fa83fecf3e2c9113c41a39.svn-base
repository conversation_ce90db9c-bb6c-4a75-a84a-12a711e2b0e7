﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateStatus;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.ResiliencyReadiness.Controllers;

[Area("ResiliencyReadiness")]
public class ManageResilienceReadinessController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly ILogger<ManageResilienceReadinessController> _logger;
    private readonly IDataProvider _dataProvider;

    public ManageResilienceReadinessController(IPublisher publisher, IMapper mapper, ILogger<ManageResilienceReadinessController> logger, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }
    [EventCode(EventCodes.ManageResiliencyReadiness.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in ManageResilienceReadiness");

        await _publisher.Publish(new InfraObjectSchedulerPaginatedEvent(), CancellationToken.None);

        var drReadyView = await _dataProvider.DrReady.GetDrReadyPaginatedList(new GetInfraObjectSchedulerPaginatedListQuery());

        var infraObjects = (await _dataProvider.InfraObject.GetInfraObjectList()).ToList();

        var drReadyInfra = infraObjects.Where(x => x.DRReady).Select(infra => new GetInfraObjectNameVm
        {
            Id = infra.Id,
            Name = infra.Name,

        }).ToList();

        var workflowName = await _dataProvider.Workflow.GetWorkflowNames();

        var groupPolicies = await _dataProvider.GroupPolicy.GetGroupPolicies();

        var drReadyModel = new InfraObjectSchedulerViewModel
        {
            PaginationDrReady = drReadyView,
            InfraObjectNameVms = drReadyInfra,
            WorkflowNamesvms = workflowName,
            GroupPolicies = groupPolicies
        };

        return View(drReadyModel);
    }
    [EventCode(EventCodes.ManageResiliencyReadiness.GetWorkflowNameByInfraId)]
    public async Task<JsonResult> GetWorkflowNameByInfraId(string infraId)
    {
        _logger.LogDebug("Entering GetWorkflowNameByInfraId method in ManageResilienceReadiness");

        try
        {
            var workflowNameLists = await _dataProvider.WorkflowInfraObject.GetWorkflowByInfraObjectId(infraId);
            return Json(new { Success = true, data = workflowNameLists });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred while processing the request. {ex.GetMessage()}");
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    [EventCode(EventCodes.ManageResiliencyReadiness.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(InfraObjectSchedulerViewModel infraObjectSchedulerViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in ManageResilienceReadiness");
        try
        {
            if (infraObjectSchedulerViewModel.Id.IsNullOrWhiteSpace())
            {
                infraObjectSchedulerViewModel.NodeId = "283e93e2-bbb1-443c-a156-6d8084ad72c2";
                infraObjectSchedulerViewModel.NodeName = "node";
                var drReadyCommand = _mapper.Map<CreateInfraObjectSchedulerCommand>(infraObjectSchedulerViewModel);
                var result = await _dataProvider.DrReady.CreateAsync(drReadyCommand);
                TempData.NotifySuccess(result.Message);
                _logger.LogDebug($"Creating ManageResilienceReadiness for InfraObject '{drReadyCommand.InfraObjectName}'");
            }
            else
            {
                var drReadyCommand = _mapper.Map<UpdateInfraObjectSchedulerCommand>(infraObjectSchedulerViewModel);
                var result = await _dataProvider.DrReady.UpdateAsync(drReadyCommand);
                TempData.NotifySuccess(result.Message);
                _logger.LogDebug($"Updating ManageResilienceReadiness for InfraObject '{drReadyCommand.InfraObjectName}'");
            }
            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on manage resilience readiness page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (AuthenticationException ex)
        {
            _logger.LogError($"Authentication error on manage resilience readiness page: {ex.Message}");

            TempData.NotifyUnauthorised(ex.Message);

            return RedirectToAction("Logout", "Account", new { Area = "" });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on manage resilience readiness page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.ManageResiliencyReadiness.ResetManageResilienceReadinessStatus)]
    public async Task<IActionResult> ResetManageResilienceReadinessStatus(UpdateInfraObjectSchedulerStatusCommand updateInfraObjectSchedulerStatusCommand)
    {
        _logger.LogDebug("Entering ResetManageResilienceReadinessStatus method in ManageResilienceReadiness");
        try
        {
            updateInfraObjectSchedulerStatusCommand.Status = "Pending";
            var drCommand = _mapper.Map<UpdateInfraObjectSchedulerStatusCommand>(updateInfraObjectSchedulerStatusCommand);
            var result = await _dataProvider.DrReady.UpdateInfraObjectSchedulerStatus(drCommand);
            _logger.LogDebug($"Successfully reset status as '{drCommand.Status}' in ManageResilienceReadiness");
            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on manage resilience readiness page while reset the status.", ex);
            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [EventCode(EventCodes.ManageResiliencyReadiness.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in ManageResilienceReadiness");
        try
        {
            var drReadiness = await _dataProvider.DrReady.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in ManageResilienceReadiness");
            TempData.NotifySuccess(drReadiness.Message);
            return RedirectToAction("List");
        }

        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on manage resilience readiness.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }

    }
    [EventCode(EventCodes.ManageResiliencyReadiness.Pagination)]
    public async Task<JsonResult> GetPagination(GetInfraObjectSchedulerPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginationList method in ManageResilienceReadiness");
        try
        {
            var pagination = await _dataProvider.DrReady.GetDrReadyPaginatedList(query);
            _logger.LogDebug("Successfully retrieved manage resilience readiness paginated list on ManageResilienceReadiness");
            return Json(new { Success = true, data = pagination });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on manage resilience readiness page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }

    }

    [EventCode(EventCodes.ManageResiliencyReadiness.UpdateManageResilienceReadinessState)]
    public async Task<IActionResult> UpdateManageResilienceReadinessState(UpdateInfraObjectSchedulerStateCommand updateInfraObjectSchedulerStateCommand)
    {
        _logger.LogDebug("Entering UpdateManageResilienceReadinessState method in ManageResilienceReadiness");
        try
        {
            var getState = await _dataProvider.DrReady.UpdateInfraObjectSchedulerState(updateInfraObjectSchedulerStateCommand);
            _logger.LogDebug("Successfully updating manage resilience readiness state");
            return Json(new { Success = true, data = getState });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on manage resilience readiness page while updating the state.", ex);
            return ex.GetJsonException();
        }

    }
}