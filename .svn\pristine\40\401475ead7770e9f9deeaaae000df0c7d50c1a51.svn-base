using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGap.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Commands;

public class UpdateCyberAirGapTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<ICyberAirGapRepository> _mockCyberAirGapRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateCyberAirGapCommandHandler _handler;

    public UpdateCyberAirGapTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockCyberAirGapRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(_cyberAirGapFixture.CyberAirGaps);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new UpdateCyberAirGapCommandHandler(
            _cyberAirGapFixture.Mapper,
            _mockCyberAirGapRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGap_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new UpdateCyberAirGapCommand
        {
            Id = existingAirGap.ReferenceId,
            Name = "UpdatedAirGap",
            Description = "Updated Air Gap Description",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Updated Source Site",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Updated Target Site",
            Port = 9999,
            Source = "{\"serverId\":\"updated-server-001\",\"componentId\":\"updated-comp-001\"}",
            Target = "{\"serverId\":\"updated-server-002\",\"componentId\":\"updated-comp-002\"}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Updated Source Component",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Updated Target Component",
            WorkflowStatus = "Updated",
            EnableWorkflowId = Guid.NewGuid().ToString(),
            DisableWorkflowId = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateCyberAirGapResponse>();
        result.Id.ShouldBe(existingAirGap.ReferenceId);
        result.Message.ShouldContain("UpdatedAirGap");
        result.Message.ShouldContain("Airgap");
    }

    [Fact]
    public async Task Handle_CallGetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new UpdateCyberAirGapCommand
        {
            Id = existingAirGap.ReferenceId,
            Name = "TestUpdate_GetOnce"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockCyberAirGapRepository.Verify(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId), Times.Once);
    }

    /// <summary>
    /// Test: Repository UpdateAsync is called exactly once
    /// Expected: Verifies proper update operation
    /// </summary>
    [Fact]
    public async Task Handle_CallUpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new UpdateCyberAirGapCommand
        {
            Id = existingAirGap.ReferenceId,
            Name = "TestUpdate_UpdateOnce"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockCyberAirGapRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGap>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishCyberAirGapUpdatedEvent_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new UpdateCyberAirGapCommand
        {
            Id = existingAirGap.ReferenceId,
            Name = "TestUpdate_Event"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new UpdateCyberAirGapCommand
        {
            Id = nonExistentId,
            Name = "TestUpdate_NotFound"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapIsInactive()
    {
        // Arrange
        var inactiveAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        inactiveAirGap.IsActive = false;
        var command = new UpdateCyberAirGapCommand
        {
            Id = inactiveAirGap.ReferenceId,
            Name = "TestUpdate_Inactive"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    /// <summary>
    /// Test: Event contains correct data
    /// Expected: Published event has the updated air gap name
    /// </summary>
    [Fact]
    public async Task Handle_VerifyEventData_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new UpdateCyberAirGapCommand
        {
            Id = existingAirGap.ReferenceId,
            Name = "TestUpdate_EventData"
        };

        CyberAirGapUpdatedEvent publishedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapUpdatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe("TestUpdate_EventData");
    }

    /// <summary>
    /// Test: Repository receives correctly updated entity
    /// Expected: Entity properties are updated correctly
    /// </summary>
    [Fact]
    public async Task Handle_VerifyEntityUpdate_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new UpdateCyberAirGapCommand
        {
            Id = existingAirGap.ReferenceId,
            Name = "TestUpdate_EntityUpdate",
            Description = "Updated description for verification",
            Port = 7777,
            WorkflowStatus = "VerificationStatus"
        };

        Domain.Entities.CyberAirGap updatedEntity = null;
        _mockCyberAirGapRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGap>()))
            .Callback<Domain.Entities.CyberAirGap>(entity => updatedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAirGap entity) => entity);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        updatedEntity.ShouldNotBeNull();
        updatedEntity.ReferenceId.ShouldBe(existingAirGap.ReferenceId);
        updatedEntity.Name.ShouldBe("TestUpdate_EntityUpdate");
        updatedEntity.Description.ShouldBe("Updated description for verification");
        updatedEntity.Port.ShouldBe(7777);
        updatedEntity.WorkflowStatus.ShouldBe("VerificationStatus");
    }

    /// <summary>
    /// Test: Update all properties of air gap
    /// Expected: All properties are updated correctly
    /// </summary>
    [Fact]
    public async Task Handle_UpdateAllProperties_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new UpdateCyberAirGapCommand
        {
            Id = existingAirGap.ReferenceId,
            Name = "CompletelyUpdatedAirGap",
            Description = "Completely updated description",
            SourceSiteId = "new-source-site-id",
            SourceSiteName = "New Source Site",
            TargetSiteId = "new-target-site-id",
            TargetSiteName = "New Target Site",
            Port = 5555,
            Source = "{\"serverId\":\"new-source-server\"}",
            Target = "{\"serverId\":\"new-target-server\"}",
            SourceComponentId = "new-source-component-id",
            SourceComponentName = "New Source Component",
            TargetComponentId = "new-target-component-id",
            TargetComponentName = "New Target Component",
            WorkflowStatus = "NewStatus",
            EnableWorkflowId = "new-enable-workflow-id",
            DisableWorkflowId = "new-disable-workflow-id"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingAirGap.ReferenceId);
        result.Message.ShouldContain("CompletelyUpdatedAirGap");
    }

    /// <summary>
    /// Test: Handler supports cancellation
    /// Expected: OperationCanceledException when cancellation is requested
    /// </summary>
    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new UpdateCyberAirGapCommand
        {
            Id = existingAirGap.ReferenceId,
            Name = "TestUpdate_Cancellation"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        
    }

    /// <summary>
    /// Test: Update air gap with different workflow statuses
    /// Expected: All workflow statuses are handled correctly
    /// </summary>
    [Fact]
    public async Task Handle_UpdateWithDifferentWorkflowStatuses_When_ValidStatuses()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var statuses = new[] { "Active", "Inactive", "Pending", "Error", "Completed" };

        foreach (var status in statuses)
        {
            var command = new UpdateCyberAirGapCommand
            {
                Id = existingAirGap.ReferenceId,
                Name = $"TestUpdate_Status_{status}",
                WorkflowStatus = status
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(existingAirGap.ReferenceId);
            result.Message.ShouldContain($"TestUpdate_Status_{status}");
        }
    }
}
