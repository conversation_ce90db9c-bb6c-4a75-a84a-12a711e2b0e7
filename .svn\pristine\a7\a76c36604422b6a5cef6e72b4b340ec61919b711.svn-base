﻿namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;

public class UpdateWorkflowProfileInfoCommandValidator : AbstractValidator<UpdateWorkflowProfileInfoCommand>
{
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;

    public UpdateWorkflowProfileInfoCommandValidator(IWorkflowProfileInfoRepository workflowProfileInfoRepository)
    {
        _workflowProfileInfoRepository = workflowProfileInfoRepository;

        RuleFor(p => p.ProfileName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+(?:[_\s-]?))([a-zA-Z\d]+(?:[_\s-]?))*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters.");

        //RuleFor(e => e)
        //    .MustAsync(WorkflowProfileInfoNameUnique)
        //    .WithMessage("A same name already exists.");

        RuleFor(p => p)
            .NotNull()
            .MustAsync(IsValidGuidId)
            .WithMessage("Id is invalid.");
    }

    //private async Task<bool> WorkflowProfileInfoNameUnique(UpdateWorkflowProfileInfoCommand e, CancellationToken token)
    //{
    //    return !await _workflowProfileInfoRepository.IsWorkflowProfileInfoNameExist(e.ProfileName, e.Id);
    //}

    private Task<bool> IsValidGuidId(UpdateWorkflowProfileInfoCommand workflowProfileInfoCommand,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowProfileInfoCommand.Id, "Id");
        Guard.Against.InvalidGuidOrEmpty(workflowProfileInfoCommand.ProfileId, "Profile Id");
        Guard.Against.InvalidGuidOrEmpty(workflowProfileInfoCommand.BusinessServiceId, "BusinessService Id");
        Guard.Against.InvalidGuidOrEmpty(workflowProfileInfoCommand.BusinessFunctionId, "BusinessFunction Id");
        Guard.Against.InvalidGuidOrEmpty(workflowProfileInfoCommand.WorkflowId, "Workflow Id");
        return Task.FromResult(true);
    }
}