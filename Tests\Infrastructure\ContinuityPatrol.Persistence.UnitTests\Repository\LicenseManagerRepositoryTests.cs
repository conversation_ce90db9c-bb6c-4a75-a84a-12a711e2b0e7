using AutoFixture;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;
using LicenseManager = ContinuityPatrol.Domain.Entities.LicenseManager;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class LicenseManagerRepositoryTests : IClassFixture<LicenseManagerFixture>, IDisposable
{
    private readonly LicenseManagerRepository _repository;
    private readonly ApplicationDbContext _context;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly LicenseManagerFixture _fixture;
    private readonly Fixture _autoFixture;

    public LicenseManagerRepositoryTests(LicenseManagerFixture fixture)
    {
        _fixture = fixture;
        _autoFixture = new Fixture();
        _context = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        // Setup default logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns("TEST_USER");
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(LicenseManagerFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        _repository = new LicenseManagerRepository(_context, _mockLoggedInUserService.Object);
    }

    #region Basic CRUD Operations

    //[Fact]
    //public async Task AddAsync_ShouldAddEntity()
    //{
    //    // Arrange
    //    var entity = _fixture.LicenseManagerDto;

    //    // Act
    //    await _repository.AddAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Assert
    //    var result = await _context.LicenseManagers.FindAsync(entity.Id);
    //    Assert.NotNull(result);
    //    Assert.Equal(entity.PoNumber, result.PoNumber);
    //    Assert.Equal(entity.CompanyId, result.CompanyId);
    //    Assert.Equal(entity.LicenseKey, result.LicenseKey);
    //}

    //[Fact]
    //public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    //}

    //[Fact]
    //public async Task UpdateAsync_ShouldUpdateEntity()
    //{
    //    // Arrange
    //    var entity = _fixture.LicenseManagerDto;
    //    await _context.LicenseManagers.AddAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    entity.PoNumber = "UPDATED_PO";
    //    entity.HostName = "UPDATED_HOST";
    //    await _repository.UpdateAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Assert
    //    var result = await _context.LicenseManagers.FindAsync(entity.Id);
    //    Assert.NotNull(result);
    //    Assert.Equal("UPDATED_PO", result.PoNumber);
    //    Assert.Equal("UPDATED_HOST", result.HostName);
    //}

    //[Fact]
    //public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    //}



    //[Fact]
    //public async Task DeleteAsync_ShouldDeleteEntity()
    //{
    //    // Arrange
    //    var entity = _fixture.LicenseManagerDto;
    //    await _context.LicenseManagers.AddAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    await _repository.DeleteAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Assert
    //    var result = await _context.LicenseManagers.FindAsync(entity.Id);
    //    Assert.Null(result);
    //}

    //[Fact]
    //public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    //}

    //[Fact]
    //public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    //{
    //    // Arrange
    //    var entity = _fixture.LicenseManagerDto;
    //    await _context.LicenseManagers.AddAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    var result = await _repository.GetByIdAsync(entity.Id);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(entity.Id, result.Id);
    //    Assert.Equal(entity.PoNumber, result.PoNumber);
    //}

    //[Fact]
    //public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    //{
    //    // Act
    //    var result = await _repository.GetByIdAsync(999999);

    //    // Assert
    //    Assert.Null(result);
    //}

    //[Fact]
    //public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    //{
    //    // Arrange
    //    var entity = _fixture.LicenseManagerDto;
    //    await _context.LicenseManagers.AddAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    var result = await _repository.GetByReferenceIdAsync(entity.ReferenceId);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(entity.ReferenceId, result.ReferenceId);
    //    Assert.Equal(entity.CompanyId, result.CompanyId);
    //}

    //[Fact]
    //public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    //{
    //    // Act
    //    var result = await _repository.GetByReferenceIdAsync("NON_EXISTENT_ID");

    //    // Assert
    //    Assert.Null(result);
    //}

    //[Fact]
    //public async Task GetByReferenceIdAsync_ShouldFilterByCompanyId_WhenIsParentFalse()
    //{
    //    // Arrange
    //    var entity1 = _fixture.LicenseManagerDto;
    //    entity1.CompanyId = LicenseManagerFixture.CompanyId;

    //    var entity2 = _fixture.LicenseManagerDto;
    //    entity2.CompanyId = "OTHER_COMPANY";
    //    entity2.ReferenceId = Guid.NewGuid().ToString();

    //    await _context.LicenseManagers.AddRangeAsync(entity1, entity2);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    var result1 = await _repository.GetByReferenceIdAsync(entity1.ReferenceId);
    //    var result2 = await _repository.GetByReferenceIdAsync(entity2.ReferenceId);

    //    // Assert
    //    Assert.NotNull(result1); // Should find entity from same company
    //    Assert.Null(result2); // Should not find entity from different company
    //}

    //[Fact]
    //public async Task GetByReferenceIdAsync_ShouldReturnAnyEntity_WhenIsParentTrue()
    //{
    //    // Arrange
    //    _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

    //    var entity1 = _fixture.LicenseManagerDto;
    //    entity1.CompanyId = LicenseManagerFixture.CompanyId;

    //    var entity2 = _fixture.LicenseManagerDto;
    //    entity2.CompanyId = "OTHER_COMPANY";
    //    entity2.ReferenceId = Guid.NewGuid().ToString();

    //    await _context.LicenseManagers.AddRangeAsync(entity1, entity2);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    var result1 = await _repository.GetByReferenceIdAsync(entity1.ReferenceId);
    //    var result2 = await _repository.GetByReferenceIdAsync(entity2.ReferenceId);

    //    // Assert
    //    Assert.NotNull(result1); // Should find entity from same company
    //    Assert.NotNull(result2); // Should find entity from different company (parent user)
    //}

    #endregion



    #region LicenseManager-Specific Methods

    [Fact]
    public async Task GetMacAddress_ShouldReturnMacAddresses()
    {
        // Act
        var result = await _repository.GetMacAddress();

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<string>>(result);
        // Note: This test may return empty list in test environment, which is expected
    }

    [Fact]
    public async Task GetLicenseExpiryDateByIds_ShouldReturnMatchingLicenses()
    {
        // Arrange
        var entities = _fixture.LicenseManagerList.Take(3).ToList();
        var ids = entities.Select(x => x.ReferenceId).ToList();
        entities.ForEach(x =>
        {
            x.CompanyId = LicenseManagerFixture.CompanyId;
            x.IsActive = true;
        });

        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseExpiryDateByIds(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, ids));
    }

    [Fact]
    public async Task GetLicenseExpiryDateByIds_ShouldFilterByCompanyId()
    {
        // Arrange
        var entity1 = _fixture.LicenseManagerDto;
        entity1.CompanyId = LicenseManagerFixture.CompanyId;
        entity1.IsActive = true;

        var entity2 = _autoFixture.Create<LicenseManager>();
        entity2.CompanyId = "OTHER_COMPANY";
        entity2.IsActive = true;

        var ids = new List<string> { entity1.ReferenceId, entity2.ReferenceId };

        await _context.LicenseManagers.AddRangeAsync(entity1, entity2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseExpiryDateByIds(ids);

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetDerivedLicenseByCompanyIdAsync_ShouldReturnMatchingLicenses()
    {
        // Arrange
        var entities = _fixture.LicenseManagerList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.ParentId = LicenseManagerFixture.CompanyId;
            x.ParentPoNumber = LicenseManagerFixture.PoNumber;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseManager>();
        otherCompanyEntity.ParentId = "OTHER_COMPANY";
        otherCompanyEntity.ParentPoNumber = LicenseManagerFixture.PoNumber;
        otherCompanyEntity.IsActive = true;
        otherCompanyEntity.ParentPoNumber = "PO123";
        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.LicenseManagers.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDerivedLicenseByCompanyIdAsync(
            new List<string> { LicenseManagerFixture.CompanyId },
            new List<string> { LicenseManagerFixture.PoNumber });

        // Assert
        Assert.NotNull(result);

    }

    [Fact]
    public async Task GetIpAddress_ShouldReturnCurrentIpAddress()
    {
        // Act
        var result = await _repository.GetIpAddress();

        // Assert
        Assert.NotNull(result);
        // Note: This will return actual IP address or localhost in test environment
    }

    [Fact]
    public async Task GetDateByExpireTime_ShouldReturnFormattedDate()
    {
        // Arrange
        var testDate = DateTime.Now.AddMonths(6);
        var expireTime = "POC-12 Month";
        var endDate = testDate.ToString("dd MMMM yyyy");

        // Act
        var result = await _repository.GetDateByExpireTime(expireTime, testDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<string>(result);
    }

    [Fact]
    public async Task GetLicenseDetailByIdAsync_ShouldReturnLicense_WhenExists()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseDetailByIdAsync(entity.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(entity.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetLicenseDetailByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetLicenseDetailByIdAsync("NON_EXISTENT");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetLicenseDetailByPoNumber_ShouldReturnLicense_WhenExists()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.PoNumber = SecurityHelper.Encrypt(LicenseManagerFixture.PoNumber);
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseDetailByPoNumber(LicenseManagerFixture.PoNumber);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseManagerFixture.PoNumber, result.PoNumber);
    }

    [Fact]
    public async Task GetLicenseDetailByPoNumber_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetLicenseDetailByPoNumber("NON_EXISTENT");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsLicenseKeyUnique_ShouldReturnTrue_WhenUnique()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.LicenseKey = "EXISTING_KEY";
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLicenseKeyUnique("NEW_UNIQUE_KEY");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsLicenseKeyUnique_ShouldReturnFalse_WhenNotUnique()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.LicenseKey = SecurityHelper.Encrypt(LicenseManagerFixture.LicenseKey);
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLicenseKeyUnique(LicenseManagerFixture.LicenseKey);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsLicenseKeyPoNumberUnique_ShouldReturnTrue_WhenUnique()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.PoNumber = SecurityHelper.Encrypt("EXISTING_PO");
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLicenseKeyPoNumberUnique("EXISTING_PO");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsLicenseKeyPoNumberUnique_ShouldReturnFalse_WhenNotUnique()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.PoNumber =SecurityHelper.Encrypt("PO123");
        //entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLicenseKeyPoNumberUnique("PO123");

        // Assert
        Assert.True(result); // Note: This method returns true if it exists, opposite of IsUnique
    }

    [Fact]
    public async Task ListAllLicense_ShouldReturnAllLicenses_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entities = _fixture.LicenseManagerList.Take(5).ToList();
        entities.ForEach(x => x.IsActive = true);

        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task ListAllLicense_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entities = _fixture.LicenseManagerList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.CompanyId = LicenseManagerFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseManager>();
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.LicenseManagers.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseManagerFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task GetLicenseExpiryDateByCompanyId_ShouldReturnMatchingLicenses()
    {
        // Arrange
        var entities = _fixture.LicenseManagerList.Take(3).ToList();
        var expityDate = DateTime.Now.AddDays(30).ToString("dd MMMM yyyy");
        entities.ForEach(x =>
        {
            x.CompanyId = LicenseManagerFixture.CompanyId;
            x.IsActive = true;
            x.ExpiryDate = SecurityHelper.Encrypt(expityDate);
        });

        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseExpiryDateByCompanyId(LicenseManagerFixture.CompanyId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(expityDate, x.ExpiryDate));
    }

    [Fact]
    public async Task GetLicenseExpiryDateByCompanyId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseExpiryDateByCompanyId("NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicenseDetailByCompanyId_ShouldReturnMatchingLicenses()
    {
        // Arrange
        var entities = _fixture.LicenseManagerList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.CompanyId = LicenseManagerFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseManager>();
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.LicenseManagers.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseDetailByCompanyId(LicenseManagerFixture.CompanyId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseManagerFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task GetLicenseDetailByCompanyId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseDetailByCompanyId("NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Error Handling

    [Fact]
    public async Task GetLicenseExpiryDateByIds_ShouldReturnEmpty_WhenEmptyList()
    {
        // Act
        var result = await _repository.GetLicenseExpiryDateByIds(new List<string>());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicenseExpiryDateByIds_ShouldReturnEmpty_WhenNullList()
    {
        // Act
        var result = await _repository.GetLicenseExpiryDateByIds(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDerivedLicenseByCompanyIdAsync_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetDerivedLicenseByCompanyIdAsync(
            new List<string> { "NON_EXISTENT" },
            new List<string> { "NON_EXISTENT" });

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task IsLicenseKeyUnique_ShouldReturnTrue_WhenNullOrEmpty()
    {
        // Act & Assert
        Assert.False(await _repository.IsLicenseKeyUnique(null));
        Assert.False(await _repository.IsLicenseKeyUnique(""));
        Assert.False(await _repository.IsLicenseKeyUnique("   "));
    }

    [Fact]
    public async Task IsLicenseKeyPoNumberUnique_ShouldReturnFalse_WhenNullOrEmpty()
    {
        // Act & Assert
        Assert.False(await _repository.IsLicenseKeyPoNumberUnique(null));
        Assert.False(await _repository.IsLicenseKeyPoNumberUnique(""));
        Assert.False(await _repository.IsLicenseKeyPoNumberUnique("   "));
    }

    [Fact]
    public async Task ListAllLicense_ShouldReturnEmpty_WhenNoLicenses()
    {
        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllLicense_ShouldExcludeInactiveLicenses()
    {
        // Arrange
        var activeLicense = _fixture.LicenseManagerDto;
        activeLicense.IsActive = true;
        activeLicense.CompanyId = LicenseManagerFixture.CompanyId;

        var inactiveLicense = _autoFixture.Create<LicenseManager>();
        inactiveLicense.IsActive = false;
        inactiveLicense.CompanyId = LicenseManagerFixture.CompanyId;

         _context.LicenseManagers.AddRange(activeLicense, inactiveLicense);
         _context.SaveChanges();

        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
        Assert.Equal(activeLicense.ReferenceId, result.First().ReferenceId);
    }

    [Fact]
    public async Task ListAllLicense_ShouldDecryptSensitiveFields()
    {
        // Arrange
        var license = _fixture.LicenseManagerDto;
        license.CompanyId = LicenseManagerFixture.CompanyId;
        license.IsActive = true;

        await _context.LicenseManagers.AddAsync(license);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var returnedLicense = result.First();
        // Verify that sensitive fields are decrypted (they should not be encrypted in the result)
        Assert.NotNull(returnedLicense.PoNumber);
        Assert.NotNull(returnedLicense.HostName);
        Assert.NotNull(returnedLicense.Properties);
        Assert.NotNull(returnedLicense.IpAddress);
        Assert.NotNull(returnedLicense.MacAddress);
        Assert.NotNull(returnedLicense.Validity);
        Assert.NotNull(returnedLicense.ExpiryDate);
    }

    [Fact]
    public async Task ListAllLicense_ShouldOrderByReferenceIdDescending()
    {
        // Arrange
        var license1 = _fixture.LicenseManagerDto;
        license1.ReferenceId = "AAA-111";
        license1.CompanyId = LicenseManagerFixture.CompanyId;
        license1.IsActive = true;

        var license2 = _autoFixture.Create<LicenseManager>();
        license2.ReferenceId = "ZZZ-999";
        license2.CompanyId = LicenseManagerFixture.CompanyId;
        license2.IsActive = true;

        var license3 = _autoFixture.Create<LicenseManager>();
        license3.ReferenceId = "MMM-555";
        license3.CompanyId = LicenseManagerFixture.CompanyId;
        license3.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(license1, license2, license3);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);

        // Should be ordered by ReferenceId descending
        Assert.Equal("ZZZ-999", result[0].ReferenceId);
        Assert.Equal("MMM-555", result[1].ReferenceId);
        Assert.Equal("AAA-111", result[2].ReferenceId);
    }

    [Fact]
    public async Task ListAllLicense_ShouldIncludeBothParentAndDerivedLicenses()
    {
        // Arrange
        var parentLicense = _fixture.LicenseManagerDto;
        parentLicense.IsParent = true;
        parentLicense.CompanyId = LicenseManagerFixture.CompanyId;
        parentLicense.IsActive = true;

        var derivedLicense = _autoFixture.Create<LicenseManager>();
        derivedLicense.IsParent = false;
        derivedLicense.ParentId = parentLicense.ReferenceId;
        derivedLicense.CompanyId = LicenseManagerFixture.CompanyId;
        derivedLicense.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(parentLicense, derivedLicense);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.IsParent);
        Assert.Contains(result, x => !x.IsParent);
    }

    [Fact]
    public async Task ListAllLicense_ShouldMapAllRequiredFields()
    {
        // Arrange
        var license = _fixture.LicenseManagerDto;
        license.CompanyId = LicenseManagerFixture.CompanyId;
        license.IsActive = true;

        await _context.LicenseManagers.AddAsync(license);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var returnedLicense = result.First();
        Assert.Equal(license.Id, returnedLicense.Id);
        Assert.Equal(license.ReferenceId, returnedLicense.ReferenceId);
        Assert.Equal(license.CompanyId, returnedLicense.CompanyId);
        Assert.Equal(license.CompanyName, returnedLicense.CompanyName);
        Assert.Equal(license.ParentId, returnedLicense.ParentId);
        Assert.Equal(license.IsActive, returnedLicense.IsActive);
        Assert.Equal(license.IsParent, returnedLicense.IsParent);
        Assert.Equal(license.LicenseKey, returnedLicense.LicenseKey);
        Assert.Equal(license.IsState, returnedLicense.IsState);
        Assert.Equal(license.IsAmc, returnedLicense.IsAmc);
        Assert.Equal(license.AmcStartDate, returnedLicense.AmcStartDate);
        Assert.Equal(license.AmcEndDate, returnedLicense.AmcEndDate);
        Assert.Equal(license.CreatedDate, returnedLicense.CreatedDate);
        Assert.Equal(license.CreatedBy, returnedLicense.CreatedBy);
        Assert.Equal(license.LastModifiedBy, returnedLicense.LastModifiedBy);
        Assert.Equal(license.LastModifiedDate, returnedLicense.LastModifiedDate);
        Assert.Equal(license.IsExpired, returnedLicense.IsExpired);
    }

    [Fact]
    public async Task GetDateByExpireTime_ShouldHandleMinValue()
    {
        // Act
        var result = await _repository.GetDateByExpireTime("POC-12 Month", DateTime.MinValue, DateTime.MinValue.ToString("dd MMMM yyyy"));

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetDateByExpireTime_ShouldHandleMaxValue()
    {
        // Act
        var result = await _repository.GetDateByExpireTime("POC-35 Days", DateTime.MaxValue, DateTime.MaxValue.ToString("dd MMMM yyyy"));

        // Assert
        Assert.NotNull(result);
    }

    #endregion

    #region Missing LicenseManager-Specific Methods

    [Fact]
    public async Task GetLicensePoNumber_ShouldReturnLicensesForCurrentCompany()
    {
        // Arrange
        var entities = _fixture.LicenseManagerList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.CompanyId = LicenseManagerFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseManager>();
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.LicenseManagers.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicensePoNumber();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseManagerFixture.CompanyId, x.CompanyId));
        Assert.All(result, x => Assert.NotNull(x.PoNumber));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetLicensePoNumber_ShouldReturnEmpty_WhenNoLicensesForCompany()
    {
        // Arrange
        var entity = _autoFixture.Create<LicenseManager>();
        entity.CompanyId = "OTHER_COMPANY";
        entity.IsActive = true;

        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicensePoNumber();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetHostName_ShouldReturnCurrentHostName()
    {
        // Act
        var result = await _repository.GetHostName();

        // Assert
        Assert.NotNull(result);
        Assert.IsType<string>(result);
        Assert.NotEmpty(result);
    }

    [Fact]
    public async Task IsLicenseKeyExist_ShouldReturnFalse_WhenKeyExists()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.LicenseKey = SecurityHelper.Encrypt("EXISTING_KEY");
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLicenseKeyExist("EXISTING_KEY", entity.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsLicenseKeyExist_ShouldReturnFalse_WhenKeyNotExists()
    {
        // Act
        var result = await _repository.IsLicenseKeyExist("NON_EXISTENT_KEY", Guid.NewGuid().ToString());

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsLicenseKeyPoNumberExist_ShouldReturnFalse_WhenPoNumberExists()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.PoNumber = SecurityHelper.Encrypt("EXISTING_PO");
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLicenseKeyPoNumberExist("EXISTING_PO", entity.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsLicenseKeyPoNumberExist_ShouldReturnFalse_WhenPoNumberNotExists()
    {
        // Act
        var result = await _repository.IsLicenseKeyPoNumberExist("NON_EXISTENT_PO", Guid.NewGuid().ToString());

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetBaseLicenseDetailByDerivedLicenseDetailAsync_ShouldReturnBaseLicense()
    {
        // Arrange
        var baseLicense = _fixture.LicenseManagerDto;
        var basePo = LicenseManagerFixture.PoNumber;
        baseLicense.CompanyId = LicenseManagerFixture.CompanyId;
        baseLicense.PoNumber = SecurityHelper.Encrypt(LicenseManagerFixture.PoNumber);
        baseLicense.IsParent = true;
        baseLicense.IsActive = true;
        baseLicense.Id = 10;

        var derivedLicense = _fixture.LicenseManagerList;
        derivedLicense[0].ParentId = baseLicense.CompanyId;
        derivedLicense[0].ParentPoNumber =baseLicense.PoNumber;
        derivedLicense[0].Id = 14;
        derivedLicense[0].ReferenceId= "DERIVED-123";
        derivedLicense[0].IsParent = false;

        await _context.LicenseManagers.AddAsync(baseLicense);
        await _context.LicenseManagers.AddRangeAsync(derivedLicense);
        await _context.SaveChangesAsync();

        _mockLoggedInUserService.Setup(x=>x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetBaseLicenseDetailByDerivedLicenseDetailAsync(derivedLicense[0].ParentId, basePo);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseManagerFixture.CompanyId, result.CompanyId);
        Assert.Equal(LicenseManagerFixture.PoNumber, result.PoNumber);
        Assert.True(result.IsParent);
    }

    [Fact]
    public async Task GetBaseLicenseDetailByDerivedLicenseDetailAsync_ShouldThrow_WhenNotFound()
    {
        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _repository.GetBaseLicenseDetailByDerivedLicenseDetailAsync("NON_EXISTENT", "NON_EXISTENT"));
    }

    [Fact]
    public async Task GetDerivedLicenseDetailByBaseLicenseDetailAsync_ShouldReturnDerivedLicenses()
    {
        // Arrange
        var derivedLicenses = _fixture.LicenseManagerList.Take(3).ToList();
        derivedLicenses.ForEach(x =>
        {
            x.ParentId = LicenseManagerFixture.CompanyId;
            x.ParentPoNumber =SecurityHelper.Encrypt( LicenseManagerFixture.PoNumber);
            x.IsParent = false;
            x.IsActive = true;
        });

        // Add license with different parent
        var otherParentLicense = _autoFixture.Create<LicenseManager>();
        otherParentLicense.ParentId = "OTHER_PARENT";
        otherParentLicense.ParentPoNumber = "OTHER_PO";
        otherParentLicense.IsParent = false;
        otherParentLicense.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(derivedLicenses);
        await _context.LicenseManagers.AddAsync(otherParentLicense);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDerivedLicenseDetailByBaseLicenseDetailAsync(LicenseManagerFixture.CompanyId, LicenseManagerFixture.PoNumber);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseManagerFixture.CompanyId, x.ParentId));
        Assert.All(result, x => Assert.Equal(LicenseManagerFixture.PoNumber, x.ParentPoNumber));
    }

    [Fact]
    public async Task GetDerivedLicenseDetailByBaseLicenseDetailAsync_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetDerivedLicenseDetailByBaseLicenseDetailAsync("NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task IsDerivedLicenseCompanyIdUnique_ShouldReturnTrue_WhenCompanyIdExists()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.CompanyId = LicenseManagerFixture.CompanyId;
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsDerivedLicenseCompanyIdUnique(LicenseManagerFixture.CompanyId);

        // Assert
        Assert.True(result); // Returns true if company ID exists
    }

    [Fact]
    public async Task IsDerivedLicenseCompanyIdUnique_ShouldReturnFalse_WhenCompanyIdNotExists()
    {
        // Act
        var result = await _repository.IsDerivedLicenseCompanyIdUnique("NON_EXISTENT_COMPANY");

        // Assert
        Assert.False(result); // Returns false if company ID doesn't exist
    }

    [Fact]
    public async Task GetBaseLicenseByIdAsync_ShouldReturnBaseLicense_WhenExists()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.IsParent = true;
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetBaseLicenseByIdAsync(entity.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(entity.ReferenceId, result.ReferenceId);
        Assert.True(result.IsParent);
    }

    [Fact]
    public async Task GetBaseLicenseByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetBaseLicenseByIdAsync("NON_EXISTENT");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetBaseLicenseByIdAsync_ShouldReturnNull_WhenNotParent()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.IsParent = false; // Not a parent license
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetBaseLicenseByIdAsync(entity.ReferenceId);

        // Assert
        Assert.Null(result); // Should return null for non-parent licenses
    }

    [Fact]
    public async Task GetDerivedLicenseByIdAsync_ShouldReturnDerivedLicense_WhenExists()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.IsParent = false;
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDerivedLicenseByIdAsync(entity.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(entity.ReferenceId, result.ReferenceId);
        Assert.False(result.IsParent);
    }

    [Fact]
    public async Task GetDerivedLicenseByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetDerivedLicenseByIdAsync("NON_EXISTENT");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetDerivedLicenseByIdAsync_ShouldReturnNull_WhenIsParent()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.IsParent = true; // Is a parent license
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDerivedLicenseByIdAsync(entity.ReferenceId);

        // Assert
        Assert.Null(result); // Should return null for parent licenses
    }

    [Fact]
    public async Task ListAllBaseLicense_ShouldReturnBaseLicenses_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var baseLicenses = _fixture.LicenseManagerList.Take(3).ToList();
        baseLicenses.ForEach(x =>
        {
            x.IsParent = true;
            x.IsActive = true;
        });

        // Add derived license
        var derivedLicense = _autoFixture.Create<LicenseManager>();
        derivedLicense.IsParent = false;
        derivedLicense.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(baseLicenses);
        await _context.LicenseManagers.AddAsync(derivedLicense);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllBaseLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.IsParent));

        // Verify ordering (should be descending by Id)
        var orderedIds = result.Select(x => x.Id).ToList();
        var expectedOrder = baseLicenses.OrderByDescending(x => x.Id).Select(x => x.Id).ToList();
        Assert.Equal(expectedOrder, orderedIds);
    }

    [Fact]
    public async Task ListAllBaseLicense_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entities = _fixture.LicenseManagerList.Take(2).ToList();
        entities.ForEach(x =>
        {
            x.CompanyId = LicenseManagerFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseManager>();
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.LicenseManagers.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllBaseLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseManagerFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task GetLicenseDetailByParentId_ShouldReturnChildLicenses()
    {
        // Arrange
        var childLicenses = _fixture.LicenseManagerList.Take(3).ToList();
        childLicenses.ForEach(x =>
        {
            x.ParentId = LicenseManagerFixture.CompanyId;
            x.IsActive = true;
        });

        // Add license with different parent
        var otherParentLicense = _autoFixture.Create<LicenseManager>();
        otherParentLicense.ParentId = "OTHER_PARENT";
        otherParentLicense.IsActive = true;

        await _context.LicenseManagers.AddRangeAsync(childLicenses);
        await _context.LicenseManagers.AddAsync(otherParentLicense);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseDetailByParentId(LicenseManagerFixture.CompanyId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseManagerFixture.CompanyId, x.ParentId));
        Assert.All(result, x => Assert.NotNull(x.PoNumber));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetLicenseDetailByParentId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseDetailByParentId("NON_EXISTENT_PARENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task IsLicenseKeyUnique_WithId_ShouldReturnTrue_WhenKeyIsUnique()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.LicenseKey = "EXISTING_KEY";
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act - Check with different key
        var result = await _repository.IsLicenseKeyUnique(entity.ReferenceId, "NEW_UNIQUE_KEY");

        // Assert
        Assert.True(result); // Should return true for unique key
    }

    [Fact]
    public async Task IsLicenseKeyUnique_WithId_ShouldReturnFalse_WhenKeyExists()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.LicenseKey = SecurityHelper.Encrypt("EXISTING_KEY");
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act - Check with same key
        var result = await _repository.IsLicenseKeyUnique(entity.ReferenceId, "EXISTING_KEY");

        // Assert
        Assert.False(result); // Should return false for existing key
    }

    [Fact]
    public async Task GetDerivedLicenseDetailByBaseLicenseDetailAsync_ShouldHandleNullParameters()
    {
        // Arrange
        var derivedLicense = _fixture.LicenseManagerDto;
        derivedLicense.ParentId = LicenseManagerFixture.CompanyId;
        derivedLicense.ParentPoNumber = SecurityHelper.Encrypt(LicenseManagerFixture.PoNumber);
        derivedLicense.IsActive = true;

        await _context.LicenseManagers.AddAsync(derivedLicense);
        await _context.SaveChangesAsync();

        // Act - Test with null companyId
        var result1 = await _repository.GetDerivedLicenseDetailByBaseLicenseDetailAsync(null, LicenseManagerFixture.PoNumber);

        // Act - Test with null poNumber
        var result2 = await _repository.GetDerivedLicenseDetailByBaseLicenseDetailAsync(LicenseManagerFixture.CompanyId, null);

        // Assert
        Assert.NotNull(result1);
        Assert.Single(result1);
        Assert.NotNull(result2);
        Assert.Single(result2);
    }

    [Fact]
    public async Task ListAllBaseLicense_ShouldReturnEmpty_WhenNoBaseLicenses()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Add only derived licenses
        var derivedLicense = _autoFixture.Create<LicenseManager>();
        derivedLicense.IsParent = false;
        derivedLicense.IsActive = true;

        await _context.LicenseManagers.AddAsync(derivedLicense);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllBaseLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicensePoNumber_ShouldOnlyReturnActiveAndCurrentCompanyLicenses()
    {
        // Arrange
        var activeLicense = _fixture.LicenseManagerDto;
        activeLicense.CompanyId = LicenseManagerFixture.CompanyId;
        activeLicense.IsActive = true;

        var inactiveLicense = _autoFixture.Create<LicenseManager>();
        inactiveLicense.CompanyId = LicenseManagerFixture.CompanyId;
        inactiveLicense.IsActive = false;

         _context.LicenseManagers.AddRange(activeLicense, inactiveLicense);
         _context.SaveChanges();

        // Act
        var result = await _repository.GetLicensePoNumber();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(activeLicense.ReferenceId, result.First().ReferenceId);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task IsLicenseKeyExist_ShouldHandleInvalidGuid()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.LicenseKey =SecurityHelper.Encrypt( "EXISTING_KEY");
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act - Test with invalid GUID
        var result = await _repository.IsLicenseKeyExist("EXISTING_KEY", "INVALID_GUID");

        // Assert
        Assert.True(result); // Should return true when key exists and ID is invalid GUID
    }

    [Fact]
    public async Task IsLicenseKeyPoNumberExist_ShouldHandleInvalidGuid()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.PoNumber = SecurityHelper.Encrypt("EXISTING_PO");
        entity.IsActive = true;
        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act - Test with invalid GUID
        var result = await _repository.IsLicenseKeyPoNumberExist("EXISTING_PO", "INVALID_GUID");

        // Assert
        Assert.True(result); // Should return true when PO exists and ID is invalid GUID
    }

    [Fact]
    public async Task GetLicenseDetailByParentId_ShouldDecryptSensitiveFields()
    {
        // Arrange
        var childLicense = _fixture.LicenseManagerDto;
        childLicense.ParentId = LicenseManagerFixture.CompanyId;
        childLicense.IsActive = true;

        await _context.LicenseManagers.AddAsync(childLicense);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseDetailByParentId(LicenseManagerFixture.CompanyId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var license = result.First();

        // Verify that sensitive fields are present (they would be decrypted in real implementation)
        Assert.NotNull(license.PoNumber);
        Assert.NotNull(license.Properties);
        Assert.NotNull(license.IpAddress);
        Assert.NotNull(license.MacAddress);
        Assert.NotNull(license.Validity);
        Assert.NotNull(license.ExpiryDate);
        Assert.NotNull(license.ParentPoNumber);
        Assert.NotNull(license.AmcPlan);
    }

    #endregion

    #region Missing Basic Repository Methods

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        var entities = new List<LicenseManager>
        {
            _fixture.LicenseManagerDto,
            _autoFixture.Create<LicenseManager>()
        };

        // Act
        var result = await _repository.AddRangeAsync(entities);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());

        var savedEntities = await _context.LicenseManagers.ToListAsync();
        Assert.Equal(2, savedEntities.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    [Fact]
    public async Task RemoveRangeAsync_ShouldRemoveMultipleEntities()
    {
        // Arrange
        var entities = new List<LicenseManager>
        {
            _fixture.LicenseManagerDto,
            _autoFixture.Create<LicenseManager>()
        };

        await _context.LicenseManagers.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.RemoveRangeAsync(entities);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());

        var remainingEntities = await _context.LicenseManagers.ToListAsync();
        Assert.Empty(remainingEntities);
    }

    [Fact]
    public async Task RemoveRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    [Fact]
    public async Task FindByFilterAsync_ShouldReturnMatchingEntities()
    {
        // Arrange
        var entity1 = _fixture.LicenseManagerDto;
        entity1.CompanyId = LicenseManagerFixture.CompanyId;
        entity1.IsActive = true;
        entity1.IsParent = true;

        var entity2 = _autoFixture.Create<LicenseManager>();
        entity2.CompanyId = LicenseManagerFixture.CompanyId;
        entity2.IsActive = true;
        entity2.IsParent = false;

        await _context.LicenseManagers.AddRangeAsync(entity1, entity2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindByFilterAsync(x => x.IsParent);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsParent);
    }

    [Fact]
    public async Task FindByFilterAsync_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.CompanyId = LicenseManagerFixture.CompanyId;
        entity.IsActive = true;
        entity.IsParent = false;

        await _context.LicenseManagers.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindByFilterAsync(x => x.IsParent);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void FilterBy_ShouldReturnQueryableWithFilter()
    {
        // Arrange
        var entities = new List<LicenseManager>
        {
            _fixture.LicenseManagerDto,
            _autoFixture.Create<LicenseManager>()
        };
        entities[0].IsParent = true;
        entities[1].IsParent = false;

        _context.LicenseManagers.AddRange(entities);
        _context.SaveChanges();

        // Act
        var result = _repository.FilterBy(x => x.IsParent);

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<LicenseManager>>(result);

        var materializedResult = result.ToList();
        Assert.Single(materializedResult);
        Assert.True(materializedResult.First().IsParent);
    }

    [Fact]
    public void QueryAll_ShouldReturnFilteredEntities()
    {
        // Arrange
        var entities = new List<LicenseManager>
        {
            _fixture.LicenseManagerDto,
            _autoFixture.Create<LicenseManager>()
        };
        entities[0].IsActive = true;
        entities[0].CompanyId = LicenseManagerFixture.CompanyId;
        entities[1].IsActive = false;
        entities[1].CompanyId = LicenseManagerFixture.CompanyId;

        _context.LicenseManagers.AddRange(entities);
        _context.SaveChanges();

        // Act
        var result = _repository.QueryAll(x => x.IsActive);

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<LicenseManager>>(result);

        var materializedResult = result.ToList();
        Assert.Single(materializedResult);
        Assert.True(materializedResult.First().IsActive);
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnQueryableForPagination()
    {
        // Arrange
        var entities = new List<LicenseManager>
        {
            _fixture.LicenseManagerDto,
            _autoFixture.Create<LicenseManager>()
        };
        entities.ForEach(x =>
        {
            x.IsActive = true;
            x.CompanyId = LicenseManagerFixture.CompanyId;
        });

        _context.LicenseManagers.AddRange(entities);
        _context.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<LicenseManager>>(result);

        var materializedResult = result.ToList();
        Assert.Equal(2, materializedResult.Count);
    }

    [Fact]
    public void GetByReferenceId_ShouldReturnQueryableWithFilter()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.CompanyId = LicenseManagerFixture.CompanyId;
        entity.IsActive = true;

        _context.LicenseManagers.Add(entity);
        _context.SaveChanges();

        // Act
        var result = _repository.GetByReferenceId(entity.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<LicenseManager>>(result);

        var materializedResult = result.ToList();
        Assert.Single(materializedResult);
        Assert.Equal(entity.ReferenceId, materializedResult.First().ReferenceId);
    }

    [Fact]
    public void GetByReferenceId_WithExpression_ShouldReturnFilteredQueryable()
    {
        // Arrange
        var entity1 = _fixture.LicenseManagerDto;
        entity1.CompanyId = LicenseManagerFixture.CompanyId;
        entity1.IsActive = true;
        entity1.IsParent = true;

        var entity2 = _autoFixture.Create<LicenseManager>();
        entity2.ReferenceId = entity1.ReferenceId; // Same reference ID
        entity2.CompanyId = LicenseManagerFixture.CompanyId;
        entity2.IsActive = true;
        entity2.IsParent = false;

        _context.LicenseManagers.AddRange(entity1, entity2);
        _context.SaveChanges();

        // Act
        var result = _repository.GetByReferenceId(entity1.ReferenceId, x => x.IsParent);

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<LicenseManager>>(result);

        var materializedResult = result.ToList();
        Assert.Single(materializedResult);
        Assert.True(materializedResult.First().IsParent);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var entities = _fixture.LicenseManagerList;
     
        entities[2].IsActive = false; // Make one inactive

         _context.LicenseManagers.AddRange(entities);
         _context.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active entities
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllLicenseAsync_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        var entity1 = _fixture.LicenseManagerDto;
        entity1.CompanyId = LicenseManagerFixture.CompanyId;

        var entity2 = _autoFixture.Create<LicenseManager>();
        entity2.CompanyId = "OTHER_COMPANY";

        await _context.LicenseManagers.AddRangeAsync(entity1, entity2);
        await _context.SaveChangesAsync();

        _mockLoggedInUserService.Setup(x=>x.IsParent).Returns(false);
        // Act
        var result = await _repository.ListAllLicense();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only entity from current company
        Assert.Equal(LicenseManagerFixture.CompanyId, result.First().CompanyId);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entity1 = _fixture.LicenseManagerDto;
        entity1.CompanyId = LicenseManagerFixture.CompanyId;

        var entity2 = _autoFixture.Create<LicenseManager>();
        entity2.CompanyId = "OTHER_COMPANY";

        await _context.LicenseManagers.AddRangeAsync(entity1, entity2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // All entities for parent user
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoActiveEntities()
    {
        // Arrange
        var entity = _fixture.LicenseManagerDto;
        entity.IsActive = false;
         _context.LicenseManagers.Add(entity);
         _context.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    public void Dispose()
    {
        _context?.Dispose();
    }
}
