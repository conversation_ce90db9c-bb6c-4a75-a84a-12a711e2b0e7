using AutoFixture;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoGlobalVariableDataAttribute : AutoDataAttribute
{
    public AutoGlobalVariableDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateGlobalVariableCommand>(p => p.VariableName, 10));
            fixture.Customize<CreateGlobalVariableCommand>(c => c.With(b => b.VariableName, "TestVariable"));
            fixture.Customize<CreateGlobalVariableCommand>(c => c.With(b => b.VariableValue, "TestValue"));
            fixture.Customize<CreateGlobalVariableCommand>(c => c.With(b => b.Type, "String"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateGlobalVariableCommand>(p => p.VariableName, 10));
            fixture.Customize<UpdateGlobalVariableCommand>(c => c.With(b => b.Id, 10.ToString()));
            fixture.Customize<UpdateGlobalVariableCommand>(c => c.With(b => b.VariableName, "TestVariable"));
            fixture.Customize<UpdateGlobalVariableCommand>(c => c.With(b => b.VariableValue, "TestValue"));

            return fixture;
        })
    {

    }
}
