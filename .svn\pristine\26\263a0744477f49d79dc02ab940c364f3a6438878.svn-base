using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class OneViewRiskMitigationFailedDrillViewFixture : IDisposable
{
    public static string UserId => "USER_123";

    public List<OneViewRiskMitigationFailedDrillView> OneViewRiskMitigationFailedDrillViewPaginationList { get; set; }
    public List<OneViewRiskMitigationFailedDrillView> OneViewRiskMitigationFailedDrillViewList { get; set; }
    public OneViewRiskMitigationFailedDrillView OneViewRiskMitigationFailedDrillViewDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public OneViewRiskMitigationFailedDrillViewFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data for the view structure
        _fixture.Customize<OneViewRiskMitigationFailedDrillView>(composer => composer
            .With(x => x.ActionDate, () => DateTime.UtcNow.AddDays(-_fixture.Create<int>() % 30))
            .With(x => x.FailedCount, () => _fixture.Create<int>() % 100)
            .With(x => x.FailedWorkflowOperationGroupIds, () => _fixture.Create<string>()));

        OneViewRiskMitigationFailedDrillViewPaginationList = _fixture.CreateMany<OneViewRiskMitigationFailedDrillView>(20).ToList();
        OneViewRiskMitigationFailedDrillViewList = _fixture.CreateMany<OneViewRiskMitigationFailedDrillView>(5).ToList();
        OneViewRiskMitigationFailedDrillViewDto = _fixture.Create<OneViewRiskMitigationFailedDrillView>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithProperties(
        DateTime? actionDate = null,
        int? failedCount = null,
        string failedWorkflowOperationGroupIds = null)
    {
        return _fixture.Build<OneViewRiskMitigationFailedDrillView>()
            .With(x => x.ActionDate, actionDate ?? DateTime.UtcNow.AddDays(-1))
            .With(x => x.FailedCount, failedCount ?? _fixture.Create<int>() % 100)
            .With(x => x.FailedWorkflowOperationGroupIds, failedWorkflowOperationGroupIds ?? _fixture.Create<string>())
            .Create();
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithSpecificFailedCount(int failedCount)
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(failedCount: failedCount);
    }

    public List<OneViewRiskMitigationFailedDrillView> CreateOneViewRiskMitigationFailedDrillViewWithDifferentDates()
    {
        return new List<OneViewRiskMitigationFailedDrillView>
        {
            CreateOneViewRiskMitigationFailedDrillViewWithProperties(actionDate: DateTime.UtcNow.AddDays(-1), failedCount: 5),
            CreateOneViewRiskMitigationFailedDrillViewWithProperties(actionDate: DateTime.UtcNow.AddDays(-7), failedCount: 3),
            CreateOneViewRiskMitigationFailedDrillViewWithProperties(actionDate: DateTime.UtcNow.AddDays(-14), failedCount: 8),
            CreateOneViewRiskMitigationFailedDrillViewWithProperties(actionDate: DateTime.UtcNow.AddDays(-30), failedCount: 2)
        };
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithZeroFailedCount()
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.UtcNow,
            failedCount: 0,
            failedWorkflowOperationGroupIds: string.Empty);
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithMaxFailedCount()
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.UtcNow.AddDays(-1),
            failedCount: int.MaxValue,
            failedWorkflowOperationGroupIds: "critical-group1,critical-group2,critical-group3");
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithNegativeFailedCount()
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.UtcNow.AddDays(-1),
            failedCount: -1,
            failedWorkflowOperationGroupIds: "error-group");
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithWhitespace()
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.UtcNow.AddDays(-1),
            failedCount: 5,
            failedWorkflowOperationGroupIds: "  group1  ,  group2  ,  group3  ");
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithSpecialCharacters()
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.UtcNow.AddDays(-1),
            failedCount: 3,
            failedWorkflowOperationGroupIds: "group@#$%,group-test,group_test,group.test");
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithLongGroupIds(int length)
    {
        var longGroupIds = string.Join(",", Enumerable.Range(1, length / 10).Select(i => $"very-long-group-name-{i}"));
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.UtcNow.AddDays(-1),
            failedCount: 10,
            failedWorkflowOperationGroupIds: longGroupIds);
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithFutureDate()
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.UtcNow.AddDays(30),
            failedCount: 1,
            failedWorkflowOperationGroupIds: "future-group");
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithMinDate()
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.MinValue,
            failedCount: 1,
            failedWorkflowOperationGroupIds: "min-date-group");
    }

    public OneViewRiskMitigationFailedDrillView CreateOneViewRiskMitigationFailedDrillViewWithMaxDate()
    {
        return CreateOneViewRiskMitigationFailedDrillViewWithProperties(
            actionDate: DateTime.MaxValue,
            failedCount: 1,
            failedWorkflowOperationGroupIds: "max-date-group");
    }

    public static class TestData
    {
        public static readonly List<string> ValidGroupIds = new()
        {
            "group1,group2,group3",
            "cyber-security,network-security,data-protection",
            "incident-response,threat-analysis,vulnerability-assessment",
            "backup-restore,disaster-recovery,business-continuity"
        };

        public static readonly List<string> SpecialCharacterGroupIds = new()
        {
            "group@#$,group%^&,group*()_+",
            "group-test,group_test,group.test",
            "group[1],group{2},group(3)",
            "group|pipe,group\\slash,group/forward"
        };

        public static readonly List<int> ValidFailedCounts = new()
        {
            0, 1, 5, 10, 25, 50, 100, 500, 1000
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
