using ContinuityPatrol.Application.Contracts.Job;
using ContinuityPatrol.Application.Features.BulkImportOperation.Events.Create;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;

public class
    CreateBulkImportOperationCommandHandler : IRequestHandler<CreateBulkImportOperationCommand,
        CreateBulkImportOperationResponse>
{
    private readonly IBulkImportOperationGroupRepository _bulkImportOperationGroupRepository;
    private readonly IBulkImportOperationRepository _bulkImportOperationRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IQuartzJobScheduler _client;

    public CreateBulkImportOperationCommandHandler(IMapper mapper,
        IBulkImportOperationRepository bulkImportOperationRepository, IPublisher publisher,
        IBulkImportOperationGroupRepository bulkImportOperationGroupRepository,
        ILoggedInUserService loggedInUserService, IQuartzJobScheduler client)
    {
        _mapper = mapper;
        _publisher = publisher;
        _bulkImportOperationGroupRepository = bulkImportOperationGroupRepository;
        _loggedInUserService = loggedInUserService;
        _client = client;
        _bulkImportOperationRepository = bulkImportOperationRepository;
    }

    public async Task<CreateBulkImportOperationResponse> Handle(CreateBulkImportOperationCommand request,
        CancellationToken cancellationToken)
    {
        //var nodeConfig =
        //    await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
        //    ?? await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),ServiceType.LoadBalancer.ToString());

        //if (nodeConfig is null) throw new InvalidException("LoadBalancer not configured!.");

        request.UserName = _loggedInUserService.LoginName;
        request.CompanyId = _loggedInUserService.CompanyId;
        request.Status = "Pending";
        request.StartTime = DateTime.Now;

        var bulkImportOperation = _mapper.Map<Domain.Entities.BulkImportOperation>(request);

        bulkImportOperation = await _bulkImportOperationRepository.AddAsync(bulkImportOperation);

        var bulkImportOperations = request.BulkImportOperationList.Select(bulkImport =>
        {
            var serverCount = bulkImport.IsServer ? (bulkImport.ServerList?.Count ?? 0) : 0;
            var dbCount = bulkImport.IsDatabase ? (bulkImport.DatabaseList?.Count ?? 0) : 0;
            var replCount = bulkImport.IsReplication ? (bulkImport.ReplicationList?.Count ?? 0) : 0;
            var infraCount = bulkImport.IsInfraObject && bulkImport.InfraObject != null ? 1 : 0;

            var extraFlagCount = (bulkImport!.IsSwitchOver ? 1 : 0)
                                 + (bulkImport!.IsSwitchBack ? 1 : 0)
                                 + (bulkImport!.IsFailOver ? 1 : 0)
                                 + (bulkImport!.IsFailBack ? 1 : 0);

            var totalCount = serverCount + dbCount + replCount + infraCount + extraFlagCount;

            return new Domain.Entities.BulkImportOperationGroup
            {
                InfraObjectName = bulkImport.InfraObject?.Name ?? "NA",
                BulkImportOperationId = bulkImportOperation.ReferenceId,
                CompanyId = _loggedInUserService.CompanyId,
                Properties = JsonConvert.SerializeObject(bulkImport),
                ProgressStatus = $"0/{totalCount}",
                Status = "Pending"
            };
        }).ToList();


        //var bulkImportOperations = request.BulkImportOperationList.Select(bulkImport =>
        //    new Domain.Entities.BulkImportOperationGroup
        //    {
        //        InfraObjectName = bulkImport?.InfraObject?.Name ?? "NA",
        //        BulkImportOperationId = bulkImportOperation.ReferenceId,
        //        CompanyId = _loggedInUserService.CompanyId,
        //        Properties = JsonConvert.SerializeObject(bulkImport),
        //        ProgressStatus =
        //            $"0/{(bulkImport?.ServerList?.Count ?? 0) + (bulkImport?.DatabaseList?.Count ?? 0) + (bulkImport?.ReplicationList?.Count ?? 0) + (bulkImport?.InfraObject != null ? 1 : 0) + (bulkImport!.IsSwitchOver ? 1 : 0) + (bulkImport.IsSwitchBack ? 1 : 0) + (bulkImport.IsFailOver ? 1 : 0) + (bulkImport.IsFailBack ? 1 : 0)}",
        //        Status = "Pending"
        //    }).ToList();

        await _bulkImportOperationGroupRepository.AddRangeAsync(bulkImportOperations);

        await _publisher.Publish(new BulkImportOperationCreatedEvent { Name = bulkImportOperation.UserName },
            cancellationToken);

        await _client.ScheduleJob<BulkImportJob>(bulkImportOperation.ReferenceId,
            new Dictionary<string, string> { ["bulkImportOperationId"] = bulkImportOperation.ReferenceId, ["CompanyId"] = _loggedInUserService.CompanyId, ["UserId"] = _loggedInUserService.UserId, ["operationtype"] = "" });





        //var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        //var url = UrlHelper.GenerateBulkImportUrl(nodeConfig.TypeCategory, baseUrl, bulkImportOperation.ReferenceId);


        //  await _client.ScheduleJob(bulkImportOperation.ReferenceId, new Dictionary<string, string> { ["url"] = url });

        var response = new CreateBulkImportOperationResponse
        {
            Message = Message.Create(nameof(Domain.Entities.BulkImportOperation), bulkImportOperation.UserName),

            Id = bulkImportOperation.ReferenceId
        };

        return response;
    }
}