﻿using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Commands;

public class DeleteGlobalVariableTests : IClassFixture<GlobalVariableFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;
    private readonly Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;
    private readonly DeleteGlobalVariableCommandHandler _handler;

    public DeleteGlobalVariableTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.DeleteGlobalVariableRepository(_globalVariableFixture.GlobalVariables);

        _handler = new DeleteGlobalVariableCommandHandler(_mockGlobalVariableRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_GlobalVariableDeleted()
    {
        var validGuid = Guid.NewGuid();

        _globalVariableFixture.GlobalVariables[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteGlobalVariableCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteGlobalVariableResponse_When_GlobalVariableDeleted()
    {
        var validGuid = Guid.NewGuid();

        _globalVariableFixture.GlobalVariables[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteGlobalVariableCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteGlobalVariableResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_GlobalVariableDeleted_ByReferenceId()
    {
        var validGuid = Guid.NewGuid();

        _globalVariableFixture.GlobalVariables[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteGlobalVariableCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var globalVariable = await _mockGlobalVariableRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        globalVariable.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidGlobalVariableId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteGlobalVariableCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _globalVariableFixture.GlobalVariables[0].ReferenceId = validGuid.ToString();

        _globalVariableFixture.GlobalVariables[0].ReferenceId = Guid.NewGuid().ToString();

        await _handler.Handle(new DeleteGlobalVariableCommand { Id = _globalVariableFixture.GlobalVariables[0].ReferenceId }, CancellationToken.None);

        _mockGlobalVariableRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockGlobalVariableRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.GlobalVariable>()), Times.Once);
    }
}