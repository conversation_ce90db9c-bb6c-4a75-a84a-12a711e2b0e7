using ContinuityPatrol.Application.Features.CyberAlert.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Commands;

public class UpdateCyberAlertTests : IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<UpdateCyberAlertCommandHandler>> _mockLogger;
    private readonly UpdateCyberAlertCommandHandler _handler;

    private readonly Mock<IMapper> _mapper;
    private readonly Mock<IPublisher> _publisher;
    public UpdateCyberAlertTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);
        _mockUserActivityRepository = CyberRepositoryMocks.CreateUserActivityRepository(_cyberAlertFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<UpdateCyberAlertCommandHandler>>();

        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");

        _mapper = new Mock<IMapper>();
        _publisher = new Mock<IPublisher>();
        _handler = new UpdateCyberAlertCommandHandler(_mapper.Object ,
            _mockCyberAlertRepository.Object, _publisher.Object );
    }

    [Fact]
    public async Task Handle_UpdateCyberAlert_When_ValidCommand()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var command = new UpdateCyberAlertCommand
        {
            Id = existingAlert.ReferenceId,
            Severity = "Critical"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync(existingAlert.ReferenceId), Times.Once);
        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAlert>()), Times.Once);
    }

    /// <summary>
    /// Test: Repository interaction updates entity with correct properties
    /// Expected: Entity is updated with all command properties mapped correctly
    /// </summary>
    [Fact]
    public async Task Handle_UpdateEntityWithCorrectProperties_When_ValidCommand()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var command = new UpdateCyberAlertCommand
        {
            Id = existingAlert.ReferenceId,
            Severity = "High",
        };

        Domain.Entities.CyberAlert deletedEntity = null;
        _mockCyberAlertRepository.Setup(x => x.DeleteAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity => deletedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        
    }
    //[Fact]
    //public async Task Handle_ThrowNotFoundException_When_AlertNotFound()
    //{
    //    // Arrange
    //    var command = new UpdateCyberAlertCommand
    //    {
    //        Id = "non-existent-alert-id",
    //        Severity = "Medium"
    //    };

    //    // Act & Assert
    //    await Should.ThrowAsync<NotFoundException>(async () =>
    //        await _handler.Handle(command, CancellationToken.None));

    //    _mockCyberAlertRepository.Verify(x => x.GetByReferenceIdAsync("non-existent-alert-id"), Times.Once);
    //    _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<CyberAlert>()), Times.Never);
    //}

   
    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var command = new UpdateCyberAlertCommand
        {
            Id = existingAlert.ReferenceId,
            Severity = "Medium"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        
    }

    [Fact]
    public async Task Handle_ProcessMultipleUpdateCommands_When_ValidCommands()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlerts.Take(3).ToList();
        var commands = alerts.Select((alert, index) => new UpdateCyberAlertCommand
        {
            Id = alert.ReferenceId,
            Severity = new[] { "Low", "Medium", "High" }[index]
        }).ToArray();

        // Act
        foreach (var command in commands)
        {
            var result = await _handler.Handle(command, CancellationToken.None);
            result.Success.ShouldBeTrue();
        }

        // Assert
        _mockCyberAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAlert>()), Times.Exactly(3));
    }

    [Fact]
    public async Task Handle_HandleStatusProgression_When_ValidStatusUpdates()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var statusProgression = new[] { "Open", "Acknowledged", "In Progress", "Under Investigation", "Resolved" };

        foreach (var status in statusProgression)
        {
            var command = new UpdateCyberAlertCommand
            {
                Id = existingAlert.ReferenceId,
                Severity = "Medium"
            };

            Domain.Entities.CyberAlert deletedEntity = null;
            _mockCyberAlertRepository.Setup(x => x.DeleteAsync(It.IsAny<Domain.Entities.CyberAlert>()))
                .Callback<Domain.Entities.CyberAlert>(entity => deletedEntity = entity)
                .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Success.ShouldBeTrue();
        }
    }

    [Fact]
    public async Task Handle_HandleSeverityEscalation_When_ValidSeverityUpdates()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var severityEscalation = new[] { "Low", "Medium", "High", "Critical" };

        foreach (var severity in severityEscalation)
        {
            var command = new UpdateCyberAlertCommand
            {
                Id = existingAlert.ReferenceId,
                Severity = severity
            };

            Domain.Entities.CyberAlert deletedEntity = null;
            _mockCyberAlertRepository.Setup(x => x.DeleteAsync(It.IsAny<Domain.Entities.CyberAlert>()))
                .Callback<Domain.Entities.CyberAlert>(entity => deletedEntity = entity)
                .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Success.ShouldBeTrue();
        }
    }
    [Fact]
    public async Task Handle_HandlePartialUpdates_When_PartialCommand()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();

        var command = new UpdateCyberAlertCommand
        {
            Id = existingAlert.ReferenceId,
            Severity = "Critical"
        };

        Domain.Entities.CyberAlert deletedEntity = null;
        _mockCyberAlertRepository.Setup(x => x.DeleteAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity => deletedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleSpecialCharactersInUpdate_When_ValidCommand()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var command = new UpdateCyberAlertCommand
        {
            Id = existingAlert.ReferenceId,
            Severity = "High"
        };

        Domain.Entities.CyberAlert deletedEntity = null;
        _mockCyberAlertRepository.Setup(x => x.DeleteAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity => deletedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.ShouldBeTrue();
    }

    /// <summary>
    /// Test: Command with long text fields
    /// Expected: Long text fields are handled correctly in updates
    /// </summary>
    [Fact]
    public async Task Handle_HandleLongTextFields_When_ValidCommand()
    {
        // Arrange
        var existingAlert = _cyberAlertFixture.CyberAlerts.First();
        var longTitle = new string('A', 200);
        var longDescription = new string('B', 1000);

        var command = new UpdateCyberAlertCommand
        {
            Id = existingAlert.ReferenceId,
            Severity = "Medium"
        };

        Domain.Entities.CyberAlert deletedEntity = null;
        _mockCyberAlertRepository.Setup(x => x.DeleteAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity => deletedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.ShouldBeTrue();
    }
}
