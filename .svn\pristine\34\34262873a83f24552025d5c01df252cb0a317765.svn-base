﻿$('#createpageModal, #widgetCreationCard').hide()
$(".contextMenu").css({ display: 'none' });
$(".footerContainer").addClass('d-none')
$(".contextMenuCustom").css({ display: 'none' });
$("#backBtn").hide()
let buttonContent = ['table', 'chart']
let globalPageList = []
let globalWidgetId = ""
let globalLayout = ""

let monitoringwiseInfraList=[]

getPageBuilderList();
GetMonitorTypeByInfraObject();
layoutcarddraggable()
let noDataImageData = "<figure class='figure'><img src='../../img/isomatric/no_data_found.svg' class='Card_NoData_Img' style='width: 250px;margin-top:100px'><figcaption class='figure-caption text-center text-danger'>No Data Records Found</figcaption></figure > "
/*$(".pageBuilderSetColumnResize").resizable();*/
$('#blankpage').on('click', function () {
    $('#homepageModal').hide()
    $("#createpageModal").show()
    $(".footerContainer").addClass('d-none')
})
function allowDrop(e) {
    e.preventDefault();
}
function allowRowDrop(e) {
    e.preventDefault();
}
function allowColumnDrop(e) {
    e.preventDefault();
}

$(".btn_preview").on('click', function (e) {
    $(".preview-chart").empty()
    $("#previewZoom").modal("show")
    $(".preview-chart").append($("#widgetCreationCard").html())
})

let dragText = '';
function dragg(e) {
    dragText = $(e.srcElement).attr('name')
    e.dataTransfer.setData("text", $(e.srcElement).attr('name'));
    e.dataTransfer.setData("htmlElement", e.target.getAttribute("htmlPropertics"))
    e.dataTransfer.setData("name", e.target.getAttribute("name"))
}

$("#pageHeight").on("keyup", function () {
    let pageHeight = $("#pageHeight").val();
    validateHeight(pageHeight)
})
$("#pageWidth").on("keyup", function () {
    let pageWidth = $("#pageWidth").val();
    validateWidth(pageWidth)
})
$("#PageAdd").on("click", function () {

    let randomNum = Math.floor(Math.random() * 90000) + 10000;
    let pageHeight = $("#pageHeight").val();
    let pageWidth = $("#pageWidth").val();
    validateHeight(pageHeight)
    validateWidth(pageWidth)
    let style = $(".pageBuilderSetDesign").last().css("top")
    let gettopVlaue;
    if (style) {
        gettopVlaue = style.trim() // Output: e.g., "100px"
    }
    else {
        gettopVlaue = 0;
    }

    pageHeight && pageWidth ? $("#PB_pageContent").append('<div class="layoutcard pageBuilderSetDesign border-dashed PB_layout1Design' + randomNum + '" designId="PB_layout1Design' + randomNum + '" id="card" ondrop="drop(event)" onclick="pageWidgetListModel(this)" style="height:' + pageHeight + 'px;width:' + pageWidth + 'px;margin-bottom:10px; position:relative;top:' + gettopVlaue +' "  ondragover="allowDrop(event)"></div>') : ""

    LayoutHeightDiv()

    //$('#PB_pageContent .PB_layout1Design' + randomNum + '').each(function () {
    //    $(this).css({
    //        top: '',
    //        left: '',
    //        position: 'relative',
    //        marginBottom: '10px'
    //    });
    //});
    layoutcarddraggable()

    //$('.PB_layout1Design' + randomNum ).css({
    //    'margin': '0',
    //    'padding': '0'
    //});

    $("#pageHeight").val("")
    $("#pageWidth").val("")

    
})

function pageWidgetListModel(data){
    
    SliderbarPageBuilderList(data)

}

$("#WidgetListApply").on("click", function (e) {

    let selectWidgetList = $("#selectWidgetList option:selected").val()
    let designid = e.target.getAttribute("designid")
    globalWidgetDetails.forEach((data) => {
        let widgetHtml = JSON.parse(data.properties)
        if (selectWidgetList == widgetHtml.widgetId) {
            let randomNum = Math.floor(Math.random() * 90000) + 10000;
            let widgetElement = document.createElement('div');
            // Set the HTML content of the new element
            widgetElement.className = "pageDiagram"
            widgetElement.id = "pageDiagram" + randomNum
            widgetElement.innerHTML = widgetHtml.widgetDetails;
            // Append the new element to the current target
            $("." + designid).empty();
            //$("." + designid).css("height", "")
            $("." + designid).append(widgetElement);
            $(".pageBuilderInfraId").removeClass("dotted-border")
            $("#" + "pageDiagram" + randomNum).attr("datasetId", widgetHtml.datasetId)
            $("#" + "pageDiagram" + randomNum).attr("datasetName", widgetHtml.datasetName)
            $("#" + "pageDiagram" + randomNum).attr("datasetQuery", widgetHtml.datasetQuery)
            $("#" + "pageDiagram" + randomNum).attr("datasetType", widgetHtml.datasetType)
            $("#" + "pageDiagram" + randomNum).attr("chartType", widgetHtml.chartType)
            $("#" + "pageDiagram" + randomNum).attr("xView", widgetHtml.xView)
            $("#" + "pageDiagram" + randomNum).attr("yview", widgetHtml.yview)
            $("#" + "pageDiagram" + randomNum).attr("title", widgetHtml.title)
            //$(".pageDiagram").attr("id", "pageDiagram" + randomNum)
        }

    })
    $("#WidgetList").modal("hide")
})


async function validateHeight(value) {
    const errorElement = $('#pageHeight_Error');
    if (!value) {
        errorElement.text("Enter height");
        errorElement.addClass('field-validation-error');
        return false;
    } else if (value.length > 6) {
        errorElement.text("Enter the less than 6 digits");
        errorElement.addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}

async function validateWidth(value) {
    const errorElement = $('#pageWidth_error');
    if (!value) {
        errorElement.text("Enter width");
        errorElement.addClass('field-validation-error');
        return false;
    } else if (value.length > 6) {
        errorElement.text("Enter the less than 6 digits");
        errorElement.addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}

function drop(e) {   
    $('#widgetCreationCard').empty();
    if (e.dataTransfer.getData("name") != "chart") {
        let htmlElement = e.dataTransfer.getData('htmlElement')
        let cardData = htmlElement   
        $('#widgetCreationCard').append(cardData);
    }
    else {
        ChartFunction("bar");
    }
    //if (dragText === "table") {
    //    $('#widgetChartData').hide()
    //    setTimeout(() => {
    //        $('#widget_dragContent').append(tableContainer)
    //    },200)
    //}
    $('#collapseExample').collapse('show');
}



//$(function () {
//    $('#widget_dragContent').droppable();
//})

$("#PageCardApply").on("click", function () {
    $('#PB_pageContent').empty()
    $('#PB_pageContent').append($("#PB_pageLayoutContent").children())
    $("#layoutDesignModal").modal("hide")
    $(".pageBuilderSetRowDesign").removeAttr("ondrop")
    SliderbarPageBuilderList()
    layoutcarddraggable()
})

$("#pageBuilderEditApply").on('click', function () {
    $(".btn_save").removeAttr("pageId")
    $(".footerContainer").removeClass('d-none')
    $('#homepageModal').hide()
    $("#createpageModal").show()
    $('#PB_pageContent').empty()
    $("#createLayoutBtn").hide()
    $(".hideshowPage").hide();
    $(".sideSearchButton").hide();
    $("#backBtn").show()
    let titleName = $("#pageEditTitle").val()
    $("#pageBuilderTitle").text(titleName)
    let titleId = $("#pageBuilderEditApply").attr("pageId")
    SliderbarPageBuilderList()
    globalWidgetId = ""
    globalPageList.forEach((page) => {
        if (titleId == page.id) {
            let jsonpropertices = JSON.parse(page.properties)
            //$("#pageBuilderTitle").text(page.name)
            globalWidgetId = jsonpropertices.pagebuilderId
            $('#PB_pageContent').append(jsonpropertices.pageDetails)
            $(".btn_save").text("")
            $(".btn_save").text("Update")
            $(".btn_save").attr("pageId", page?.id)
            $(".btn_save").attr("layoutname", jsonpropertices?.layoutname)
            if (jsonpropertices.layoutname =='PB_layout15') {
                $(".cardCreation").addClass('card-show')
                $(".cardCreation").removeClass("d-none")
            }
            //$('#PB_pageContent').append('<div class="text-end"><button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button><button pageId=' + page.id + ' name=' + page.name + ' class="btn btn-sm btn-primary btn_save" type="button">Update</button></div>')
        }
    })
    $("#TitleEditModal").modal("hide")
    $(".searchWidgets").removeClass("d-none")

    layoutcarddraggable()
})




$('.PB_layout').on('click', function () {   
    $(".pageBuilderSetRowDesign").empty()
    $("#pageBuilderApply").attr("layoutName", this.id)
    $("#TitleModal").modal('show');
})



$("#search-PageBuilder").on("keyup", function () {
    
    let filter = $(this).val();
    $(".pagebuilderlist .widgetPageBuilder").each(function () {

        let $i = 0;
        $(this).find(".widgetPageBuilderName").each(function () {
            
            let splitText = $(this).text().trim()
            if (splitText.search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }
        });
        if ($i > 0) {
            $(this).closest(".pagebuilderlist .widgetPageBuilder").show();
        } else {
            $(this).closest(".pagebuilderlist .widgetPageBuilder").hide();
        }
    });
})


//$("#search-inp").on("keyup",function () {

//    let filter = $(this).val();
//    $("#pageBuilderWidgets .widget").each(function () {

//        let $i = 0;
//        $(this).find(".widgetName").each(function () {

//            let splitText = $(this).text()
//            if (splitText.search(new RegExp(filter, "i")) >= 0) {
//                $i++;
//            }
//        });
//        if ($i > 0) {
//            $(this).closest("#pageBuilderWidgets .widget").show();
//        } else {
//            $(this).closest("#pageBuilderWidgets .widget").hide();
//        }

//    });

//})

//document.getElementById("search-PageBuilder").addEventListener("search", function (event) {
//    $(".pagebuilderlist .widgetPageBuilder").removeAttr("style")
//});
//document.getElementById("search-inp").addEventListener("search", function (event) {
//    $("#pageBuilderWidgets .widget").removeAttr("style")
//});

function pagebuilderTitleValidation(value) {
    if (!value) {
        $('#pageTitleError').text('Enter title').addClass('field-validation-error');
        return false;
    } else {
        $('#pageTitleError').text('').removeClass('field-validation-error');
        return true;
    }
}

$('#pageTitle').on('change', async function () {
    let pageBuilderTitle = $('#pageTitle').val();
    await pagebuilderTitleValidation(pageBuilderTitle);
});

$("#createLayoutBtn").on('click', function () {   
    $(".dropdownComponentType").removeClass("d-none")
})

$('#backBtn').on('click', function () {
    $("#createLayoutBtn").show();
    $(".hideshowPage").show();
    $(".sideSearchButton").show();
    $("#backBtn").hide();
    $('#pageTitle').val('');
    $(".footerContainer").addClass('d-none')
    $("#homepageModal").show();
    $("#createpageModal").hide();
    $('#PB_pageContent').empty();
    let pageTitle = document.getElementById("pageBuilderTitle");
    pageTitle.textContent = 'Page Builder';
});
$(document).on("click", "#cancelBtn",function () {
    $("#createLayoutBtn").show();
    $(".hideshowPage").show();
    $(".sideSearchButton").show();
    $("#backBtn").hide();
    $('#pageTitle').val('');
    $(".footerContainer").addClass('d-none')
    $("#homepageModal").show();
    $("#createpageModal").hide();
    $('#PB_pageContent').empty();
    let pageTitle = document.getElementById("pageBuilderTitle");
    pageTitle.textContent = 'Page Builder';
    $(".searchWidgets").addClass("d-none")
    $(".cardCreation").addClass("d-none")
});

$(document).on("click", ".btn_save",async function () {

    let name = $("#pageTitle").val();
    let randomNum = Math.floor(Math.random() * 90000) + 10000;
    //if (name == "") {

    //    return false; 
    //}
    let builderHtml = document.getElementById("PB_pageContent").innerHTML;
    let data = {}
    let href = ''
    let container = document.getElementById('PB_pageContent'); /* full page */
    let layoutname = $(".btn_save").attr("layoutname")
    let infraid = this.getAttribute('infraid')
    let monitortype = this.getAttribute('monitortype')
    await html2canvas(container, { allowTaint: true, useCORS: true }).then(function (canvas) {

        href = canvas.toDataURL('image/png');

    });
    let commonId;
    if ($(".btn_save").text() == 'Update') {
        data.id = this.getAttribute('pageid')
        data.Name = $("#pageBuilderTitle").text();
        commonId = globalWidgetId
    }
    else {
        commonId = 'page_' + randomNum
        data.Name = name
    }
    data.__RequestVerificationToken=gettoken()
    data.Properties = JSON.stringify({
        pagebuilderId: commonId,
        pageDetails: builderHtml,
        hrefImage: href,
        Name: data.Name,
        layoutname: layoutname,
        infraObjectId: infraid,
        monitorType: monitortype

    })
    $.ajax({
        type: "POST",
        url: RootUrl + 'Admin/ConfigurePage/CreateOrUpdate',
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                $(".footerContainer").addClass('d-none')
                $('#homepageModal').show()
                $("#createpageModal").hide()
                $("#createLayoutBtn").show();
                $(".hideshowPage").show();
                $(".sideSearchButton").show();
                $("#backBtn").text("Create Layout")
                $('#alertClass').removeClass("info-toast")
                $('#alertClass').addClass("success-toast")
                $('#notificationAlertmessage').text(result.message)
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                $(".iconClass").removeClass("cp-exclamation")
                $(".iconClass").addClass("cp-check")
                $("#widgetTitle").val("")
                $("#componentTitle").val("")
                getPageBuilderList()
                $(".searchWidgets").addClass("d-none")
                $(".cardCreation").addClass("d-none")
                $("#pageBuilderTitle").text("Page Builder")
            }

        }
    })

})


$("#selectComponentType").on("change", function (e) {
    $("#selectInfraobjectName").empty()
    let ComponentType = $("#selectComponentType option:selected").val()

    let monitoringFilterInfra = monitoringwiseInfraList.filter((data) => {
        return data.monitorType == ComponentType
    })
    if (monitoringFilterInfra.length != 0) {

        monitoringFilterInfra.forEach((data, index) => {

            if (index == 0) {

                $("#selectInfraobjectName").append('<option value="">Select InfraObject</option>')
            }

            $("#selectInfraobjectName").append('<option value="' + data.infraObjectId + '">' + data.infraObjectName + '</option>')
        })  
    }
})


$("#pageBuilderApply").on('click',async function (e) {
    $(".searchWidgets").removeClass("d-none")
    let pageBuilderTitle = $('#pageTitle').val();
    let titleValidation = await pagebuilderTitleValidation(pageBuilderTitle);
    let solutionType=$("#selectComponentType").val()
    if (titleValidation) {  
        
        $("#LayoutModal").modal('hide')
        $(".footerContainer").removeClass('d-none')
        $("#homepageModal").hide();
        $("#createLayoutBtn").hide();
        $(".hideshowPage").hide();
        $(".sideSearchButton").hide();
        
        $("#backBtn").show();
        $("#TitleModal").modal('hide');
        $("#createpageModal").show();
        $('#PB_pageContent').empty();
        $(".btn_save").text("Save")

        
        //$('#pageTitle').text($("#pageBuilderTitle").text());
        let pageTitle = document.getElementById("pageBuilderTitle");
        let InfraobjectId = $("#selectInfraobjectName option:selected").val()
        let monitorType = $("#selectComponentType option:selected").val()
        pageTitle.textContent = $('#pageTitle').val();
        let layoutName = this.getAttribute("layoutname")
        $(".btn_save").attr("layoutName", layoutName)
        $(".btn_save").attr("infraId", InfraobjectId)
        $(".btn_save").attr("monitorType", monitorType)
        pageLayoutDesign(layoutName)
        //SliderbarPageBuilderList()
    }

    
})
function draggLayout(e) {
    
    globalLayout = ""
    let appendLayout = ""
    let ColumnLayout = e.target.getAttribute("layoutVal")
    let rowNo = Math.floor((Math.random() * 1000000) + 1);
    if (e.target.getAttribute("name") == "row") {
        e.target.getAttribute("name")
        appendLayout = `
            <div class="${ColumnLayout}  border-dashed h-100  pageBuilderSetRowDesign"  id="pageBuilderInfraId_${rowNo}" ondrop="rowRowDropLayout(event)" ondragover="allowRowDrop(event)"> 
            </div>
    `
        $(".pageBuilderSetRowDesign").removeAttr("ondrop")
        $(".PB_pageLayoutContent").attr("ondrop", "dropLayout(event)")
    }
    else {
        appendLayout = `
            <div class="${ColumnLayout} g-3 pageBuilderSetColumnResize" id="pageBuilderSetColumnResize">
                <div class="card h-100  border-dashed ui-widget-content pageBuilderSetColumnDesign" id="pageBuilderSetColumnDesign" ondrop="drop(event)" ondragover="allowDrop(event)">  
                </div>
            </div>
    `
        $(".PB_pageLayoutContent").removeAttr("ondrop")
        $(".pageBuilderSetRowDesign").attr("ondrop", "rowRowDropLayout(event)")
    }
    globalLayout = appendLayout
    //$("#PB_pageLayoutContent").append(appendLayout)
}

//$("#pageBuilderSetColumnDesign").resizable();


function dropLayout(e) {
    
    e.preventDefault()
    
    $("#PB_pageLayoutContent").append(globalLayout)

}

function rowRowDropLayout(e) {
    e.preventDefault()
    
    $("#" + e.currentTarget.id).append(globalLayout)
    
    $(".pageBuilderSetColumnResize").resizable();
}


function GetMonitorTypeByInfraObject() {
    $.ajax({
        type: "GET",
        url: RootUrl + 'Admin/ConfigurePage/GetMonitorTypeByInfraObject',
        dataType: "json",
        traditional: true,
        success: function (result) {
            
            if (result.success) {
                monitoringwiseInfraList = result.data
                
            }
        }
    })
}



function SliderbarPageBuilderList(data) {
    $.ajax({
        type: "GET",
        url: RootUrl + 'Admin/ConfigureWidget/GetPageWidgetList',
        dataType: "json",
        traditional: true,
        success: function (result) {
            globalWidgetDetails = []
            $("#selectWidgetList").empty()
            if (result.success) {
                if (result.message.length != 0) {
                    result.message.forEach((data, index) => {
                        globalWidgetDetails.push(data)
                        let widgetHtml = JSON.parse(data.properties)

                        let html = "";
                        if (index == 0) {

                            html += '<option value="">Select</option>'
                        }
                        html += '<option value=' + widgetHtml.widgetId + '> ' + data.name + '</option>'

                        $("#selectWidgetList").append(html)

                    })
                    let designid = data.getAttribute("designid")
                    $("#WidgetListApply").attr("designid", designid)
                    $("#WidgetList").modal("show")
                }
            }
        }
    })
}



function dragg(e) {
    
    //$("#widgetCreationCard").removeClass('d-none')
    //$("#collapseExample").removeClass('d-none')
    //dragText = $(e.srcElement).attr('name')
    //e.dataTransfer.setData("text", $(e.srcElement).attr('name'));
    //e.dataTransfer.setData("htmlElement", e.target.getAttribute("htmlPropertics"))
    //e.dataTransfer.setData("name", e.target.getAttribute("name"))
    e.dataTransfer.setData("widgetid", e.currentTarget.getAttribute("widgetid"))

}

function drop(e) {
    globalWidgetDetails.forEach((data) => {
        let widgetHtml = JSON.parse(data.properties)
        if (e.dataTransfer.getData("widgetid") == widgetHtml.widgetId) {
            let randomNum = Math.floor(Math.random() * 90000) + 10000;
            let widgetElement = document.createElement('div');
            // Set the HTML content of the new element
            widgetElement.className = "pageDiagram"
            widgetElement.id = "pageDiagram" + randomNum
            widgetElement.innerHTML = widgetHtml.widgetDetails;
            // Append the new element to the current target
            $(e.currentTarget).empty();
            $(e.currentTarget).css("height","")
            e.currentTarget.appendChild(widgetElement);
            $("#" + "pageDiagram" + randomNum).attr("datasetId", widgetHtml.datasetId)
            $("#" + "pageDiagram" + randomNum).attr("datasetName", widgetHtml.datasetName)
            $("#" + "pageDiagram" + randomNum).attr("datasetQuery", widgetHtml.datasetQuery)
            $("#" + "pageDiagram" + randomNum).attr("datasetType", widgetHtml.datasetType)
            $("#" + "pageDiagram" + randomNum).attr("chartType", widgetHtml.chartType)
            $("#" + "pageDiagram" + randomNum).attr("xView", widgetHtml.xView)
            $("#" + "pageDiagram" + randomNum).attr("yview", widgetHtml.yview)
            $("#" + "pageDiagram" + randomNum).attr("title", widgetHtml.title)

            //$(".pageDiagram").attr("id", "pageDiagram" + randomNum)
        }

        //var table = $('.table-builder').DataTable({
        //    //columnDefs: [{ targets: [1, 2, 3,], className:"truncate" }],
        //    //createdRow: function (row) {
        //    //    $(row).find(".truncate").each(function () {
        //    //        $(this).attr("title", this.innerText);
        //    //    });
        //    //},
        //    language: {
        //        paginate: {
        //            next: '<i class="cp-rignt-arrow"></i>',
        //            previous: '<i class="cp-left-arrow"></i>'
        //        }
        //    },
        //    dom: '<"pull-left"B><"pull-right"f>rt<"row mt-3"<"col"l><"col text-center"i><"col"p>>',
        //    scrollY: true,
        //    deferRender: true,
        //    scroller: true,
        //});
        //$('#search-inp').on('keyup', function () {
        //    table.search($(this).val()).draw();
        //});
    })
}

function getPageBuilderList() {
    $.ajax({
        type: "GET",
        url: RootUrl + 'Admin/ConfigurePage/GetPageBuilderList',
        dataType: "json",
        traditional: true,
        success: function (result) {
            
            globalPageList = []
            $(".pagebuilderlist").empty()
            if (result.success) {
                if (result.message.length != 0) {
                    $(".figure").remove()
                    $(".pagebuilderlist").removeClass('text-center')
                result.message.forEach((data) => {
                    globalPageList.push(data)
                    let pageHtml = JSON.parse(data.properties)
                    let html = "";
                    html += '<div class="col widgetPageBuilder">'
                    html += '<div class="card border mb-0 h-100" >'
                    html += '<div class="dropdown d-flex justify-content-md-end" >'
                    html += '<i class="cp-horizontal-dots p-1" title="More" role="button" data-bs-toggle="dropdown" aria-expanded="false"></i>'
                    html += '<ul class="dropdown-menu" style="">'
                    html += '<li class="editActionList" id="' + data.id + '" name="' + data.name + '" onclick="editPageView(this,event)"> <a class="dropdown-item" href="#"><i class="cp-edit me-2" title="Edit"></i>Edit</a></li>'
                    html += '<li class="deleteActionList " id="' + data.id + '" name="' + data.name + '" onclick="deletePageListView(this)" data-bs-toggle="modal" data-bs-target="#DeleteModal"><a class="dropdown-item" href="#"><i class="cp-Delete me-2" title="Delete"></i>Delete</a></li>'
                    html += '<li class="RunActionList " id="' + data.id + '" name="' + data.name + '" infraObjectId="' + pageHtml.infraObjectId + '" monitorType="' + pageHtml.monitorType +'" onclick="RunPageListView(this)" ><a class="dropdown-item" ><i class="cp-download me-2" title="Run"></i>Run</a></li>'
                    html += '</ul>'
                    html += '</div >'
                    html += '<div class="card-body">'
                    html += '<img src = "' + pageHtml.hrefImage + '" class="w-100" />'
                    html += '</div>'
                    html += '<div class="card-footer  widgetPageBuilderName list-title text-center" title="' + data.name + '">' + data.name + '</div>'
                    html += '</div>'
                    html += '</div>'
                    $(".pagebuilderlist").append(html)
                })
                }
                else {
                    $("#homepageModal").append(noDataImageData)
                    $("#homepageModal").addClass('text-center')
                }
            }
        }
    })
}






function editPageView(e,data) {
    $(".dropdownComponentType").addClass("d-none")
    $("#TitleEditModal").modal("show")
    $("#pageBuilderEditApply").attr("pageId", e.getAttribute("id"))
    $("#pageEditTitle").val(e.getAttribute("name"))

}
function RunPageListView(data) {

    sessionStorage.setItem("dashboardViewId", data.id)
    sessionStorage.setItem("infraobjectId", data.getAttribute("infraObjectId"))
    sessionStorage.setItem("moniterType", data.getAttribute("monitorType"))
    
    window.location.href = "/Admin/ITViewBuilder/List" 

}
function deletePageListView(data) {

    $("#deleteData").text(data.getAttribute("name"));
    let workflowpageId = data.id
    $('#textDeleteId').val(workflowpageId);

    $('#commonFormDelete').attr('action', '/Admin/ConfigurePage/Delete')

}

//data - bs - toggle="offcanvas" data - bs - target="#offcanvasScrolling" aria - controls="offcanvasScrolling"


// Hide context menu on clicking anywhere else
$(document).on("click", function () {
    $("#contextMenu").hide();
});
let currentFlag = false;
// Show context menu based on class type
$(document).on("contextmenu", ".pageDiagram, .pageBuilderSetDesign", function (event) {


    event.preventDefault();

    if (currentFlag) {
        currentFlag=false
        return false;
    }
    // Hide any existing context menu first
    $("#contextMenu").hide();

    // Show the context menu at the mouse location
    $("#contextMenu").css({
        display: 'block',
        top: event.pageY,
        left: event.pageX
    });

    const targetId = event.currentTarget.id;
    const designId = event.currentTarget.getAttribute("designid");

    // Clear previous attributes
    $("#btnDelete, #btncopy").removeAttr("currentTargetId");

    // Conditionally assign currentTargetId based on element type
    if ($(event.currentTarget).hasClass("pageDiagram")) {
        currentFlag=true
        $("#btnDelete").attr("currentTargetId", targetId);
    } else if ($(event.currentTarget).hasClass("pageBuilderSetDesign")) {
        currentFlag = true
        $("#btnDelete").attr("currentTargetId", designId);
        $("#btncopy").attr("currentTargetId", designId);
    }
});

$("#btnDelete").on("click", function () {

    let currenttargetid = this.getAttribute("currenttargetid")
    $("#" + currenttargetid).remove()
     $("." + currenttargetid).remove()

    LayoutHeightDiv()
})

$("#btncopy").on("click", function () {

    let currenttargetid = this.getAttribute("currenttargetid")

    $("#PB_pageContent").append($("." + currenttargetid).html())
})


//$("#btnDeleteCustom").on("click", function () {

//    let currenttargetid = this.getAttribute("currenttargetid")
//    $("#" + currenttargetid).remove()
//})


//$("#layoutDesignModal .modal-content").on("contextmenu", ".pageBuilderSetColumnResize", function (event) {
//    event.preventDefault();

//    $("#contextMenuCustom").css({
//        display: 'block', // Show the menu
//        top: event.pageY, // Make the menu appear at the click location (Y)
//        left: event.pageX // Make the menu appear at the click location (X)
//    });
//    $("#btnDeleteCustom").attr("currentTargetId", event.currentTarget.id);
//});


function LayoutHeightDiv() {
    let totalLayoutHeight = 0;

    if ($(".layoutcard").length != 0) {
        $(".layoutcard").each(function () {
            let left = parseFloat($(this).css("left"));
            let top = parseFloat($(this).css("top"));
            debugger
            if (left <= 3) {
                let height = $(this).outerHeight(true);
                totalLayoutHeight += Math.max(totalLayoutHeight, top + height);
            }
        });
        $("#PB_pageContent").height(totalLayoutHeight);
    }
}