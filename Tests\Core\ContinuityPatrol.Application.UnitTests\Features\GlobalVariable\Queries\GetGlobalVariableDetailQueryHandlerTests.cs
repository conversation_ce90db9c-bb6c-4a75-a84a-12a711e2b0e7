﻿using AutoMapper;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Queries;

public class GetGlobalVariableDetailQueryHandlerTests : IClassFixture<GlobalVariableFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;
    private readonly Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;
    private readonly GetGlobalVariableDetailsQueryHandler _handler;
    private readonly GetGlobalVariableDetailsQueryHandler _invalidHandler;

    public GetGlobalVariableDetailQueryHandlerTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.GetGlobalVariableRepository(_globalVariableFixture.GlobalVariables);

        _handler = new GetGlobalVariableDetailsQueryHandler(_globalVariableFixture.Mapper, _mockGlobalVariableRepository.Object);

        var mockInvalidGlobalVariableRepository = GlobalVariableRepositoryMocks.GetGlobalVariableRepository(_globalVariableFixture.InvalidGlobalVariables);

        _invalidHandler = new GetGlobalVariableDetailsQueryHandler(_globalVariableFixture.Mapper, mockInvalidGlobalVariableRepository.Object);

        _globalVariableFixture.GlobalVariables[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
    }

    [Fact]
    public async Task Handle_Return_GlobalVariableDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetGlobalVariableDetailQuery { Id = _globalVariableFixture.GlobalVariables[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<GlobalVariableDetailVm>();
        result.Id.ShouldBeGreaterThan(0.ToString());
        result.VariableName.ShouldNotBeEmpty();
        result.VariableValue.ShouldNotBeEmpty();
        result.Type.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidGlobalVariableId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetGlobalVariableDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OneTime_When_ValidId()
    {
        await _handler.Handle(new GetGlobalVariableDetailQuery { Id = _globalVariableFixture.GlobalVariables[0].ReferenceId }, CancellationToken.None);

        _mockGlobalVariableRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyVariableValue_When_GlobalVariableValueIsEmpty()
    {
        var result = await _invalidHandler.Handle(
            new GetGlobalVariableDetailQuery { Id = _globalVariableFixture.InvalidGlobalVariables[2].ReferenceId }, CancellationToken.None);

        result.VariableValue.ShouldBeEmpty();
    }

    [Fact]
    public async Task Handle_Return_ValidVariableValue_When_GlobalVariableValueIsNotEmpty()
    {
        var result = await _handler.Handle(new GetGlobalVariableDetailQuery { Id = _globalVariableFixture.GlobalVariables[0].ReferenceId }, CancellationToken.None);

        result.VariableValue.ShouldNotBeEmpty();
    }
}