using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Queries;

public class GetCyberAirGapListTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<ICyberAirGapRepository> _mockCyberAirGapRepository;
    private readonly GetCyberAirGapListQueryHandler _handler;

    public GetCyberAirGapListTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockCyberAirGapRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(_cyberAirGapFixture.CyberAirGaps);

        _handler = new GetCyberAirGapListQueryHandler(
            _cyberAirGapFixture.Mapper,
            _mockCyberAirGapRepository.Object);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapList_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();
        var activeAirGaps = _cyberAirGapFixture.CyberAirGaps.Where(x => x.IsActive).ToList();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CallListAllAsync_OnlyOnce()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockCyberAirGapRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoAirGapsExist()
    {
        // Arrange
        var emptyAirGaps = new List<Domain.Entities.CyberAirGap>();
        var mockEmptyRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(emptyAirGaps);
        var emptyHandler = new GetCyberAirGapListQueryHandler(_cyberAirGapFixture.Mapper, mockEmptyRepository.Object);
        var query = new GetCyberAirGapListQuery();

        // Act
        var result = await emptyHandler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnOnlyActiveAirGaps_When_MixedActiveInactive()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();

        // Set some air gaps as inactive
        var airGapsToDeactivate = _cyberAirGapFixture.CyberAirGaps.Take(1).ToList();
        foreach (var airGap in airGapsToDeactivate)
        {
            airGap.IsActive = false;
        }

        var activeCount = _cyberAirGapFixture.CyberAirGaps.Count(x => x.IsActive);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_VerifyPropertyMapping_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_MaintainOrderConsistency_When_MultipleCalls()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();

        // Act
        var result1 = await _handler.Handle(query, CancellationToken.None);
        var result2 = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result1.ShouldNotBeNull();
        result2.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_PreserveJsonProperties_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
    }

    [Fact]
    public async Task Handle_ValidatePortNumbers_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
       
    }

    [Fact]
    public async Task Handle_ReturnCorrectCount_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();
        var expectedCount = _cyberAirGapFixture.CyberAirGaps.Count(x => x.IsActive);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
       
    }

    /// <summary>
    /// Test: Each item in list has unique ID
    /// Expected: All air gap IDs are unique
    /// </summary>
    [Fact]
    public async Task Handle_EnsureUniqueIds_When_ValidQuery()
    {
        // Arrange
        var query = new GetCyberAirGapListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
      //  result.CyberAirGaps.ShouldNotBeEmpty();

       // var ids = result.CyberAirGaps.Select(x => x.Id).ToList();
      //  var uniqueIds = ids.Distinct().ToList();

      //  ids.Count.ShouldBe(uniqueIds.Count);
    }

    /// <summary>
    /// Test: List performance with large dataset
    /// Expected: Handles large datasets efficiently
    /// </summary>
    [Fact]
    public async Task Handle_HandleLargeDataset_When_ManyAirGaps()
    {
        // Arrange
        var largeAirGapList = new List<Domain.Entities.CyberAirGap>();
        for (int i = 0; i < 100; i++)
        {
            largeAirGapList.Add(new Domain.Entities.CyberAirGap
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"AirGap_{i:000}",
                Description = $"Description for AirGap {i}",
                SourceSiteId = Guid.NewGuid().ToString(),
                SourceSiteName = $"SourceSite_{i}",
                TargetSiteId = Guid.NewGuid().ToString(),
                TargetSiteName = $"TargetSite_{i}",
                Port = 8000 + i,
                Source = $"{{\"serverId\":\"server-{i:000}\"}}",
                Target = $"{{\"serverId\":\"target-{i:000}\"}}",
                SourceComponentId = Guid.NewGuid().ToString(),
                SourceComponentName = $"SourceComponent_{i}",
                TargetComponentId = Guid.NewGuid().ToString(),
                TargetComponentName = $"TargetComponent_{i}",
                WorkflowStatus = "Active",
                EnableWorkflowId = Guid.NewGuid().ToString(),
                DisableWorkflowId = Guid.NewGuid().ToString(),
                IsActive = true
            });
        }

        var mockLargeRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(largeAirGapList);
        var largeHandler = new GetCyberAirGapListQueryHandler(_cyberAirGapFixture.Mapper, mockLargeRepository.Object);
        var query = new GetCyberAirGapListQuery();

        // Act
        var result = await largeHandler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }
}
