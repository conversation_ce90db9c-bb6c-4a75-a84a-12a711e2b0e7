using AutoFixture;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class HacmpClusterRepositoryMocks
{
    public static Mock<IHacmpClusterRepository> CreateHacmpClusterRepository(List<HacmpCluster> hacmpClusters)
    {
        var hacmpClusterRepository = new Mock<IHacmpClusterRepository>();

        hacmpClusterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(hacmpClusters);

        hacmpClusterRepository.Setup(repo => repo.AddAsync(It.IsAny<HacmpCluster>())).ReturnsAsync(
            (HacmpCluster hacmpCluster) =>
            {
                hacmpCluster.Id = new Fixture().Create<int>();
                hacmpCluster.ReferenceId = new Fixture().Create<Guid>().ToString();
                hacmpCluster.CreatedDate = DateTime.UtcNow;
                hacmpCluster.IsActive = true;

                hacmpClusters.Add(hacmpCluster);

                return hacmpCluster;
            });

        return hacmpClusterRepository;
    }

    public static Mock<IHacmpClusterRepository> UpdateHacmpClusterRepository(List<HacmpCluster> hacmpClusters)
    {
        var mockHacmpClusterRepository = new Mock<IHacmpClusterRepository>();

        mockHacmpClusterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(hacmpClusters);

        mockHacmpClusterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => hacmpClusters.SingleOrDefault(x => x.ReferenceId == i));

        mockHacmpClusterRepository.Setup(repo => repo.UpdateAsync(It.IsAny<HacmpCluster>())).ReturnsAsync((HacmpCluster hacmpCluster) =>
        {
            var index = hacmpClusters.FindIndex(item => item.ReferenceId == hacmpCluster.ReferenceId);

            hacmpClusters[index] = hacmpCluster;

            return hacmpCluster;
        });

        return mockHacmpClusterRepository;
    }

    public static Mock<IHacmpClusterRepository> DeleteHacmpClusterRepository(List<HacmpCluster> hacmpClusters)
    {
        var mockHacmpClusterRepository = new Mock<IHacmpClusterRepository>();

        mockHacmpClusterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(hacmpClusters);

        mockHacmpClusterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => hacmpClusters.SingleOrDefault(x => x.ReferenceId == i));

        mockHacmpClusterRepository.Setup(repo => repo.UpdateAsync(It.IsAny<HacmpCluster>())).ReturnsAsync((HacmpCluster hacmpCluster) =>
        {
            var index = hacmpClusters.FindIndex(item => item.ReferenceId == hacmpCluster.ReferenceId);

            hacmpCluster.IsActive = false;

            hacmpClusters[index] = hacmpCluster;

            return hacmpCluster;
        });

        return mockHacmpClusterRepository;
    }

    public static Mock<IHacmpClusterRepository> GetHacmpClusterRepository(List<HacmpCluster> hacmpClusters)
    {
        var hacmpClusterRepository = new Mock<IHacmpClusterRepository>();

        hacmpClusterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(hacmpClusters);

        hacmpClusterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => hacmpClusters.SingleOrDefault(x => x.ReferenceId == i));

        return hacmpClusterRepository;
    }

    //Events
    public static Mock<IUserActivityRepository> CreateHacmpClusterEventRepository(List<UserActivity> userActivities)
    {
        var hacmpClusterEventRepository = new Mock<IUserActivityRepository>();

        hacmpClusterEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        hacmpClusterEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivity.CreatedDate = DateTime.UtcNow;
                userActivity.IsActive = true;

                userActivities.Add(userActivity);

                return userActivity;
            });

        return hacmpClusterEventRepository;
    }

    public static Mock<IHacmpClusterRepository> GetHacmpClusterEmptyRepository()
    {
        var hacmpClusterRepository = new Mock<IHacmpClusterRepository>();

        hacmpClusterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<HacmpCluster>());

        return hacmpClusterRepository;
    }

    public static Mock<IHacmpClusterRepository> GetHacmpClusterNameUniqueRepository(List<HacmpCluster> hacmpClusters)
    {
        var hacmpClusterNameUniqueRepository = new Mock<IHacmpClusterRepository>();

        hacmpClusterNameUniqueRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) =>
        {
            return j == 0.ToString() ? hacmpClusters.Exists(x => x.Name == i) : hacmpClusters.Exists(x => x.Name == i && x.ReferenceId == j);
        });

        return hacmpClusterNameUniqueRepository;
    }
}
