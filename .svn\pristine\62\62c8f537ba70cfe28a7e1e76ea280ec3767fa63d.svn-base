﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Queries;

public class GetWorkflowProfileInfoNameQueryHandlerTests : IClassFixture<WorkflowProfileInfoViewFixture>
{
    private readonly WorkflowProfileInfoViewFixture _workflowProfileInfoViewFixture;

    private Mock<IWorkflowProfileInfoViewRepository> _mockWorkflowProfileInfoViewRepository;

    private readonly GetWorkflowProfileInfoNameQueryHandler _handler;

    public GetWorkflowProfileInfoNameQueryHandlerTests(WorkflowProfileInfoViewFixture workflowProfileInfoViewFixture)
    {
        _workflowProfileInfoViewFixture = workflowProfileInfoViewFixture;

        _mockWorkflowProfileInfoViewRepository = WorkflowProfileInfoViewRepositoryMocks.GetWorkflowProfileInfoNames(_workflowProfileInfoViewFixture.WorkflowProfileInfoViews);

        _handler = new GetWorkflowProfileInfoNameQueryHandler(_workflowProfileInfoViewFixture.Mapper, _mockWorkflowProfileInfoViewRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowProfileInfo_ProfileName()
    {
        var result = await _handler.Handle(new GetWorkflowProfileInfoNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileInfoNameVm>>();

        result[0].Id.ShouldBe(_workflowProfileInfoViewFixture.WorkflowProfileInfoViews[0].ReferenceId);
        result[0].ProfileName.ShouldBe(_workflowProfileInfoViewFixture.WorkflowProfileInfoViews[0].ProfileName);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowProfileInfoNamesCount()
    {
        var result = await _handler.Handle(new GetWorkflowProfileInfoNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowProfileInfoNameVm>>();

        result.Count.ShouldBe(_workflowProfileInfoViewFixture.WorkflowProfileInfoViews.Count);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowProfileInfoViewRepository = WorkflowProfileInfoViewRepositoryMocks.GetWorkflowProfileInfoViewEmptyRepository();

        var handler = new GetWorkflowProfileInfoNameQueryHandler(_workflowProfileInfoViewFixture.Mapper, _mockWorkflowProfileInfoViewRepository.Object);

        var result = await handler.Handle(new GetWorkflowProfileInfoNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowProfileInfoNamesMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowProfileInfoNameQuery(), CancellationToken.None);

        _mockWorkflowProfileInfoViewRepository.Verify(x => x.GetWorkflowProfileInfoNames(), Times.Once);
    }
}