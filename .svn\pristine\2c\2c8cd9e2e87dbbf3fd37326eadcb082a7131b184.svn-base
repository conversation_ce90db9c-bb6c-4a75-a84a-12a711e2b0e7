using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Queries;

public class GetCyberAirGapStatusPaginatedListTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<ICyberAirGapStatusRepository> _mockCyberAirGapStatusRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetCyberAirGapStatusPaginatedListQueryHandler _handler;

    public GetCyberAirGapStatusPaginatedListTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockCyberAirGapStatusRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(_cyberAirGapStatusFixture.CyberAirGapStatuses);
        _mockMapper = new Mock<IMapper>();

        _handler = new GetCyberAirGapStatusPaginatedListQueryHandler(
            _mockMapper.Object,
            _mockCyberAirGapStatusRepository.Object);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_ValidParameters()
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapStatus>
        {
            Data = activeEntities.Take(10).ToList(),
            PageSize = 10,
            TotalCount = activeEntities.Count,
            TotalPages = (int)Math.Ceiling((double)activeEntities.Count / 10)
        };

        var expectedVmList = paginatedEntities.Data.Select(e => new CyberAirGapStatusListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName,
            Status = e.Status
        }).ToList();

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapStatusListVm>
        {
            Data = expectedVmList,
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapStatusListVm>>(paginatedEntities))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
        
    }

    [Theory]
    [InlineData("Enterprise", "Should find entities with 'Enterprise' in name")]
    [InlineData("Primary", "Should find entities with 'Primary' in description")]
    [InlineData("Active", "Should find entities with 'Active' status")]
    [InlineData("8443", "Should find entities with port 8443")]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_SearchFiltering(string searchString, string description)
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = searchString,
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var filteredEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses
            .Where(x => x.IsActive &&
                       (x.AirGapName.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                        x.Description?.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true ||
                        x.Status?.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true ||
                        x.Port.ToString().Contains(searchString)))
            .ToList();

        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapStatus>
        {
            Data = filteredEntities.Take(10).ToList(),
            PageSize = 10,
            TotalCount = filteredEntities.Count,
            TotalPages = (int)Math.Ceiling((double)filteredEntities.Count / 10)
        };

        var expectedVmList = paginatedEntities.Data.Select(e => new CyberAirGapStatusListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName,
            Status = e.Status
        }).ToList();

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapStatusListVm>
        {
            Data = expectedVmList,
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapStatusListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapStatus>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Data.ShouldNotBeNull();

        
    }

    [Theory]
    [InlineData("AirGapName", "asc", "Sort by air gap name ascending")]
    [InlineData("AirGapName", "desc", "Sort by air gap name descending")]
    [InlineData("Status", "asc", "Sort by status ascending")]
    [InlineData("Status", "desc", "Sort by status descending")]
    [InlineData("CreatedDate", "asc", "Sort by created date ascending")]
    [InlineData("CreatedDate", "desc", "Sort by created date descending")]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_DifferentSorting(string sortColumn, string sortOrder, string description)
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = sortColumn,
            SortOrder = sortOrder
        };

        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapStatus>
        {
            Data = activeEntities.Take(10).ToList(),
            PageSize = 10,
            TotalCount = activeEntities.Count,
            TotalPages = (int)Math.Ceiling((double)activeEntities.Count / 10)
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapStatusListVm>
        {
            Data = paginatedEntities.Data.Select(e => new CyberAirGapStatusListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName,
                Status = e.Status
            }).ToList(),
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapStatusListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapStatus>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Data.ShouldNotBeNull();

    }

    /// <summary>
    /// Test: Get paginated cyber air gap status list with different page sizes
    /// Expected: Successfully handles various page sizes
    /// </summary>
    [Theory]
    [InlineData(1, 5, "Small page size")]
    [InlineData(1, 10, "Standard page size")]
    [InlineData(1, 25, "Medium page size")]
    [InlineData(1, 50, "Large page size")]
    [InlineData(1, 100, "Very large page size")]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_DifferentPageSizes(int pageNumber, int pageSize, string description)
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapStatus>
        {
            Data = activeEntities.Take(pageSize).ToList(),
            PageSize = pageSize,
            TotalCount = activeEntities.Count,
            TotalPages = (int)Math.Ceiling((double)activeEntities.Count / pageSize)
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapStatusListVm>
        {
            Data = paginatedEntities.Data.Select(e => new CyberAirGapStatusListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName
            }).ToList(),
            PageSize = pageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapStatusListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapStatus>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.PageSize.ShouldBe(pageSize);
        result.Data.Count.ShouldBeLessThanOrEqualTo(pageSize);

        
    }

    /// <summary>
    /// Test: Get paginated cyber air gap status list with pagination logic
    /// Expected: Correctly calculates pagination information
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_PaginationLogic()
    {
        // Arrange
        var largeRepository = CyberAirGapStatusRepositoryMocks.CreateLargeCyberAirGapStatusRepository(100);
        var handler = new GetCyberAirGapStatusPaginatedListQueryHandler(_mockMapper.Object, largeRepository.Object);

        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 3,
            PageSize = 15,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapStatus>
        {
            Data = new List<Domain.Entities.CyberAirGapStatus>(), // Simulated page 3
            PageSize = 15,
            TotalCount = 100,
            TotalPages = 7 // 100 / 15 = 6.67, rounded up to 7
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapStatusListVm>
        {
            Data = new List<CyberAirGapStatusListVm>(),
            PageSize = 15,
            TotalCount = 100,
            TotalPages = 7
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapStatusListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapStatus>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.PageSize.ShouldBe(15);
        result.TotalCount.ShouldBe(100);
        result.TotalPages.ShouldBe(7);

       // largeRepository.Verify(x => x.PaginatedListAllAsync(3, 15, It.IsAny<object>(), "AirGapName", "asc"), Times.Once);
    }

    /// <summary>
    /// Test: Get paginated cyber air gap status list with empty results
    /// Expected: Returns empty paginated result
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_EmptyResults()
    {
        // Arrange
        var emptyRepository = CyberAirGapStatusRepositoryMocks.CreateEmptyCyberAirGapStatusRepository();
        var handler = new GetCyberAirGapStatusPaginatedListQueryHandler(_mockMapper.Object, emptyRepository.Object);

        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var emptyPaginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapStatus>
        {
            Data = new List<Domain.Entities.CyberAirGapStatus>(),
            PageSize = 10,
            TotalCount = 0,
            TotalPages = 0
        };

        var expectedEmptyPaginatedVm = new PaginatedResult<CyberAirGapStatusListVm>
        {
            Data = new List<CyberAirGapStatusListVm>(),
            TotalCount = 0,
            TotalPages = 0
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapStatusListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapStatus>>()))
            .Returns(expectedEmptyPaginatedVm);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Data.ShouldNotBeNull();
        result.Data.Count.ShouldBe(0);
        result.TotalCount.ShouldBe(0);
        result.TotalPages.ShouldBe(0);

      //  emptyRepository.Verify(x => x.PaginatedListAllAsync(1, 10, It.IsAny<object>(), "AirGapName", "asc"), Times.Once);
    }

    /// <summary>
    /// Test: Get paginated cyber air gap status list with cancellation token
    /// Expected: Respects cancellation and throws OperationCanceledException
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_CancellationRequested()
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };
        var cancellationToken = new CancellationToken(true);

        
    }

    /// <summary>
    /// Test: Get paginated cyber air gap status list when repository fails
    /// Expected: Throws exception
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_RepositoryFails()
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var mockFailingRepository = CyberAirGapStatusRepositoryMocks.CreateFailingCyberAirGapStatusRepository();
        

        var handler = new GetCyberAirGapStatusPaginatedListQueryHandler(_mockMapper.Object, mockFailingRepository.Object);

       
    }

    /// <summary>
    /// Test: Get paginated cyber air gap status list with default parameters
    /// Expected: Uses default values when parameters are not specified
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_DefaultParameters()
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery(); // No parameters set

        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapStatus>
        {
            Data = activeEntities.Take(10).ToList(),
            PageSize = 10,
            TotalCount = activeEntities.Count,
            TotalPages = (int)Math.Ceiling((double)activeEntities.Count / 10)
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapStatusListVm>
        {
            Data = paginatedEntities.Data.Select(e => new CyberAirGapStatusListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName
            }).ToList(),
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapStatusListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapStatus>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Data.ShouldNotBeNull();

        
    }

    /// <summary>
    /// Test: Get paginated cyber air gap status list response validation
    /// Expected: Response contains all required pagination properties
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapStatusPaginatedList_When_ValidatingResponse()
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 5,
            SearchString = "",
            SortColumn = "AirGapName",
            SortOrder = "asc"
        };

        var activeEntities = _cyberAirGapStatusFixture.CyberAirGapStatuses.Where(x => x.IsActive).Take(5).ToList();
        var paginatedEntities = new PaginatedResult<Domain.Entities.CyberAirGapStatus>
        {
            Data = activeEntities,
            PageSize = 5,
            TotalCount = _cyberAirGapStatusFixture.CyberAirGapStatuses.Count(x => x.IsActive),
            TotalPages = (int)Math.Ceiling((double)_cyberAirGapStatusFixture.CyberAirGapStatuses.Count(x => x.IsActive) / 5)
        };

        var expectedPaginatedVm = new PaginatedResult<CyberAirGapStatusListVm>
        {
            Data = activeEntities.Select(e => new CyberAirGapStatusListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName,
                Status = e.Status
            }).ToList(),
            PageSize = paginatedEntities.PageSize,
            TotalCount = paginatedEntities.TotalCount,
            TotalPages = paginatedEntities.TotalPages
        };

        _mockMapper.Setup(x => x.Map<PaginatedResult<CyberAirGapStatusListVm>>(It.IsAny<PaginatedResult<Domain.Entities.CyberAirGapStatus>>()))
            .Returns(expectedPaginatedVm);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<PaginatedResult<CyberAirGapStatusListVm>>();

        // Validate pagination properties
        result.PageSize.ShouldBe(5);
        result.TotalCount.ShouldBeGreaterThanOrEqualTo(0);
        result.TotalPages.ShouldBeGreaterThanOrEqualTo(0);

        // Validate data
        result.Data.ShouldNotBeNull();
        result.Data.Count.ShouldBeLessThanOrEqualTo(5);

        // Validate each item has required properties
        foreach (var item in result.Data)
        {
            item.Id.ShouldNotBeNullOrEmpty();
            item.AirGapName.ShouldNotBeNullOrEmpty();
        }
    }
}
