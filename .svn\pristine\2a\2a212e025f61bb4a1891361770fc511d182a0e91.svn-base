﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Create;
using ContinuityPatrol.Application.Features.CGExecutionReport.Events.Create;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CGExecutionReport.Commands
{
    public class CreateCGExecutionCommandHandlerTests
    {
        private readonly Mock<IRpForVmCgEnableDisableStatusRepository> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly CreateCGExecutionCommandHandler _handler;

        public CreateCGExecutionCommandHandlerTests()
        {
            _mockRepository = new Mock<IRpForVmCgEnableDisableStatusRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new CreateCGExecutionCommandHandler(
                _mockRepository.Object,
                _mockMapper.Object,
                _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ShouldMapCommandToEntity()
        {
            // Arrange
            var command = new CreateCGExecutionCommand
            {
                WorkflowName = "TestWorkflow"
            };

            var expectedEntity = new RpForVmCgEnableDisableStatus();

            _mockMapper.Setup(m => m.Map<RpForVmCgEnableDisableStatus>(command))
                .Returns(expectedEntity)
                .Verifiable();

            _mockRepository.Setup(r => r.AddAsync(It.IsAny<RpForVmCgEnableDisableStatus>()))
                .ReturnsAsync(expectedEntity);

            // Act
            await _handler.Handle(command, CancellationToken.None);

            // Assert
            _mockMapper.Verify();
        }

        [Fact]
        public async Task Handle_ShouldAddEntityToRepository()
        {
            // Arrange
            var command = new CreateCGExecutionCommand();
            var mappedEntity = new RpForVmCgEnableDisableStatus();

            _mockMapper.Setup(m => m.Map<RpForVmCgEnableDisableStatus>(command))
                .Returns(mappedEntity);

            _mockRepository.Setup(r => r.AddAsync(mappedEntity))
                .ReturnsAsync(mappedEntity)
                .Verifiable();

            // Act
            await _handler.Handle(command, CancellationToken.None);

            // Assert
            _mockRepository.Verify();
        }

        [Fact]
        public async Task Handle_ShouldReturnCorrectResponse()
        {
            // Arrange
            var command = new CreateCGExecutionCommand { WorkflowName = "TestWorkflow" };
            var mappedEntity = new RpForVmCgEnableDisableStatus
            {
                WorkflowName = "TestWorkflow",
                ReferenceId = "12345"
            };

            _mockMapper.Setup(m => m.Map<RpForVmCgEnableDisableStatus>(command))
                .Returns(mappedEntity);

            _mockRepository.Setup(r => r.AddAsync(It.IsAny<RpForVmCgEnableDisableStatus>()))
                .ReturnsAsync(mappedEntity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("12345", result.Id);
            Assert.Contains("TestWorkflow", result.Message);
            Assert.Contains("CG Execution Report", result.Message);
        }

        [Fact]
        public async Task Handle_ShouldPublishCGExecutionCreateEvent()
        {
            // Arrange
            var command = new CreateCGExecutionCommand { WorkflowName = "TestWorkflow" };
            var mappedEntity = new RpForVmCgEnableDisableStatus
            {
                WorkflowName = "TestWorkflow",
                ReferenceId = "12345"
            };

            _mockMapper.Setup(m => m.Map<RpForVmCgEnableDisableStatus>(command))
                .Returns(mappedEntity);

            _mockRepository.Setup(r => r.AddAsync(It.IsAny<RpForVmCgEnableDisableStatus>()))
               .ReturnsAsync(mappedEntity);

            CGExecutionCreateEvent publishedEvent = null!;
            _mockPublisher.Setup(p => p.Publish(It.IsAny<CGExecutionCreateEvent>(), It.IsAny<CancellationToken>()))
                .Callback<CGExecutionCreateEvent, CancellationToken>((e, _) => publishedEvent = e)
                .Returns(Task.CompletedTask);

            // Act
            await _handler.Handle(command, CancellationToken.None);

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<CGExecutionCreateEvent>(), It.IsAny<CancellationToken>()), Times.Once);
            Assert.NotNull(publishedEvent);
            Assert.Equal("TestWorkflow", publishedEvent.WorkflowName);
        }

        [Fact]
        public async Task Handle_ShouldPropagateCancellationToken()
        {
            // Arrange
            var command = new CreateCGExecutionCommand();
            var cts = new CancellationTokenSource();
            var token = cts.Token;

            _mockMapper.Setup(m => m.Map<RpForVmCgEnableDisableStatus>(command))
                .Returns(new RpForVmCgEnableDisableStatus());

            _mockRepository.Setup(r => r.AddAsync(It.IsAny<RpForVmCgEnableDisableStatus>()))
               .ReturnsAsync(new RpForVmCgEnableDisableStatus())
               .Verifiable();

            _mockPublisher.Setup(p => p.Publish(It.IsAny<CGExecutionCreateEvent>(), token))
                .Returns(Task.CompletedTask)
                .Verifiable();

            // Act
            await _handler.Handle(command, token);

            // Assert
            _mockRepository.Verify();
            _mockPublisher.Verify();
        }

        [Fact]
        public async Task Handle_ShouldThrowNullReferenceException_WhenCommandIsNull()
        {
            // Arrange
            CreateCGExecutionCommand command = null!;

            // Act & Assert
            await Assert.ThrowsAsync<NullReferenceException>(() =>
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldHandleRepositoryException()
        {
            // Arrange
            var command = new CreateCGExecutionCommand();
            var expectedException = new Exception("Database error");

            _mockMapper.Setup(m => m.Map<RpForVmCgEnableDisableStatus>(command))
                .Returns(new RpForVmCgEnableDisableStatus());

            _mockRepository.Setup(r => r.AddAsync(It.IsAny<RpForVmCgEnableDisableStatus>()))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(command, CancellationToken.None));

            Assert.Equal(expectedException.Message, exception.Message);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<CGExecutionCreateEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }
    }
}
