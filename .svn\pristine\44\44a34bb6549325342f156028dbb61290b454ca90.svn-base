﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Infrastructure.Impl;
using ContinuityPatrol.Shared.Core.Helper;


namespace ContinuityPatrol.Infrastructure.UnitTests.Impl;

public class LicenseValidationServiceTests
{
    private readonly Mock<ILogger<LicenseValidationService>> _loggerMock;
    private readonly LicenseValidationService _service;

    public LicenseValidationServiceTests()
    {
        _loggerMock = new Mock<ILogger<LicenseValidationService>>();
        _service = new LicenseValidationService(_loggerMock.Object);
    }

    [Theory]
    [InlineData("primary", "database", "{\"primarydatabaseCount\":5}", 5)]
    [InlineData("dr", "app", "{\"drappCount\":3}", 3)]
    [InlineData("custom dr", "thirdparty", "{\"custom drthirdPartyCount\":2}", 2)]
    public async Task ServerLicenseCount_ReturnsCorrectCount(string siteCategory, string serverType, string properties, int expectedCount)
    {
        // Arrange
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.ServerLicenseCount(licenseManager, siteType, serverType, 0);

        // Assert
        Assert.Equal(expectedCount, result);
    }

    [Theory]
    [InlineData("Near", "thirdparty", "{\"NearthirdPartyCount\":2}", 0)]
    public async Task ServerLicenseCount_ReturnsInCorrectCount(string siteCategory, string serverType, string properties, int expectedCount)
    {
        // Arrange
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.ServerLicenseCount(licenseManager, siteType, serverType, 0);

        // Assert
        Assert.Equal(expectedCount, result);
    }



    [Theory]
    [InlineData("primary", "{\"primarydatabaseCount\":5}", 5)]
    [InlineData("dr", "{\"drdatabaseCount\":3}", 3)]
    [InlineData("custom dr", "{\"custom drdatabaseCount\":2}", 2)]
    public async Task DatabaseLicenseCount_ReturnsCorrectCount(string siteCategory, string properties, int expectedCount)
    {
        // Arrange
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.DatabaseLicenseCount(licenseManager, siteType, 0);

        // Assert
        Assert.Equal(expectedCount, result);
    }

    [Theory]
    [InlineData("01 January 2020", false)] // Past date
    [InlineData("invalid date", true)] // Invalid format
    public async Task IsLicenseExpired_ValidatesCorrectly(string date, bool expectedResult)
    {
        // Act
        var result = await _service.IsLicenseExpired(date);

        // Assert
        Assert.Equal(expectedResult, result);
    }

    [Fact]
    public async Task IsLicenseExpired_WithFutureDate_ReturnsTrue()
    {
        // Arrange
        var futureDate = DateTime.UtcNow.AddDays(1).ToString("dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"));

        // Act
        var result = await _service.IsLicenseExpired(futureDate);

        // Assert
        Assert.True(result);
    }



    [Theory]
    [InlineData("CompanyA", "CompanyA", true)]
    [InlineData("CompanyA", "COMPANYA", true)] // Case insensitive
    [InlineData("CompanyA", "CompanyB", false)]
    [InlineData("", "CompanyA", false)]
    [InlineData("CompanyA", "", false)]
    public async Task IsCompanyNameValidate_ValidatesCorrectly(string newName, string existingName, bool expectedResult)
    {
        // Act
        var result = await _service.IsCompanyNameValidate(newName, existingName);

        // Assert
        Assert.Equal(expectedResult, result);

        if (!expectedResult)
        {
            _loggerMock.Verify(
                x => x.Log(
                    It.Is<LogLevel>(l => l == LogLevel.Error),
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
        else
        {
            _loggerMock.Verify(
                x => x.Log(
                    It.Is<LogLevel>(l => l == LogLevel.Error),
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Never);
        }
    }

    [Theory]
    [InlineData("YbPcHHFTR/s1tiCrDsJTe5zh+yfFfIIcpNU041kb8JQ=$ShRxs04Lz00TSapR5eeIMqgf8chOtdXd3tMczHbhDnyUYFqTxg==", true)] // Valid format
    [InlineData("PREFIX$ABCDEFGH", false)] // Too short
    [InlineData("PREFIX$ABCDEFGH!@#$%^", false)] // Invalid chars
    [InlineData("PREFIX", false)] // No $ separator
    public async Task IsLicenseKeyFormatValid_ValidatesCorrectly(string licenseKey, bool expectedResult)
    {
        // Act
        var result = await _service.IsLicenseKeyFormatValid(licenseKey);

        // Assert
        Assert.Equal(expectedResult, result);
    }

    [Fact]
    public async Task IsLicenseSiteAvailable_ForCustomDr_ReturnsCorrectValue()
    {
        // Arrange
        var properties = "{\"custom drappCount\":2,\"custom drdatabaseCount\":1}";
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsLicenseSiteAvailable(properties, siteType, "server", 0, "app");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsLicenseSiteAvailable_ForPrimary_ReturnsTrue()
    {
        // Arrange
        var properties = "{}"; // Doesn't matter, it will short-circuit
        var siteType = new SiteType { Category = "Primary" };

        // Act
        var result = await _service.IsLicenseSiteAvailable(properties, siteType, "server", 0, "app");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsLicenseSiteAvailable_ForDr_ReturnsTrue()
    {
        // Arrange
        var properties = "{}"; // Doesn't matter
        var siteType = new SiteType { Category = "DR" };

        // Act
        var result = await _service.IsLicenseSiteAvailable(properties, siteType, "database", 0);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsLicenseSiteAvailable_CustomDr_Database_ReturnsTrue()
    {
        // Arrange
        var properties = "{\"custom drdatabaseCount\": 1}";
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsLicenseSiteAvailable(properties, siteType, "database", 0);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsLicenseSiteAvailable_CustomDr_Replication_ReturnsTrue()
    {
        // Arrange
        var properties = "{\"custom drreplicationCount\": 2}";
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsLicenseSiteAvailable(properties, siteType, "replication", 0);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsLicenseSiteAvailable_CustomDr_EmptyCount_ReturnsFalse()
    {
        // Arrange
        var properties = "{\"NearreplicationCount\": 2}";
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsLicenseSiteAvailable(properties, siteType, "replication", 0);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_ValidatesCorrectly()
    {
        // Arrange
        var properties = "{\"isDatabase\":true ,\"DatabaseDto\":{\"primary\":[{\"id\":\"1\",\"count\":5}]}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 3, 0);

        // Assert (5 > 3)
        Assert.True(result);
    }

    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_WhenTypeCountsIsNull_ReturnsFalse()
    {
        // Arrange: Missing `primary` key inside DatabaseDto
        var properties = "{\"isDatabase\":true ,\"DatabaseDto\":{}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 1, 0);

        // Assert (count = 0 > 1) → false
        Assert.False(result);
    }

    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_UsesOthersEntry_WhenIdNotFound()
    {
        // Arrange: id="99" not found, but "Others" exists
        var properties = "{\"isDatabase\":true ,\"DatabaseDto\":{\"primary\":[{\"type\":\"Others\",\"count\":4,\"id\":\"99\"}]}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "nonexistent-id", 3, 0);

        // Assert (4 > 3) → true
        Assert.True(result);
    }

    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_WhenUnexpectedJsonStructure_ThrowsHandledException()
    {
        // Arrange: DatabaseDto.primary is an object instead of an array
        var invalidJson = @"{
        ""isDatabase"": true,
        ""DatabaseDto"": {
            ""primary"": [5]
        }
    }";
        var licenseManager = new LicenseManager { Properties = invalidJson };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 1, 0);

        // Assert (should hit catch → returns 0 > 1 → false)
        Assert.False(result);
    }




    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_WhenDatabaseDtoMissing_ReturnsFalse()
    {
        // Arrange
        var properties = "{\"isDatabase\":true}"; // Missing "DatabaseDto"
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 1, 0);

        // Assert (0 > 1 => false)
        Assert.False(result);
    }

    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_WhenNoCustomDrKeyInDatabaseDto_ReturnsFalse()
    {
        // Arrange
        var properties = "{\"isDatabase\":true,\"DatabaseDto\":{\"noncustom\":[{\"id\":\"1\",\"count\":5}]}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 1, 0);

        // Assert (0 > 1 => false)
        Assert.False(result);
    }

    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_WhenIdNotFound_UsesOthersEntry()
    {

        var properties = "{\"isDatabase\":true,\"DatabaseDto\":{\"custom dr\":[{\"id\":\"1\",\"count\":4,\"type\": \"Others\"}]}}";


        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "nonexistent-id", 2, 0);

        // Assert (4 > 2)
        Assert.True(result);
    }

    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_WhenDatabaseDtoIsInvalid_HandlesException()
    {
        // Arrange: "DatabaseDto" is an integer (invalid type for JObject.FromObject)
        var properties = "{\"isDatabase\":true, \"DatabaseDto\": 123}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 1, 0);

        // Assert: catch block returns 0, so 0 > 1 = false
        Assert.False(result);
    }




    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_PrimarySite_NonDatabase_ReturnsTrue()
    {
        // Arrange
        var properties = "{\"isDatabase\":false ,\"DatabaseDto\":{\"primary\":[{\"id\":\"1\",\"count\":5}]}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 3, 0);

        // Assert (5 > 3)
        Assert.False(result);
    }


    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_CustomDrWithDatabase_ReturnsExpected()
    {
        // Arrange
        var properties = "{\"isDatabase\":true ,\"DatabaseDto\":{\"custom drprimary\":[{\"id\":\"1\",\"count\":4}]}}";

        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 3, 0);

        // Assert (4 > 3)
        Assert.True(result);
    }

    [Fact]
    public async Task IsDatabaseTypeLicenseCountExitMaxLimit_CustomDrWithoutDatabase_ReturnsExpected()
    {
        // Arrange
        var properties = @"{
        ""isDatabase"": false,
        ""custom drdatabaseCount"": 5
    }";

        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "custom dr" };

        // Act
        var result = await _service.IsDatabaseTypeLicenseCountExitMaxLimit(
            licenseManager, siteType, "1", 3, 0);

        // Assert (5 > 3)
        Assert.True(result);
    }





    [Theory]
    [InlineData("{\"AMC\":1}", true)] // Has AMC
    [InlineData("{\"AMC\":0}", false)] // No AMC
    //[InlineData("{\"Warranty\":1}", true)] // Has Warranty
    [InlineData("{}", false)] // Empty
    public async Task IsAmc_ValidatesCorrectly(string json, bool expectedResult)
    {
        // Act - Test both AMC and Warranty
        var amcResult = await _service.IsAmc(json);
       // var warrantyResult = await _service.IsWarranty(json);

        // Assert
        Assert.Equal(expectedResult, amcResult);
    }

    [Theory]
    [InlineData("{\"Warranty\":1}", true)] 
    [InlineData("{\"Warranty\":0}", false)]
    [InlineData("{}", false)] // Empty
    public async Task IsWarranty_ValidatesCorrectly(string json, bool expectedResult)
    {
        // Act - Test both AMC and Warranty
        var amcResult = await _service.IsWarranty(json);
        // var warrantyResult = await _service.IsWarranty(json);

        // Assert
        Assert.Equal(expectedResult, amcResult);
    }


    [Fact]
    public async Task GetDateByExpireTime_CalculatesCorrectly()
    {
        // Arrange
        var createDate = new DateTime(2023, 1, 1);
        var expireTime = "License-1 Year";

        // Act
        var result = await _service.GetDateByExpireTime(expireTime, createDate, null);

        // Assert
        Assert.Equal("01 January 2024", result);
    }

    [Fact]
    public async Task GetDateByExpireTime_WithMonthDuration_ReturnsCorrectDate()
    {
        // Arrange
        var createDate = new DateTime(2023, 1, 1);
        var expireTime = "License-2 Month";

        // Act
        var result = await _service.GetDateByExpireTime(expireTime, createDate, null);

        // Assert
        Assert.Equal("01 March 2023", result); // 2 months after Jan 1, 2023
    }

    [Fact]
    public async Task GetDateByExpireTime_WithUnlimited_ReturnsMaxDate()
    {
        // Arrange
        var createDate = new DateTime(2023, 1, 1);
        var expireTime = "License-Unlimited";

        // Act
        var result = await _service.GetDateByExpireTime(expireTime, createDate, null);

        // Assert
        Assert.Equal(DateTime.MaxValue.ToString("dd MMMM yyyy"), result);
    }


    [Fact]
    public async Task GetDateByExpireTime_WithDaysDuration_ReturnsCorrectDate()
    {
        // Arrange
        var createDate = new DateTime(2023, 1, 1);
        var expireTime = "License-10 Days";

        // Act
        var result = await _service.GetDateByExpireTime(expireTime, createDate, null);

        // Assert
        Assert.Equal("11 January 2023", result); // 10 days after Jan 1, 2023
    }

    [Fact]
    public async Task GetDateByExpireTime_WithInvalidDurationUnit_ThrowsException()
    {
        // Arrange
        var createDate = new DateTime(2023, 1, 1);
        var expireTime = "License-1 InvalidUnit";

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(() =>
            _service.GetDateByExpireTime(expireTime, createDate, null));

        Assert.Equal("LicenseKey Type not match.", exception.Message);
    }



    [Fact]
    public async Task IsMacAddressValidAsync_ValidatesCorrectly()
    {
        // Arrange
        var macAddresses = await GetMacAddress();

        var firstMacAddress = macAddresses.FirstOrDefault();

        // Act
        var result = await _service.IsMacAddressValidAsync(firstMacAddress);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsMacAddressValidAsync_ReturnsFalse_WhenMacAddressDoesNotMatch()
    {
        // Arrange
        var macAddresses = "ef-de-fr-fr-vf-sd-ef";

        // Act
        var result = await _service.IsMacAddressValidAsync(macAddresses);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task UpdateExpiryDate_ForPOCLicense_ReturnsBasicExpiry()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "POC-1 Year",
            LicenseCount = "{\"startDate\":\"2023-01-01\"}"
        };
        var eventToUpdate = new LicenseManager();

        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.Equal("01 January 2024", result);
    }

    [Fact]
    public async Task UpdateExpiryDate_ForUATLicense_ReturnsBasicExpiry()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "UAT-6 Month",
            LicenseCount = "{\"startDate\":\"2023-01-01\"}"
        };
        var eventToUpdate = new LicenseManager();

        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.Equal("01 July 2023", result);
    }


    [Fact]
    public async Task UpdateExpiryDate_ForSubscriptionWithValidCurrent_ReturnsExtendedDate()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "Subscription-1 Year",
            LicenseCount = "{\"startDate\":\"2023-01-01\"}"
        };

        var eventToUpdate = new LicenseManager
        {
            Validity = "ahX3dhbwj3Sot4ZZ/ESxi9+x5zHVV8G3yjXQbHF2yXg=$I11cTwTsaPXWzg0muGh3scqv9TKoFJl96eE5LmHBFJriXLmn/Z4g2Vza1xJ5YHA=",
            ExpiryDate = "uoYat2CWGS3ORB3lpPdqaxdsMENcWC2aSCMsbvJhYwI=$6IXj/BLPDQ1N/U5DthEPBscTHU+wrW6+LB3AmoRF1FS1utifuu+SlQSYdQ=="
        };

        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.Equal("01 January 2024", result); // 1 year + 5 remaining days
    }

    [Fact]
    public async Task UpdateExpiryDate_ForSubscriptionWithExpiredCurrent_ReturnsNewExpiry()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "Subscription-1 Year",
            LicenseCount = "{\"startDate\":\"2023-01-01\"}"
        };

        var eventToUpdate = new LicenseManager
        {
            Validity = "ahX3dhbwj3Sot4ZZ/ESxi9+x5zHVV8G3yjXQbHF2yXg=$I11cTwTsaPXWzg0muGh3scqv9TKoFJl96eE5LmHBFJriXLmn/Z4g2Vza1xJ5YHA=",
            ExpiryDate = "ntCrYBeTl/LcBuZHvE5hdg5Vt8Mu1w4/+GkEtLzba9Y=$uNWZILQOWtgEG0Tri6HdHiDrLa2jPDjQluTvZSa2e6d1pMdf8M7JB8uu0g==" // Expired date
        };

        
        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.Equal("01 January 2024", result); // Just the new 1 year period
    }


    [Fact]
    public async Task UpdateExpiryDate_ShouldExtendExpiry_WhenSubscriptionIsValid()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "Subscription-1 Year",
            LicenseCount = "{}"
        };

        var expiryDate = DateTime.UtcNow.AddYears(1).ToString("dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"));

        var expiryDateEncrypt = SecurityHelper.Encrypt(expiryDate);


        var eventToUpdate = new LicenseManager
        {
            Validity = "ahX3dhbwj3Sot4ZZ/ESxi9+x5zHVV8G3yjXQbHF2yXg=$I11cTwTsaPXWzg0muGh3scqv9TKoFJl96eE5LmHBFJriXLmn/Z4g2Vza1xJ5YHA=",
            ExpiryDate = expiryDateEncrypt 
        };

        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.NotNull(result); // Just the new 1 year period
    }



    [Fact]
    public async Task UpdateExpiryDate_ForRegularLicense_ReturnsBasicExpiry()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "Production-2 Year",
            LicenseCount = "{\"startDate\":\"2023-01-01\"}"
        };
        var eventToUpdate = new LicenseManager();

        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.Equal("01 January 2025", result);
    }

    [Fact]
    public async Task UpdateExpiryDate_WithEndDate_ReturnsEndDate()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "Production-2 Year",
            LicenseCount = "{\"startDate\":\"2023-01-01\",\"endDate\":\"2024-06-01\"}"
        };
        var eventToUpdate = new LicenseManager();

        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.Equal("01 June 2024", result);
    }

    [Fact]
    public async Task UpdateExpiryDate_WithUnlimitedLicense_ReturnsMaxDate()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "Production-Unlimited",
            LicenseCount = "{\"startDate\":\"2023-01-01\"}"
        };
        var eventToUpdate = new LicenseManager();

        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.Equal(DateTime.MaxValue.ToString("dd MMMM yyyy"), result);
    }

    [Fact]
    public async Task UpdateExpiryDate_WithInvalidStartDate_UsesCurrentDate()
    {
        // Arrange
        var newLicenseKey = new LicenseDto
        {
            LicenseType = "Production-1 Year",
            LicenseCount = "{\"startDate\":\"invalid-date\"}"
        };
        var eventToUpdate = new LicenseManager();

        // Act
        var result = await _service.UpdateExpiryDate(newLicenseKey, eventToUpdate);

        // Assert
        var expectedDate = DateTime.UtcNow.AddYears(1).ToString("dd MMMM yyyy");
        Assert.Equal(expectedDate, result);
    }

    [Fact]
    public async Task UpdateAmcDate_ShouldReturnExtendedDate_WhenAMCStartDateIsPresent()
    {
        // Arrange
        var startDate = new DateTime(2024, 7, 2);
        var amcEndDate = startDate.AddYears(1); // AMC end date 1 year from start
        var supportPlan = $@"{{ ""AMCStartDate"": ""{startDate:dd MMMM yyyy}"", ""AMC"": ""2"" }}";

        var newLicenseKey = new LicenseDto
        {
            SupportPlan = supportPlan
        };

        var eventToUpdate = new LicenseManager
        {
            AmcEndDate = SecurityHelper.Encrypt(amcEndDate.ToString("dd MMMM yyyy"))
        };

        // Expected: (startDate + 2 years) + (amcEndDate - startDate) → 2 years + 1 year = 3 years
        var expected = startDate.AddYears(2).AddYears(1).ToString("dd MMMM yyyy");

        // Act
        var result = await _service.UpdateAmcDate(newLicenseKey, eventToUpdate);

        // Assert
        Assert.Equal(expected, result);
    }


    [Fact]
    public async Task UpdateAmcDate_ShouldAddRemainingDays_WhenRemainingDaysPositive()
    {
        // Arrange
        var startDate = DateTime.Today;
        var amcStartDate = startDate.ToString("dd MMMM yyyy");
        var amcEndDate = startDate.AddDays(30).ToString("dd MMMM yyyy");

        var licenseKey = new LicenseDto
        {
            SupportPlan = $@"{{ ""AMCStartDate"": ""{amcStartDate}"", ""AMC"": ""1"" }}"
        };

        var eventToUpdate = new LicenseManager
        {
            AmcEndDate = SecurityHelper.Encrypt(amcEndDate)
        };

        // Act
        var result = await _service.UpdateAmcDate(licenseKey, eventToUpdate);

        // Assert
        var expected = startDate.AddYears(1).AddDays(30).ToString("dd MMMM yyyy");
        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task UpdateAmcDate_ShouldHandlePastAmcEndDate()
    {
        // Arrange
        var amcStartDate = new DateTime(2024, 7, 3); // AMC starts after the end date
        var amcEndDate = new DateTime(2023, 7, 3);   // already expired

        var licenseKey = new LicenseDto
        {
            SupportPlan = $@"{{ ""AMCStartDate"": ""{amcStartDate:dd MMMM yyyy}"", ""AMC"": ""1"" }}"
        };

        var eventToUpdate = new LicenseManager
        {
            AmcEndDate = SecurityHelper.Encrypt(amcEndDate.ToString("dd MMMM yyyy"))
        };

        // Act
        var result = await _service.UpdateAmcDate(licenseKey, eventToUpdate);

        // Assert
        var expected = amcStartDate.AddYears(1).ToString("dd MMMM yyyy");
        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task UpdateAmcDate_ShouldUseToday_WhenAmcStartDateIsMissing()
    {
        // Arrange
        var today = DateTime.Today;
        var parsedDate = today.AddDays(10); // simulate decrypted AmcEndDate 10 days in future

        var licenseKey = new LicenseDto
        {
            SupportPlan = @"{ ""AMC"": ""1"" }"
        };

        var eventToUpdate = new LicenseManager
        {
            AmcEndDate = SecurityHelper.Encrypt(parsedDate.ToString("dd MMMM yyyy"))
        };

        // Expected behavior: Add 10 days to parsedDate (as per current logic)
        var expectedDate = parsedDate.AddDays(10).ToString("dd MMMM yyyy");

        // Act
        var result = await _service.UpdateAmcDate(licenseKey, eventToUpdate);

        // Assert
        Assert.Equal(expectedDate, result);
    }

    [Fact]
    public async Task UpdateAmcDate_ShouldReturnEmpty_WhenAmcEndDateIsInvalid()
    {
        // Arrange
        var licenseKey = new LicenseDto
        {
            SupportPlan = "{}"
        };

        var eventToUpdate = new LicenseManager
        {
            AmcEndDate = SecurityHelper.Encrypt("invalid-date")
        };

        // Act
        var result = await _service.UpdateAmcDate(licenseKey, eventToUpdate);

        // Assert
        Assert.Equal(string.Empty, result);
    }


    [Fact]
    public async Task IsDatabaseTypeAsync_ReturnsSuccess_WhenMatchingIdExists()
    {
        // Arrange
        var json = @"{
        ""DatabaseDto"": {
            ""primary"": [
                { ""id"": ""1"", ""type"": ""custom"" },
                { ""id"": ""2"", ""type"": ""custom"" }
            ]
        }
    }";

        // Act
        var result = await _service.IsDatabaseTypeAsync(json, "primary", "1");

        // Assert
        Assert.True(result.Success);
        Assert.Contains("1", result.TypeIds);
        Assert.Contains("2", result.TypeIds);
    }

    [Fact]
    public async Task IsDatabaseTypeAsync_ReturnsFalse_WhenIdDoesNotMatch()
    {
        // Arrange
        var json = @"{
        ""DatabaseDto"": {
            ""primary"": [
                { ""id"": ""2"", ""type"": ""custom"" }
            ]
        }
    }";

        // Act
        var result = await _service.IsDatabaseTypeAsync(json, "primary", "1");

        // Assert
        Assert.False(result.Success);
        Assert.Contains("2", result.TypeIds);
    }

    [Fact]
    public async Task IsDatabaseTypeAsync_ShouldIgnoreOthersType()
    {
        // Arrange
        var json = @"{
        ""DatabaseDto"": {
            ""primary"": [
                { ""id"": ""3"", ""type"": ""Others"" },
                { ""id"": ""4"", ""type"": ""custom"" }
            ]
        }
    }";

        // Act
        var result = await _service.IsDatabaseTypeAsync(json, "primary", "3");

        // Assert
        Assert.False(result.Success); // Should ignore the "Others" type
        Assert.Contains("4", result.TypeIds);
        Assert.DoesNotContain("3", result.TypeIds);
    }

    [Fact]
    public async Task IsDatabaseTypeAsync_ReturnsFalse_WhenDatabaseDtoMissing()
    {
        // Arrange
        var json = @"{ ""OtherDto"": {} }";

        // Act
        var result = await _service.IsDatabaseTypeAsync(json, "primary", "1");

        // Assert
        Assert.False(result.Success);
        Assert.Single(result.TypeIds);
        Assert.Contains("1", result.TypeIds); // fallback
    }

    [Fact]
    public async Task IsDatabaseTypeAsync_ReturnsFalse_OnInvalidJson()
    {
        // Arrange
        var json = @"{ invalid json }";

        // Act
        var result = await _service.IsDatabaseTypeAsync(json, "primary", "1");

        // Assert
        Assert.False(result.Success);
        Assert.Single(result.TypeIds);
        Assert.Equal("1", result.TypeIds[0]);
    }


    [Fact]
    public async Task IsDatabaseTypeAsync_ShouldMatchCustomDrPath()
    {
        // Arrange
        var json = @"{
        ""DatabaseDto"": {
            ""custom dr"": [
                { ""id"": ""11"", ""type"": ""custom"" },
                { ""id"": ""22"", ""type"": ""Others"" }
            ]
        }
    }";

        // Act
        var result = await _service.IsDatabaseTypeAsync(json, "custom dr", "11");

        // Assert
        Assert.True(result.Success);
        Assert.Contains("11", result.TypeIds);
        Assert.DoesNotContain("22", result.TypeIds); // type == "Others" is skipped
    }


    [Fact]
    public async Task AmcEndDate_WithStartDate_ReturnsCorrectDate()
    {
        // Arrange
        var startDate = new DateTime(2022, 1, 1);
        var amcYears = 3;
        var json = $@"{{
            ""AMC"": ""{amcYears}"",
            ""AMCStartDate"": ""{startDate:yyyy-MM-dd}""
        }}";

        // Act
        var result = await _service.AmcEndDate(json);

        // Assert
        Assert.Equal(startDate.AddYears(amcYears).ToString("dd MMMM yyyy"), result);
    }

    [Fact]
    public async Task AmcEndDate_WithoutStartDate_UsesToday()
    {
        // Arrange
        var today = DateTime.Today;
        var json = $@"{{
            ""AMC"": ""2""
        }}";

        // Act
        var result = await _service.AmcEndDate(json);

        // Assert
        Assert.Equal(today.AddYears(2).ToString("dd MMMM yyyy"), result);
    }
    [Fact]
    public async Task AmcEndDate_InvalidJson_ReturnsNA()
    {
        // Arrange
        var invalidJson = @"{ ""AMC"": ""0"" }";

        // Act
        var result = await _service.AmcEndDate(invalidJson);

        // Assert
        Assert.Equal("NA", result);
    }

    [Fact]
    public async Task ValidateLicenseKeyDate_ShouldReturnTrue_WhenDateIsToday()
    {
        var today = DateTime.UtcNow.ToString();

        var result = await _service.ValidateLicenseKeyDate(today);

        Assert.True(result);
    }

    [Fact]
    public async Task ValidateLicenseKeyDate_ShouldReturnTrue_WhenDateWithin15Days()
    {
        // Arrange
        var dateWithin15Days = DateTime.UtcNow.AddDays(-10); // dynamically within 15 days
        var formatted = dateWithin15Days.ToString("dd MMMM, yyyy h:mm:ss tt", CultureInfo.InvariantCulture);

        // Act
        var result = await _service.ValidateLicenseKeyDate(formatted);

        // Assert
        Assert.True(result);
    }


    [Fact]
    public async Task ValidateLicenseKeyDate_ShouldReturnFalse_IfDateIsInFuture()
    {
        // Arrange
        var futureDate = DateTime.UtcNow.AddDays(1).ToString("u");

        // Act
        var result = await _service.ValidateLicenseKeyDate(futureDate);

        // Assert
        Assert.False(result);

        //_loggerMock.Verify(
        //    x => x.Log(
        //        LogLevel.Error,
        //        It.IsAny<EventId>(),
        //        It.Is<It.IsAnyType>((v, _) =>
        //            v.ToString()!.Contains("License Generated date is in the future")),
        //        It.IsAny<Exception>(),
        //        It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task ValidateLicenseKeyDate_ShouldReturnFalse_IfDateIsOlderThan15Days()
    {
        // Arrange
        var oldDate = DateTime.UtcNow.AddDays(-16).ToString("u");

        // Act
        var result = await _service.ValidateLicenseKeyDate(oldDate);

        // Assert
        Assert.False(result);

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("day difference")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ValidateLicenseKeyDate_ShouldReturnTrue_IfDateIsValid()
    {
        // Arrange
        // var validDate = DateTime.UtcNow.AddDays(-5).ToString("u");

        var validDate = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd");

        // Act
        var result = await _service.ValidateLicenseKeyDate(validDate);

        // Assert
        Assert.True(result);

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Never);
    }


    [Fact]
    public async Task AmcStartDate_ShouldReturnFormattedDate_WhenAMCStartDateExists()
    {
        // Arrange
        var json = @"{ ""AMC"": ""1"",""AMCStartDate"": ""2023-09-25"" }";
        
        // Act
        var result = await _service.AmcStartDate(json);

        // Assert
        Assert.Equal("25 September 2023", result);
    }

    [Fact]
    public async Task AmcStartDate_ShouldReturnToday_WhenAMCStartDateMissing()
    {
        // Arrange
        var json = @"{ }";
        
        // Act
        var result = await _service.AmcStartDate(json);

        // Assert
        Assert.Equal(DateTime.Today.ToString("dd MMMM yyyy"), result);
    }

    [Fact]
    public async Task AmcStartDate_ShouldReturnNA_WhenIsAmcIsFalse()
    {
        // Arrange
        var json = @"{ ""AMC"": ""0"",""AMCStartDate"": ""2023-09-25"" }";
        
        // Act
        var result = await _service.AmcStartDate(json);

        // Assert
        Assert.Equal("NA", result);
    }


    [Fact]
    public async Task IsHostNameValidAsync_ValidatesCorrectly()
    {
        // Arrange
        var hostName = await GetHostName();

        // Act
        var result = await _service.IsHostNameValidAsync(hostName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsHostNameValidAsync_ReturnsFalse_WhenHostNameDoesNotMatch()
    {
        // Arrange
        var hostName = "PTSDomain";

        // Act
        var result = await _service.IsMacAddressValidAsync(hostName);

        // Assert
        Assert.False(result);
    }


    [Fact]
    public async Task IsHostNameValidAsync_ShouldLogError_WhenHostNameDoesNotMatch()
    {
        var inputHostNames = "SOME-OTHER-HOST,ANOTHER";

        // Act
        var result = await _service.IsHostNameValidAsync(inputHostNames);

        // Assert
        Assert.False(result);

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) =>
                    v.ToString()!.Contains("Base License Host Name and CP Installed System Host Name MisMatch")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }


    [Fact]
    public async Task IsIpAddressValidAsync_ValidatesCorrectly()
    {
        // Arrange
        var ipAddress = await GetIpAddress();

        var firstIpAddress = ipAddress.FirstOrDefault();

        // Act
        var result = await _service.IsIpAddressValidAsync(firstIpAddress);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsIpAddressValidAsync_ReturnsFalse_WhenIpAddressDoesNotMatch()
    {
        // Arrange
        var ipAddress = "172.365.254.44";

        // Act
        var result = await _service.IsIpAddressValidAsync(ipAddress);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task DatabaseTypeLicenseCount_ShouldReturnCorrectCount_ForPrimaryDatabase()
    {
        // Arrange
        var properties = "{\"isDatabase\":true,\"DatabaseDto\":{\"primary\":[{\"id\":\"1\",\"count\":5}]}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };
        var databaseTypeId = "1";
        var index = 0;

        var expectedCount = 5;

        // Act
        var result = await _service.DatabaseTypeLicenseCount(licenseManager, siteType, databaseTypeId, index);

        // Assert
        Assert.Equal(expectedCount, result);
    }

    [Theory]
    [InlineData("primary", "4", "{\"isDatabase\":true ,\"DatabaseDto\":{\"primary\":[{\"id\":\"4\",\"count\":5}]}}", 5)]
    [InlineData("dr", "2", "{\"isDatabase\":true ,\"DatabaseDto\":{\"dr\":[{\"id\":\"2\",\"count\":3}]}}", 3)]
    [InlineData("custom dr", "thirdparty", "{\"isDatabase\":false, \"custom drdatabaseCount\":2}", 2)]
    [InlineData("custom dr", "1", "{\"isDatabase\":true ,\"DatabaseDto\":{\"custom dr\":[{\"id\":\"1\",\"count\":2}]}}", 2)]
    public async Task DatabaseTypeLicenseCount_ReturnsCorrectCount(string siteCategory, string databaseTypeId, string propertiesJson, int expectedCount)
    {
        // Arrange

        //custom drdatabaseCount
        var licenseManager = new LicenseManager { Properties = propertiesJson };
        var siteType = new SiteType { Category = siteCategory };

        
        // Act
        var result = await _service.DatabaseTypeLicenseCount(
            licenseManager,
            siteType,
            databaseTypeId,
            0); // using index 0 for testing

        // Assert
        Assert.Equal(expectedCount, result);
    }

    [Fact]
    public async Task DatabaseTypeLicenseCount_ForPrimaryWithDatabase_ReturnsDatabaseCount()
    {
        // Arrange
        var properties = "{\"isDatabase\":true ,\"DatabaseDto\":{\"primary\":[{\"id\":\"4\",\"count\":5}]}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.DatabaseTypeLicenseCount(
            licenseManager,
            siteType,
            "4",
            0);

        // Assert
        Assert.Equal(5, result);
    }

    [Fact]
    public async Task DatabaseTypeLicenseCount_ForNonDatabaseType_ReturnsAppCount()
    {
        // Arrange
        var properties = "{\"isDatabase\":true ,\"DatabaseDto\":{\"dr\":[{\"id\":\"2\",\"count\":3}]}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "dr" };

        // Act
        var result = await _service.DatabaseTypeLicenseCount(
            licenseManager,
            siteType,
            "2",
            0);

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public async Task DatabaseTypeLicenseCount_ForCustomDr_ReturnsThirdPartyCount()
    {
        // Arrange
        var properties = "{\"isDatabase\":false, \"custom drdatabaseCount\":2}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "custom dr" };


        // Act
        var result = await _service.DatabaseTypeLicenseCount(
            licenseManager,
            siteType,
            "thirdparty",
            0);

        // Assert
        Assert.Equal(2, result);
    }


    [Fact]
    public async Task DatabaseTypeLicenseCount_ForPrimarySiteWithNonDatabaseType_ReturnsCustomCount()
    {
        // Arrange
        var properties = "{\"isDatabase\":false, \"primarydatabaseCount\":5}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.DatabaseTypeLicenseCount(
            licenseManager,
            siteType,
            "app", // non-database type
            0);

        // Assert
        Assert.Equal(0, result);
    }


    [Theory]
    [InlineData("primary", "database", 5, 3, true)]  // count > serverCount
    [InlineData("primary", "database", 2, 3, false)] // count < serverCount
    [InlineData("dr", "database", 4, 4, false)]      // count == serverCount
    public async Task IsServerLicenseCountExitMaxLimit_PrimaryOrDrDatabase_ReturnsCorrectResult(
        string siteCategory, string type, int licenseCount, int serverCount, bool expected)
    {
        // Arrange
        var properties = $"{{\"{siteCategory.ToLower()}databaseCount\":{licenseCount}}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.IsServerLicenseCountExitMaxLimit(
            licenseManager, siteType, type, serverCount, 0);

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
    [InlineData("primary", "app", 5, 3, true)]
    [InlineData("dr", "web", 2, 5, false)]
    public async Task IsServerLicenseCountExitMaxLimit_PrimaryOrDrNonDatabase_ReturnsCorrectResult(
        string siteCategory, string type, int licenseCount, int serverCount, bool expected)
    {
        // Arrange
        var serverType = type.ToLower() + "Count";
        var properties = $"{{\"{siteCategory.ToLower()}{serverType}\":{licenseCount}}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.IsServerLicenseCountExitMaxLimit(
            licenseManager, siteType, type, serverCount, 0);

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
    //[InlineData("custom dr", "database", 5, 3, true)]
    [InlineData("custom dr", "database", 2, 5, false)]
    public async Task IsServerLicenseCountExitMaxLimit_CustomDrDatabase_ReturnsCorrectResult(
        string siteCategory, string type, int licenseCount, int serverCount, bool expected)
    {
        // Arrange
        var properties = "{}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.IsServerLicenseCountExitMaxLimit(
            licenseManager, siteType, type, serverCount, 0);

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
   // [InlineData("custom dr", "thirdparty", 5, 3, true)]
    [InlineData("custom dr", "app", 2, 5, false)]
    public async Task IsServerLicenseCountExitMaxLimit_CustomDrNonDatabase_ReturnsCorrectResult(
        string siteCategory, string type, int licenseCount, int serverCount, bool expected)
    {
        // Arrange
        var properties = "{}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.IsServerLicenseCountExitMaxLimit(
            licenseManager, siteType, type, serverCount, 0);

        // Assert
        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task IsServerLicenseCountExitMaxLimit_InvalidJson_ReturnsFalse()
    {
        // Arrange
        var invalidJson = "invalid json";
        var licenseManager = new LicenseManager { Properties = invalidJson };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsServerLicenseCountExitMaxLimit(
            licenseManager, siteType, "database", 5, 0);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsServerLicenseCountExitMaxLimit_ThirdPartyType_HandlesCorrectPropertyName()
    {
        // Arrange
        var properties = "{\"primarythirdPartyCount\":5}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsServerLicenseCountExitMaxLimit(
            licenseManager, siteType, "thirdparty", 3, 0);

        // Assert
        Assert.True(result);
    }

    [Theory]
    [InlineData("primary", 5, 3, true)]  // license count > replication count
    [InlineData("primary", 2, 3, false)] // license count < replication count
    [InlineData("primary", 4, 4, false)] // license count == replication count
    [InlineData("dr", 5, 3, true)]
    [InlineData("dr", 2, 3, false)]
    [InlineData("dr", 4, 4, false)]
    public async Task IsReplicationLicenseCountExitMaxLimit_PrimaryOrDr_ReturnsCorrectResult(
        string siteCategory, int licenseCount, int replicationCount, bool expected)
    {
        // Arrange
        var properties = $"{{\"{siteCategory.ToLower()}replicationCount\":{licenseCount}}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.IsReplicationLicenseCountExitMaxLimit(
            licenseManager, siteType, replicationCount, 0);

        // Assert
        Assert.Equal(expected, result);
    }

    // Custom DR site tests
    [Theory]
    [InlineData("custom dr", 5, 3, false)]
    [InlineData("custom dr", 2, 5, false)]
    [InlineData("custom dr", 4, 4, false)]
    [InlineData("backup site", 5, 3, false)]
    public async Task IsReplicationLicenseCountExitMaxLimit_CustomDr_ReturnsCorrectResult(
        string siteCategory, int licenseCount, int replicationCount, bool expected)
    {
        // Arrange
        var properties = "{}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.IsReplicationLicenseCountExitMaxLimit(
            licenseManager, siteType, replicationCount, 0);

        // Assert
        Assert.Equal(expected, result);
    }

    // Edge cases
    [Fact]
    public async Task IsReplicationLicenseCountExitMaxLimit_InvalidJson_ReturnsFalse()
    {
        // Arrange
        var invalidJson = "invalid json";
        var licenseManager = new LicenseManager { Properties = invalidJson };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsReplicationLicenseCountExitMaxLimit(
            licenseManager, siteType, 5, 0);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsReplicationLicenseCountExitMaxLimit_NegativeCounts_HandlesCorrectly()
    {
        // Arrange
        var properties = "{\"primaryreplicationCount\":-1}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsReplicationLicenseCountExitMaxLimit(
            licenseManager, siteType, -2, 0);

        // Assert
        Assert.True(result); // -1 > -2
    }

    [Fact]
    public async Task IsReplicationLicenseCountExitMaxLimit_ZeroCounts_HandlesCorrectly()
    {
        // Arrange
        var properties = "{\"primaryreplicationCount\":0}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsReplicationLicenseCountExitMaxLimit(
            licenseManager, siteType, 0, 0);

        // Assert
        Assert.False(result); // 0 == 0
    }

    [Theory]
    [InlineData("primary", 5)]
    [InlineData("primary", 0)]
    [InlineData("primary", 100)]
    public async Task ReplicationLicenseCount_PrimarySite_ReturnsJsonValue(string siteCategory, int expectedCount)
    {
        // Arrange
        var properties = $"{{\"{siteCategory.ToLower()}replicationCount\":{expectedCount}}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.ReplicationLicenseCount(licenseManager, siteType, 0);

        // Assert
        Assert.Equal(expectedCount, result);
    }

    // DR site tests
    [Theory]
    [InlineData("dr", 3)]
    [InlineData("dr", 0)]
    [InlineData("DR", 10)] // Test case insensitivity
    public async Task ReplicationLicenseCount_DrSite_ReturnsJsonValue(string siteCategory, int expectedCount)
    {
        // Arrange
        var lowerCategory = siteCategory.ToLower();
        var properties = $"{{\"{lowerCategory}replicationCount\":{expectedCount}}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.ReplicationLicenseCount(licenseManager, siteType, 0);

        // Assert
        Assert.Equal(expectedCount, result);
    }

    // Custom DR/other site tests
    [Theory]
    [InlineData("custom dr", 0)]
    [InlineData("backup", 0)]
    [InlineData("test site", 0)]
    public async Task ReplicationLicenseCount_NonPrimaryDrSite_ReturnsCustomValidation(string siteCategory, int expectedCount)
    {
        // Arrange
        var properties = "{}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.ReplicationLicenseCount(licenseManager, siteType, 0);

        // Assert
        Assert.Equal(expectedCount, result);
    }

    // Error case tests
    [Fact]
    public async Task ReplicationLicenseCount_InvalidJson_ReturnsZero()
    {
        // Arrange
        var invalidJson = "invalid json";
        var licenseManager = new LicenseManager { Properties = invalidJson };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.ReplicationLicenseCount(licenseManager, siteType, 0);

        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public async Task ReplicationLicenseCount_MissingProperty_ReturnsZero()
    {
        // Arrange
        var properties = "{\"otherProperty\":5}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.ReplicationLicenseCount(licenseManager, siteType, 0);

        // Assert
        Assert.Equal(0, result);
    }

    [Theory]
    [InlineData("primary", 5, 3, true)]  // license count > database count
    [InlineData("primary", 2, 3, false)] // license count < database count
    [InlineData("primary", 3, 3, false)] // license count == database count
    [InlineData("dr", 5, 3, true)]
    [InlineData("dr", 2, 3, false)]
    [InlineData("DR", 3, 3, false)] // case insensitivity test
    public async Task IsDatabaseLicenseCountExitMaxLimit_PrimaryOrDr_ReturnsCorrectComparison(
        string siteCategory, int licenseCount, int databaseCount, bool expected)
    {
        // Arrange
        var lowerCategory = siteCategory.ToLower();
        var properties = $"{{\"{lowerCategory}databaseCount\":{licenseCount}}}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.IsDatabaseLicenseCountExitMaxLimit(
            licenseManager, siteType, databaseCount, 0);

        // Assert
        Assert.Equal(expected, result);
    }

    // Custom/other site tests
    [Theory]
    //[InlineData("custom dr", 5, 3, true)]
    [InlineData("backup", 2, 5, false)]
    [InlineData("test site", 3, 3, false)]
    public async Task IsDatabaseLicenseCountExitMaxLimit_NonPrimaryDr_ReturnsCustomValidationResult(
        string siteCategory, int customCount, int databaseCount, bool expected)
    {
        // Arrange
        var properties = "{}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = siteCategory };

        // Act
        var result = await _service.IsDatabaseLicenseCountExitMaxLimit(
            licenseManager, siteType, databaseCount, 0);

        // Assert
        Assert.Equal(expected, result);
    }

    // Edge cases
    [Fact]
    public async Task IsDatabaseLicenseCountExitMaxLimit_InvalidJson_ReturnsFalse()
    {
        // Arrange
        var invalidJson = "invalid json";
        var licenseManager = new LicenseManager { Properties = invalidJson };
        var siteType = new SiteType { Category = "primary" };


        // Act
        var result = await _service.IsDatabaseLicenseCountExitMaxLimit(
            licenseManager, siteType, 5, 0);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsDatabaseLicenseCountExitMaxLimit_MissingProperty_ReturnsFalse()
    {
        // Arrange
        var properties = "{\"otherProperty\":5}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsDatabaseLicenseCountExitMaxLimit(
            licenseManager, siteType, 5, 0);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsDatabaseLicenseCountExitMaxLimit_NegativeCounts_HandlesCorrectly()
    {
        // Arrange
        var properties = "{\"primarydatabaseCount\":-1}";
        var licenseManager = new LicenseManager { Properties = properties };
        var siteType = new SiteType { Category = "primary" };

        // Act
        var result = await _service.IsDatabaseLicenseCountExitMaxLimit(
            licenseManager, siteType, -2, 0);

        // Assert
        Assert.True(result); // -1 > -2
    }

    [Theory]
    [InlineData("renewal")]
    [InlineData("RENEWAL")] // Test case insensitivity
    [InlineData("upgrade")]
    [InlineData("transfer")]
    [InlineData("")]
    public async Task IsRenewalLicenseValidate_NonNewLicense_ReturnsTrue(string licenseType)
    {
        // Act
        var result = await _service.IsRenewalLicenseValidate(licenseType);

        // Assert
        Assert.True(result);
        VerifyNoErrorLogged();
    }

    [Theory]
    [InlineData("new")]
    [InlineData("NEW")] // Test case insensitivity
    [InlineData("New")]
    public async Task IsRenewalLicenseValidate_NewLicense_ReturnsFalseAndLogsError(string licenseType)
    {
        // Act
        var result = await _service.IsRenewalLicenseValidate(licenseType);

        // Assert
        Assert.False(result);
        VerifyErrorLogged(licenseType);
    }

    private void VerifyNoErrorLogged()
    {
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => true),
                It.IsAny<Exception>(),
                It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)!),
            Times.Never);
    }

    private void VerifyErrorLogged(string licenseType)
    {
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) =>
                    o.ToString()!.Contains($"The specified license type '{licenseType}' is not supported for 'renewal'")),
                It.IsAny<Exception>(),
                It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)!),
            Times.Once);
    }



    private Task<List<string>> GetMacAddress()
    {
        var macAddress = (
            from nic in NetworkInterface.GetAllNetworkInterfaces()
            where nic.OperationalStatus == OperationalStatus.Up
            select nic.GetPhysicalAddress().ToString()).ToList();

        return Task.FromResult(macAddress);
    }
    private Task<string> GetHostName()
    {
        var hostname = Dns.GetHostName();
        return Task.FromResult(hostname);
    }

    private async Task<List<string>> GetIpAddress()
    {
        var hostEntry = await Dns.GetHostEntryAsync(Dns.GetHostName());
        var ipAddresses = hostEntry.AddressList
            .Where(ip => ip.AddressFamily == AddressFamily.InterNetwork) // Filter only IPv4 addresses if needed
            .Select(ip => ip.ToString())
            .ToList();
        return ipAddresses;
    }

}