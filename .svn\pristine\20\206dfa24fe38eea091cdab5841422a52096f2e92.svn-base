﻿using ContinuityPatrol.Application.Features.FiaInterval.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaInterval.Events;

public class UpdateFiaIntervalEventTests : IClassFixture<FiaIntervalFixture>, IClassFixture<UserActivityFixture>
{
    private readonly FiaIntervalFixture _fiaIntervalFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly FiaIntervalUpdatedEventHandler _handler;

    public UpdateFiaIntervalEventTests(FiaIntervalFixture fiaIntervalFixture, UserActivityFixture userActivityFixture)
    {
        _fiaIntervalFixture = fiaIntervalFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockFiaIntervalEventLogger = new Mock<ILogger<FiaIntervalUpdatedEventHandler>>();

        _mockUserActivityRepository = FiaIntervalRepositoryMocks.CreateFiaIntervalEventRepository(_userActivityFixture.UserActivities);

        _handler = new FiaIntervalUpdatedEventHandler(mockLoggedInUserService.Object, mockFiaIntervalEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateFiaIntervalEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_fiaIntervalFixture.FiaIntervalUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_fiaIntervalFixture.FiaIntervalUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}