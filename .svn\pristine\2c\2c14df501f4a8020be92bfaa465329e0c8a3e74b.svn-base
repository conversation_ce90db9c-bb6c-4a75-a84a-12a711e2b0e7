﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Events
{
   public class WorkflowDrCalenderCreatedEventHandlerTests : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly WorkflowDrCalenderFixture _workflowDrcalenderFixture;

        private readonly WorkflowDrCalenderCreatedEventHandler _handler;

        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        public WorkflowDrCalenderCreatedEventHandlerTests(WorkflowDrCalenderFixture workflowDrCalenderFixture)
        {
            _workflowDrcalenderFixture = workflowDrCalenderFixture;
            var mockLoggedInUserService = new Mock<ILoggedInUserService>();

            var mockWorkflowTempEventLogger = new Mock<ILogger<WorkflowDrCalenderCreatedEventHandler>>();

            _mockUserActivityRepository = WorkflowDrCalenderRepositoryMocks.CreateWorkflowDrCalenderEventRepository(_workflowDrcalenderFixture.UserActivities);


            _handler = new WorkflowDrCalenderCreatedEventHandler(mockLoggedInUserService.Object, mockWorkflowTempEventLogger.Object, _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_IncreaseUserActivityCount_When_CreateWorkflowDrEventCreated()
        {
            _workflowDrcalenderFixture.UserActivities[0].LoginName = "Test";

            var result = _handler.Handle(_workflowDrcalenderFixture.WorkflowDrCreatedEvent, CancellationToken.None);

            result.Equals(_workflowDrcalenderFixture.UserActivities[0].LoginName);

            await Task.CompletedTask;
        }

        [Fact]
        public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
        {
            await _handler.Handle(_workflowDrcalenderFixture.WorkflowDrCreatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_UserActivityCount_When_CreateWorkflowDrEventCreated()
        {
            _workflowDrcalenderFixture.UserActivities[0].LoginName = "Test";

            var result = _handler.Handle(_workflowDrcalenderFixture.WorkflowDrCreatedEvent, CancellationToken.None);

            result.Equals(_workflowDrcalenderFixture.UserActivities[0].Id);

            result.Equals(_workflowDrcalenderFixture.WorkflowDrCreatedEvent.Name);

            await Task.CompletedTask;
        }
    }
}
