using ContinuityPatrol.Application.Features.CyberAirGap.Commands.IsAttached;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Commands;

public class IsAttachedCyberAirGapTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<ICyberAirGapRepository> _mockCyberAirGapRepository;
    private readonly Mock<IMapper> _mapper;
    private readonly Mock<IPublisher> _publisher;
    private readonly AirGapAttachedCommandHandler _handler;

    public IsAttachedCyberAirGapTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockCyberAirGapRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(_cyberAirGapFixture.CyberAirGaps);
        _mapper = new Mock<IMapper>();
        _publisher = new Mock<IPublisher>();

        _handler = new AirGapAttachedCommandHandler(
            _mockCyberAirGapRepository.Object,
            _mapper.Object, _publisher.Object );
    }

    [Fact]
    public async Task Handle_ReturnTrue_When_AirGapIsAttached()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        // Setup: Cyber jobs associated with this air gap
        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
             .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<AirGapAttachedResponse>();
        result.Message.ShouldContain(existingAirGap.Name);
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_AirGapIsNotAttached()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        // Setup: No cyber jobs associated with this air gap
        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<AirGapAttachedResponse>();
        result.Message.ShouldContain(existingAirGap.Name);
    }

    [Fact]
    public async Task Handle_CallGetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGap>()); ;

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockCyberAirGapRepository.Verify(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_CallCyberJobManagement_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new AirGapAttachedCommand { Id = nonExistentId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapIsInactive()
    {
        // Arrange
        var inactiveAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        inactiveAirGap.IsActive = false;
        var command = new AirGapAttachedCommand { Id = inactiveAirGap.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_CyberJobListIsNull()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        // Setup: Null cyber job list
        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
             .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnTrue_When_SingleCyberJobExists()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        // Setup: Single cyber job associated with this air gap
        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnTrue_When_MultipleCyberJobsExist()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

       
        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
      .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

       
    }

    [Fact]
    public async Task Handle_CheckMultipleAirGaps_When_DifferentAttachmentStates()
    {
        // Arrange
        var airGaps = _cyberAirGapFixture.CyberAirGaps.Take(3).ToList();

        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        // Setup different attachment states
        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(airGaps[0].ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(airGaps[1].ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(airGaps[2].ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());
        

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
     .ReturnsAsync(existingAirGap);
        // Act & Assert
        var command1 = new AirGapAttachedCommand { Id = airGaps[0].ReferenceId };
        var result1 = await _handler.Handle(command1, CancellationToken.None);

        var command2 = new AirGapAttachedCommand { Id = airGaps[1].ReferenceId };
        var result2 = await _handler.Handle(command2, CancellationToken.None);

        var command3 = new AirGapAttachedCommand { Id = airGaps[2].ReferenceId };
        var result3 = await _handler.Handle(command3, CancellationToken.None);
    }

    [Fact]
    public async Task Handle_VerifyResponseMessage_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
             .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
        .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain(existingAirGap.Name);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_ValidCommand()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var command = new AirGapAttachedCommand { Id = existingAirGap.ReferenceId };

        _mockCyberAirGapRepository.Setup(x => x.GetAirGapByServerId(existingAirGap.ReferenceId))
             .ReturnsAsync(new List<Domain.Entities.CyberAirGap>());

        _mockCyberAirGapRepository.Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
       .ReturnsAsync(existingAirGap);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<AirGapAttachedResponse>();
        result.GetType().Name.ShouldBe("AirGapAttachedResponse");
    }
}
