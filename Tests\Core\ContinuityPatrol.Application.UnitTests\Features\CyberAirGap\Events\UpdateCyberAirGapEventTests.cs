using ContinuityPatrol.Application.Features.CyberAirGap.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Events;

public class UpdateCyberAirGapEventTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly CyberAirGapUpdatedEventHandler _handler;

    private readonly Mock<ILoggedInUserService> _userService;
    private readonly Mock<ILogger<CyberAirGapUpdatedEventHandler>> _logger;

    public UpdateCyberAirGapEventTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockUserActivityRepository = CyberAirGapRepositoryMocks.CreateUserActivityRepository(_cyberAirGapFixture.UserActivities);

        _userService = new Mock<ILoggedInUserService>();
        _logger = new Mock<ILogger<CyberAirGapUpdatedEventHandler>>();
        _handler = new CyberAirGapUpdatedEventHandler(_userService.Object, _logger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_ProcessCyberAirGapUpdatedEvent_When_ValidEvent()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapUpdatedEvent
        {
            Name = "TestAirGap_Updated"
        };

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivityWithCorrectUpdateProperties_When_ValidEvent()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapUpdatedEvent
        {
            Name = "TestAirGap_UpdateUserActivity"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.  UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapUpdatedEvent
        {
            Name = "TestAirGap_UpdateCancellation"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

    }

    [Fact]
    public async Task Handle_ProcessMultipleUpdateEvents_When_ValidEvents()
    {
        // Arrange
        var events = new[]
        {
            new CyberAirGapUpdatedEvent
            {
                Name = "TestAirGap_UpdateMultiple_1"
            },
            new CyberAirGapUpdatedEvent
            {
                Name = "TestAirGap_UpdateMultiple_2"
            },
            new CyberAirGapUpdatedEvent
            {
                Name = "TestAirGap_UpdateMultiple_3"
            }
        };

        // Act
        foreach (var eventItem in events)
        {
            await _handler.Handle(eventItem, CancellationToken.None);
        }

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(3));
    }

    [Fact]
    public async Task Handle_CreateCompleteUpdateUserActivity_When_EventWithAllProperties()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapUpdatedEvent
        {
            Name = "CompleteUpdateTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleWorkflowStatusChanges_When_EventWithDifferentStatuses()
    {
        // Arrange
        var workflowStatuses = new[] { "Active", "Inactive", "Pending", "Error", "Completed", "Processing" };

        foreach (var status in workflowStatuses)
        {
            var cyberAirGapEvent = new CyberAirGapUpdatedEvent
            {
                Name = $"TestAirGap_WorkflowStatus_{status}"
            };

            Domain.Entities.UserActivity createdActivity = null;
            _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
                .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

            // Act
            await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

            // Assert
            createdActivity.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Handle_HandlePortChanges_When_EventWithDifferentPorts()
    {
        // Arrange
        var ports = new[] { 80, 443, 8080, 9090, 65535 };

        foreach (var port in ports)
        {
            var cyberAirGapEvent = new CyberAirGapUpdatedEvent
            {
                Name = $"TestAirGap_Port_{port}"
            };

            Domain.Entities.UserActivity createdActivity = null;
            _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
                .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

            // Act
            await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

            // Assert
            createdActivity.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Handle_HandleComponentChanges_When_EventWithUpdatedComponents()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapUpdatedEvent
        {
            Name = "TestAirGap_ComponentChange"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }
    [Fact]
    public async Task Handle_HandleSiteChanges_When_EventWithUpdatedSites()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapUpdatedEvent
        {
            Name = "TestAirGap_SiteChange"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_HandleJsonChanges_When_EventWithUpdatedSourceTarget()
    {
        // Arrange
        var updatedSource = "{\"serverId\":\"updated-srv-001\",\"componentId\":\"updated-comp-001\",\"version\":\"2.0\"}";
        var updatedTarget = "{\"serverId\":\"updated-srv-002\",\"componentId\":\"updated-comp-002\",\"version\":\"2.0\"}";

        var cyberAirGapEvent = new CyberAirGapUpdatedEvent
        {
            Name = "TestAirGap_JsonChange"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_HandleNullUpdatedBy_When_EventWithNullUpdatedBy()
    {
        // Arrange
        var cyberAirGapEvent = new CyberAirGapUpdatedEvent
        {
            Name = "NullUpdatedByTestAirGap"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ProcessRapidUpdateEvents_When_MultipleEventsInSuccession()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new CyberAirGapUpdatedEvent
        {
            Name = $"RapidUpdateTestAirGap_{i:00}"
        }).ToList();

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(10));
    }
}
