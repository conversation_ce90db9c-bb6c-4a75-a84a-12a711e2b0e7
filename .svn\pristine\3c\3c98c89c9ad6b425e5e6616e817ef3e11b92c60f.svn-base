using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Delete;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetByFormTypeId;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetFormTypeCategoryByName;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetList;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using FluentAssertions;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class FormTypeCategoryControllerTests : IClassFixture<FormTypeCategoryFixture>
{
    private readonly FormTypeCategoryController _controller;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;

    public FormTypeCategoryControllerTests(FormTypeCategoryFixture formTypeCategoryFixture)
    {
        _formTypeCategoryFixture = formTypeCategoryFixture;
        var testBuilder = new ControllerTestBuilder<FormTypeCategoryController>();
        _controller = testBuilder.CreateController(
            _ => new FormTypeCategoryController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task CreateFormTypeCategory_WithValidRequest_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formTypeCategoryFixture.CreateFormTypeCategoryCommand;
        var response = _formTypeCategoryFixture.CreateFormTypeCategoryResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormTypeCategoryCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateFormTypeCategory(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormTypeCategoryResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateFormTypeCategory_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var command = _formTypeCategoryFixture.UpdateFormTypeCategoryCommand;
        var response = _formTypeCategoryFixture.UpdateFormTypeCategoryResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormTypeCategoryCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateFormTypeCategory(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormTypeCategoryResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task DeleteFormTypeCategory_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var response = _formTypeCategoryFixture.DeleteFormTypeCategoryResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<DeleteFormTypeCategoryCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.DeleteFormTypeCategory(id);

        // Assert
        result.Should().BeOfType<ActionResult<DeleteFormTypeCategoryResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<DeleteFormTypeCategoryCommand>(c => c.Id == id), default), Times.Once);
    }

    [Fact]
    public async Task GetFormTypeCategoryDetail_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var response = _formTypeCategoryFixture.FormTypeCategoryDetailVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryDetailQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormTypeCategoryById(id);

        // Assert
        result.Should().BeOfType<ActionResult<FormTypeCategoryDetailVm>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeCategoryDetailQuery>(q => q.Id == id), default), Times.Once);
    }

    [Fact]
    public async Task GetFormTypeCategoryByFormTypeId_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var query = _formTypeCategoryFixture.GetFormTypeCategoryByFormTypeIdQuery;
        var response = _formTypeCategoryFixture.FormTypeCategoryByFormTypeIdVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryByFormTypeIdQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormTypeCategoryByFormTypeId(query.FormTypeId, query.Version);

        // Assert
        result.Should().BeOfType<ActionResult<FormTypeCategoryByFormTypeIdVm>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeCategoryByFormTypeIdQuery>(q => 
            q.FormTypeId == query.FormTypeId && q.Version == query.Version), default), Times.Once);
    }

    [Fact]
    public async Task GetFormTypeCategoryList_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var response = _formTypeCategoryFixture.FormTypeCategoryListVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryListQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormTypeCategories();

        // Assert
        result.Should().BeOfType<ActionResult<List<FormTypeCategoryListVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetFormTypeCategoryListQuery>(), default), Times.Once);
    }

    [Fact]
    public async Task GetFormTypeCategoryNames_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var response = _formTypeCategoryFixture.FormTypeCategoryNameVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryNameQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormTypeCategoryNames();

        // Assert
        result.Should().BeOfType<ActionResult<List<FormTypeCategoryNameVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetFormTypeCategoryNameQuery>(), default), Times.Once);
    }

    [Fact]
    public async Task IsFormTypeCategoryNameExist_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var query = _formTypeCategoryFixture.GetFormTypeCategoryNameUniqueQuery;
        var response = true;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryNameUniqueQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.IsFormTypeCategoryNameExist(query.Name, query.Id);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var actionResult = result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeCategoryNameUniqueQuery>(q => 
            q.Name == query.Name && q.Id == query.Id), default), Times.Once);
    }

    [Fact]
    public async Task GetPaginatedFormTypeCategories_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var query = _formTypeCategoryFixture.GetFormTypeCategoryPaginatedListQuery;
        var response = _formTypeCategoryFixture.FormTypeCategoryPaginatedResult;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryPaginatedListQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetPaginatedFormTypeCategory(query);

        // Assert
        result.Should().BeOfType<ActionResult<List<FormTypeCategoryListVm>>>();
        var actionResult = result.Result as OkObjectResult;
        var resultValue = actionResult?.Value as PaginatedResult<FormTypeCategoryListVm>;
        resultValue?.Data.Should().NotBeNull();
        _mediatorMock.Verify(m => m.Send(query, default), Times.Once);
    }

    [Fact]
    public async Task GetFormTypeCategoryByName_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var query = _formTypeCategoryFixture.GetFormTypeCategoryByNameQuery;
        var response = _formTypeCategoryFixture.FormTypeCategoryByNameVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryByNameQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormTypeCategoryByType(query.Name);

        // Assert
        result.Should().BeOfType<ActionResult<List<FormTypeCategoryByNameVm>>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeCategoryByNameQuery>(q => q.Name == query.Name), default), Times.Once);
    }

    [Fact]
    public async Task CreateFormTypeCategory_WithComplexProperties_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formTypeCategoryFixture.CreateFormTypeCategoryCommand;
        command.Properties = "{\"category\":\"enterprise-risk\",\"priority\":\"critical\",\"department\":\"Risk Management\",\"features\":[\"audit-trail\",\"compliance-check\"]}";
        var response = _formTypeCategoryFixture.CreateFormTypeCategoryResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormTypeCategoryCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateFormTypeCategory(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormTypeCategoryResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<CreateFormTypeCategoryCommand>(c => 
            c.Properties.Contains("enterprise-risk")), default), Times.Once);
    }

    [Fact]
    public async Task UpdateFormTypeCategory_WithVersionUpdate_ReturnsOkResult()
    {
        // Arrange
        var command = _formTypeCategoryFixture.UpdateFormTypeCategoryCommand;
        command.Version = "2.0";
        command.FormVersion = "2.0";
        var response = _formTypeCategoryFixture.UpdateFormTypeCategoryResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<UpdateFormTypeCategoryCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateFormTypeCategory(command);

        // Assert
        result.Should().BeOfType<ActionResult<UpdateFormTypeCategoryResponse>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<UpdateFormTypeCategoryCommand>(c => 
            c.Version == "2.0" && c.FormVersion == "2.0"), default), Times.Once);
    }

    [Fact]
    public async Task GetFormTypeCategoryByFormTypeId_WithSpecificVersion_ReturnsCorrectData()
    {
        // Arrange
        var formTypeId = Guid.NewGuid().ToString();
        var version = "1.5";
        var response = _formTypeCategoryFixture.FormTypeCategoryByFormTypeIdVm;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryByFormTypeIdQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetFormTypeCategoryByFormTypeId(formTypeId, version);

        // Assert
        result.Should().BeOfType<ActionResult<FormTypeCategoryByFormTypeIdVm>>();
        var actionResult = result.Result as OkObjectResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeCategoryByFormTypeIdQuery>(q => 
            q.FormTypeId == formTypeId && q.Version == version), default), Times.Once);
    }

    [Fact]
    public async Task IsFormTypeCategoryNameExist_WithDuplicateName_ReturnsTrue()
    {
        // Arrange
        var name = "Enterprise Risk Management Category";
        var id = Guid.NewGuid().ToString();
        var response = true;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryNameUniqueQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.IsFormTypeCategoryNameExist(name, id);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var actionResult = result as OkObjectResult;
        actionResult?.Value.Should().Be(true);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeCategoryNameUniqueQuery>(q => 
            q.Name == name && q.Id == id), default), Times.Once);
    }

    [Fact]
    public async Task GetPaginatedFormTypeCategories_WithLargePageSize_ReturnsOkResult()
    {
        // Arrange
        var query = _formTypeCategoryFixture.GetFormTypeCategoryPaginatedListQuery;
        query.PageSize = 50;
        query.PageNumber = 1;
        var response = _formTypeCategoryFixture.FormTypeCategoryPaginatedResult;
        response.PageSize = 50;
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeCategoryPaginatedListQuery>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.GetPaginatedFormTypeCategory(query);

        // Assert
        result.Should().BeOfType<ActionResult<List<FormTypeCategoryListVm>>>();
        var actionResult = result.Result as OkObjectResult;
        var resultValue = actionResult?.Value as PaginatedResult<FormTypeCategoryListVm>;
        resultValue?.PageSize.Should().Be(50);
        _mediatorMock.Verify(m => m.Send(It.Is<GetFormTypeCategoryPaginatedListQuery>(q => 
            q.PageSize == 50), default), Times.Once);
    }

    [Fact]
    public async Task CreateFormTypeCategory_WithLogoAndFormMapping_ReturnsCreatedResult()
    {
        // Arrange
        var command = _formTypeCategoryFixture.CreateFormTypeCategoryCommand;
        command.Logo = "enterprise-category-logo.png";
        command.FormName = "Enterprise Risk Assessment Form";
        command.FormTypeName = "Enterprise Risk Management";
        var response = _formTypeCategoryFixture.CreateFormTypeCategoryResponse;
        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateFormTypeCategoryCommand>(), default))
                   .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateFormTypeCategory(command);

        // Assert
        result.Should().BeOfType<ActionResult<CreateFormTypeCategoryResponse>>();
        var actionResult = result.Result as CreatedAtActionResult;
        actionResult?.Value.Should().Be(response);
        _mediatorMock.Verify(m => m.Send(It.Is<CreateFormTypeCategoryCommand>(c =>
            c.Logo == "enterprise-category-logo.png" &&
            c.FormName == "Enterprise Risk Assessment Form"), default), Times.Once);
    }

    // Null validation tests
    [Fact]
    public async Task GetFormTypeCategoryDetail_WithNullId_ThrowsInvalidArgumentException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _controller.GetFormTypeCategoryById(nullId));
    }

    [Fact]
    public async Task GetFormTypeCategoryByFormTypeId_WithNullFormTypeId_ThrowsInvalidArgumentException()
    {
        // Arrange
        string nullFormTypeId = null;
        string version = "1.0";

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _controller.GetFormTypeCategoryByFormTypeId(nullFormTypeId, version));
    }

    [Fact]
    public async Task IsFormTypeCategoryNameExist_WithNullName_ThrowsInvalidArgumentException()
    {
        // Arrange
        string nullName = null;
        string id = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _controller.IsFormTypeCategoryNameExist(nullName, id));
    }

    [Fact]
    public async Task CreateFormTypeCategory_WithNullCommand_ThrowsNullReferenceException()
    {
        // Arrange
        CreateFormTypeCategoryCommand command = null;

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(
            () => _controller.CreateFormTypeCategory(command));
    }

    [Fact]
    public async Task UpdateFormTypeCategory_WithNullCommand_ThrowsNullReferenceException()
    {
        // Arrange
        UpdateFormTypeCategoryCommand command = null;

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(
            () => _controller.UpdateFormTypeCategory(command));
    }
}
