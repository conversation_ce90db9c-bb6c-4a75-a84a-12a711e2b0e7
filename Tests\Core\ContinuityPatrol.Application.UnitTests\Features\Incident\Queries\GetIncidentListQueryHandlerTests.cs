﻿using ContinuityPatrol.Application.Features.Incident.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.IncidentModel;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Queries;

public class GetIncidentListQueryHandlerTests : IClassFixture<IncidentFixture>
{
    private readonly IncidentFixture _incidentFixture;
    private Mock<IIncidentRepository> _mockIncidentRepository;
    private readonly GetIncidentListQueryHandler _handler;

    public GetIncidentListQueryHandlerTests(IncidentFixture incidentFixture)
    {
        _incidentFixture = incidentFixture;

        _mockIncidentRepository = IncidentRepositoryMocks.GetIncidentRepository(_incidentFixture.Incidents);

        _handler = new GetIncidentListQueryHandler(_mockIncidentRepository.Object, _incidentFixture.Mapper);

        _incidentFixture.Incidents[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
    }

    [Fact]
    public async Task Handle_Return_Valid_IncidentsDetail()
    {
        var result = await _handler.Handle(new GetIncidentListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<IncidentListVm>>();

        result[0].Id.ShouldBe(_incidentFixture.Incidents[0].ReferenceId);
        result[0].IncidentName.ShouldBe(_incidentFixture.Incidents[0].IncidentName);
        result[0].IncidentNumber.ShouldBe(_incidentFixture.Incidents[0].IncidentNumber);
        result[0].Description.ShouldBe(_incidentFixture.Incidents[0].Description);
        result[0].AlertId.ShouldBe(_incidentFixture.Incidents[0].AlertId);
        result[0].WorkflowId.ShouldBe(_incidentFixture.Incidents[0].WorkflowId);
        result[0].WorkflowName.ShouldBe(_incidentFixture.Incidents[0].WorkflowName);
        result[0].BusinessServiceId.ShouldBe(_incidentFixture.Incidents[0].BusinessServiceId);
        result[0].BusinessServiceName.ShouldBe(_incidentFixture.Incidents[0].BusinessServiceName);
        result[0].BusinessFunctionId.ShouldBe(_incidentFixture.Incidents[0].BusinessFunctionId);
        result[0].BusinessFunctionName.ShouldBe(_incidentFixture.Incidents[0].BusinessFunctionName);
        result[0].InfraObjectId.ShouldBe(_incidentFixture.Incidents[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_incidentFixture.Incidents[0].InfraObjectName);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockIncidentRepository = IncidentRepositoryMocks.GetIncidentEmptyRepository();
        var handler = new GetIncidentListQueryHandler(_mockIncidentRepository.Object, _incidentFixture.Mapper);
        var result = await handler.Handle(new GetIncidentListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetIncidentListQuery(), CancellationToken.None);
        _mockIncidentRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public void GetIncidentListQuery_CanBeInstantiated()
    {
        var query = new GetIncidentListQuery();
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Return_CorrectCount_When_MultipleIncidents()
    {
        var result = await _handler.Handle(new GetIncidentListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<IncidentListVm>>();
        result.Count.ShouldBe(_incidentFixture.Incidents.Count);
    }

    [Fact]
    public async Task Handle_Return_MappedIncidentList_When_ValidData()
    {
        var result = await _handler.Handle(new GetIncidentListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<IncidentListVm>>();
        result.ShouldNotBeEmpty();

        foreach (var incidentVm in result)
        {
            incidentVm.Id.ShouldNotBeNullOrEmpty();
            incidentVm.IncidentName.ShouldNotBeNullOrEmpty();
            incidentVm.IncidentNumber.ShouldNotBeNullOrEmpty();
        }
    }
}