﻿using AutoMapper;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetailByName;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Queries;

public class GetGlobalVariableDetailByNameQueryHandlerTests : IClassFixture<GlobalVariableFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;
    private Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;
    private readonly GetGlobalVariableDetailByNameQueryHanlder _handler;

    public GetGlobalVariableDetailByNameQueryHandlerTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.GetGlobalVariableByNameRepository(_globalVariableFixture.GlobalVariables);

        _handler = new GetGlobalVariableDetailByNameQueryHanlder(_mockGlobalVariableRepository.Object, _globalVariableFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_Active_GlobalVariables_ByName()
    {
        var result = await _handler.Handle(new GetGlobalVariableDetailByNameQuery { Name = _globalVariableFixture.GlobalVariables[0].VariableName }, CancellationToken.None);

        result.ShouldBeOfType<List<GlobalVariableDetailVm>>();

        result[0].Id.ShouldBe(_globalVariableFixture.GlobalVariables[0].ReferenceId);
        result[0].VariableName.ShouldBe(_globalVariableFixture.GlobalVariables[0].VariableName);
    }

    [Fact]
    public async Task Handle_Return_Active_GlobalVariablesCount()
    {
        var result = await _handler.Handle(new GetGlobalVariableDetailByNameQuery { Name = _globalVariableFixture.GlobalVariables[0].VariableName }, CancellationToken.None);

        result.ShouldBeOfType<List<GlobalVariableDetailVm>>();
        result.Count.ShouldBeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.GetGlobalVariableEmptyRepository();

        var handler = new GetGlobalVariableDetailByNameQueryHanlder(_mockGlobalVariableRepository.Object, _globalVariableFixture.Mapper);
        var result = await handler.Handle(new GetGlobalVariableDetailByNameQuery { Name = "NonExistentName" }, CancellationToken.None);
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetByVariableNameMethod_OneTime()
    {
        await _handler.Handle(new GetGlobalVariableDetailByNameQuery { Name = "TestName" }, CancellationToken.None);
        _mockGlobalVariableRepository.Verify(x => x.GetByVariableName(It.IsAny<string>()), Times.Once);
    }
}