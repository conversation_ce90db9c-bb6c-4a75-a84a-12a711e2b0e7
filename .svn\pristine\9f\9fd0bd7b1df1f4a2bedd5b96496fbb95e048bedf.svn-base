using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapLog.Queries;

public class GetCyberAirGapLogListTests : IClassFixture<CyberAirGapLogFixture>
{
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;
    private readonly Mock<ICyberAirGapLogRepository> _mockCyberAirGapLogRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetCyberAirGapLogListQueryHandler _handler;

    public GetCyberAirGapLogListTests(CyberAirGapLogFixture cyberAirGapLogFixture)
    {
        _cyberAirGapLogFixture = cyberAirGapLogFixture;
        _mockCyberAirGapLogRepository = CyberAirGapLogRepositoryMocks.CreateCyberAirGapLogRepository(_cyberAirGapLogFixture.CyberAirGapLogs);
        _mockMapper = new Mock<IMapper>();

        _handler = new GetCyberAirGapLogListQueryHandler(
            _mockMapper.Object,
            _mockCyberAirGapLogRepository.Object);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_ValidData()
    {
        // Arrange
        var query = new GetCyberAirGapLogListQuery();
        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select(e => new CyberAirGapLogListVm
        {
            Id = e.ReferenceId,
            AirGapId = e.AirGapId,
            AirGapName = e.AirGapName,
            SourceSiteName = e.SourceSiteName,
            TargetSiteName = e.TargetSiteName,
            Port = e.Port,
            Status = e.Status
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapLogListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<CyberAirGapLogListVm>>();
        result.Count.ShouldBe(activeEntities.Count);
        result.ShouldAllBe(vm => !string.IsNullOrEmpty(vm.Id));
        result.ShouldAllBe(vm => !string.IsNullOrEmpty(vm.AirGapName));

        _mockCyberAirGapLogRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockMapper.Verify(x => x.Map<List<CyberAirGapLogListVm>>(activeEntities), Times.Once);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_NoData()
    {
        // Arrange
        var query = new GetCyberAirGapLogListQuery();
        var emptyRepository = CyberAirGapLogRepositoryMocks.CreateEmptyCyberAirGapLogRepository();

        var handler = new GetCyberAirGapLogListQueryHandler(
            _mockMapper.Object,
            emptyRepository.Object);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<CyberAirGapLogListVm>>();
        result.Count.ShouldBe(0);

        emptyRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockMapper.Verify(x => x.Map<List<CyberAirGapLogListVm>>(It.IsAny<List<Domain.Entities.CyberAirGapLog>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_CancellationRequested()
    {
        // Arrange
        var query = new GetCyberAirGapLogListQuery();
        var cancellationToken = new CancellationToken(true);

      
    }

    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_RepositoryFails()
    {
        // Arrange
        var query = new GetCyberAirGapLogListQuery();
        var mockFailingRepository = CyberAirGapLogRepositoryMocks.CreateFailingCyberAirGapLogRepository();

        var handler = new GetCyberAirGapLogListQueryHandler(
            _mockMapper.Object,
            mockFailingRepository.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(query, CancellationToken.None));

        exception.Message.ShouldBe("List operation failed");
        _mockMapper.Verify(x => x.Map<List<CyberAirGapLogListVm>>(It.IsAny<List<Domain.Entities.CyberAirGapLog>>()), Times.Never);
    }

    /// <summary>
    /// Test: Get cyber air gap log list with mapper integration
    /// Expected: Correctly maps entities to view models
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_MapperIntegration()
    {
        // Arrange
        var query = new GetCyberAirGapLogListQuery();
        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select((e, index) => new CyberAirGapLogListVm
        {
            Id = e.ReferenceId,
            AirGapId = e.AirGapId,
            AirGapName = e.AirGapName,
            SourceSiteId = e.SourceSiteId,
            SourceSiteName = e.SourceSiteName,
            TargetSiteId = e.TargetSiteId,
            TargetSiteName = e.TargetSiteName,
            Port = e.Port,
            Description = e.Description,
            Source = e.Source,
            Target = e.Target,
            SourceComponentId = e.SourceComponentId,
            SourceComponentName = e.SourceComponentName,
            TargetComponentId = e.TargetComponentId,
            TargetComponentName = e.TargetComponentName,
            EnableWorkflowId = e.EnableWorkflowId,
            DisableWorkflowId = e.DisableWorkflowId,
            ErrorMessage = e.ErrorMessage,
            WorkflowStatus = e.WorkflowStatus,
            StartTime = e.StartTime,
            EndTime = e.EndTime,
            RPO = e.RPO,
            Status = e.Status,
            IsFileTransfered = e.IsFileTransfered
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapLogListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(expectedVmList.Count);

        for (int i = 0; i < result.Count; i++)
        {
            result[i].Id.ShouldBe(expectedVmList[i].Id);
            result[i].AirGapId.ShouldBe(expectedVmList[i].AirGapId);
            result[i].AirGapName.ShouldBe(expectedVmList[i].AirGapName);
            result[i].SourceSiteName.ShouldBe(expectedVmList[i].SourceSiteName);
            result[i].TargetSiteName.ShouldBe(expectedVmList[i].TargetSiteName);
            result[i].Port.ShouldBe(expectedVmList[i].Port);
            result[i].Status.ShouldBe(expectedVmList[i].Status);
        }

        _mockMapper.Verify(x => x.Map<List<CyberAirGapLogListVm>>(activeEntities), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap log list for different air gap types
    /// Expected: Successfully retrieves logs for various air gap types
    /// </summary>
    [Theory]
    [InlineData("Database")]
    [InlineData("File")]
    [InlineData("Archive")]
    [InlineData("Backup")]
    [InlineData("Network")]
    public async Task Handle_GetCyberAirGapLogList_When_DifferentAirGapTypes(string airGapType)
    {
        // Arrange
        var testEntities = new List<Domain.Entities.CyberAirGapLog>
        {
            new Domain.Entities.CyberAirGapLog
            {
                ReferenceId = $"test-{airGapType.ToLower()}-001",
                AirGapId = $"airgap-{airGapType.ToLower()}-001",
                AirGapName = $"{airGapType} Air Gap System",
                SourceSiteName = $"{airGapType} Source Site",
                TargetSiteName = $"{airGapType} Target Site",
                Status = "Active",
                IsActive = true
            }
        };

        var mockRepository = CyberAirGapLogRepositoryMocks.CreateCyberAirGapLogRepository(testEntities);
        var handler = new GetCyberAirGapLogListQueryHandler(_mockMapper.Object, mockRepository.Object);

        var query = new GetCyberAirGapLogListQuery();

        var expectedVmList = testEntities.Select(e => new CyberAirGapLogListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName,
            SourceSiteName = e.SourceSiteName,
            TargetSiteName = e.TargetSiteName,
            Status = e.Status
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapLogListVm>>(testEntities))
            .Returns(expectedVmList);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        result.First().AirGapName.ShouldContain(airGapType);
        result.First().SourceSiteName.ShouldContain(airGapType);
        result.First().TargetSiteName.ShouldContain(airGapType);

        mockRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap log list only returns active entities
    /// Expected: Filters out inactive entities
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_OnlyActiveEntities()
    {
        // Arrange
        var testEntities = new List<Domain.Entities.CyberAirGapLog>
        {
            new Domain.Entities.CyberAirGapLog
            {
                ReferenceId = "active-001",
                AirGapName = "Active Air Gap 1",
                IsActive = true
            },
            new Domain.Entities.CyberAirGapLog
            {
                ReferenceId = "active-002",
                AirGapName = "Active Air Gap 2",
                IsActive = true
            },
            new Domain.Entities.CyberAirGapLog
            {
                ReferenceId = "inactive-001",
                AirGapName = "Inactive Air Gap 1",
                IsActive = false
            },
            new Domain.Entities.CyberAirGapLog
            {
                ReferenceId = "inactive-002",
                AirGapName = "Inactive Air Gap 2",
                IsActive = false
            }
        };

        var mockRepository = CyberAirGapLogRepositoryMocks.CreateCyberAirGapLogRepository(testEntities);
        var handler = new GetCyberAirGapLogListQueryHandler(_mockMapper.Object, mockRepository.Object);

        var query = new GetCyberAirGapLogListQuery();
        var activeEntities = testEntities.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select(e => new CyberAirGapLogListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapLogListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(2); // Only active entities
        result.ShouldAllBe(vm => vm.AirGapName.Contains("Active"));
        result.ShouldNotContain(vm => vm.AirGapName.Contains("Inactive"));

        mockRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap log list with large dataset
    /// Expected: Handles large datasets efficiently
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_LargeDataset()
    {
        // Arrange
        var largeRepository = CyberAirGapLogRepositoryMocks.CreateLargeCyberAirGapLogRepository(1000);
        var handler = new GetCyberAirGapLogListQueryHandler(_mockMapper.Object, largeRepository.Object);

        var query = new GetCyberAirGapLogListQuery();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapLogListVm>>(It.IsAny<List<Domain.Entities.CyberAirGapLog>>()))
            .Returns((List<Domain.Entities.CyberAirGapLog> entities) => entities.Select(e => new CyberAirGapLogListVm
            {
                Id = e.ReferenceId,
                AirGapName = e.AirGapName,
                Status = e.Status
            }).ToList());

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await handler.Handle(query, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1000);
        stopwatch.ElapsedMilliseconds.ShouldBeLessThan(1000); // Should complete within 1 second

        largeRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    /// <summary>
    /// Test: Get cyber air gap log list response validation
    /// Expected: Response contains all required properties
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_ValidatingResponse()
    {
        // Arrange
        var query = new GetCyberAirGapLogListQuery();
        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select(e => new CyberAirGapLogListVm
        {
            Id = e.ReferenceId,
            AirGapId = e.AirGapId,
            AirGapName = e.AirGapName,
            SourceSiteId = e.SourceSiteId,
            SourceSiteName = e.SourceSiteName,
            TargetSiteId = e.TargetSiteId,
            TargetSiteName = e.TargetSiteName,
            Port = e.Port,
            Description = e.Description,
            Source = e.Source,
            Target = e.Target,
            SourceComponentId = e.SourceComponentId,
            SourceComponentName = e.SourceComponentName,
            TargetComponentId = e.TargetComponentId,
            TargetComponentName = e.TargetComponentName,
            EnableWorkflowId = e.EnableWorkflowId,
            DisableWorkflowId = e.DisableWorkflowId,
            ErrorMessage = e.ErrorMessage,
            WorkflowStatus = e.WorkflowStatus,
            StartTime = e.StartTime,
            EndTime = e.EndTime,
            RPO = e.RPO,
            Status = e.Status,
            IsFileTransfered = e.IsFileTransfered
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapLogListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<CyberAirGapLogListVm>>();

        // Validate each item in the list has required properties
        foreach (var item in result)
        {
            item.Id.ShouldNotBeNullOrEmpty();
            item.AirGapName.ShouldNotBeNullOrEmpty();
            // Other properties can be null/empty but should be mapped correctly
        }
    }

    /// <summary>
    /// Test: Get cyber air gap log list repository verification
    /// Expected: Repository is called exactly once
    /// </summary>
    [Fact]
    public async Task Handle_GetCyberAirGapLogList_When_RepositoryVerification()
    {
        // Arrange
        var query = new GetCyberAirGapLogListQuery();
        var activeEntities = _cyberAirGapLogFixture.CyberAirGapLogs.Where(x => x.IsActive).ToList();

        var expectedVmList = activeEntities.Select(e => new CyberAirGapLogListVm
        {
            Id = e.ReferenceId,
            AirGapName = e.AirGapName
        }).ToList();

        _mockMapper.Setup(x => x.Map<List<CyberAirGapLogListVm>>(activeEntities))
            .Returns(expectedVmList);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();

        // Verify repository was called exactly once
        _mockCyberAirGapLogRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockCyberAirGapLogRepository.VerifyNoOtherCalls();

        // Verify mapper was called exactly once with the correct entities
        _mockMapper.Verify(x => x.Map<List<CyberAirGapLogListVm>>(activeEntities), Times.Once);
        _mockMapper.VerifyNoOtherCalls();
    }
}
