﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactCategory.Queries
{
    public class GetFiaImpactCategoryListQueryHandlerTests
    {
        private readonly IMapper _mapper;
        private readonly Mock<IFiaImpactCategoryRepository> _mockRepository;
        private readonly GetFiaImpactCategoryListQueryHandler _handler;
        private readonly List<Domain.Entities.FiaImpactCategory> _fiaImpactCategories;
        private readonly Fixture _fixture;

        public GetFiaImpactCategoryListQueryHandlerTests()
        {
            _fixture = new Fixture();

            _fiaImpactCategories = _fixture.CreateMany<Domain.Entities.FiaImpactCategory>(3).ToList();

            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<FiaImpactCategoryProfile>();
            });
            _mapper = config.CreateMapper();

            _mockRepository = FiaImpactCategoryRepositoryMocks.GetFiaImpactCategoryRepository(_fiaImpactCategories);

            _handler = new GetFiaImpactCategoryListQueryHandler(_mapper, _mockRepository.Object);
        }

        [Fact(DisplayName = "Handle_Should_Return_Mapped_List_When_Data_Exists")]
        public async Task Handle_Should_Return_Mapped_List_When_Data_Exists()
        {
            // Arrange
            var query = new GetFiaImpactCategoryListQuery();

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(_fiaImpactCategories.Count, result.Count);
            Assert.All(result, item => Assert.IsType<FiaImpactCategoryListVm>(item));
        }

        [Fact(DisplayName = "Handle_Should_Return_Empty_List_When_No_Data_Exists")]
        public async Task Handle_Should_Return_Empty_List_When_No_Data_Exists()
        {
            // Arrange
            var emptyRepo = FiaImpactCategoryRepositoryMocks.GetFiaImpactCategoryRepository(new List<Domain.Entities.FiaImpactCategory>());
            var handlerWithEmptyRepo = new GetFiaImpactCategoryListQueryHandler(_mapper, emptyRepo.Object);
            var query = new GetFiaImpactCategoryListQuery();

            // Act
            var result = await handlerWithEmptyRepo.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }
    }
}
