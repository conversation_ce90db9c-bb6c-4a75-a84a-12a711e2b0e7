using AutoFixture;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Events.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Events.Delete;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Events.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Events.Paginated;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class CyberComponentGroupFixture : IDisposable
{
    // Entity Collections
    public List<CyberComponentGroup> CyberComponentGroups { get; set; }
    public List<CyberComponentGroup> InvalidCyberComponentGroups { get; set; }
    public List<CyberComponent> CyberComponents { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // Commands
    public CreateCyberComponentGroupCommand CreateCyberComponentGroupCommand { get; set; }
    public UpdateCyberComponentGroupCommand UpdateCyberComponentGroupCommand { get; set; }
    public DeleteCyberComponentGroupCommand DeleteCyberComponentGroupCommand { get; set; }

    // Queries
    public GetCyberComponentGroupDetailQuery GetCyberComponentGroupDetailQuery { get; set; }
    public GetCyberComponentGroupListQuery GetCyberComponentGroupListQuery { get; set; }
    public GetCyberComponentGroupNameUniqueQuery GetCyberComponentGroupNameUniqueQuery { get; set; }
    public GetCyberComponentGroupPaginatedListQuery GetCyberComponentGroupPaginatedListQuery { get; set; }

    // Events
    public CyberComponentGroupCreatedEvent CyberComponentGroupCreatedEvent { get; set; }
    public CyberComponentGroupUpdatedEvent CyberComponentGroupUpdatedEvent { get; set; }
    public CyberComponentGroupDeletedEvent CyberComponentGroupDeletedEvent { get; set; }
    public CyberComponentGroupPaginatedEvent CyberComponentGroupPaginatedEvent { get; set; }

    // View Models
    public List<CyberComponentGroupListVm> CyberComponentGroupListVms { get; set; }
    public CyberComponentGroupDetailVm CyberComponentGroupDetailVm { get; set; }

    // Mapper
    public IMapper Mapper { get; set; }

    public CyberComponentGroupFixture()
    {
        // Initialize manual test data with known values for reliable testing
        CyberComponentGroups = new List<CyberComponentGroup>
        {
            new CyberComponentGroup
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Test Cyber Component Group 01",
                ComponentProperties = "[{\"id\":\"comp1\",\"name\":\"Firewall01\"},{\"id\":\"comp2\",\"name\":\"IDS01\"}]",
                SiteId = Guid.NewGuid().ToString(),
                SiteName = "Test Site 01",
                IsActive = true,
                Id = 1,
                CreatedDate = DateTime.UtcNow.AddDays(-1),
                LastModifiedDate = DateTime.UtcNow
            },
            new CyberComponentGroup
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Test Cyber Component Group 02",
                ComponentProperties = "[{\"id\":\"comp3\",\"name\":\"Antivirus01\"}]",
                SiteId = Guid.NewGuid().ToString(),
                SiteName = "Test Site 02",
                IsActive = true,
                Id = 2,
                CreatedDate = DateTime.UtcNow.AddDays(-2),
                LastModifiedDate = DateTime.UtcNow.AddHours(-1)
            }
        };

        // Initialize invalid test data for error scenarios
        InvalidCyberComponentGroups = new List<CyberComponentGroup>
        {
            new CyberComponentGroup
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = null, // Invalid - null name
                ComponentProperties = "[]",
                SiteId = Guid.NewGuid().ToString(),
                SiteName = "Invalid Site",
                IsActive = false, // Inactive
                Id = 999
            }
        };

        // Initialize related components
        CyberComponents = new List<CyberComponent>
        {
            new CyberComponent
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Component in Group 01",
                Type = "Firewall",
                Properties = "{\"version\":\"1.0\",\"config\":\"default\"}",
                SiteId = CyberComponentGroups.First().SiteId,
                SiteName = CyberComponentGroups.First().SiteName,
                Status = "Active",
                IsActive = true,
                Id = 1
            }
        };

        // Initialize UserActivities
        UserActivities = new List<UserActivity>
        {
            new UserActivity
            {
                ReferenceId = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                LoginName = "TestUser01",
                Entity = "CyberComponentGroup",
                Action = "Create CyberComponentGroup",
                ActivityType = "Create",
                ActivityDetails = "Test cyber component group created successfully.",
                RequestUrl = "/api/cybercomponentgroup",
                HostAddress = "127.0.0.1",
                IsActive = true,
                Id = 1,
                CreatedDate = DateTime.UtcNow
            }
        };

        // Initialize Commands
        CreateCyberComponentGroupCommand = new CreateCyberComponentGroupCommand
        {
            GroupName = "New Test Group",
            ComponentProperties = "[{\"id\":\"newcomp1\",\"name\":\"NewFirewall\"}]",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "New Test Site"
        };

        UpdateCyberComponentGroupCommand = new UpdateCyberComponentGroupCommand
        {
            Id = CyberComponentGroups.First().ReferenceId,
            GroupName = "Updated Test Group",
            ComponentProperties = "[{\"id\":\"updatedcomp1\",\"name\":\"UpdatedFirewall\"}]",
            SiteId = CyberComponentGroups.First().SiteId,
            SiteName = "Updated Test Site"
        };

        DeleteCyberComponentGroupCommand = new DeleteCyberComponentGroupCommand
        {
            Id = CyberComponentGroups.First().ReferenceId
        };

        // Initialize Queries
        GetCyberComponentGroupDetailQuery = new GetCyberComponentGroupDetailQuery
        {
            Id = CyberComponentGroups.First().ReferenceId
        };

        GetCyberComponentGroupListQuery = new GetCyberComponentGroupListQuery();

        GetCyberComponentGroupNameUniqueQuery = new GetCyberComponentGroupNameUniqueQuery
        {
            Name = "Test Unique Name",
            Id = "0"
        };

        GetCyberComponentGroupPaginatedListQuery = new GetCyberComponentGroupPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "GroupName",
            SortOrder = "asc"
        };

        // Initialize Events
        CyberComponentGroupCreatedEvent = new CyberComponentGroupCreatedEvent
        {
            Name = "Test Created Group"
        };

        CyberComponentGroupUpdatedEvent = new CyberComponentGroupUpdatedEvent
        {
            Name = "Test Updated Group"
        };

        CyberComponentGroupDeletedEvent = new CyberComponentGroupDeletedEvent
        {
            Name = "Test Deleted Group"
        };

        CyberComponentGroupPaginatedEvent = new CyberComponentGroupPaginatedEvent();

        // Initialize View Models
        CyberComponentGroupListVms = new List<CyberComponentGroupListVm>
        {
            new CyberComponentGroupListVm
            {
                Id = CyberComponentGroups.First().ReferenceId,
                GroupName = CyberComponentGroups.First().GroupName,
                ComponentProperties = CyberComponentGroups.First().ComponentProperties,
                SiteId = CyberComponentGroups.First().SiteId,
                SiteName = CyberComponentGroups.First().SiteName
            }
        };

        CyberComponentGroupDetailVm = new CyberComponentGroupDetailVm
        {
            Id = CyberComponentGroups.First().ReferenceId,
            GroupName = CyberComponentGroups.First().GroupName,
            ComponentProperties = CyberComponentGroups.First().ComponentProperties,
            SiteId = CyberComponentGroups.First().SiteId,
            SiteName = CyberComponentGroups.First().SiteName
        };

        // Configure AutoMapper for CyberComponentGroup mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CyberComponentGroupProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }
    
    public Fixture AutoCyberComponentGroupFixture
    {
        get
        {
            var fixture = new Fixture();
            
            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations for commands
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateCyberComponentGroupCommand>(p => p.GroupName, 100));
            fixture.Customize<CreateCyberComponentGroupCommand>(c => c
                .With(b => b.GroupName, () => $"Test Cyber Component Group {fixture.Create<int>():000}")
                .With(b => b.ComponentProperties, () => $"[{{\"id\":\"comp{fixture.Create<int>():000}\",\"name\":\"Component{fixture.Create<int>():000}\"}}]")
                .With(b => b.SiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SiteName, () => $"Test Site {fixture.Create<int>():00}")
                );

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateCyberComponentGroupCommand>(p => p.GroupName, 100));
            fixture.Customize<UpdateCyberComponentGroupCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.GroupName, () => $"Updated Cyber Component Group {fixture.Create<int>():000}")
                .With(b => b.ComponentProperties, () => $"[{{\"id\":\"updcomp{fixture.Create<int>():000}\",\"name\":\"UpdatedComponent{fixture.Create<int>():000}\"}}]")
                .With(b => b.SiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SiteName, () => $"Updated Test Site {fixture.Create<int>():00}")
                );

            fixture.Customize<DeleteCyberComponentGroupCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            //fixture.Customize<AddComponentToGroupCommand>(c => c
            //    .With(b => b.GroupId, () => Guid.NewGuid().ToString())
            //    .With(b => b.ComponentId, () => Guid.NewGuid().ToString()));

            //fixture.Customize<RemoveComponentFromGroupCommand>(c => c
            //    .With(b => b.GroupId, () => Guid.NewGuid().ToString())
            //    .With(b => b.ComponentId, () => Guid.NewGuid().ToString()));

            // CyberComponentGroup entity customizations
            fixture.Customize<CyberComponentGroup>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.GroupName, () => $"Test Cyber Component Group {fixture.Create<int>():000}")
                .With(b => b.ComponentProperties, () => $"[{{\"id\":\"comp{fixture.Create<int>():000}\",\"name\":\"Component{fixture.Create<int>():000}\"}}]")
                .With(b => b.SiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SiteName, () => $"Test Site {fixture.Create<int>():00}")
                .With(b => b.Id, () => fixture.Create<int>())
                .With(b => b.CreatedDate, () => DateTime.UtcNow.AddDays(-fixture.Create<int>() % 30))
                .With(b => b.LastModifiedDate, () => DateTime.UtcNow.AddHours(-fixture.Create<int>() % 24))
                );

            // CyberComponent entity customizations for group relationships
            fixture.Customize<CyberComponent>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.Name, () => $"Test Component {fixture.Create<int>():000}")
                .With(b => b.Type, () => new[] { "Firewall", "IDS", "IPS", "Antivirus", "SIEM" }[fixture.Create<int>() % 5])
                .With(b => b.Properties, () => $"{{\"version\":\"1.{fixture.Create<int>() % 10}\",\"config\":\"test\"}}")
                .With(b => b.SiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SiteName, () => $"Component Site {fixture.Create<int>():00}")
                .With(b => b.ServerTypeId, () => Guid.NewGuid().ToString())
                .With(b => b.ServerType, () => new[] { "Physical", "Virtual", "Cloud" }[fixture.Create<int>() % 3])
                .With(b => b.Logo, () => $"logo{fixture.Create<int>() % 10}.png")
                .With(b => b.Status, () => new[] { "Active", "Inactive", "Maintenance" }[fixture.Create<int>() % 3])
                .With(b => b.Description, () => $"Test component description {fixture.Create<int>()}")
                .With(b => b.Id, () => fixture.Create<int>()));

            // UserActivity customization for CyberComponentGroup
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "CyberComponentGroup")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test cyber component group activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
