﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Constants;
using MediatR;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Commands;

public class CreateHacmpClusterTests : IClassFixture<HacmpClusterFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;
    private readonly Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;
    private readonly CreateHacmpClusterCommandHandler _handler;

    public CreateHacmpClusterTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.CreateHacmpClusterRepository(_hacmpClusterFixture.HacmpClusters);

        _handler = new CreateHacmpClusterCommandHandler(_hacmpClusterFixture.Mapper, _mockHacmpClusterRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseHacmpClusterCount_When_HacmpClusterCreated()
    {
        await _handler.Handle(_hacmpClusterFixture.CreateHacmpClusterCommand, CancellationToken.None);

        var allHacmpClusters = await _mockHacmpClusterRepository.Object.ListAllAsync();

        allHacmpClusters.Count.ShouldBe(_hacmpClusterFixture.HacmpClusters.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateHacmpClusterResponse_When_HacmpClusterCreated()
    {
        var result = await _handler.Handle(_hacmpClusterFixture.CreateHacmpClusterCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateHacmpClusterResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);

        result.Success.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_hacmpClusterFixture.CreateHacmpClusterCommand, CancellationToken.None);

        _mockHacmpClusterRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.HacmpCluster>()), Times.Once);
    }
}