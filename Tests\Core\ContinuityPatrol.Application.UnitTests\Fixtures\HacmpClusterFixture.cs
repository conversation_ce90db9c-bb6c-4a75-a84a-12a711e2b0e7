using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.Features.HacmpCluster.Events.Create;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class HacmpClusterFixture : IDisposable
{
    public List<HacmpCluster> HacmpClusters { get; set; }
    public CreateHacmpClusterCommand CreateHacmpClusterCommand { get; set; }
    public UpdateHacmpClusterCommand UpdateHacmpClusterCommand { get; set; }
    public HacmpClusterCreatedEvent HacmpClusterCreatedEvent { get; set; }
    public IMapper Mapper { get; private set; }

    public HacmpClusterFixture()
    {
        var fixture = new Fixture();

        HacmpClusters = fixture.CreateMany<HacmpCluster>(5).ToList();

        AddHacmpClusterBusinessLogic(HacmpClusters);

        CreateHacmpClusterCommand = fixture.Create<CreateHacmpClusterCommand>();

        UpdateHacmpClusterCommand = fixture.Create<UpdateHacmpClusterCommand>();

        HacmpClusterCreatedEvent = fixture.Create<HacmpClusterCreatedEvent>();

        var mapperConfig = new MapperConfiguration(c =>
        {
            c.AddProfile<HacmpClusterProfile>();
        });

        Mapper = mapperConfig.CreateMapper();
    }

    private void AddHacmpClusterBusinessLogic(List<HacmpCluster> hacmpClusters)
    {
        foreach (var hacmpCluster in hacmpClusters)
        {
            hacmpCluster.IsActive = true;
            hacmpCluster.CreatedDate = DateTime.UtcNow;
            hacmpCluster.ReferenceId = Guid.NewGuid().ToString();
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
