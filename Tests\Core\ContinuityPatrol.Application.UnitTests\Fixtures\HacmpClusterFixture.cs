using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.Features.HacmpCluster.Events.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Events.Delete;
using ContinuityPatrol.Application.Features.HacmpCluster.Events.PaginatedView;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetList;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class HacmpClusterFixture : IDisposable
{
    public List<HacmpCluster> HacmpClusters { get; set; }
    public CreateHacmpClusterCommand CreateHacmpClusterCommand { get; set; }
    public UpdateHacmpClusterCommand UpdateHacmpClusterCommand { get; set; }
    public HacmpClusterCreatedEvent HacmpClusterCreatedEvent { get; set; }
    public HacmpClusterDeletedEvent HacmpClusterDeletedEvent { get; set; }
    public HacmpClusterPaginatedViewEvent HacmpClusterPaginatedViewEvent { get; set; }
    public GetHacmpClusterDetailQuery GetHacmpClusterDetailQuery { get; set; }
    public GetHacmpClusterListQuery GetHacmpClusterListQuery { get; set; }
    public GetHacmpClusterNameUniqueQuery GetHacmpClusterNameUniqueQuery { get; set; }
    public GetHacmpClusterPaginatedListQuery GetHacmpClusterPaginatedListQuery { get; set; }
    public IMapper Mapper { get; private set; }

    public HacmpClusterFixture()
    {
        var fixture = new Fixture();

        HacmpClusters = fixture.CreateMany<HacmpCluster>(5).ToList();

        AddHacmpClusterBusinessLogic(HacmpClusters);

        CreateHacmpClusterCommand = fixture.Create<CreateHacmpClusterCommand>();

        UpdateHacmpClusterCommand = fixture.Create<UpdateHacmpClusterCommand>();

        HacmpClusterCreatedEvent = fixture.Create<HacmpClusterCreatedEvent>();

        HacmpClusterDeletedEvent = fixture.Create<HacmpClusterDeletedEvent>();

        HacmpClusterPaginatedViewEvent = fixture.Create<HacmpClusterPaginatedViewEvent>();

        GetHacmpClusterDetailQuery = fixture.Create<GetHacmpClusterDetailQuery>();

        GetHacmpClusterListQuery = new GetHacmpClusterListQuery();

        GetHacmpClusterNameUniqueQuery = fixture.Create<GetHacmpClusterNameUniqueQuery>();

        GetHacmpClusterPaginatedListQuery = new GetHacmpClusterPaginatedListQuery();

        var mapperConfig = new MapperConfiguration(c =>
        {
            c.AddProfile<HacmpClusterProfile>();
        });

        Mapper = mapperConfig.CreateMapper();
    }

    private void AddHacmpClusterBusinessLogic(List<HacmpCluster> hacmpClusters)
    {
        foreach (var hacmpCluster in hacmpClusters)
        {
            hacmpCluster.IsActive = true;
            hacmpCluster.CreatedDate = DateTime.UtcNow;
            hacmpCluster.ReferenceId = Guid.NewGuid().ToString();
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
