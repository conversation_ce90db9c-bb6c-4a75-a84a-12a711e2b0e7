﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.WorkflowPermissionModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetPaginatedList;

public class GetWorkflowPermissionPaginatedListQueryHandler : IRequestHandler<GetWorkflowPermissionPaginatedListQuery,
    PaginatedResult<WorkflowPermissionListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowPermissionRepository _workflowPermissionRepository;


    public GetWorkflowPermissionPaginatedListQueryHandler(IMapper mapper,
        IWorkflowPermissionRepository workflowPermissionRepository)
    {
        _mapper = mapper;
        _workflowPermissionRepository = workflowPermissionRepository;
    }

    public async Task<PaginatedResult<WorkflowPermissionListVm>> Handle(GetWorkflowPermissionPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new WorkflowPermissionFilterSpecification(request.SearchString);

        var queryable = await _workflowPermissionRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var workflowProfileList = _mapper.Map<PaginatedResult<WorkflowPermissionListVm>>(queryable);

        return workflowProfileList;
    }
}