using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Validators;

public class CreateGlobalVariableValidatorTests : IClassFixture<GlobalVariableFixture>
{
    private readonly Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;

    private readonly GlobalVariableFixture _globalVariableFixture;

    public CreateGlobalVariableValidatorTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        var globalVariables = new Fixture().Create<List<Domain.Entities.GlobalVariable>>();

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.CreateGlobalVariableRepository(globalVariables);
    }

    [Theory]
    [AutoGlobalVariableData]
    public async Task Verify_Create_ValidCommand_ShouldPass(CreateGlobalVariableCommand createGlobalVariableCommand)
    {
        var validator = new CreateGlobalVariableCommandValidator(_mockGlobalVariableRepository.Object);

        createGlobalVariableCommand.VariableName = "TestVariable";
        createGlobalVariableCommand.VariableValue = "TestValue";
        createGlobalVariableCommand.Type = "TestType";

        var validateResult = await validator.ValidateAsync(createGlobalVariableCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeTrue();
    }
}
