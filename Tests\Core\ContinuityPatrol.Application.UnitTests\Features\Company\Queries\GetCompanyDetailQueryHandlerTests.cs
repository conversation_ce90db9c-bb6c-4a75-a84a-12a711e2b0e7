﻿using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Company.Queries;

public class GetCompanyDetailQueryHandlerTests : IClassFixture<CompanyFixture>
{
    private readonly CompanyFixture _companyFixture;
    private readonly Mock<ICompanyRepository> _mockCompanyRepository;
    private readonly GetCompanyDetailQueryHandler _handler;
    private readonly GetCompanyDetailQueryHandler _invalidHandler;

    public GetCompanyDetailQueryHandlerTests(CompanyFixture companyFixture)
    {
        _companyFixture = companyFixture;

        _mockCompanyRepository = CompanyRepositoryMocks.GetCompanyRepository(_companyFixture.Companies);

        _handler = new GetCompanyDetailQueryHandler(_companyFixture.Mapper, _mockCompanyRepository.Object);

        var mockInvalidCompanyRepository = CompanyRepositoryMocks.GetCompanyRepository(_companyFixture.InvalidCompanies);

        _invalidHandler = new GetCompanyDetailQueryHandler(_companyFixture.Mapper, mockInvalidCompanyRepository.Object);

        _companyFixture.Companies[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _companyFixture.Companies[1].ParentId = _companyFixture.Companies[0].ReferenceId;

    }

    [Fact]
    public async Task Handle_Return_CompanyDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetCompanyDetailQuery { Id = _companyFixture.Companies[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<CompanyDetailVm>();
        result.Id.ShouldBeGreaterThan(0.ToString());
        result.Name.ShouldNotBeEmpty();
        result.DisplayName.ShouldNotBeEmpty();
		result.ParentName.ShouldBe("NA");
        result.ParentId.ShouldBe(0.ToString());
        result.WebAddress.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidCompanyId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<InvalidArgumentException>(() => _handler.Handle(new GetCompanyDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("Input 'Id' is not valid format.");
    }

    [Fact]
    public async Task Handle_Return_ParentCompanyName_When_FetchChildCompany()
    {
        var companyId = _companyFixture.Companies.FirstOrDefault(x => x.IsParent == false)!.ReferenceId = Guid.NewGuid().ToString();

        var result = await _handler.Handle(new GetCompanyDetailQuery { Id = companyId }, CancellationToken.None);

        result.ShouldBeOfType<CompanyDetailVm>();

        result.ParentName.ShouldBe(_companyFixture.Companies[0].DisplayName);

        result.ParentId.ShouldBeGreaterThan(0.ToString());
    }

    [Fact]
    public async Task Handle_Return_ParentCompanyNameIsNA_When_FetchParentCompany()
    {
        var result = await _handler.Handle(new GetCompanyDetailQuery { Id = _companyFixture.Companies.FirstOrDefault(x => x.IsParent)!.ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<CompanyDetailVm>();

        result.ParentName.ShouldBe("NA");

        result.ParentId.ShouldBe(0.ToString());
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OneTime_When_ParentCompanyId()
    {
        await _handler.Handle(new GetCompanyDetailQuery { Id = _companyFixture.Companies.FirstOrDefault(x => x.IsParent)!.ReferenceId }, CancellationToken.None);

        _mockCompanyRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdIdAsync_TwoTime_When_ChildCompanyId()
    {
        var companyId = _companyFixture.Companies.FirstOrDefault(x => x.IsParent == false)!.ReferenceId = Guid.NewGuid().ToString();

        await _handler.Handle(new GetCompanyDetailQuery { Id = companyId }, CancellationToken.None);

        _mockCompanyRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Exactly(2));
    }

    //[Fact]
    //public async Task Handle_Throw_InvalidArgumentException_When_ParentIdIsZeroForChild()
    //{
    //    var exceptionDetails = await Assert.ThrowsAsync<InvalidArgumentException>(() => _invalidHandler.Handle(new GetCompanyDetailQuery { Id = _companyFixture.InvalidCompanies[2].ReferenceId }, CancellationToken.None));

    //    exceptionDetails.Message.ShouldContain("Parent Company Id cannot be Zero or Negative value for child company.");
    //}

    [Fact]
    public async Task Handle_Return_EmptyParentCompanyName_When_InvalidParentId()
    {
        _companyFixture.InvalidCompanies[0].ReferenceId = Guid.NewGuid().ToString();

        var result = await _invalidHandler.Handle(
            new GetCompanyDetailQuery { Id = _companyFixture.InvalidCompanies[0].ReferenceId }, CancellationToken.None);

        result.ParentName.ShouldBe("NA");
    }

    [Fact]
    public async Task Handle_Return_EmptyLogoName_When_CompanyLogoIsEmpty()
    {
        _companyFixture.InvalidCompanies[4].ReferenceId = Guid.NewGuid().ToString();

        var result = await _invalidHandler.Handle(new GetCompanyDetailQuery { Id = _companyFixture.InvalidCompanies[4].ReferenceId }, CancellationToken.None);

        result.CompanyLogo.ShouldBeEmpty();

        result.LogoName.ShouldBeEmpty();
    }

    [Fact]
    public async Task Handle_Return_LogoName_When_CompanyLogoIsNotEmpty()
    {
        var result = await _handler.Handle(new GetCompanyDetailQuery { Id = _companyFixture.Companies[0].ReferenceId }, CancellationToken.None);

        result.CompanyLogo.ShouldNotBeEmpty();

        result.LogoName.ShouldNotBeEmpty();
    }
}