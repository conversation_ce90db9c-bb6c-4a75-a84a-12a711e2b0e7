﻿namespace ContinuityPatrol.Application.UnitTests;

public static class CommonConstants
{
    public static string CreatedSuccessfully = "has been created successfully";

    public static string UpdatedSuccessfully = "has been updated successfully";

    public static string DeletedSuccessfully = "has been deleted successfully";

    public static string StateMonitorStatus = "State Monitor Status created Successfully.";

    public static string StateMonitorStatuses = "State Monitor Status Updated Successfully.";

    public static string StateMonitorLog = "State Monitor Log created Successfully.";

    public static string StateMonitorLogs = "State Monitor Log updated Successfully.";

    // Configuration

    public static string PasswordUpdatedSuccessfully = "Password changed successfully.";

    public static string InvalidOldPassword = "Old Password is not matched.";

    public static string PasswordUnique = "A same Password already exists.";

    public static string InvalidConfirmPassword = "Password and Confirm Password does not matched.";

    public static string BadRequestException = "Password contains Username";

    // Admin

    public static string CreatedActiveSuccess = "has been created successfully and Node is in 'Active' state.";

    public static string CreatedInActiveSuccess = "LoadBalancer '0' has been created successfully";

    public static string AddedSuccessfully = "Added successfully";

    public static string UpgradedSuccessfully = "Upgraded Successfully.";

    public static string UpdatedActiveSuccess = "has been updated successfully and Node is in 'Active' state.";

    public static string UpdatedInActiveSuccess = "has been updated successfully and Node is in 'InActive' state.";

    // Orchestration

    public static string AttachedSuccessfully = "InfraObject is attached successfully.";

    public static string DeAttachedSuccessfully = "InfraObject is de-attached successfully.";

}

public static class ValidatorConstants
{
    #region Configuration

    public static class AccessManager
    {
        public static string AccessManagerRoleNameRequired =
           "Role Name is Required.";

        public static string AccessManagerRoleNameNotNullRequired =
           "'Role Name' must not be empty.";

        public static string AccessManagerRoleNameRangeRequired =
           "Role Name should contain between 3 to 30 characters.";

        public static string AccessManagerRoleNameValidRequired =
               "Please Enter Valid Role Name.";
    }

    public static class Company
    {
        public static string CompanyConfigurationNotInProperFormat =
            "A Parent/Child company must configure with proper parameter.";

        public static string CompanyNameRequired =
            "Name is required.";

        public static string CompanyWebAddress =
            "Enter the Valid WebAddress";

        public static string CompanyNameNotEmpty =
            "'Name' must not be empty.";

        public static string CompanyNameValid =
            "Please Enter Valid Name";

        public static string CompanyLogo =
            "Please Enter Valid Company Logo";

        public static string CompanyNameAndDisplayNameExists =
            "A company with the same name and display name already exists.";

        public static string CompanyNameRange =
            "Name should contain between 3 to 100 characters.";

        public static string DisplayNameRequired =
            "Display Name is required.";

        public static string DisplayNameNotEmpty =
            "'Display Name' must not be empty.";

        public static string CompanyDisplayNameRange =
            "Display Name should contain between 3 to 15 characters.";

        public static string CompanyDisplayNameValid =
            "Please Enter Valid Display Name";

        public static string CompanyNameMinimumLength =
            "Name should contain minimum 3 characters.";

        public static string ValidCompanyWebAddress =
            "Enter the Valid WebAddress";

        public static string WebAddressRequired =
            "Web Address is required.";

        public static string WebAddressRequiredNotNull =
            "'Web Address' must not be empty.";

        //public static string CompanyLogoMatches =
        //    "Please Enter Valid Logo Matches";
    }

    public static class User
    {
        public static string UserLoginNameRequired =
            "Login Name is Required.";

        public static string UserLoginNameValid =
            "Please Enter Valid LoginName.";

        public static string UserLoginNameRange =
            "Login Name should contain between 3 to 30 characters.";

        public static string UserLoginNameFormatInvalid =
            "Login Name format is invalid.";

        public static string UserLoginNameRangeRequired =
            "Login Name should contain between 3 to 200 characters.";

        public static string UserLoginPasswordFormatInvalid =
            "Login Password format is invalid.";

        public static string UserLoginPasswordRequired =
            "Login Password is Required.";

        public static string UserLoginPasswordNotEmpty =
            "'Login Password' must not be empty.";

        public static string UserCompanyNameRequired =
            "Select Company Name.";

        public static string UserCompanyNameNotEmpty =
            "'Company Name' must not be empty.";

        public static string UserLoginTypeRequired =
            "Select Login Type.";

        public static string UserLoginTypeInvalid =
            "Login Type is invalid.";

        public static string UserLoginTypeNotEmpty =
            "'Login Type' must not be empty.";

        public static string UserLoginTypeNotNullRequired =
            "LoginType cannot be null.";

        public static string UserLoginTypeIsRequired =
            "Login Type is Required.";

        public static string UserRoleRequired =
            "Role is Required.";

        public static string UserSessionTimeoutRequired =
            "Session Timeout is Required.";

        public static string UserInfoUserNameRequired =
            "User Name is Required.";

        public static string UserInfoUserNameNotEmpty =
            "'User Info Command User Name' must not be empty.";

        public static string UserInfoUserNameRange =
            "User Name should contain between 3 to 100 characters.";

        public static string UserInfoUserNameValid =
            "Please Enter Valid User Name";

        public static string UserInfoUserEmailRequired =
            "Email is required.";

        public static string UserInfoUserEmailValid =
            "Please Enter Valid Email.";

        public static string UserInfoEmailRequired =
            "Email is Required.";

        public static string UserInfoEmailNotEmpty =
            "'User Info Command Email' must not be empty.";

        public static string UserInfoEmailRange =
            "Email should contain between 6 to 30 characters.";

        public static string UserRoleLoginUserNameValid =
            "The user Role and Login User Role are not matched.";

        public static string UserInfoEmailValid =
            "Please enter a valid email.";
    }

    public static class UpdatePassword
    {
        public static string UpdatePasswordLoginNameRequired =
            "LoginName is required.";

        public static string UpdatePasswordLoginNameNotEmpty =
            "'Login Name' must not be empty.";

        public static string UpdatePasswordOldPasswordRequired =
            "Old Password is required.";

        public static string UpdatePasswordOldPasswordNotEmpty =
            "'Old Password' must not be empty.";

        public static string UpdatePasswordNewPasswordRequired =
            "New Password is required.";

        public static string UpdatePasswordNewPasswordNotEmpty =
            "'New Password' must not be empty.";

        public static string UpdatePasswordConfirmPasswordRequired =
            "Confirm Password is required.";

        public static string UpdatePasswordConfirmPasswordNotEmpty =
            "'Confirm Password' must not be empty.";
    }

    public static class Site
    {
        public static string SiteNameRequired =
            "Name is required";

        public static string SiteNameRange =
            "Name should contain between 3 to 100 characters";

        public static string SiteNameNotEmpty =
            "'Name' must not be empty.";

        public static string SiteNameUnique =
            "A same name already exists";

        public static string SiteNameValid =
            "Please Enter Valid Name";

        public static string SiteLocationRequired =
            "Location is required";

        public static string SiteLocationNotEmpty =
            "'Location' must not be empty.";

        public static string SiteLocationValid =
            "Please Enter Valid Location";

        public static string SiteTypeRequired =
            "Select Site Type";

        public static string SiteTypeNotEmpty =
            "'Type' must not be empty.";

        public static string SiteTypeValid =
            "Please Enter Valid Type";

        public static string SiteCompanyNameRequired =
            "Select Company Name.";

        public static string SiteCompanyNameNotEmpty =
            "'Company Name' must not be empty.";

        public static string SiteCompanyNameValid =
            "Please Enter Valid Company Name";

        public static string SitePlatformTypeValid =
            "Please Enter Valid Platform Type";

        public static string SitePlatformTypeRequired =
            "Select Site Platform Type";
    }

    public static class SiteType
    {
        public static string SiteTypeNameRequired =
            "Name is required";

        public static string SiteTypeTypeRequired =
               "Type is required";

        public static string SiteTypeNotEmpty =
              "'Type' must not be empty.";

        public static string SiteTypeRangeRequired =
              "Type should contain between 3 to 100 characters";

        public static string SiteTypeValidRequired =
               "Please Enter Valid Type";

        public static string SiteTypeValidNameRequired =
            "Please Enter Valid Name";

        public static string SiteTypeIconRequired =
              "Icon is required";

        public static string SiteTypeIconNotEmpty =
             "'Icon' must not be empty.";

        public static string SiteTypeUnique =
                "A same name already exists";
    }

    public static class BusinessService
    {
        public static string BusinessServiceNameRequired =
            "Name is required.";

        public static string BusinessServiceNameNotEmpty =
            "'Name' must not be empty.";

        public static string BusinessServiceDescriptionContains =
            "Operational Service Description contains invalid characters.";

        public static string BusinessServiceNameRange =
            "Name should contain between 3 to 100 characters.";

        public static string BusinessServiceNameValid =
            "Please Enter Valid Name.";

        public static string BusinessServiceNameUnique =
            "A same name already exists.";

        public static string BusinessServiceDescriptionRequired =
            "Business Service Description is required";

        public static string BusinessServiceDescriptionNotEmpty =
            "'Description' must not be empty.";

        public static string BusinessServiceDescriptionRange =
            "Business Service Description Maximum 250 characters.";

        public static string OperationalServiceDescriptionRange =
            "Operational Service Description Maximum 250 characters.";

        public static string BusinessServiceInfraObjectDescriptionRange =
            "InfraObject Description Maximum 250 characters.";

        public static string BusinessServiceCompanyNameRequired =
            "Select Company Name.";

        public static string BusinessServiceCompanyNameNotNullRequired =
            "'Company Name' must not be empty.";

        public static string BusinessServiceCompanyNameValid =
            "Please Enter Valid Company Name";

        public static string BusinessServiceCompanyIdRequired =
            "Select Company Id.";

        public static string BusinessServiceCompanyIdNotEmpty =
            "'Company Id' must not be empty.";

        public static string BusinessServicePriorityRequired =
            "Select Priority.";
    }

    public static class BusinessServiceAvailability
    {
        public static string BusinessServiceAvailabilityNameValid =
            "Please Enter Valid Name.";
    }

    public static class BusinessFunction
    {
        public static string BusinessFunctionNameRangeRequired =
            "Name should contain between 3 to 100 characters.";

        public static string BusinessFunctionNameRequired =
            "Name is required.";

        public static string BusinessFunctionNameValidRequired =
            "Please Enter Valid Name.";
        
        public static string BusinessFunctionBusinessServiceNameValid =
            "Please Enter Valid Business Service Name.";

        public static string BusinessFunctionDescriptionContains =
            "Operational Function Description contains invalid characters.";

        public static string BusinessFunctionDescriptionMaximum =
            "Operational Function Description Maximum 250 characters.";

        public static string BusinessFunctionOperationalServiceRequired =
            "Please Enter Valid Operational Service.";

        public static string BusinessFunctionNotNullRequired =
            "'Name' must not be empty.";

        public static string BusinessFunctionNameUnique =
            "A same name already exists";

        public static string BusinessFunctionDescriptionRequired =
            "Business Service Description is required";

        public static string BusinessFunctionDescriptionNotNullRequired =
            "'Description' must not be empty.";

        public static string BusinessFunctionDescriptionRangeRequired =
            "Business Service Description Maximum 250 characters.";

        public static string BusinessFunctionCriticalityLevelRequired =
            "Select Criticality Level.";

        public static string BusinessFunctionCriticalityLevelNotNullRequired =
            "'Criticality Level' must not be empty.";

        public static string BusinessFunctionCriticalityLevelAlphabetsOnlyRequired =
            "Only alphabets are allowed.";

        public static string BusinessFunctionBusinessServiceNameRequired =
            "Select Operational Service.";

        public static string BusinessFunctionBusinessServiceNameNotNullRequired =
            "'Business Service Name' must not be empty.";

        public static string BusinessFunctionConfiguredRtoRequired =
            "RTO is required.";

        public static string BusinessFunctionConfiguredRtoNumbersOnlyRequired =
            "Please enter number only.";

        public static string BusinessFunctionConfiguredRtoNotNullRequired =
            "'Configured RTO' must not be empty.";

        public static string BusinessFunctionConfiguredRpoRequired =
            "RPO is required.";

        public static string BusinessFunctionConfiguredRpoNumbersOnlyRequired =
            "Please enter number only.";

        public static string BusinessFunctionConfiguredRpoNotNullRequired =
            "'Configured RPO' must not be empty.";

        public static string BusinessFunctionConfiguredMaoRequired =
            "MAO is required.";

        public static string BusinessFunctionConfiguredMaoNumbersOnlyRequired =
            "Please enter number only.";

        public static string BusinessFunctionConfiguredMaoNotNullRequired =
            "'Configured MAO' must not be empty.";
    }

    public static class SingleSignOn
    {
        public static string SingleSignOnProfileNameRange =
            "Profile Name should contain between 3 to 100 characters.";

        public static string SingleSignOnProfileNameRequired =
            "Profile Name is required.";

        public static string SingleSignOnNameUnique =
            "A same name already exists.";

        public static string SelectSingleSignOnType =
            "Select Sign On Type";

        public static string SelectSingleSignOnTypeNotNull =
            "'Sign On Type' must not be empty.";

        public static string SingleSignOnProfileNameNotNullRequired =
            "'Profile Name' must not be empty.";

        public static string SingleSignOnPropertyRequired =
            "Enter Properties";

        public static string SingleSignOnPropertyNotNullRequired =
            "'Properties' must not be empty.";
        public static string SingleSignOnProfileNameValidator =
            "Please Enter Valid Profile Name";

    }

    public static class Database
    {
        public static string DatabaseLicenseKeyRequired =
            "Select License Key.";

        public static string DatabaseLicenseKeyNotNullRequired =
            "'License Key' must not be empty.";

        public static string DatabaseCountLimit =
        "License is in 'InActive' state";

        public static string DatabaseNameRequired =
            "Name is required.";

        public static string DatabaseNameNotEmpty =
            "'Name' must not be empty.";

        public static string DatabaseNameRange =
            "Name should contain between 3 to 100 characters.";

        public static string DatabaseLicenseCount =
            "Database count Reached maximum limit.";

        public static string DatabaseNameValid =
            "Please Enter Valid Name";

        public static string DatabaseNameUnique =
            "A same name already exists.";

        public static string DatabaseTypeRequired =
            "Select Database Type.";

        public static string DatabaseTypeNotEmpty =
            "'Database Type' must not be empty.";

        public static string DatabaseTypeValid =
            "Please Enter Valid Database Type";

        public static string DatabaseServerNameValid =
            "Please Enter Valid Server Name";

        public static string DatabaseServerNameRange =
            "Server Name should contain between 3 to 100 characters.";

        public static string DatabaseServerNameRequired =
            "Select Server Name.";

        public static string DatabaseServerNameNotEmpty =
            "'Server Name' must not be empty.";

        public static string DatabasePropertiesRequired =
            "Properties is required.";

        public static string DatabasePropertiesNotEmpty =
            "'Properties' must not be empty.";
    }

    public static class Replication
    {
        public static string ReplicationNameRangeRequired =
            "Name should contain between 3 to 100 characters.";

        public static string ReplicationNameRequired =
            "Name is required.";

        public static string ReplicationNameNotNullRequired =
            "'Name' must not be empty.";

        public static string ReplicationNameUnique =
            "A same name already exists.";

        public static string ReplicationNameValidRequired =
            "Please Enter Valid Name";

        public static string ReplicationTypeRequired =
            "Select Type.";

        public static string ReplicationTypeNotNullRequired =
            "'Type' must not be empty.";

        public static string ReplicationSiteNameRequired =
            "Select Site Name.";

        public static string ReplicationSiteNameNotNullRequired =
            "'Site Name' must not be empty.";

        public static string ReplicationPropertiesRequired =
            "Properties is required.";

        public static string ReplicationPropertiesNotNullRequired =
            "'Properties' must not be empty.";

        public static string ReplicationNameValidator =
            "Please Enter Valid Name";

        public static string ReplicationLicenseKeyRequired =
            "Select License Key.";

        public static string ReplicationOperationalServiceRequired =
            "Please Enter Valid Operational Service";

        public static string ReplicationSiteNameValidator =
            "Please Enter Valid Site Name";

        public static string ReplicationLicenseKeyNotNullRequired =
            "'License Key' must not be empty.";

        public static string ReplicationLicenseCount =
            "License is in 'InActive' state";

        public static string ReplicationCountLimit =
            "Replication count Reached maximum limit.";
    }

    public static class Server
    {
        public static string ServerNameRangeRequired =
            "Name should contain between 3 to 100 characters.";

        public static string ServerNameRequired =
            "Name is required.";

        public static string ServerNameNotNullRequired =
            "'Name' must not be empty.";

        public static string ServerNameValidRequired =
            "Please Enter Valid Name";

        public static string ServerNameUnique =
            "A same Name already exists.";

        public static string ServerSiteNameRequired =
            "Select Site Name.";

        public static string ServerSiteNameNotNullRequired =
            "'Site Name' must not be empty.";

        public static string ServerTypeRequired =
            "Select Server Type.";

        public static string ServerTypeNotNullRequired =
            "'Server Type' must not be empty.";

        public static string ServerOsTypeRequired =
            "Select OS Type.";

        public static string ServerOsTypeNotNullRequired =
            "'OS Type' must not be empty.";

        public static string ServerRoleTypeRequired =
            "Select Role Type.";

        public static string ServerRoleTypeNotNullRequired =
            "'Role Type' must not be empty.";

        public static string ServerPropertiesRequired =
            "Properties is required.";

        public static string ServerPropertiesNotNullRequired =
            "'Properties' must not be empty.";

        public static string ServerLicenseKeyRequired =
            "Select the License Key.";

        public static string ServerLicenseKeyNotNullRequired =
            "'License Key' must not be empty.";

        public static string ServerCountLimit =
            "Server count reached maximum limit.";
    }

    public static class ServerType
    {
        public static string ServerTypeRequired =
            "Name is required";

        public static string ServerTypeNotNullRequired =
            "'Name' must not be empty.";

        public static string ServerTypeRangeRequired =
            "Name should contain between 3 to 30 characters.";

        public static string ServerTypeValidRequired =
            "Please Enter Valid Name";
    }

    public static class ComponentType
    {
        public static string ComponentTypeRangeRequired =
            "Name should contain between 3 to 30 characters.";

        public static string ComponentTypeRequired =
            "Name is required";

        public static string ComponentTypeNotNullRequired =
            "'Name' must not be empty.";

        public static string ComponentTypeValidRequired =
            "Please Enter Valid Name";

        public static string ComponentTypeUnique =
            "A same Name already exists.";
    }

    public static class CredentialProfile
    {
        public static string CredentialProfileNameRange =
            "Name should contain between 3 to 30 characters.";

        public static string CredentialProfileNameRequired =
            "Name is required.";

        public static string CredentialProfileNameNotEmpty =
            "'Name' must not be empty.";

        public static string CredentialProfileNameValid =
            "Please Enter Valid Name";

        public static string CredentialProfileTypeRequired =
            "Select Credential Type.";

        public static string CredentialProfileTypeNotNullRequired =
            "'Credential Type' must not be empty.";

        public static string CredentialProfileNameUnique =
            "A same name already exists.";
    }

    public static class Node
    {
        public static string NodeNameRange =
            "Name should contain between 3 to 30 characters.";

        public static string NodeNameRequired =
            "Name is required.";

        public static string ServerNameValid =
            "Please Enter Valid Server Name";

        public static string NodeNameUnique =
            "A same name already exists.";

        public static string NodeNameNotNullRequired =
            "'Name' must not be empty.";

        public static string SelectNodeServerNameNotNullRequired =
            "'Server Name' must not be empty.";

        public static string SelectNodeServerNameRequired =
            "Server Name is required.";

        public static string NodePasswordRequired =
            "Password is required";

        public static string NodePasswordNotNullRequired =
            "'Password' must not be empty.";

        public static string NodePasswordMinimumRange =
            "Please Enter password minimum 8 and maximum 10 characters, at least one uppercase letter, one lowercase letter, one number and one special character:";

        public static string NodePasswordMaximumRange =
            "Please Enter password minimum 8 and maximum 10 characters, at least one uppercase letter, one lowercase letter, one number and one special character:";

        public static string UserNameRequired =
            "User Name is required";

        public static string UserNameNotNullRequired =
            "'User Name' must not be empty.";

        public static string UserNameRange =
            "User Name should contain between 3 to 50 characters.";

        public static string OracleSidRequired =
            "Oracle SID is required";

        public static string OracleSidNotNullRequired =
            "'Oracle SID' must not be empty.";

        public static string InstanceNameNotNullRequired =
            "'Instance Name' must not be empty.";

        public static string InstanceNameRequired =
            "Instance Name is required";

        public static string PortRange =
            "Port is Must 3 to 5 Digit only.";

        public static string PortWithEmpty =
            "Port is required.";

        public static string PortNotNullRequired =
            "'Port' must not be empty.";

        public static string PortNumberValidate =
            "Valid numbers only.";
        public static string NodeNameValidate =
            "Please Enter Valid Name";

    }

    public static class InfraObject
    {
        public static string InfraObjectNameRequired =
            "Name is required.";

        public static string InfraObjectNameNotNullRequired =
            "'Name' must not be empty.";

        public static string InfraObjectNameRangeRequired =
            "Name should contain between 3 to 100 characters.";

        public static string InfraObjectDescriptionRequired =
            "InfraObject Description is required";

        public static string InfraObjectDescriptionNotNullRequired =
            "'Description' must not be empty.";

        public static string InfraObjectDescriptionRangeRequired =
            "InfraObject Description Maximum 250 characters.";

        public static string InfraObjectDescriptionContainsInvalid =
            "InfraObject Description contains invalid characters.";

        public static string InfraObjectBusinessFunctionNameRequired =
            "Select Business Function Name.";

        public static string InfraObjectBusinessFunctionNameNotNullRequired =
            "'Business Function Name' must not be empty.";

        public static string InfraObjectBusinessServiceNameRequired =
            "Select Business Service Name.";

        public static string InfraObjectOperationalServiceRequired =
            "Select Operational Service.";

        public static string InfraObjectOperationalFunctionNameValid =
            "Select Operational Function.";

        public static string InfraObjectBusinessFunctionNameValid =
            "Please Enter Valid Business Function Name.";

        public static string InfraObjectBusinessServiceNameValid =
            "Please Enter Valid Business Service Name.";

        public static string InfraObjectOperationalFunctionRequired =
            "Please Enter Valid Operational Function.";

        public static string InfraObjectBusinessServiceNameNotNullRequired =
            "'Business Service Name' must not be empty.";

        public static string InfraObjectNameUniqueRequired =
            "A same name already exists.";
        public static string InfraObjectNameValid =
            "Please Enter Valid Name";
    }

    public static class Setting
    {

        public static string SettingSKeyRequired =
            "S Key is Required.";

        public static string SettingSKeyNotNullRequired =
            "'S Key' must not be empty.";

        public static string SettingSValueRequired =
            "S Value is Required.";

        public static string SettingSValueNotNullRequired =
            "'S Value' must not be empty.";

        public static string SettingLoginUserIdRequired =
            "Select Login User Id.";

        public static string SettingLoginUserIdNotNullRequired =
            "'Login User Id' must not be empty.";

    }

    public static class GlobalSetting
    {
        public static string GlobalSettingKeyRequired =
            "Global Setting Key is Required.";

        public static string GlobalSettingKeyNotEmpty =
            "'Global Setting Key' must not be empty.";

        public static string GlobalSettingKeyValid =
            "Enter valid {propertyName}";

        public static string GlobalSettingValueNotEmpty =
            "'Global Setting Value' must not be empty.";

        public static string GlobalSettingValueRequired =
            "Global Setting Value is Required.";

        public static string GlobalSettingValueInValid =
            "Invalid Global Setting Value";
    }

    public static class UserRole
    {
        public static string UserRoleRequired =
            "Role is Required.";

        public static string UserRoleNotNullRequired =
            "'Role' must not be empty.";

        public static string UserRoleRangeRequired =
            "Role should contain between 3 to 100 characters.";

        public static string UserRoleValidRequired =
            "Please Enter Valid Role.";

        public static string UserRoleNameExistRequired =
            "A same name already exists.";
    }

    //public static class Job
    //{
    //    public static string JobNameRequired =
    //        "Name is required.";

    //    public static string JobNameNotNullRequired =
    //      "'Name' must not be empty.";

    //    public static string JobNameRangeRequired =
    //       "Name should contain between 3 to 100 characters.";

    //    public static string JobNameValidRequired =
    //       "Please Enter Valid Name";

    //    public static string JobInfraObjectNameRequired =
    //      "Select Infra Object Name.";

    //    public static string JobInfraObjectNameNotNullRequired =
    //     "'Infra Object Name' must not be empty.";

    //    public static string JobInfraObjectNameValidRequired =
    //       "Please Enter Valid Infra Object Name";

    //    public static string JobWorkflowNameRequired =
    //     "Select Workflow Name.";

    //    public static string JobWorkflowNameNotNullRequired =
    //    "'Workflow Name' must not be empty.";

    //    public static string JobWorkflowNameValidRequired =
    //       "Please Enter Valid Workflow Name";

    //    public static string JobCronExpressionRequired =
    //       "Cron Expression is required.";

    //    public static string JobCronExpressionNotNullRequired =
    //     "'Cron Expression' must not be empty.";
    //}

    public static class DataSet
    {
        public static string DataSetNameRequired =
          "Data Set Name is Required.";

        public static string DataSetNameNotNullRequired =
          "'Data Set Name' must not be empty.";

        public static string DataSetNameRangeRequired =
          "Data Set Name should contain between 3 to 100 characters.";

        public static string DataSetNameValidRequired =
          "Please Enter Valid Data Set Name.";
    }

    public static class DataSetColumns
    {
        public static string DataSetColumnsTableNameRequired =
         "Table Name is Required.";

        public static string DataSetColumnsTableNameNotNullRequired =
          "'Table Name' must not be empty.";

        public static string DataSetColumnsTableNameRangeRequired =
          "Table Name should contain between 3 to 100 characters.";

        public static string DataSetColumnsTableNameValidRequired =
          "Please Enter Valid Table Name.";
    }

    public static class Report
    {
        public static string ReportNameRequired =
         "Name is Required.";

        public static string ReportNameNotNullRequired =
         "'Name' must not be empty.";

        public static string ReportNameRangeRequired =
         "Name should contain between 3 to 100 characters.";

        public static string ReportNameValidRequired =
         "Please Enter Valid Name.";

        public static string ReportNameUnique =
         "A same name already exists.";
    }

    public static class TableAccess
    {
        public static string TableAccessTableNameRequired =
         "Table Name is Required.";

        public static string TableAccessTableNameNotNullRequired =
         "'Table Name' must not be empty.";

        public static string TableAccessTableNameRangeRequired =
         "Table Name should contain between 3 to 30 characters.";

        public static string TableAccessTableNameValidRequired =
         "Please Enter Valid Table Name.";

        public static string TableAccessTableNameUnique =
         "A same name already exists.";
    }

    public static class AlertReceiver
    {
        public static string AlertReceiverNameRequired =
            "Name is required.";

        public static string AlertReceiverNameNotNullRequired =
            "'Name' must not be empty.";

        public static string AlertReceiverNameRangeRequired =
            "Name should contain between 3 to 100 characters.";

        public static string AlertReceiverNameValidRequired =
            "Please Enter Valid Name";
    }

    #endregion


        #region Admin
        public static class Form
    {
        public static string FormNameRequired =
            "Name is required.";

        public static string FormNameNotNullRequired =
            "'Name' must not be empty.";

        public static string FormNameRangeRequired =
            "Name should contain between 3 to 100 characters.";

        public static string FormNameValidRequired =
            "Please Enter Valid Name";

        public static string FormTypeRequired =
            "Type is required";

        public static string FormTypeValidRequired =
            "Please Enter valid Type";

        public static string FormTypeNotNullRequired =
            "'Type' must not be empty.";

        public static string FormPropertiesRequired =
            "Please Enter the Properties";

        public static string FormPropertiesNotNullRequired =
            "'Properties' must not be empty.";
    }

    public static class WorkflowCategory
    {
        public static string WorkflowCategoryNameRequired =
            "Name is required.";

        public static string WorkflowCategoryNameNotNullRequired =
            "'Name' must not be empty.";

        public static string WorkflowCategoryNameRangeRequired =
            "Name should contain between 3 to 100 characters.";

        public static string WorkflowCategoryNameValidRequired =
            "Please Enter Valid Name.";

        public static string WorkflowCategoryPropertiesRequired =
            "Please Enter the Properties.";

        public static string WorkflowCategoryPropertiesNotNullRequired =
            "'Properties' must not be empty.";

        public static string WorkflowCategoryVersionRequired =
            "Version is required.";

        public static string WorkflowCategoryVersionNotNullRequired =
            "'Version' must not be empty.";
    }

    public static class FormHistory
    {
        public static string FormHistoryNameRequired =
            "Form Name is Required.";

        public static string FormHistoryNameNotNullRequired =
            "'Form Name' must not be empty.";

        public static string FormHistoryNameRangeRequired =
            "Form Name should contain between 3 to 100 characters.";

        public static string FormHistoryNameValidRequired =
            "Please Enter Valid Form Name.";

        public static string FormHistoryLoginNameRequired =
            "Please Enter the Login Name.";

        public static string FormHistoryLoginNameNotNullRequired =
            "'Login Name' must not be empty.";

        public static string FormHistoryPropertiesRequired =
            "Please Enter the Properties.";

        public static string FormHistoryPropertiesNotNullRequired =
            "'Properties' must not be empty.";

        public static string FormHistoryTypeRequired =
            "Type is Required.";

        public static string FormHistoryTypeNotNullRequired =
            "'Type' must not be empty.";

        public static string FormHistoryTypeValidRequired =
            "Please Enter Valid Type.";

        public static string FormHistoryDescriptionRequired =
            "Please Enter the Description.";

        public static string FormHistoryDescriptionNotNullRequired =
            "'Description' must not be empty.";

        public static string FormHistoryCommentsRequired =
            "Please Enter the Comments.";

        public static string FormHistoryCommentsNotNullRequired =
            "'Comments' must not be empty.";
    }

    public static class FormType
    {
        public static string FormTypeNameRequired =
            "Form Type Name is required.";

        public static string FormTypeNameNotNullRequired =
            "'Form Type Name' must not be empty.";

        public static string FormTypeNameRangeRequired =
            "Form Type Name should contain between 3 to 100 characters.";

        public static string FormTypeNameValidRequired =
            "Please Enter Valid Form Type Name";

        public static string FormTypeNameUnique =
            "A same Name already exists";
    }

    public static class WorkflowAction
    {
        public static string WorkflowActionNameRequired =
            "Action Name is Required.";

        public static string WorkflowActionNameNotNullRequired =
            "'Action Name' must not be empty.";

        public static string WorkflowActionNameRangeRequired =
            "Action Name should contain between 3 to 100 characters.";

        public static string WorkflowActionNameValidRequired =
            "Please Enter Valid Action Name.";

        public static string WorkflowActionPropertiesRequired =
            "Please Enter the Properties.";

        public static string WorkflowActionPropertiesNotNullRequired =
            "'Properties' must not be empty.";

        public static string WorkflowActionListNameUnique =
            "A same Name Already Exists";
    }

    public static class SolutionHistory
    {
        public static string SolutionHistoryActionNameRequired =
            "Action Name is Required.";

        public static string SolutionHistoryActionNameNotNullRequired =
            "'Action Name' must not be empty.";

        public static string SolutionHistoryActionNameRangeRequired =
            "Action Name should contain between 3 to 30 characters.";

        public static string SolutionHistoryActionNameValidRequired =
            "Please Enter Valid Action Name.";

        public static string SolutionHistoryLoginNameRequired =
            "Please Enter the Login Name.";

        public static string SolutionHistoryLoginNameNotNullRequired =
            "'Login Name' must not be empty.";

        public static string SolutionHistoryPropertiesRequired =
            "Please Enter the Properties.";

        public static string SolutionHistoryPropertiesNotNullRequired =
            "'Properties' must not be empty.";
    }

    public static class LicenseManager
    {
        public static string LicenseManagerLicenseKeyRequired =
            "License Key is Required.";

        public static string LicenseManagerLicenseKeyNotNullRequired =
            "'License Key' must not be empty.";

        public static string SameLicenseKeyExist =
            "This License Key already used,Please Enter the new license key.";
    }

    public static class PluginManager
    {
        public static string PluginManagerNameRequired =
            "Name is Required.";

        public static string PluginManagerNameNotNullRequired =
            "'Name' must not be empty.";

        public static string PluginManagerNameRangeRequired =
            "Name should contain between 3 to 100 characters.";

        public static string PluginManagerNameValidRequired =
            "Please Enter Valid Name.";

        public static string PluginManagerDescriptionNotNullRequired =
            "'Description' must not be empty.";

        public static string PluginManagerMaximumRangeRequired =
            "Plugin Manager Description Maximum 250 characters.";

        public static string PluginManagerPropertiesRequired =
            "Properties is Required.";

        public static string PluginManagerPropertiesNotNullRequired =
            "'Properties' must not be empty.";

        public static string PluginNameUniqueRequired =
            "A same Name Already Exists";
    }

    public static class PluginManagerHistory
    {
        public static string PluginManagerHistoryNameRequired =
            "Plugin Manager Name is Required.";

        public static string PluginManagerHistoryNameNotNullRequired =
            "'Plugin Manager Name' must not be empty.";

        public static string PluginManagerHistoryNameRangeRequired =
            "Plugin Manager Name should contain between 3 to 30 characters.";

        public static string PluginManagerHistoryNameValidRequired =
            "Please Enter Valid Plugin Manager Name.";

        public static string PluginManagerHistoryLoginNameRequired =
            "Please Enter the Login Name.";

        public static string PluginManagerHistoryLoginNameNotNullRequired =
            "'Login Name' must not be empty.";

        public static string PluginManagerHistoryPropertiesRequired =
            "Please Enter the Properties.";

        public static string PluginManagerHistoryPropertiesNotNullRequired =
            "'Properties' must not be empty.";

        public static string PluginManagerHistoryVersionRequired =
            "Please Enter the Version.";

        public static string PluginManagerHistoryVersionNotNullRequired =
            "'Version' must not be empty.";

        public static string PluginManagerHistoryCommentsRequired =
            "Please Enter the Comments.";

        public static string PluginManagerHistoryCommentsNotNullRequired =
            "'Comments' must not be empty.";

        public static string PluginManagerHistoryDescriptionRequired =
            "Please Enter the Description.";

        public static string PluginManagerHistoryDescriptionNotNullRequired =
            "'Description' must not be empty.";
    }

    public static class LoadBalancer
    {
        public static string LoadBalancerNameRequired =
            "Name is required.";

        public static string LoadBalancerNameRange =
            "Name should contain between 3 to 100 characters.";

        public static string LoadBalancerNameNotEmpty =
            "'Name' must not be empty.";

        public static string LoadBalancerNameValid =
           "Please Enter Valid Name";

        public static string LoadBalancerHostNameRequired =
           "Host Name is required";

        public static string LoadBalancerHostNameRange =
            "Host Name should contain between 3 to 30 characters.";

        public static string LoadBalancerHostNameNotEmpty =
            "'Host Name' must not be empty.";

        public static string LoadBalancerHostNameValid =
           "Please Enter Valid Host Name";

        public static string LoadBalancerIpAddressValid =
          "Please Enter Valid IP Address.";

        public static string LoadBalancerIpAddressNotEmpty =
           "'IP Address' must not be empty.";

        public static string LoadBalancerConnectionTypeRequired =
           "Connection Type is required.";

        public static string LoadBalancerConnectionTypeNotEmpty =
            "'Connection Type' must not be empty.";

        public static string LoadBalancerConnectionTypeValid =
           "Please Enter Valid Connection Type";

        public static string LoadBalancerNameUnique =
          "A same Name Already Exist.";

    }
    #endregion


    #region Orchestration
    public static class Workflow
    {
        public static string WorkflowNameRequired =
            "Name is required";

        public static string WorkflowNameNotEmpty =
            "'Name' must not be empty.";

        public static string WorkflowNameValid =
            "Please Enter Valid Name";

        public static string WorkflowNameRange =
            "Name should contain between 3 to 200 characters";
    }

    public static class WorkflowProfileInfo
    {
        public static string WorkflowProfileInfoNameRequired =
            "Profile Name is required.";

        public static string WorkflowProfileInfoNameNotEmpty =
            "'Profile Name' must not be empty.";

        public static string WorkflowProfileInfoNameRange =
            "Profile Name should contain between 3 to 200 characters.";

        public static string WorkflowProfileInfoNameValid =
            "Please Enter Valid Profile Name";

        public static string WorkflowProfileInfoBusinessServiceNameValid =
            "Please Enter Valid Business Service Name.";
    }

    public static class WorkflowHistory
    {
        public static string WorkflowHistoryNameRequired =
            "Workflow Name is required.";

        public static string WorkflowHistoryNameNotEmptyRequired =
            "'Workflow Name' must not be empty.";

        public static string WorkflowHistoryNameValidRequired =
            "Please Enter Valid Workflow Name";

        public static string WorkflowHistoryNameRangeRequired =
            "Workflow Name should contain between 3 to 30 characters.";
    }

    public static class WorkflowInfraObject
    {
        public static string WorkflowAndInfraObjectNameUnique =
            "This workflow is already attached to this infraObject.";
    }

    public static class WorkflowActionType
    {
        public static string WorkflowActionTypeRequired =
            "Action Type is Required.";

        public static string WorkflowActionTypeNotNullRequired =
            "'Action Type' must not be empty.";

        public static string WorkflowActionTypeRangeRequired =
            "Action Type should contain between 3 to 100 characters.";

        public static string WorkflowActionTypeValidRequired =
            "Please Enter Valid Action Type.";
    }

    public static class WorkflowProfile
    {
        public static string WorkflowProfileNameRequired =
            "Name is Required.";

        public static string WorkflowProfileNameNotNullRequired =
            "'Name' must not be empty.";

        public static string WorkflowProfileNameValid =
            "Please Enter Valid Group Policy Name.";

        public static string WorkflowProfileNameRangeRequired =
            "Name should contain between 3 to 200 characters.";

        public static string WorkflowProfileNameValidRequired =
            "Please Enter Valid Name.";

        public static string WorkflowProfileNameUnique =
            "A same name already exists";

        public static string WorkflowProfileGroupPolicyNameValid =
            "Please Enter Valid Group Policy Name.";
    }

    public static class ApprovalMatrix
    {
        public static string ApprovalMatrixNameRequired =
            "Name is required.";

        public static string ApprovalMatrixNameNotNullRequired =
           "'Name' must not be empty.";

        public static string ApprovalMatrixNameRangeRequired =
          "Name should contain between 3 to 100 characters.";

        public static string ApprovalMatrixNameValidRequired =
            "Please Enter Valid Name";

        public static string ApprovalMatrixUserNameValid =
            "Please Enter Valid User Name";

        public static string ApprovalMatrixDescriptionNotNullRequired =
            "'Description' must not be empty.";

        public static string ApprovalMatrixDescriptionRangeRequired =
          "Description Maximum 250 characters.";

        public static string ApprovalMatrixDescriptionContainsRequired =
            "Description contains invalid characters.";

        public static string ApprovalMatrixUserNameRequired =
           "Select User Name.";

        public static string ApprovalMatrixUserNameNotNullRequired =
           "'User Name' must not be empty.";

        public static string ApprovalMatrixBusinessServiceNameRequired =
            "Select Business Service Name..";

        public static string ApprovalMatrixBusinessServiceNameNotNullRequired =
           "'Business Service Name' must not be empty.";

        public static string ApprovalaMatrixNameUnique =
          "A same name already exists.";
    }

    public static class NodeWorkflowExecution
    {
        public static string NodeWorkflowExecutionWorkflowNameRequired =
            "Select Workflow Name.";

        public static string NodeWorkflowExecutionWorkflowNameNotNullRequired =
           "'Workflow Name' must not be empty.";

        public static string NodeWorkflowExecutionProfileNameRequired =
            "Select Profile Name.";

        public static string NodeWorkflowExecutionProfileNameNotNullRequired =
            "'Profile Name' must not be empty.";

        public static string NodeWorkflowExecutionNodeNameRequired =
            "Select Node Name.";

        public static string NodeWorkflowExecutionNodeNameNotNullRequired =
            "'Node Name' must not be empty.";
    }

    public static class FiaInterval
    {
        public static string MinTimeRequired =
            "Min Time is required.";

        public static string MinTimeNotEmpty =
            "'Min Time' must not be empty.";

        public static string MinTimeGreaterThanZero =
            "Min Time must be greater than 0.";

        public static string MaxTimeRequired =
            "Max Time is required.";

        public static string MaxTimeNotEmpty =
            "'Max Time' must not be empty.";

        public static string MaxTimeGreaterThanZero =
            "Max Time must be greater than 0.";

        public static string MaxTimeGreaterThanMinTime =
            "Max Time must be greater than Min Time.";

        public static string MinTimeUnitRequired =
            "Min Time Unit is required.";

        public static string MinTimeUnitNotEmpty =
            "'Min Time Unit' must not be empty.";

        public static string MinTimeUnitGreaterThanZero =
            "Min Time Unit must be greater than 0.";

        public static string MaxTimeUnitRequired =
            "Max Time Unit is required.";

        public static string MaxTimeUnitNotEmpty =
            "'Max Time Unit' must not be empty.";

        public static string MaxTimeUnitGreaterThanZero =
            "Max Time Unit must be greater than 0.";

        public static string IdRequired =
            "Id is required.";

        public static string IdNotEmpty =
            "'Id' must not be empty.";

        public static string IdInvalidFormat =
            "Id must be a valid GUID format.";
    }

    public static class FiaTemplate
    {
        public static string NameRequired =
            "Name is required.";

        public static string NameNotEmpty =
            "'Name' must not be empty.";

        public static string NameRange =
            "Name should contain between 3 to 100 characters.";

        public static string NameValid =
            "Please Enter Valid Name";

        public static string NameUnique =
            "A same name already exists.";

        public static string IdRequired =
            "Id is required.";

        public static string IdNotEmpty =
            "'Id' must not be empty.";

        public static string IdInvalidFormat =
            "Id must be a valid GUID format.";

        public static string DescriptionRequired =
            "Description is required.";

        public static string DescriptionNotEmpty =
            "'Description' must not be empty.";

        public static string DescriptionMinimumLength =
            "Description should contain at least 10 characters.";
    }

    public static class FormTypeCategory
    {
        public static string NameRequired =
            "Name is required.";

        public static string NameNotEmpty =
            "'Name' must not be empty.";

        public static string NameValid =
            "Please Enter Valid Name";

        public static string FormNameRequired =
            "Form Name is required.";

        public static string FormNameNotEmpty =
            "'Form Name' must not be empty.";

        public static string FormNameValid =
            "Please Enter Valid FormName";

        public static string FormTypeNameRequired =
            "Form Type Name is required.";

        public static string FormTypeNameNotEmpty =
            "'Form Type Name' must not be empty.";

        public static string FormTypeNameValid =
            "Please Enter Valid FormTypeName";

        public static string VersionRequired =
            "Version is required.";

        public static string VersionNotEmpty =
            "'Version' must not be empty.";

        public static string VersionValidJson =
            "Version must be a valid Json string.";

        public static string IdRequired =
            "Id is required.";

        public static string IdNotEmpty =
            "'Id' must not be empty.";

        public static string IdInvalidFormat =
            "Id must be a valid GUID format.";

        public static string FormTypeIdInvalid =
            "Invalid Id.";

        public static string FormIdInvalid =
            "Invalid Id.";
    }
    #endregion

}