﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Delete;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Commands;

public class DeleteWorkflowPredictionTests
{
    private readonly Mock<IWorkflowPredictionRepository> _mockWorkflowPredictionRepository;
    private readonly DeleteWorkflowPredictionCommandHandler _handler;

    public DeleteWorkflowPredictionTests()
    {
        _mockWorkflowPredictionRepository = new Mock<IWorkflowPredictionRepository>();
        Mock<IPublisher> mockPublisher = new();
        _handler = new DeleteWorkflowPredictionCommandHandler(
            _mockWorkflowPredictionRepository.Object,
            mockPublisher.Object
        );
    }

    [Fact]
    public async Task Handle_ReturnsCorrectResponse_WhenWorkflowPredictionDeletedSuccessfully()
    {
        var command = new DeleteWorkflowPredictionCommand
        {
            Id = Guid.NewGuid().ToString(),
        };
        var workflowPrediction = new Domain.Entities.WorkflowPrediction
        {
            ActionId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(workflowPrediction);

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.UpdateAsync(workflowPrediction))
            .ReturnsAsync(workflowPrediction);

        var expectedMessage = $" WorkflowPrediction '{workflowPrediction.ActionId}' has been deleted successfully";

        var result = await _handler.Handle(command, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(expectedMessage, result.Message);
        Assert.False(result.IsActive);

        _mockWorkflowPredictionRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockWorkflowPredictionRepository.Verify(repo => repo.UpdateAsync(workflowPrediction), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowsNotFoundException_WhenWorkflowPredictionNotFound()
    {
        var command = new DeleteWorkflowPredictionCommand
        {
            Id = Guid.NewGuid().ToString()
        };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync((Domain.Entities.WorkflowPrediction)null!);

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));

        _mockWorkflowPredictionRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockWorkflowPredictionRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.WorkflowPrediction>()), Times.Never);
    }
}