﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Events.Update;
using ContinuityPatrol.Application.Features.WorkflowTemp.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Events
{
   public class WorkflowDrCalenderUpdatedEventHandlerTests : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly WorkflowDrCalenderFixture _workflowDrcalenderFixture;

        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

        private readonly WorkflowDrCalenderUpdatedEventHandler _handler;

        public WorkflowDrCalenderUpdatedEventHandlerTests(WorkflowDrCalenderFixture workflowDrCalenderFixture)
        {
            _workflowDrcalenderFixture = workflowDrCalenderFixture;

            var mockLoggedInUserService = new Mock<ILoggedInUserService>();

            var mockWorkflowDrCalenderEventLogger = new Mock<ILogger<WorkflowDrCalenderUpdatedEventHandler>>();

            _mockUserActivityRepository = WorkflowTempRepositoryMocks.CreateWorkflowTempEventRepository(_workflowDrcalenderFixture.UserActivities);

            _handler = new WorkflowDrCalenderUpdatedEventHandler(mockLoggedInUserService.Object, mockWorkflowDrCalenderEventLogger.Object, _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_IncreaseUserActivityCount_When_UpdateWorkflowTestEventUpdated()
        {
            _workflowDrcalenderFixture.UserActivities[0].LoginName = "Test";

            var result = _handler.Handle(_workflowDrcalenderFixture.WorkflowDrUpdatedEvent, CancellationToken.None);

            result.Equals(_workflowDrcalenderFixture.UserActivities[0].LoginName);

            await Task.CompletedTask;
        }

        [Fact]
        public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
        {
            await _handler.Handle(_workflowDrcalenderFixture.WorkflowDrUpdatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_UserActivityCount_When_UpdateWorkflowTempEventUpdated()
        {
            _workflowDrcalenderFixture.UserActivities[0].LoginName = "Test";

            var result = _handler.Handle(_workflowDrcalenderFixture.WorkflowDrUpdatedEvent, CancellationToken.None);

            result.Equals(_workflowDrcalenderFixture.UserActivities[0].Id);

            result.Equals(_workflowDrcalenderFixture.WorkflowDrUpdatedEvent.Name);

            await Task.CompletedTask;
        }
    }
}
