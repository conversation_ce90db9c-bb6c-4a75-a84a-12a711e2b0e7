﻿using AutoMapper;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Queries;

public class GetGlobalVariableListQueryHandlerTests : IClassFixture<GlobalVariableFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;
    private Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;
    private readonly GetGlobalVariableListQueryHandler _handler;

    public GetGlobalVariableListQueryHandlerTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.GetGlobalVariableRepository(_globalVariableFixture.GlobalVariables);

        _handler = new GetGlobalVariableListQueryHandler(_globalVariableFixture.Mapper, _mockGlobalVariableRepository.Object);

        _globalVariableFixture.GlobalVariables[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
    }

    [Fact]
    public async Task Handle_Return_Valid_GlobalVariablesDetail()
    {
        var result = await _handler.Handle(new GetGlobalVariableListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<GlobalVariableListVm>>();

        result[0].Id.ShouldBe(_globalVariableFixture.GlobalVariables[0].ReferenceId);
        result[0].VariableName.ShouldBe(_globalVariableFixture.GlobalVariables[0].VariableName);
        result[0].VariableValue.ShouldBe(_globalVariableFixture.GlobalVariables[0].VariableValue);
        result[0].Type.ShouldBe(_globalVariableFixture.GlobalVariables[0].Type);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.GetGlobalVariableEmptyRepository();
        var handler = new GetGlobalVariableListQueryHandler(_globalVariableFixture.Mapper, _mockGlobalVariableRepository.Object);
        var result = await handler.Handle(new GetGlobalVariableListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetGlobalVariableListQuery(), CancellationToken.None);
        _mockGlobalVariableRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public void GetGlobalVariableListQuery_CanBeInstantiated()
    {
        var query = new GetGlobalVariableListQuery();
        query.ShouldNotBeNull();
    }
}