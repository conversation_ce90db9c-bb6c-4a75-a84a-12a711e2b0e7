<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width">
    <title>Notification Mananger Unit Test</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.19.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>

    <!-- Mock DOM elements for testing -->
    <div id="test-container" style="display:none;">
        <!-- Elements will be cloned into qunit-fixture during tests -->
    </div>

    <!-- QUnit Framework -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>

    <script src="https://code.jquery.com/qunit/qunit-2.19.4.js"></script>

    <!-- Sinon.js for mocking AJAX calls -->
    <script src="https://cdn.jsdelivr.net/npm/sinon@15.2.0/pkg/sinon.min.js"></script>

    <!-- Files to test -->
    <script src="/js/Common/common.js"></script>
    <script src="/js/Manage/Notification Manager/NotificationManager.js"></script>
    <script src="/js/Manage/Notification Manager/NotificationManagerTest.js"></script>
</body>
</html>