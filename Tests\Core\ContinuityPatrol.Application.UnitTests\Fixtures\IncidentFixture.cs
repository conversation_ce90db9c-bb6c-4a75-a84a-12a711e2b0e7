using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.Incident.Commands.Create;
using ContinuityPatrol.Application.Features.Incident.Commands.Delete;
using ContinuityPatrol.Application.Features.Incident.Commands.Update;
using ContinuityPatrol.Application.Features.Incident.Events.Create;
using ContinuityPatrol.Application.Features.Incident.Events.Delete;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class IncidentFixture
{
    public List<Incident> Incidents { get; set; }
    public CreateIncidentCommand CreateIncidentCommand { get; set; }
    public DeleteIncidentCommand DeleteIncidentCommand { get; set; }
    public UpdateIncidentCommand UpdateIncidentCommand { get; set; }
    public IncidentCreatedEvent IncidentCreatedEvent { get; set; }
    public IncidentDeletedEvent IncidentDeletedEvent { get; set; }
    public IMapper Mapper { get; private set; }

    public IncidentFixture()
    {
        var fixture = new Fixture();

        Incidents = fixture.CreateMany<Incident>(5).ToList();

        CreateIncidentCommand = fixture.Create<CreateIncidentCommand>();

        DeleteIncidentCommand = fixture.Create<DeleteIncidentCommand>();

        UpdateIncidentCommand = fixture.Create<UpdateIncidentCommand>();

        IncidentCreatedEvent = fixture.Create<IncidentCreatedEvent>();

        IncidentDeletedEvent = fixture.Create<IncidentDeletedEvent>();

        var mapperConfig = new MapperConfiguration(c =>
        {
            c.AddProfile<IncidentProfile>();
        });

        Mapper = mapperConfig.CreateMapper();
    }
}
