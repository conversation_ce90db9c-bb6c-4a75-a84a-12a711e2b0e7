using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class LicenseManagerFixture : IDisposable
{
    public List<LicenseManager> LicenseManagerPaginationList { get; set; }
    public List<LicenseManager> LicenseManagerList { get; set; }
    public LicenseManager LicenseManagerDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string PoNumber = "PO123";
    public const string LicenseKey = "TESTLICENSEKEY";

    public ApplicationDbContext DbContext { get; private set; }

    public LicenseManagerFixture()
    {
        var fixture = new Fixture();

        LicenseManagerList = fixture.Create<List<LicenseManager>>();

        LicenseManagerPaginationList = fixture.CreateMany<LicenseManager>(20).ToList();

        LicenseManagerPaginationList.ForEach(x =>
        {
            x.ReferenceId = Guid.NewGuid().ToString();
            x.CompanyId = CompanyId;
            x.IsActive = true;
            x.PoNumber = "PO" + fixture.Create<int>();
            x.CompanyName = "Company" + fixture.Create<int>();
            x.HostName = "Host" + fixture.Create<int>();
            x.Properties = "Properties" + fixture.Create<int>();
            x.IpAddress = "192.168.1." + fixture.Create<int>() % 255;
            x.MacAddress = "00:11:22:33:44:" + (fixture.Create<int>() % 100).ToString("X2");
            x.LicenseKey = "LK" + fixture.Create<string>().Substring(0, 10);
            x.IsParent = fixture.Create<bool>();
            x.ParentId = fixture.Create<bool>() ? "PARENT_" + fixture.Create<int>() : null;
            x.ParentPoNumber = fixture.Create<bool>() ? "PARENT_PO" + fixture.Create<int>() : null;
            x.Validity = "12 Months";
            x.ExpiryDate = DateTime.Now.AddMonths(12).ToString("dd MMMM yyyy");
            x.IsPrimary = fixture.Create<bool>();
            x.IsState = true;
            x.IsAmc = fixture.Create<bool>();
            x.AmcPlan = fixture.Create<bool>() ? "Premium Plan" : null;
            x.AmcStartDate = fixture.Create<bool>() ? DateTime.Now.ToString("dd MMMM yyyy") : null;
            x.AmcEndDate = fixture.Create<bool>() ? DateTime.Now.AddYears(1).ToString("dd MMMM yyyy") : null;
            x.IsExpired = false;
            x.ReferenceId=Guid.NewGuid().ToString();
        });

        LicenseManagerList.ForEach(x =>
        {
            x.ReferenceId = Guid.NewGuid().ToString();
            x.CompanyId = CompanyId;
            x.IsActive = true;
            x.PoNumber = "PO" + fixture.Create<int>();
            x.CompanyName = "Company" + fixture.Create<int>();
            x.HostName = "Host" + fixture.Create<int>();
            x.Properties = "Properties" + fixture.Create<int>();
            x.IpAddress = "192.168.1." + fixture.Create<int>() % 255;
            x.MacAddress = "00:11:22:33:44:" + (fixture.Create<int>() % 100).ToString("X2");
            x.LicenseKey = "LK" + fixture.Create<string>().Substring(0, 10);
            x.IsParent = fixture.Create<bool>();
            x.ParentId = fixture.Create<bool>() ? "PARENT_" + fixture.Create<int>() : null;
            x.ParentPoNumber = fixture.Create<bool>() ? "PARENT_PO" + fixture.Create<int>() : null;
            x.Validity = "12 Months";
            x.ExpiryDate = DateTime.Now.AddMonths(12).ToString("dd MMMM yyyy");
            x.IsPrimary = fixture.Create<bool>();
            x.IsState = true;
            x.IsAmc = fixture.Create<bool>();
            x.AmcPlan = fixture.Create<bool>() ? "Premium Plan" : null;
            x.AmcStartDate = fixture.Create<bool>() ? DateTime.Now.ToString("dd MMMM yyyy") : null;
            x.AmcEndDate = fixture.Create<bool>() ? DateTime.Now.AddYears(1).ToString("dd MMMM yyyy") : null;
            x.IsExpired = false;
        });

        LicenseManagerDto = fixture.Create<LicenseManager>();
        LicenseManagerDto.CompanyId = CompanyId;
        LicenseManagerDto.PoNumber = PoNumber;
        LicenseManagerDto.LicenseKey = LicenseKey;
        LicenseManagerDto.IsActive = true;
        LicenseManagerDto.PoNumber = "PO123";

        LicenseManagerDto.CompanyName = "TestCompany";
        LicenseManagerDto.HostName = "TestHost";
        LicenseManagerDto.Properties = "TestProperties";
        LicenseManagerDto.IpAddress = "*************";
        LicenseManagerDto.MacAddress = "00:11:22:33:44:55";
        LicenseManagerDto.IsParent = false;
        LicenseManagerDto.ParentId = "PARENT_123";
        LicenseManagerDto.ParentPoNumber = "PARENT_PO123";
        LicenseManagerDto.Validity = "12 Months";
        LicenseManagerDto.ExpiryDate = DateTime.Now.AddMonths(12).ToString("dd MMMM yyyy");
        LicenseManagerDto.IsPrimary = true;
        LicenseManagerDto.IsState = true;
        LicenseManagerDto.IsAmc = true;
        LicenseManagerDto.AmcPlan = "Premium Plan";
        LicenseManagerDto.AmcStartDate = DateTime.Now.ToString("dd MMMM yyyy");
        LicenseManagerDto.AmcEndDate = DateTime.Now.AddYears(1).ToString("dd MMMM yyyy");
        LicenseManagerDto.IsExpired = false;
        LicenseManagerDto.ReferenceId = Guid.NewGuid().ToString();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
