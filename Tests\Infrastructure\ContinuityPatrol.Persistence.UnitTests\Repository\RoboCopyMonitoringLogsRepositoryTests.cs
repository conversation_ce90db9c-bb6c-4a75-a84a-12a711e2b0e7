using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RoboCopyMonitoringLogsRepositoryTests : IClassFixture<RoboCopyMonitoringLogsFixture>
{
    private readonly RoboCopyMonitoringLogsRepository _repository;
    private readonly RoboCopyMonitoringLogsFixture _fixture;
    private readonly IConfiguration _configuration;
    private readonly ApplicationDbContext _dbContext;

    public RoboCopyMonitoringLogsRepositoryTests(RoboCopyMonitoringLogsFixture fixture)
    {
        _fixture = fixture;
        _configuration = ConfigurationRepositoryMocks.GetConnectionString().Object;
        _repository = new RoboCopyMonitoringLogsRepository(_fixture.DbContext, _configuration);
        _dbContext=DbContextFactory.CreateInMemoryDbContext();
    }

    private async Task ClearDatabase()
    {
        _fixture.DbContext.RoboCopyMonitorLogs.RemoveRange(_fixture.DbContext.RoboCopyMonitorLogs);
        await _fixture.DbContext.SaveChangesAsync();
    }

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnLogs_WhenInfraObjectIdMatches()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_001";
        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        var logs = new List<RoboCopyMonitorLogs>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Success",
                InfraObjectId = infraObjectId,
                InfraObjectName = "Test Infrastructure 1",
                WorkflowId = "WF_001",
                WorkflowName = "Test Workflow 1",
                Properties = "{\"status\":\"completed\"}",
                ConfiguredRPO = "4 hours",
                DataLagValue = "2 hours",
                Threshold = "6 hours",
                CreatedDate = new DateTime(2024, 1, 15),
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                InfraObjectId = infraObjectId,
                InfraObjectName = "Test Infrastructure 1",
                WorkflowId = "WF_002",
                WorkflowName = "Test Workflow 2",
                Properties = "{\"status\":\"warning\"}",
                ConfiguredRPO = "2 hours",
                DataLagValue = "3 hours",
                Threshold = "4 hours",
                CreatedDate = new DateTime(2024, 1, 20),
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                InfraObjectId = "INFRA_002",
                InfraObjectName = "Different Infrastructure",
                WorkflowId = "WF_003",
                WorkflowName = "Different Workflow",
                CreatedDate = new DateTime(2024, 1, 10),
                IsActive = true 
            }
        };

        _dbContext.RoboCopyMonitorLogs.AddRange(logs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(infraObjectId, r.InfraObjectId));
        Assert.Contains(result, r => r.Type == "Success");
        Assert.Contains(result, r => r.Type == "Warning");
        Assert.DoesNotContain(result, r => r.InfraObjectId == "INFRA_002");
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmptyList_WhenNoMatchingInfraObjectId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_NONEXISTENT";
        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        var log = new RoboCopyMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Success",
            InfraObjectId = "INFRA_DIFFERENT",
            InfraObjectName = "Different Infrastructure",
            CreatedDate = new DateTime(2024, 1, 15),
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldFilterByDateRange_Correctly()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_003";
        var startDate = "2024-01-10";
        var endDate = "2024-01-20";

        var logs = new List<RoboCopyMonitorLogs>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Success",
                InfraObjectId = infraObjectId,
                InfraObjectName = "Date Test Infrastructure",
                CreatedDate = new DateTime(2024, 1, 5), // Before range
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                InfraObjectId = infraObjectId,
                InfraObjectName = "Date Test Infrastructure",
                CreatedDate = new DateTime(2024, 1, 15), // Within range
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                InfraObjectId = infraObjectId,
                InfraObjectName = "Date Test Infrastructure",
                CreatedDate = new DateTime(2024, 1, 25), // After range
                IsActive = true 
            }
        };

   
        _dbContext.RoboCopyMonitorLogs.AddRange(logs);  
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.Single(result);
        Assert.Equal("Warning", result[0].Type);
        Assert.Equal(new DateTime(2024, 1, 15).Date, result[0].CreatedDate.Date);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnOnlyActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_004";
        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        var logs = new List<RoboCopyMonitorLogs>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Active Log",
                InfraObjectId = infraObjectId,
                InfraObjectName = "Active Test Infrastructure",
                CreatedDate = new DateTime(2024, 1, 15),
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Inactive Log",
                InfraObjectId = infraObjectId,
                InfraObjectName = "Inactive Test Infrastructure",
                CreatedDate = new DateTime(2024, 1, 16),
                IsActive = false 
            }
        };

        _dbContext.RoboCopyMonitorLogs.AddRange(logs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.Single(result);
        Assert.Equal("Active Log", result[0].Type);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleComplexProperties()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_005";
        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        var complexProperties = "{\"source\":\"/path/to/source\",\"destination\":\"/path/to/dest\",\"options\":[\"/MIR\",\"/R:3\",\"/W:10\"],\"statistics\":{\"files\":1500,\"bytes\":\"2.5GB\",\"duration\":\"00:45:30\"}}";

        var log = new RoboCopyMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Complex",
            InfraObjectId = infraObjectId,
            InfraObjectName = "Complex Properties Infrastructure",
            WorkflowId = "WF_COMPLEX",
            WorkflowName = "Complex Workflow",
            Properties = complexProperties,
            ConfiguredRPO = "1 hour",
            DataLagValue = "30 minutes",
            Threshold = "2 hours",
            CreatedDate = new DateTime(2024, 1, 15),
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.Single(result);
        var returnedLog = result[0];
        Assert.Equal("Complex", returnedLog.Type);
        Assert.Equal(infraObjectId, returnedLog.InfraObjectId);
        Assert.Equal("Complex Properties Infrastructure", returnedLog.InfraObjectName);
        Assert.Equal("WF_COMPLEX", returnedLog.WorkflowId);
        Assert.Equal("Complex Workflow", returnedLog.WorkflowName);
        Assert.Equal(complexProperties, returnedLog.Properties);
        Assert.Equal("1 hour", returnedLog.ConfiguredRPO);
        Assert.Equal("30 minutes", returnedLog.DataLagValue);
        Assert.Equal("2 hours", returnedLog.Threshold);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_006";
        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnLogsOfSpecificType()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "Success";

        var logs = new List<RoboCopyMonitorLogs>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = targetType,
                InfraObjectId = "INFRA_007",
                InfraObjectName = "Success Infrastructure 1",
                WorkflowId = "WF_SUCCESS_1",
                WorkflowName = "Success Workflow 1",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = targetType,
                InfraObjectId = "INFRA_008",
                InfraObjectName = "Success Infrastructure 2",
                WorkflowId = "WF_SUCCESS_2",
                WorkflowName = "Success Workflow 2",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                InfraObjectId = "INFRA_009",
                InfraObjectName = "Error Infrastructure",
                WorkflowId = "WF_ERROR",
                WorkflowName = "Error Workflow",
                IsActive = true 
            }
        };

        foreach (var log in logs)
        {
            await _repository.AddAsync(log);
        }

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(targetType, r.Type));
        Assert.Contains(result, r => r.InfraObjectId == "INFRA_007");
        Assert.Contains(result, r => r.InfraObjectId == "INFRA_008");
        Assert.DoesNotContain(result, r => r.Type == "Error");
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenNoMatchingType()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "NonExistentType";

        var log = new RoboCopyMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "DifferentType",
            InfraObjectId = "INFRA_010",
            InfraObjectName = "Different Type Infrastructure",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "Warning";

        var logs = new List<RoboCopyMonitorLogs>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = targetType,
                InfraObjectId = "INFRA_011",
                InfraObjectName = "Active Warning Infrastructure",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = targetType,
                InfraObjectId = "INFRA_012",
                InfraObjectName = "Inactive Warning Infrastructure",
                IsActive = false 
            }
        };
        _dbContext.RoboCopyMonitorLogs.AddRange(logs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.Single(result);
        Assert.Equal("Active Warning Infrastructure", result[0].InfraObjectName);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleCaseSensitiveTypeMatching()
    {
        // Arrange
        await ClearDatabase();
        var logs = new List<RoboCopyMonitorLogs>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                InfraObjectId = "INFRA_013",
                InfraObjectName = "Lowercase Error Infrastructure",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "error",
                InfraObjectId = "INFRA_014",
                InfraObjectName = "Uppercase Error Infrastructure",
                IsActive = true 
            }
        };

        foreach (var log in logs)
        {
            await _repository.AddAsync(log);
        }

        // Act
        var resultUppercase = await _repository.GetDetailByType("Error");
        var resultLowercase = await _repository.GetDetailByType("error");

        // Assert
        Assert.Single(resultUppercase);
        Assert.Equal("Lowercase Error Infrastructure", resultUppercase[0].InfraObjectName);
        
        Assert.Single(resultLowercase);
        Assert.Equal("Uppercase Error Infrastructure", resultLowercase[0].InfraObjectName);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharactersInType()
    {
        // Arrange
        await ClearDatabase();
        var specialType = "Type@#$%^&*()_+{}|:<>?[]\\;',./";

        var log = new RoboCopyMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = specialType,
            InfraObjectId = "INFRA_015",
            InfraObjectName = "Special Characters Infrastructure",
            WorkflowId = "WF_SPECIAL",
            WorkflowName = "Special Characters Workflow",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetDetailByType(specialType);

        // Assert
        Assert.Single(result);
        Assert.Equal(specialType, result[0].Type);
        Assert.Equal("Special Characters Infrastructure", result[0].InfraObjectName);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "AnyType";

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleNullType()
    {
        // Arrange
        await ClearDatabase();
        var log = new RoboCopyMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = null,
            InfraObjectId = "INFRA_016",
            InfraObjectName = "Null Type Infrastructure",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.Single(result);
        Assert.Null(result[0].Type);
        Assert.Equal("Null Type Infrastructure", result[0].InfraObjectName);
    }

    #endregion
}
