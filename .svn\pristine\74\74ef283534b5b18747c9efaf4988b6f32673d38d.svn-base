﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetActionId;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Queries;

public class GetWorkflowPredictionListByActionIdQueryHandlerTests
{
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IWorkflowPredictionRepository> _mockWorkflowPredictionRepository;
    private readonly GetWorkflowPredictionListByActionIdQueryHandler _handler;

    public GetWorkflowPredictionListByActionIdQueryHandlerTests()
    {
        _mockMapper = new Mock<IMapper>();
        _mockWorkflowPredictionRepository = new Mock<IWorkflowPredictionRepository>();

        _handler = new GetWorkflowPredictionListByActionIdQueryHandler(
            _mockMapper.Object,
            _mockWorkflowPredictionRepository.Object
        );
    }

    [Fact]
    public async Task Handle_ReturnsMatchedPredictions_WhenActionIdMatches()
    {
        var query = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = "action-id-1",
            PreviousActionId = ""
        };

        var workflowPredictions = new List<Domain.Entities.WorkflowPrediction>
        {
            new() { Count = 1, LastModifiedDate = DateTime.Now, NextPossibleId = "action-id-1" },
            new() { Count = 2, LastModifiedDate = DateTime.Now.AddMinutes(-10), NextPossibleId = "action-id-1" }
        };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetWorkflowPredictionByActionId(query.ActionId))
            .ReturnsAsync(workflowPredictions);

        var workflowPredictionDtos = workflowPredictions.Select(_ => new WorkflowPredictionListByActionIdVm()).ToList();

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowPredictionListByActionIdVm>>(It.IsAny<List<Domain.Entities.WorkflowPrediction>>()))
            .Returns(workflowPredictionDtos);

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.NotNull(result);
        Assert.True(result.Status);
        Assert.Equal(workflowPredictionDtos, result.WorkflowPredictionListByActionIdVms);
        _mockWorkflowPredictionRepository.Verify(repo => repo.GetWorkflowPredictionByActionId(query.ActionId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyPredictions_WhenNoMatchesFound()
    {
        var query = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = "action-id-3",
            PreviousActionId = "non-matching-prev-action-id"
        };

        var workflowPredictions = new List<Domain.Entities.WorkflowPrediction>();

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetWorkflowPredictionByActionId(query.PreviousActionId))
            .ReturnsAsync(workflowPredictions);

        var workflowPredictionDtos = new List<WorkflowPredictionListByActionIdVm>();

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowPredictionListByActionIdVm>>(workflowPredictions))
            .Returns(workflowPredictionDtos);

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.NotNull(result);
        Assert.False(result.Status);
        Assert.Empty(result.MatchedActions);

        _mockWorkflowPredictionRepository.Verify(repo => repo.GetWorkflowPredictionByActionId(query.PreviousActionId), Times.Exactly(2));
    }

    [Fact]
    public async Task Handle_WhenPreviousActionIdIsNullOrEmpty_ReturnsPredictionsForActionId()
    {
        // Arrange
        var actionId = "action1";
        var request = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = actionId,
            PreviousActionId = null
        };

        var predictions = new List<Domain.Entities.WorkflowPrediction>
        {
            new() { ActionId = actionId, NextPossibleId = "action2", Count = 5 },
            new() { ActionId = actionId, NextPossibleId = "action3", Count = 3 }
        };

        var expectedMappedResult = new List<WorkflowPredictionListByActionIdVm>
        {
            new(),
            new()
        };

        _mockWorkflowPredictionRepository.Setup(x => x.GetWorkflowPredictionByActionId(actionId))
            .ReturnsAsync(predictions);

        _mockMapper.Setup(x => x.Map<List<WorkflowPredictionListByActionIdVm>>(It.IsAny<List<Domain.Entities.WorkflowPrediction>>()))
            .Returns(expectedMappedResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.True(result.Status);
        Assert.Equal(expectedMappedResult, result.WorkflowPredictionListByActionIdVms);
        _mockWorkflowPredictionRepository.Verify(x => x.GetWorkflowPredictionByActionId(actionId), Times.Once);
    }

    [Fact]
    public async Task Handle_WhenPreviousActionIdHasMatchingNextAction_ReturnsPredictionsForActionId()
    {
        // Arrange
        var previousActionId = "prevAction";
        var actionId = "action1";
        var request = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = actionId,
            PreviousActionId = previousActionId
        };

        var previousPredictions = new List<Domain.Entities.WorkflowPrediction>
        {
            new() { ActionId = previousActionId, NextPossibleId = actionId, Count = 5 },
            new() { ActionId = previousActionId, NextPossibleId = "otherAction", Count = 3 }
        };

        var currentPredictions = new List<Domain.Entities.WorkflowPrediction>
        {
            new() { ActionId = actionId, NextPossibleId = "nextAction1", Count = 2 },
            new() { ActionId = actionId, NextPossibleId = "nextAction2", Count = 1 }
        };

        var expectedMappedResult = new List<WorkflowPredictionListByActionIdVm>
        {
            new(),
            new()
        };

        _mockWorkflowPredictionRepository.Setup(x => x.GetWorkflowPredictionByActionId(previousActionId))
            .ReturnsAsync(previousPredictions);

        _mockWorkflowPredictionRepository.Setup(x => x.GetWorkflowPredictionByActionId(actionId))
            .ReturnsAsync(currentPredictions);

        _mockMapper.Setup(x => x.Map<List<WorkflowPredictionListByActionIdVm>>(It.IsAny<List<Domain.Entities.WorkflowPrediction>>()))
            .Returns(expectedMappedResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.True(result.Status);
        Assert.Equal(expectedMappedResult, result.WorkflowPredictionListByActionIdVms);
        _mockWorkflowPredictionRepository.Verify(x => x.GetWorkflowPredictionByActionId(actionId), Times.Once);
    }

    [Fact]      
    public async Task Handle_WhenPreviousActionIdHasNoMatchingNextAction_ReturnsPredictionsForPreviousActionId()
    {
        // Arrange
        var previousActionId = "prevAction";
        var actionId = "action1";
        var request = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = actionId,
            PreviousActionId = previousActionId
        };

        var previousPredictions = new List<Domain.Entities.WorkflowPrediction>
        {
            new() { ActionId = previousActionId, NextPossibleId = "otherAction1", Count = 5 },
            new() { ActionId = previousActionId, NextPossibleId = "otherAction2", Count = 3 }
        };

        var expectedMappedResult = new List<WorkflowPredictionListByActionIdVm>
        {
            new(),
            new()
        };

        _mockWorkflowPredictionRepository.Setup(x => x.GetWorkflowPredictionByActionId(previousActionId))
            .ReturnsAsync(previousPredictions);

        _mockMapper.Setup(x => x.Map<List<WorkflowPredictionListByActionIdVm>>(It.IsAny<List<Domain.Entities.WorkflowPrediction>>()))
            .Returns(expectedMappedResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.False(result.Status);
        Assert.Equal(expectedMappedResult, result.MatchedActions);
        _mockWorkflowPredictionRepository.Verify(x => x.GetWorkflowPredictionByActionId(actionId), Times.Never);
    }

    [Fact]
    public async Task Handle_WhenPredictionsExist_ReturnsTop5OrderedByCountThenByDate()
    {
        // Arrange
        var actionId = "action1";
        var request = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = actionId,
            PreviousActionId = null
        };

        var predictions = new List<Domain.Entities.WorkflowPrediction>
        {
            new() { ActionId = actionId, NextPossibleId = "action2", Count = 5, LastModifiedDate = DateTime.Now.AddDays(-1) },
            new() { ActionId = actionId, NextPossibleId = "action3", Count = 5, LastModifiedDate = DateTime.Now },
            new() { ActionId = actionId, NextPossibleId = "action4", Count = 3, LastModifiedDate = DateTime.Now },
            new() { ActionId = actionId, NextPossibleId = "action5", Count = 7, LastModifiedDate = DateTime.Now },
            new() { ActionId = actionId, NextPossibleId = "action6", Count = 1, LastModifiedDate = DateTime.Now },
            new() { ActionId = actionId, NextPossibleId = "action7", Count = 2, LastModifiedDate = DateTime.Now }
        };

        var expectedTop5 = predictions
            .OrderByDescending(x => x.Count)
            .ThenByDescending(x => x.LastModifiedDate)
            .Take(5)
            .ToList();

        var expectedMappedResult = new List<WorkflowPredictionListByActionIdVm>
        {
            new(),
            new(),
            new(),
            new(),
            new()
        };

        _mockWorkflowPredictionRepository.Setup(x => x.GetWorkflowPredictionByActionId(actionId))
            .ReturnsAsync(predictions);

        _mockMapper.Setup(x => x.Map<List<WorkflowPredictionListByActionIdVm>>(expectedTop5))
            .Returns(expectedMappedResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.True(result.Status);
        Assert.Equal(5, result.WorkflowPredictionListByActionIdVms.Count);
        _mockMapper.Verify(x => x.Map<List<WorkflowPredictionListByActionIdVm>>(expectedTop5), Times.Once);
    }


    [Fact]
    public async Task Handle_ShouldCorrectlyMapAllProperties_WhenMappingToViewModel()
    {
        // Arrange
        var actionId = "action1";
        var request = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = actionId,
            PreviousActionId = null
        };

        var testDate = DateTime.UtcNow;
        var predictionEntity = new Domain.Entities.WorkflowPrediction
        {
            ReferenceId = "prediction1",
            ActionId = "action1",
            ActionName = "Test Action",
            Count = 5,
            NextPossibleId = "action2",
            NodeId = "node1",
            NextPossibleActionName = "Next Action",
            LastModifiedDate = testDate
        };

        var predictions = new List<Domain.Entities.WorkflowPrediction> { predictionEntity };

        _mockWorkflowPredictionRepository.Setup(x => x.GetWorkflowPredictionByActionId(actionId))
            .ReturnsAsync(predictions);

        // Setup mapper to verify property mapping
        _mockMapper.Setup(x => x.Map<List<WorkflowPredictionListByActionIdVm>>(It.IsAny<List<Domain.Entities.WorkflowPrediction>>()))
            .Returns((List<Domain.Entities.WorkflowPrediction> source) =>
            {
                return source.Select(e => new WorkflowPredictionListByActionIdVm
                {
                    Id = e.ReferenceId,
                    ActionId = e.ActionId,
                    ActionName = e.ActionName,
                    Count = e.Count,
                    NextPossibleId = e.NextPossibleId,
                    NodeId = e.NodeId,
                    NextPossibleActionName = e.NextPossibleActionName,
                    LastModifiedDate = e.LastModifiedDate.ToString(CultureInfo.InvariantCulture)
                }).ToList();
            });

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        var mappedVm = result.WorkflowPredictionListByActionIdVms.First();

        Assert.Equal(predictionEntity.ReferenceId, mappedVm.Id);
        Assert.Equal(predictionEntity.ActionId, mappedVm.ActionId);
        Assert.Equal(predictionEntity.ActionName, mappedVm.ActionName);
        Assert.Equal(predictionEntity.Count, mappedVm.Count);
        Assert.Equal(predictionEntity.NextPossibleId, mappedVm.NextPossibleId);
        Assert.Equal(predictionEntity.NodeId, mappedVm.NodeId);
        Assert.Equal(predictionEntity.NextPossibleActionName, mappedVm.NextPossibleActionName);
        Assert.Equal(predictionEntity.LastModifiedDate.ToString(CultureInfo.InvariantCulture), mappedVm.LastModifiedDate);
    }

    [Fact]
    public async Task Handle_ShouldHandleNullProperties_WhenMappingToViewModel()
    {
        // Arrange
        var actionId = "action1";
        var request = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = actionId,
            PreviousActionId = null
        };

        var predictionEntity = new Domain.Entities.WorkflowPrediction
        {
            Id = 0,
            ActionId = null,
            ActionName = null,
            Count = 0,
            NextPossibleId = null,
            NodeId = null,
            NextPossibleActionName = null,
            LastModifiedDate = default
        };

        var predictions = new List<Domain.Entities.WorkflowPrediction> { predictionEntity };

        _mockWorkflowPredictionRepository.Setup(x => x.GetWorkflowPredictionByActionId(actionId))
            .ReturnsAsync(predictions);

        // Setup mapper to verify null handling
        _mockMapper.Setup(x => x.Map<List<WorkflowPredictionListByActionIdVm>>(It.IsAny<List<Domain.Entities.WorkflowPrediction>>()))
            .Returns((List<Domain.Entities.WorkflowPrediction> source) =>
            {
                return source.Select(e => new WorkflowPredictionListByActionIdVm
                {
                    Id = e.ReferenceId,
                    ActionId = e.ActionId,
                    ActionName = e.ActionName,
                    Count = e.Count,
                    NextPossibleId = e.NextPossibleId,
                    NodeId = e.NodeId,
                    NextPossibleActionName = e.NextPossibleActionName,
                    LastModifiedDate = e.LastModifiedDate.ToString(CultureInfo.InvariantCulture)
                }).ToList();
            });

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        var mappedVm = result.WorkflowPredictionListByActionIdVms.First();

        Assert.Null(mappedVm.Id);
        Assert.Null(mappedVm.ActionId);
        Assert.Null(mappedVm.ActionName);
        Assert.Equal(0, mappedVm.Count);
        Assert.Null(mappedVm.NextPossibleId);
        Assert.Null(mappedVm.NodeId);
        Assert.Null(mappedVm.NextPossibleActionName);
        Assert.Equal(default(DateTime).ToString(CultureInfo.InvariantCulture), mappedVm.LastModifiedDate);
    }

    // Add this test to verify date formatting specifically
    [Fact]
    public async Task Handle_ShouldFormatDateCorrectly_WhenMappingToViewModel()
    {
        // Arrange
        var actionId = "action1";
        var request = new GetWorkflowPredictionListByActionIdQuery
        {
            ActionId = actionId,
            PreviousActionId = null
        };

        var specificDate = new DateTime(2023, 1, 15, 10, 30, 0);
        var predictionEntity = new Domain.Entities.WorkflowPrediction
        {
            LastModifiedDate = specificDate
        };

        var predictions = new List<Domain.Entities.WorkflowPrediction> { predictionEntity };

        _mockWorkflowPredictionRepository.Setup(x => x.GetWorkflowPredictionByActionId(actionId))
            .ReturnsAsync(predictions);

        _mockMapper.Setup(x => x.Map<List<WorkflowPredictionListByActionIdVm>>(It.IsAny<List<Domain.Entities.WorkflowPrediction>>()))
            .Returns((List<Domain.Entities.WorkflowPrediction> source) =>
            {
                return source.Select(e => new WorkflowPredictionListByActionIdVm
                {
                    LastModifiedDate = e.LastModifiedDate.ToString("yyyy-MM-dd HH:mm:ss")
                }).ToList();
            });

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        var mappedVm = result.WorkflowPredictionListByActionIdVms.First();
        Assert.Equal("2023-01-15 10:30:00", mappedVm.LastModifiedDate);
    }
}