﻿using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Validators;

public class CreateHacmpClusterValidatorTests : IClassFixture<HacmpClusterFixture>
{
    private readonly Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;

    private readonly HacmpClusterFixture _hacmpClusterFixture;

    public CreateHacmpClusterValidatorTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        var hacmpClusters = new Fixture().Create<List<Domain.Entities.HacmpCluster>>();

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.CreateHacmpClusterRepository(hacmpClusters);
    }

    [Theory]
    [AutoHacmpClusterData]
    public async Task Verify_Create_ValidCommand_ShouldPass(CreateHacmpClusterCommand createHacmpClusterCommand)
    {
        var validator = new CreateHacmpClusterCommandValidator(_mockHacmpClusterRepository.Object);

        createHacmpClusterCommand.Name = "TestCluster";
        createHacmpClusterCommand.ServerId = "TestServerId";
        createHacmpClusterCommand.ServerName = "TestServerName";

        var validateResult = await validator.ValidateAsync(createHacmpClusterCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeTrue();
    }
}
