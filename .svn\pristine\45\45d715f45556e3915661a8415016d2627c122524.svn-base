﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetPassword;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Queries;

public class GetWorkflowProfilePasswordQueryHandlerTest
{
    [Fact]
    public async Task Handle_ShouldReturnTrue_When_PasswordsMatch()
    {
        // Arrange
        var password = "MySecurePassword";
        var encryptedPassword = SecurityHelper.Encrypt(password);

        var query = new GetWorkflowProfilePasswordQuery
        {
            WorkflowProfileId = "profile-123",
            Password = encryptedPassword
        };

        var workflowProfile = new Domain.Entities.WorkflowProfile
        {
            ReferenceId = "profile-123",
            Password = encryptedPassword
        };

        var mockRepo = new Mock<IWorkflowProfileRepository>();
        mockRepo.Setup(r => r.GetByReferenceIdAsync(query.WorkflowProfileId))
            .ReturnsAsync(workflowProfile);

        var handler = new GetWorkflowProfilePasswordQueryHandler(mockRepo.Object);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }
    [Fact]
    public async Task Handle_ShouldReturnFalse_When_PasswordsDoNotMatch()
    {
        // Arrange
        var correctPassword = "CorrectPassword";
        var wrongPassword = "WrongPassword";

        var query = new GetWorkflowProfilePasswordQuery
        {
            WorkflowProfileId = "profile-123",
            Password = SecurityHelper.Encrypt(wrongPassword)
        };

        var workflowProfile = new Domain.Entities.WorkflowProfile
        {
            ReferenceId = "profile-123",
            Password = SecurityHelper.Encrypt(correctPassword)
        };

        var mockRepo = new Mock<IWorkflowProfileRepository>();
        mockRepo.Setup(r => r.GetByReferenceIdAsync(query.WorkflowProfileId))
            .ReturnsAsync(workflowProfile);

        var handler = new GetWorkflowProfilePasswordQueryHandler(mockRepo.Object);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }
    [Fact]
    public async Task Handle_ShouldReturnFalse_When_ProfileNotFound()
    {
        // Arrange
        var query = new GetWorkflowProfilePasswordQuery
        {
            WorkflowProfileId = "non-existent-id",
            Password = SecurityHelper.Encrypt("any-password")
        };

        var mockRepo = new Mock<IWorkflowProfileRepository>();
        mockRepo.Setup(r => r.GetByReferenceIdAsync(query.WorkflowProfileId))
            .ReturnsAsync((Domain.Entities.WorkflowProfile?)null);

        var handler = new GetWorkflowProfilePasswordQueryHandler(mockRepo.Object);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

}