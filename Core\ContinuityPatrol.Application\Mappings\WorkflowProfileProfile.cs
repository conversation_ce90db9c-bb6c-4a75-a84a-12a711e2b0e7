﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class WorkflowProfileProfile : Profile
{
    public WorkflowProfileProfile()
    {

        CreateMap<PaginatedResult<Domain.Entities.WorkflowProfile>, PaginatedResult<WorkflowProfileListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

        CreateMap<Domain.Entities.WorkflowProfile, WorkflowProfileListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));


        CreateMap<CreateWorkflowProfileCommand, WorkflowProfileViewModel>().ReverseMap();
        CreateMap<UpdateWorkflowProfileCommand, WorkflowProfileViewModel>().ReverseMap();

        CreateMap<Domain.Entities.WorkflowProfile, CreateWorkflowProfileCommand>().ReverseMap();
        CreateMap<UpdateWorkflowProfileCommand, Domain.Entities.WorkflowProfile>()
            .ForMember(x => x.Id, y => y.Ignore());
        CreateMap<Domain.Entities.WorkflowProfile, WorkflowProfileDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
       
        CreateMap<Domain.Entities.WorkflowProfile, WorkflowProfileNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}