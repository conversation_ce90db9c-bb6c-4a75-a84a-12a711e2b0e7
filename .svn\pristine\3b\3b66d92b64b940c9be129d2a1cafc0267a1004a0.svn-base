﻿using ContinuityPatrol.Application.Features.WorkflowTemp.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowTemp.Commands
{
    public class CreateWorkflowTempTest : IClassFixture<WorkflowTempFixture>
    {
        private readonly WorkflowTempFixture _workflowTempFixture;

        private readonly Mock<IWorkflowTempRepository> _mockWorkflowTempRepository;

        private readonly CreateWorkflowTempCommandHandler _handler;


        public CreateWorkflowTempTest(WorkflowTempFixture workflowTempFixture )
        {
            _workflowTempFixture = workflowTempFixture;

            var mockPublisher = new Mock<IPublisher>();

            _mockWorkflowTempRepository = WorkflowTempRepositoryMocks.CreateWorkflowTempRepository(_workflowTempFixture.WorkflowTempInfos);

            _handler = new CreateWorkflowTempCommandHandler(_workflowTempFixture.Mapper, _mockWorkflowTempRepository.Object, mockPublisher.Object);
           
        }

        [Fact]
        public async Task Handle_Should_Increase_Count_When_AddValid_WorkflowTemp()
        {
            var initialCount = _workflowTempFixture.WorkflowTempInfos.Count;
            await _handler.Handle(_workflowTempFixture.CreateWorkflowTempCommand, CancellationToken.None);

            var allCategories = await _mockWorkflowTempRepository.Object.ListAllAsync();

            allCategories.Count.ShouldBe(initialCount + 1);
        }
        [Fact]
        public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
        {
            _workflowTempFixture.CreateWorkflowTempCommand.WorkflowId = "REQ-123";
            await _handler.Handle(_workflowTempFixture.CreateWorkflowTempCommand, CancellationToken.None);

            _mockWorkflowTempRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowTemp>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_SuccessfulWorkflowTempResponse_When_AddValidWorkflowValues()
        {
            _workflowTempFixture.CreateWorkflowTempCommand.Name = "TestTemp";
            var result = await _handler.Handle(_workflowTempFixture.CreateWorkflowTempCommand, CancellationToken.None);

            result.ShouldBeOfType(typeof(CreateWorkflowTempResponse));

            result.Id.ShouldBeGreaterThan(0.ToString());

            result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
        }
    }
}
