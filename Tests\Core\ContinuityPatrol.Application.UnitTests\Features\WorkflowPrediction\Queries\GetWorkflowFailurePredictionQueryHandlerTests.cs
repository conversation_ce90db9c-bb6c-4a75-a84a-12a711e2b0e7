﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetFailurePrediction;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Queries;

public class GetWorkflowFailurePredictionQueryHandlerTests
{
    private readonly Mock<IWorkflowPredictionRepository> _mockRepository;
    private readonly GetWorkflowFailurePredictionQueryHandler _handler;

    public GetWorkflowFailurePredictionQueryHandlerTests()
    {
        _mockRepository = new Mock<IWorkflowPredictionRepository>();
        _handler = new GetWorkflowFailurePredictionQueryHandler(_mockRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnsFailurePredictions_WhenDataExists()
    {
        // Arrange
        var workflowId = "wf-123";
        var request = new GetWorkflowFailurePredictionQuery { WorkflowId = workflowId };

        var expectedResult = new List<WorkflowFailurePredictionListVm>
        {
            new() { WorkflowId = workflowId,WorkflowName = "Test", Status = "Timeout" },
            new() { WorkflowId = workflowId, Status = "ResourceLimit" }
        };

        _mockRepository.Setup(x => x.GetWorkflowFailurePredictionById(workflowId))
                      .ReturnsAsync(expectedResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Equal(expectedResult, result);
        Assert.Equal(expectedResult[0].Status, result[0].Status);
        Assert.Equal(expectedResult[0].WorkflowId, result[0].WorkflowId);
        Assert.Equal(expectedResult[0].WorkflowName, result[0].WorkflowName);
        _mockRepository.Verify(x => x.GetWorkflowFailurePredictionById(workflowId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenNoDataExists()
    {
        // Arrange
        var workflowId = "wf-456";
        var request = new GetWorkflowFailurePredictionQuery { WorkflowId = workflowId };

        _mockRepository.Setup(x => x.GetWorkflowFailurePredictionById(workflowId))
                      .ReturnsAsync(new List<WorkflowFailurePredictionListVm>());

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.Empty(result);
        _mockRepository.Verify(x => x.GetWorkflowFailurePredictionById(workflowId), Times.Once);
    }

    
    [Fact]
    public async Task Handle_CallsRepositoryWithCorrectWorkflowId()
    {
        // Arrange
        var workflowId = "wf-999";
        var request = new GetWorkflowFailurePredictionQuery { WorkflowId = workflowId };

        _mockRepository.Setup(x => x.GetWorkflowFailurePredictionById(It.IsAny<string>()))
                      .ReturnsAsync(new List<WorkflowFailurePredictionListVm>());

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        _mockRepository.Verify(x => x.GetWorkflowFailurePredictionById(workflowId), Times.Once);
    }
}