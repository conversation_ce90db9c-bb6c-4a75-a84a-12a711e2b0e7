﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;

public class
    GetWorkflowOperationDrDrillReportQueryHandler : IRequestHandler<GetWorkflowOperationDrDrillReportQuery,
        DrDrillReport>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public GetWorkflowOperationDrDrillReportQueryHandler(IMapper mapper,
        IWorkflowOperationRepository workflowOperationRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowActionResultRepository workflowActionResultRepository,
        IInfraObjectRepository infraObjectRepository,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IServerRepository serverRepository,
        IBusinessServiceRepository businessServiceRepository,
        IBusinessFunctionRepository businessFunctionRepository,
        ILoggedInUserService loggedInUserService, IPublisher publisher, IDatabaseRepository databaseRepository,
        ILoadBalancerRepository nodeConfigurationRepository, IWorkflowActionRepository workflowActionRepository)
    {
        _mapper = mapper;
        _workflowOperationRepository = workflowOperationRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowActionResultRepository = workflowActionResultRepository;
        _infraObjectRepository = infraObjectRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _serverRepository = serverRepository;
        _businessServiceRepository = businessServiceRepository;
        _businessFunctionRepository = businessFunctionRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
        _databaseRepository = databaseRepository;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _workflowActionRepository = workflowActionRepository;
    }

    public async Task<DrDrillReport> Handle(GetWorkflowOperationDrDrillReportQuery request, CancellationToken cancellationToken)
    {
        if (request.IsCustom)
        {
            if (!_loggedInUserService.IsSuperAdmin)
            {
                throw new UnauthorizedAccessException("You are not authorized to view this report.");
            }

            var workflowDrDrillList = new WorkflowOperationDrDrillReportVm();
            var workflowOperationGroupList = new WorkflowOperationGroupDrDrillReportVm();
            var workflowActionResultList = await _workflowActionResultRepository.GetWorkflowActionResultByWorkflowOperationId(request.Id);

            var result = _mapper.Map<List<WorkflowActionResultDrDrillReportVm>>(workflowActionResultList);

            foreach (var actionResult in result)
            {
                if (actionResult.ExecutionNode.IsNullOrWhiteSpace()) continue;

                var nodeDtl = await _nodeConfigurationRepository.GetByReferenceIdAsync(actionResult.ExecutionNode);

                nodeDtl ??= new Domain.Entities.LoadBalancer();

                actionResult.ExecutionNode = nodeDtl.Name.IsNullOrWhiteSpace() ? "NA" : nodeDtl.Name;
            }

            workflowOperationGroupList.WorkflowActionResultDrDrillReportVms.AddRange(result);
            workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.Add(workflowOperationGroupList);
            if (workflowOperationGroupList.WorkflowActionResultDrDrillReportVms.Count > 0)
            {
                workflowOperationGroupList.WorkflowActionResultDrDrillReportVms.ForEach(actionlist => {
                    actionlist.TotalTime = GetTotalTime(actionlist.StartTime, actionlist.EndTime);

                });
            }
            return new DrDrillReport
            {
                ReportGeneratedBy = _loggedInUserService.LoginName,
                Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
                WorkflowOperationDrDrillReportVm = workflowDrDrillList
            };
        }
        else
        {
            var workflowActionResultList = new List<Domain.Entities.WorkflowActionResult>();

            // var workflowOperationGroupDrDrill = new WorkflowOperationGroupDrDrillReportVm();

            var workflowActionResultDrDrillList = new List<WorkflowActionResultDrDrillReportVm>();

            var workflowOperation = request.RunMode.IsNotNullOrWhiteSpace()
                ? await _workflowOperationRepository.GetByReferenceIdAndRunMode(request.Id, request.RunMode)
                : await _workflowOperationRepository.GetByReferenceIdAsync(request.Id);

            // var workflowOperation = await _workflowOperationRepository.GetByReferenceIdAndRunMode(request.Id, request.RunMode);

            Guard.Against.NullOrDeactive(workflowOperation, nameof(Domain.Entities.WorkflowOperation),
                new NotFoundException(nameof(Domain.Entities.WorkflowOperation), request.Id));

            var workflowDrDrillList = _mapper.Map<WorkflowOperationDrDrillReportVm>(workflowOperation);

            var workflowOperationGroup =
                await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationIdByReport(workflowOperation
                    .ReferenceId);

           

            workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms =
                _mapper.Map<List<WorkflowOperationGroupDrDrillReportVm>>(workflowOperationGroup);


            var nodeIdList = workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms
                .SelectMany(x => x.NodeId?
                    .Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>())
                .ToList();


            var loadBalancers = nodeIdList!.Count > 0
                ? await _nodeConfigurationRepository.GetNodeNameByIdAsync(nodeIdList)
                : new List<Domain.Entities.LoadBalancer>();

            workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms = workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.Select(groupVm =>
            {
                var nodeNames = groupVm.NodeId?
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => loadBalancers.FirstOrDefault(lb => lb.ReferenceId == id)?.Name)
                    .Where(name => name.IsNotNullOrWhiteSpace())
                    .ToList();

                groupVm.NodeName = nodeNames != null && nodeNames.Any()
                    ? string.Join(",", nodeNames)
                    : string.Empty;

                return groupVm;
            }).ToList();

            workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.ForEach(wfo =>
            {
                wfo.TotalTime = GetTotalTime(wfo.StartTime, wfo.EndTime);

                var infraObjectDrDrill = _infraObjectRepository.GetByReferenceIdAsync(wfo.InfraObjectId).Result;

                var workflowInfraObject = _workflowInfraObjectRepository
                    .GetWorkflowInfraObjectByWorkflowIdAsync(wfo.WorkflowId).Result;

                List<string> PrDbSid = new List<string>();
                List<string> DrDbSid = new List<string>();
                List<string> ProductionIpAddress = new List<string>();
                List<string> DrIpAddress = new List<string>();
                string PRServerName = string.Empty;
                string DRServerName = string.Empty;

                if (infraObjectDrDrill.SubType.IsNotNullOrEmpty() && infraObjectDrDrill.SubType.ToLower().Equals("oracle") && infraObjectDrDrill.ReplicationTypeName.IsNotNullOrEmpty() && infraObjectDrDrill.ReplicationTypeName.ToLower().Equals("native replication-oracle-rac"))
                {
                    var prServerProperties = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                    var prServerId = prServerProperties.SelectToken("PR.id")?.ToString();
                    PRServerName = prServerProperties.SelectToken("PR.name")?.ToString();
                    var prSplitId = prServerId.Split(",");

                    var drServerProperties = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                    var drServerId = drServerProperties.SelectToken("DR.id")?.ToString();
                    DRServerName = drServerProperties.SelectToken("DR.name")?.ToString();
                    var drSplitId = drServerId.Split(",");

                    var prDatabaseProperties = JObject.Parse(infraObjectDrDrill?.DatabaseProperties);
                    var prDatabaseId = prDatabaseProperties.SelectToken("PR.id")?.ToString();
                    var prSidId = prDatabaseId.Split(",");

                    var drDatabaseProperties = JObject.Parse(infraObjectDrDrill?.DatabaseProperties);
                    var drDatabaseId = drDatabaseProperties.SelectToken("DR.id")?.ToString();
                    var drSidId = drDatabaseId.Split(",");

                    foreach (var prSplit in prSplitId)
                    {
                        var prServer = _serverRepository.GetByReferenceIdAsync(prSplit).Result;
                        var prIpAddress = GetJsonProperties.GetIpAddressFromProperties(prServer.Properties);
                        ProductionIpAddress.Add(prIpAddress);
                    }
                    foreach (var drSplit in drSplitId)
                    {
                        var drServer = _serverRepository.GetByReferenceIdAsync(drSplit).Result;
                        var drIpAddress = GetJsonProperties.GetIpAddressFromProperties(drServer.Properties);
                        DrIpAddress.Add(drIpAddress);
                    }
                    foreach (var prSid in prSidId)
                    {
                        var prDatabase = _databaseRepository.GetByReferenceIdAsync(prSid).Result;
                        var prDbSid = GetJsonProperties.GetJsonDatabaseSidValue(prDatabase.Properties);
                        PrDbSid.Add(prDbSid);
                    }
                    foreach (var drSid in drSidId)
                    {
                        var drDatabase = _databaseRepository.GetByReferenceIdAsync(drSid).Result;
                        var drDbSid = GetJsonProperties.GetJsonDatabaseSidValue(drDatabase.Properties);
                        DrDbSid.Add(drDbSid);
                    }
                }
                else
                {
                    var prDatabaseProperties = JObject.Parse(infraObjectDrDrill?.DatabaseProperties);

                    var prDatabaseId = prDatabaseProperties.SelectToken("PR.id")?.ToString();

                    var prDatabase = _databaseRepository.GetByReferenceIdAsync(prDatabaseId).Result;

                    var prDatabaseSId = prDatabase is not null ? GetJsonProperties.GetJsonDatabaseSidValue(prDatabase.Properties) : "-";

                    var drDatabaseProperties = JObject.Parse(infraObjectDrDrill?.DatabaseProperties);

                    var drDatabaseId = drDatabaseProperties.SelectToken("DR.id")?.ToString();

                    var drDatabase = _databaseRepository.GetByReferenceIdAsync(drDatabaseId).Result;

                    var drDatabaseSId = drDatabase is not null ? GetJsonProperties.GetJsonDatabaseSidValue(drDatabase.Properties) : "-";

                    var prServerProperties = JObject.Parse(infraObjectDrDrill?.ServerProperties);

                    var prServerId = prServerProperties.SelectToken("PR.id")?.ToString();

                    var prServer = _serverRepository.GetByReferenceIdAsync(prServerId).Result;

                    var prIpAddress = prServer is not null ? GetJsonProperties.GetIpAddressFromProperties(prServer.Properties) : "-";

                    var drServerProperties = JObject.Parse(infraObjectDrDrill?.ServerProperties);

                    var drServerId = drServerProperties.SelectToken("DR.id")?.ToString();

                    var drServer = _serverRepository.GetByReferenceIdAsync(drServerId).Result;

                    var drIpAddress = drServer is not null ? GetJsonProperties.GetIpAddressFromProperties(drServer.Properties) : "-";

                    //Server Name and Database Name

                    var prServerDetails = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                    var prServerName = prServerDetails.SelectToken("PR.name")?.ToString();
                    PRServerName = prServerName;
                    
                    var drServerDetails = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                    var drServerName = drServerDetails.SelectToken("DR.name")?.ToString();
                   DRServerName = drServerName;

                    ProductionIpAddress.Add(prIpAddress);
                    DrIpAddress.Add(drIpAddress);
                    PrDbSid.Add(prDatabaseSId);
                    DrDbSid.Add(drDatabaseSId);
                }
                if (workflowInfraObject != null && workflowInfraObject.ActionType.Trim().ToLower().Equals("switchback"))
                {
                    wfo.WorkflowActionType = workflowInfraObject.ActionType;
                    wfo.PRServerName = DRServerName;
                    wfo.DRServerName = PRServerName;
                    wfo.PRDatabaseName = DrDbSid;
                    wfo.DRDatabaseName = PrDbSid;
                    wfo.ProductionIpAddress = DrIpAddress;
                    wfo.DrIpAddress = ProductionIpAddress;
                }
                else
                {
                    wfo.WorkflowActionType = workflowInfraObject?.ActionType;
                    wfo.PRServerName = PRServerName;
                    wfo.DRServerName = DRServerName;
                    wfo.PRDatabaseName = PrDbSid;
                    wfo.DRDatabaseName = DrDbSid;
                    wfo.ProductionIpAddress = ProductionIpAddress;
                    wfo.DrIpAddress = DrIpAddress;
                }

                var workflowActionResult = _workflowActionResultRepository
                    .GetWorkflowActionResultByWorkflowOperationIdAndGroupId(wfo.WorkflowOperationId, wfo.ReferenceId)
                    .Result;

                wfo.WorkflowActionResultDrDrillReportVms =
                    _mapper.Map<List<WorkflowActionResultDrDrillReportVm>>(workflowActionResult);

                wfo.WorkflowActionResultDrDrillReportVms.ForEach(wfa =>
                {
                    if (!string.IsNullOrWhiteSpace(wfa.ExecutionNode))
                    {
                        var nodeDtl = _nodeConfigurationRepository.GetByReferenceIdAsync(wfa.ExecutionNode).Result;
                        if (nodeDtl is not null)
                            wfa.ExecutionNode = nodeDtl.Name.IsNullOrWhiteSpace() ? "NA" : nodeDtl.Name;
                        else
                            wfa.ExecutionNode = "NA";

                        wfa.TotalTime = GetTotalTime(wfa.StartTime, wfa.EndTime);
                    }
                    else
                    {
                        wfa.ExecutionNode = "NA";
                        wfa.TotalTime = TimeSpan.Zero;
                    }
                });

                workflowActionResultList.AddRange(workflowActionResult);

                workflowActionResultDrDrillList.AddRange(wfo.WorkflowActionResultDrDrillReportVms);

                var businessService = _businessServiceRepository
                    .GetByReferenceIdAsync(infraObjectDrDrill?.BusinessServiceId).Result;

                var businessFunction = _businessFunctionRepository
                    .GetByReferenceIdAsync(infraObjectDrDrill?.BusinessFunctionId).Result;

                workflowDrDrillList.BusinessServiceDrDillDetails =
                    _mapper.Map<BusinessServiceDrDillDetails>(businessService);

                workflowDrDrillList.BusinessServiceDrDillDetails.ConfiguredRTO = businessFunction?.ConfiguredRTO;

                wfo.ConfiguredRTO = TimeSpan.FromMinutes(int.Parse(businessFunction!.ConfiguredRTO)).ToString();

                if (workflowActionResultDrDrillList.Count > 0)
                {
                    var startRTO = "start_workflowrto"; var stopRTO = "stop_workflowrto";

                    var startAction = _workflowActionRepository.GetWorkflowActionDetailsByName(startRTO).Result;
                    var stopAction = _workflowActionRepository.GetWorkflowActionDetailsByName(stopRTO).Result;

                    var startList = workflowActionResult.Where(x => x.ActionId.Equals(startAction?.ReferenceId)).ToList();
                    var stopList = workflowActionResult.Where(x => x.ActionId.Equals(stopAction?.ReferenceId)).ToList();

                    if (startList != null && stopList != null && startList.Count > 0 && stopList.Count > 0)
                    {
                        var startTime = startList.FirstOrDefault()?.StartTime.ToString();
                        var endTime = stopList.FirstOrDefault()?.EndTime.ToString();
                        wfo.ActualRTOStartTime = startTime;
                        wfo.ActualRTOEndTime = endTime;
                        wfo.ActualRTO = GetTotalTime(wfo.ActualRTOStartTime, wfo.ActualRTOEndTime);
                        wfo.ConfiguredRTOLessActualRTO = GetTotalTime(wfo.ActualRTO.ToString(), wfo.ConfiguredRTO);
                    }
                    else
                    {
                        var startTime = workflowActionResult.Min(x => x.StartTime).ToString();
                        var endTime = workflowActionResult.Max(x => x.EndTime).ToString();
                        wfo.ActualRTOStartTime = startTime;
                        wfo.ActualRTOEndTime = endTime;
                        wfo.ActualRTO = GetTotalTime(wfo.ActualRTOStartTime, wfo.ActualRTOEndTime);
                        wfo.ConfiguredRTOLessActualRTO = GetTotalTime(wfo.ActualRTO.ToString(), wfo.ConfiguredRTO);
                    }
                }
            });

            if (workflowActionResultList.Count > 0)
            {
                var drillStartTime = workflowDrDrillList.StartTime;
                var drillEndTime = workflowDrDrillList.EndTime;

                workflowDrDrillList.StartTime = drillStartTime;
                workflowDrDrillList.EndTime = drillEndTime;
                workflowDrDrillList.TotalTime = GetTotalTime(drillStartTime, drillEndTime);

                var actualRtoStartTime = workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.
                    SelectMany(x => x.WorkflowActionResultDrDrillReportVms).Min(x => x.StartTime);

                var actualRtoEndTime = workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.
                    SelectMany(x => x.WorkflowActionResultDrDrillReportVms).Max(x => x.EndTime);

                workflowDrDrillList.ActualRTOStartTime = actualRtoStartTime;
                workflowDrDrillList.ActualRTOEndTime = actualRtoEndTime;

                workflowDrDrillList.ActualRTO = GetTotalTime(actualRtoStartTime, actualRtoEndTime);
                workflowDrDrillList.TotalActionExecutedCount = workflowActionResultDrDrillList.Count;
            }

            workflowDrDrillList.ConfiguredRTO = TimeSpan
                .FromMinutes(int.Parse(workflowDrDrillList.BusinessServiceDrDillDetails.ConfiguredRTO)).ToString();

            workflowDrDrillList.ConfiguredRTOLessActualRTO = GetTotalTime(workflowDrDrillList.ActualRTO.ToString(),
                workflowDrDrillList.ConfiguredRTO);

            workflowDrDrillList.TotalProfilesExecutedCount = 1;

            workflowDrDrillList.TotalWorkflowExecutedCount =
                workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.Count;

            workflowDrDrillList.TotalWorkflowActionCount += workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms
                .Sum(count => int.TryParse(count.ProgressStatus?.Split('/').LastOrDefault(), out var actionCount)
                    ? actionCount
                    : 0);
            if (request.RunMode.IsNotNullOrWhiteSpace())
            {
                await _publisher.Publish(
                    new ReportViewedEvent { ReportName = "DR Drill Report", ActivityType = ActivityType.View.ToString() },
                    CancellationToken.None);
            }
            return new DrDrillReport
            {
                ReportGeneratedBy = _loggedInUserService.LoginName,
                Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
                WorkflowOperationDrDrillReportVm = workflowDrDrillList
            };
        }

    }

    private static TimeSpan GetTotalTime(string startTime, string endTime)
    {
        try
        {
            var start = DateTime.Parse(startTime);
            var end = DateTime.Parse(endTime);
            var ts = end - start;
            return ts;
        }
        catch (Exception)
        {
            return TimeSpan.Zero;
        }
    }
}