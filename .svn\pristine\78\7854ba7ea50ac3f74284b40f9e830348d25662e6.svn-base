﻿using ContinuityPatrol.Application.Features.HacmpCluster.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Events;

public class UpdateHacmpClusterEventTests : IClassFixture<HacmpClusterFixture>, IClassFixture<UserActivityFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly HacmpClusterUpdatedEventHandler _handler;

    public UpdateHacmpClusterEventTests(HacmpClusterFixture hacmpClusterFixture, UserActivityFixture userActivityFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");

        var mockHacmpClusterEventLogger = new Mock<ILogger<HacmpClusterUpdatedEventHandler>>();

        _mockUserActivityRepository = HacmpClusterRepositoryMocks.CreateHacmpClusterEventRepository(_userActivityFixture.UserActivities);

        _handler = new HacmpClusterUpdatedEventHandler(mockLoggedInUserService.Object, mockHacmpClusterEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateHacmpClusterEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_hacmpClusterFixture.HacmpClusterUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_hacmpClusterFixture.HacmpClusterUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}