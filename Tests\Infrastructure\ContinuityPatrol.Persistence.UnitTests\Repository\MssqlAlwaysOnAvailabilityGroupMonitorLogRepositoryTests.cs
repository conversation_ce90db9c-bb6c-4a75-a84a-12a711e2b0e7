using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;
using System.Reflection;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryTests : IClassFixture<MssqlAlwaysOnAvailabilityGroupMonitorLogFixture>
{
    private readonly MssqlAlwaysOnAvailabilityGroupMonitorLogFixture _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly MssqlAlwaysOnAvailabilityGroupMonitorLogRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public MssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryTests(MssqlAlwaysOnAvailabilityGroupMonitorLogFixture mssqlAlwaysOnAvailabilityGroupMonitorLogFixture)
    {
        _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture = mssqlAlwaysOnAvailabilityGroupMonitorLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = ConfigurationRepositoryMocks.GetConnectionString();
        
        _repository = new MssqlAlwaysOnAvailabilityGroupMonitorLogRepository(_dbContext, _mockConfiguration.Object);
    }

    private async Task ClearDatabase()
    {
        _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.RemoveRange(_dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var mssqlAlwaysOnAvailabilityGroupMonitorLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.MssqlAlwaysOnAvailabilityGroupMonitorLogDto;

        // Act
        var result = await _repository.AddAsync(mssqlAlwaysOnAvailabilityGroupMonitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(mssqlAlwaysOnAvailabilityGroupMonitorLog.Type, result.Type);
        Assert.Equal(mssqlAlwaysOnAvailabilityGroupMonitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var mssqlAlwaysOnAvailabilityGroupMonitorLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.MssqlAlwaysOnAvailabilityGroupMonitorLogDto;
        var addedEntity = await _repository.AddAsync(mssqlAlwaysOnAvailabilityGroupMonitorLog);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var mssqlAlwaysOnAvailabilityGroupMonitorLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.MssqlAlwaysOnAvailabilityGroupMonitorLogDto;
        await _repository.AddAsync(mssqlAlwaysOnAvailabilityGroupMonitorLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(mssqlAlwaysOnAvailabilityGroupMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(mssqlAlwaysOnAvailabilityGroupMonitorLog.ReferenceId, result.ReferenceId);
        Assert.Equal(mssqlAlwaysOnAvailabilityGroupMonitorLog.Type, result.Type);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenReferenceIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var mssqlAlwaysOnAvailabilityGroupMonitorLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.MssqlAlwaysOnAvailabilityGroupMonitorLogDto;
        var addedEntity = await _repository.AddAsync(mssqlAlwaysOnAvailabilityGroupMonitorLog);
        
        addedEntity.Type = "UpdatedType";
        addedEntity.InfraObjectName = "UpdatedName";

        // Act
        await _repository.UpdateAsync(addedEntity);
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedType", result.Type);
        Assert.Equal("UpdatedName", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeleteEntity()
    {
        // Arrange
        var mssqlAlwaysOnAvailabilityGroupMonitorLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.MssqlAlwaysOnAvailabilityGroupMonitorLogDto;
        var addedEntity = await _repository.AddAsync(mssqlAlwaysOnAvailabilityGroupMonitorLog);

        // Act
        await _repository.DeleteAsync(addedEntity);
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnLogs_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var log = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(type: "TestType", isActive: true);
        await _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType("TestType");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("TestType", result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var activeLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(type: "TestType", isActive: true);
        var inactiveLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(type: "TestType", isActive: false);
        
         _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddRange(new[] { activeLog, inactiveLog });
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType("TestType");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespaceInType()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithWhitespace();
        whitespaceLog.IsActive = true;
        await _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddAsync(whitespaceLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(whitespaceLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(whitespaceLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleVeryLongTypeNames()
    {
        // Arrange
        await ClearDatabase();
        var longTypeLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithLongType(1000);
        longTypeLog.IsActive = true;
        await _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddAsync(longTypeLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(longTypeLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(longTypeLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleMultipleCommonTypes()
    {
        // Arrange
        await ClearDatabase();
        var commonTypes = MssqlAlwaysOnAvailabilityGroupMonitorLogFixture.TestData.CommonTypes;
        var logs = new List<MssqlAlwaysOnAvailabilityGroupMonitorLog>();

        foreach (var type in commonTypes)
        {
            var log = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(type: type, isActive: true);
            logs.Add(log);
        }

        await _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var type in commonTypes)
        {
            var result = await _repository.GetDetailByType(type);
            Assert.Single(result);
            Assert.Equal(type, result.First().Type);
            Assert.True(result.First().IsActive);
        }
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnLogs_WhenDataExists()
    {
        // Arrange
        await ClearDatabase();
        var log = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

        await _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        // Create testable repository that returns false for table existence
        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("infra1", result[0].InfraObjectId);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenNoDataExists()
    {
        // Arrange
        await ClearDatabase();
        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("nonexistent", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldFilterByDateRange()
    {
        // Arrange
        await ClearDatabase();
        var oldLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.AddDays(-10));

        var recentLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

         _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddRange(new[] { oldLog, recentLog });
         _dbContext.SaveChanges();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(recentLog.ReferenceId, result[0].ReferenceId);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnOnlyActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var activeLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

        var inactiveLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: false,
            createdDate: DateTime.UtcNow.Date);

         _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddRange(new[] { activeLog, inactiveLog });
         _dbContext.SaveChanges();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleNullParameters()
    {
        // Arrange
        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act & Assert
        var result1 = await repo.GetByInfraObjectId(null, "2023-01-01", "2023-01-02");
        var result2 = await repo.GetByInfraObjectId("infra1", null, "2023-01-02");
        var result3 = await repo.GetByInfraObjectId("infra1", "2023-01-01", null);

        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnCombinedLogs_WhenBackupTableExists()
    {
        // Arrange
        await ClearDatabase();
        var log = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

        await _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        // Create testable repository that returns true for table existence
        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository(_dbContext, _mockConfiguration.Object, true);

        // Act & Assert - Should not throw exception even if backup table query fails
        try
        {
            var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);
            Assert.NotNull(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without actual backup table
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    #endregion

    #region GetTableName Tests

    [Fact]
    public void GetTableName_ShouldReturnCorrectTableName()
    {
        // Act
        var tableName = _repository.GetTableName<MssqlAlwaysOnAvailabilityGroupMonitorLog>();

        // Assert
        Assert.NotNull(tableName);
        Assert.NotEmpty(tableName);
    }

    [Fact]
    public void GetTableName_ShouldReturnNull_ForUnmappedEntity()
    {
        // Act
        var tableName = _repository.GetTableName<UnmappedEntity>();

        // Assert
        Assert.Null(tableName);
    }

    #endregion

    #region IsTableExistAsync Tests

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleOracleProvider()
    {
        // Arrange
        var tableName = "MssqlAlwaysOnAvailabilityGroupMonitorLogs";
        var schemaName = "TESTSCHEMA";
        var providerName = "oracle";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without Oracle connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleMsSqlProvider()
    {
        // Arrange
        var tableName = "MssqlAlwaysOnAvailabilityGroupMonitorLogs";
        var schemaName = "dbo";
        var providerName = "mssql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleMySqlProvider()
    {
        // Arrange
        var tableName = "MssqlAlwaysOnAvailabilityGroupMonitorLogs";
        var schemaName = "testdb";
        var providerName = "mysql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without MySQL connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandlePostgreSqlProvider()
    {
        // Arrange
        var tableName = "MssqlAlwaysOnAvailabilityGroupMonitorLogs";
        var schemaName = "public";
        var providerName = "npgsql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without PostgreSQL connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleCaseInsensitiveProviderName()
    {
        // Arrange
        var tableName = "MssqlAlwaysOnAvailabilityGroupMonitorLogs";
        var schemaName = "dbo";
        var providerName = "MSSQL"; // Uppercase

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    #endregion

    #region GetDatabaseNameFromConnectionString Tests

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForMySql()
    {
        // Arrange
        var connectionString = "Server=localhost;Database=testdb;Uid=user;Pwd=password;";
        var provider = "mysql";
        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act
        var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

        // Assert
        Assert.Equal("testdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForMsSql()
    {
        // Arrange
        var connectionString = "Server=localhost;Database=testdb;Trusted_Connection=true;";
        var provider = "mssql";
        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act
        var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

        // Assert
        Assert.Equal("testdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForPostgreSql()
    {
        // Arrange
        var connectionString = "Host=localhost;Database=testdb;Username=user;Password=password;";
        var provider = "npgsql";
        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act
        var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

        // Assert
        Assert.Equal("testdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldThrow_ForUnsupportedProvider()
    {
        // Arrange
        var connectionString = "some connection string";
        var provider = "unsupported";
        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act & Assert
        Assert.Throws<TargetInvocationException>(() => repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider));
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnEmptyAsDatabase_WhenDatabaseNotFound()
    {
        // Arrange
        var connectionString = "Server=localhost;User Id=testuser;Trusted_Connection=true;"; // No Database parameter
        var provider = "mssql";
        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act
        var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

        // Assert
        Assert.Empty(result); // Should return User Id in uppercase
    }

    #endregion

    #region Edge Case Tests

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialType = "MSSQLAlwaysOn@#$%^&*()";
        var log = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(type: specialType, isActive: true);
        await _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(specialType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(specialType, result[0].Type);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleSpecialCharactersInInfraObjectId()
    {
        // Arrange
        await ClearDatabase();
        var specialInfraId = "infra@#$%^&*()";
        var log = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(
            infraObjectId: specialInfraId,
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

        await _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId(specialInfraId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(specialInfraId, result[0].InfraObjectId);
    }

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var log = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties();
            tasks.Add(_repository.AddAsync(log));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.Equal(10, allLogs.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var activeLogs = new List<MssqlAlwaysOnAvailabilityGroupMonitorLog>
        {
            _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(isActive: true),
            _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(isActive: true)
        };
        var inactiveLog = _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(isActive: false);

         _dbContext.MssqlAlwaysOnAvailabilityGroupMonitorLogs.AddRange(activeLogs.Concat(new[] { inactiveLog }));
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.True(log.IsActive));
    }

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var logs = new List<MssqlAlwaysOnAvailabilityGroupMonitorLog>
        {
            _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(),
            _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(),
            _mssqlAlwaysOnAvailabilityGroupMonitorLogFixture.CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties()
        };

        // Act
        await _repository.AddRangeAsync(logs);

        // Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.Equal(3, allLogs.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region Helper Classes and Methods

    private class UnmappedEntity { }

    // Helper class to override IsTableExistAsync for testing
    private class TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository : MssqlAlwaysOnAvailabilityGroupMonitorLogRepository
    {
        private readonly bool _tableExists;

        public TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepository(ApplicationDbContext dbContext, IConfiguration config, bool tableExists)
            : base(dbContext, config)
        {
            _tableExists = tableExists;
        }

        public override async Task<bool> IsTableExistAsync(string tableName, string schemaName, string providerName)
        {
            // Simulate async operation
            await Task.Delay(1);
            return _tableExists;
        }
    }

    // Helper class to expose private methods for testing
    private class TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryWithPublicMethods : MssqlAlwaysOnAvailabilityGroupMonitorLogRepository
    {
        public TestableMssqlAlwaysOnAvailabilityGroupMonitorLogRepositoryWithPublicMethods(ApplicationDbContext dbContext, IConfiguration config)
            : base(dbContext, config)
        {
        }

        public string GetDatabaseNameFromConnectionStringPublic(string connectionString, string provider)
        {
            // Use reflection to call the private method
            var method = typeof(MssqlAlwaysOnAvailabilityGroupMonitorLogRepository).GetMethod("GetDatabaseNameFromConnectionString",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (string)method.Invoke(this, new object[] { connectionString, provider });
        }
    }

    #endregion
}
