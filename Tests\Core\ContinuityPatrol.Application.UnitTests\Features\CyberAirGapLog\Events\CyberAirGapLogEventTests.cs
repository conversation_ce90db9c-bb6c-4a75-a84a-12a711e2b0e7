using ContinuityPatrol.Application.Features.CyberAirGapLog.Events.Create;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Events.Update;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapLog.Events;

public class CyberAirGapLogEventTests : IClassFixture<CyberAirGapLogFixture>
{
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;

    public CyberAirGapLogEventTests(CyberAirGapLogFixture cyberAirGapLogFixture)
    {
        _cyberAirGapLogFixture = cyberAirGapLogFixture;
    }

    [Fact]
    public void CyberAirGapLogCreatedEvent_CreateWithCorrectProperties_When_ValidData()
    {
        // Arrange
        var airGapId = Guid.NewGuid().ToString();
        var airGapName = "Enterprise Air Gap System";
        var sourceSiteName = "Production Data Center";
        var targetSiteName = "Disaster Recovery Center";
        var status = "Active";

        // Act
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
           
            Name = airGapName
        };

        // Assert
        createdEvent.ShouldNotBeNull();
        createdEvent.ShouldBeOfType<CyberAirGapLogCreatedEvent>();
    }

    [Fact]
    public void CyberAirGapLogUpdatedEvent_CreateWithCorrectProperties_When_ValidData()
    {
        // Arrange
        var airGapId = Guid.NewGuid().ToString();
        var airGapName = "Updated Enterprise Air Gap System";
        var sourceSiteName = "Updated Production Data Center";
        var targetSiteName = "Updated Disaster Recovery Center";
        var status = "Warning";

        // Act
        var updatedEvent = new CyberAirGapLogUpdatedEvent
        {
          
            Name = airGapName
        };

        // Assert
        updatedEvent.ShouldNotBeNull();
        updatedEvent.ShouldBeOfType<CyberAirGapLogUpdatedEvent>();
    }

    /// <summary>
    /// Test: CyberAirGapLogDeletedEvent creation and properties
    /// Expected: Event is created with correct properties
    /// </summary>
    [Fact]
    public void CyberAirGapLogDeletedEvent_CreateWithCorrectProperties_When_ValidData()
    {
        // Arrange
        var airGapId = Guid.NewGuid().ToString();
        var airGapName = "Deleted Enterprise Air Gap System";
        var sourceSiteName = "Deleted Production Data Center";
        var targetSiteName = "Deleted Disaster Recovery Center";

        // Act
        var deletedEvent = new CyberAirGapLogDeletedEvent
        {
           
            Name = airGapName,
           
        };

        // Assert
        deletedEvent.ShouldNotBeNull();
        deletedEvent.ShouldBeOfType<CyberAirGapLogDeletedEvent>();
    }

    [Fact]
    public void CyberAirGapLogEvents_ImplementINotification_When_Created()
    {
        // Arrange & Act
        var createdEvent = new CyberAirGapLogCreatedEvent();
        var updatedEvent = new CyberAirGapLogUpdatedEvent();
        var deletedEvent = new CyberAirGapLogDeletedEvent();

        // Assert
        createdEvent.ShouldBeAssignableTo<INotification>();
        updatedEvent.ShouldBeAssignableTo<INotification>();
        deletedEvent.ShouldBeAssignableTo<INotification>();
    }

    [Fact]
    public void CyberAirGapLogEvents_HandleNullProperties_When_Created()
    {
        // Arrange & Act
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
            Name = null,
        };

        var updatedEvent = new CyberAirGapLogUpdatedEvent
        {
            Name = null,
        };

        var deletedEvent = new CyberAirGapLogDeletedEvent
        {
            Name = null,
        };

        // Assert
        createdEvent.ShouldNotBeNull();
        createdEvent.Name.ShouldBeNull();
        updatedEvent.ShouldNotBeNull();
        updatedEvent.Name.ShouldBeNull();
        deletedEvent.ShouldNotBeNull();
        deletedEvent.Name.ShouldBeNull();
    }

    [Fact]
    public void CyberAirGapLogEvents_HandleEmptyStringProperties_When_Created()
    {
        // Arrange & Act
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
            Name = "",
        };

        var updatedEvent = new CyberAirGapLogUpdatedEvent
        {
            Name = "",
        };

        var deletedEvent = new CyberAirGapLogDeletedEvent
        {
            Name = "",
        };

        // Assert
        createdEvent.ShouldNotBeNull();
        createdEvent.Name.ShouldBe("");
        updatedEvent.ShouldNotBeNull();
        updatedEvent.Name.ShouldBe("");
        deletedEvent.ShouldNotBeNull();
        deletedEvent.Name.ShouldBe("");
    }

    [Fact]
    public void CyberAirGapLogEvents_HandleSpecialCharacters_When_Created()
    {
        // Arrange
        var specialAirGapName = "Special Characters Test & <script>alert('xss')</script>";
        var specialSourceSite = "Source Site with émojis 🚀💻📊";
        var specialTargetSite = "Target Site with unicode 测试数据";
        var specialStatus = "Status with chars!@#$%^&*()_+-=[]{}|;':,.<>?";

        // Act
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
            Name = specialAirGapName,
        };

        var updatedEvent = new CyberAirGapLogUpdatedEvent
        {
            Name = specialAirGapName,
        };

        var deletedEvent = new CyberAirGapLogDeletedEvent
        {
            Name = specialAirGapName,
        };

        // Assert
        createdEvent.ShouldNotBeNull();
        createdEvent.Name.ShouldContain("Special Characters Test");

        updatedEvent.ShouldNotBeNull();
        updatedEvent.Name.ShouldContain("Special Characters Test");

        deletedEvent.ShouldNotBeNull();
        deletedEvent.Name.ShouldContain("Special Characters Test");
    }

    [Fact]
    public void CyberAirGapLogEvents_HandleDifferentAirGapTypes_When_Created()
    {
        // Arrange
        var airGapTypes = new[] { "Database", "File", "Archive", "Backup", "Network", "Security", "Monitoring" };

        foreach (var airGapType in airGapTypes)
        {
            // Act
            var createdEvent = new CyberAirGapLogCreatedEvent
            {
                Name = $"{airGapType} Air Gap System",
            };

            var updatedEvent = new CyberAirGapLogUpdatedEvent
            {
              
                Name = $"Updated {airGapType} Air Gap System",
               
            };

            var deletedEvent = new CyberAirGapLogDeletedEvent
            {
               
                Name = $"Deleted {airGapType} Air Gap System",
              
            };

            // Assert
            createdEvent.ShouldNotBeNull();
            createdEvent.Name.ShouldContain(airGapType);

            updatedEvent.ShouldNotBeNull();
            updatedEvent.Name.ShouldContain(airGapType);

            deletedEvent.ShouldNotBeNull();
            deletedEvent.Name.ShouldContain(airGapType);
        }
    }

    [Fact]
    public void CyberAirGapLogEvents_AllowPropertyModification_When_Created()
    {
        // Arrange
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
            Name = "Original Name",
        };

        // Act
        createdEvent.Name = "Modified Name";

        // Assert
        createdEvent.Name.ShouldBe("Modified Name");
    }

    [Fact]
    public void CyberAirGapLogEvents_SerializeCorrectly_When_Created()
    {
        // Arrange
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
            Name = "Serialization Test Air Gap"
        };

        // Act
        var json = System.Text.Json.JsonSerializer.Serialize(createdEvent);
        var deserializedEvent = System.Text.Json.JsonSerializer.Deserialize<CyberAirGapLogCreatedEvent>(json);

        // Assert
        deserializedEvent.ShouldNotBeNull();
        deserializedEvent.Name.ShouldBe(createdEvent.Name);
    }

    [Fact]
    public void CyberAirGapLogEvents_HandleComplexConfigurations_When_Created()
    {
        // Arrange
        var complexAirGapName = "Multi-Tier Enterprise Air Gap with Database Cluster Replication";
        var complexSourceSite = "Primary Production Data Center - Zone A (High Availability Cluster)";
        var complexTargetSite = "Secondary Disaster Recovery Data Center - Zone B (Standby Cluster)";
        var complexStatus = "Active - Real-time Synchronization with 99.99% Uptime";

        // Act
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
          
            Name = complexAirGapName
        };

        var updatedEvent = new CyberAirGapLogUpdatedEvent
        {
            Name = $"Updated {complexAirGapName}",
        };

        var deletedEvent = new CyberAirGapLogDeletedEvent
        {
            Name = $"Deleted {complexAirGapName}",
        };

        // Assert
        createdEvent.ShouldNotBeNull();
        createdEvent.Name.ShouldContain("Multi-Tier Enterprise");

        updatedEvent.ShouldNotBeNull();
        updatedEvent.Name.ShouldContain("Updated Multi-Tier Enterprise");

        deletedEvent.ShouldNotBeNull();
        deletedEvent.Name.ShouldContain("Deleted Multi-Tier Enterprise");
    }

    [Theory]
    [InlineData("A", "Minimum length name")]
    [InlineData("Very Long Air Gap System Name That Exceeds Normal Length Expectations For Testing Boundary Conditions", "Maximum length name")]
    [InlineData("Normal Air Gap", "Normal length name")]
    public async Task CyberAirGapLogEvents_HandleBoundaryValues_When_Created(string airGapName, string description)
    {
        // Arrange & Act
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
            Name = airGapName
        };

        var updatedEvent = new CyberAirGapLogUpdatedEvent
        {
           
            Name = $"Updated {airGapName}",
           
        };

        var deletedEvent = new CyberAirGapLogDeletedEvent
        {
          
            Name = $"Deleted {airGapName}",
          
        };

        // Assert
        createdEvent.ShouldNotBeNull();
        createdEvent.Name.ShouldBe(airGapName);
      

        updatedEvent.ShouldNotBeNull();
        updatedEvent.Name.ShouldContain(airGapName);

        deletedEvent.ShouldNotBeNull();
        deletedEvent.Name.ShouldContain(airGapName);
    }

    [Theory]
    [InlineData("Active", "System is running normally")]
    [InlineData("Warning", "System has minor issues")]
    [InlineData("Error", "System has critical errors")]
    [InlineData("Maintenance", "System is under maintenance")]
    [InlineData("Disabled", "System is temporarily disabled")]
    [InlineData("Unknown", "System status is unknown")]
    public async Task CyberAirGapLogEvents_HandleDifferentStatusValues_When_Created(string status, string description)
    {
        // Arrange & Act
        var createdEvent = new CyberAirGapLogCreatedEvent
        {
            Name = $"Air Gap with {status} Status",
        };

        var updatedEvent = new CyberAirGapLogUpdatedEvent
        {
            Name = $"Updated Air Gap with {status} Status",
        };

        // Assert
        createdEvent.ShouldNotBeNull();
        createdEvent.Name.ShouldContain(status);

        updatedEvent.ShouldNotBeNull();
        updatedEvent.Name.ShouldContain(status);
    }
}
