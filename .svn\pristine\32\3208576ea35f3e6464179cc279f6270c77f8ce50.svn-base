using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Queries;

public class GetCyberAirGapDetailTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<ICyberAirGapRepository> _mockCyberAirGapRepository;
    private readonly GetCyberAirGapDetailsQueryHandler _handler;

    public GetCyberAirGapDetailTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockCyberAirGapRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(_cyberAirGapFixture.CyberAirGaps);

        _handler = new GetCyberAirGapDetailsQueryHandler(
            _cyberAirGapFixture.Mapper,
            _mockCyberAirGapRepository.Object);
    }

    //[Fact]
    //public async Task Handle_GetCyberAirGapDetail_When_ValidId()
    //{
    //    // Arrange
    //    var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
    //    var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };
    //    _mockCyberAirGapRepository
    // .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
    // .ReturnsAsync(existingAirGap);
    //    // Act
    //    var result = await _handler.Handle(query, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //}

    [Fact]
    public async Task Handle_CallGetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockCyberAirGapRepository.Verify(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId), Times.Once);
    }

    /// <summary>
    /// Test: NotFoundException when air gap doesn't exist
    /// Expected: Throws NotFoundException with correct message
    /// </summary>
    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var query = new GetCyberAirGapDetailQuery { Id = nonExistentId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    /// <summary>
    /// Test: NotFoundException when air gap is inactive
    /// Expected: Throws NotFoundException for inactive entities
    /// </summary>
    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AirGapIsInactive()
    {
        // Arrange
        var inactiveAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        inactiveAirGap.IsActive = false;
        var query = new GetCyberAirGapDetailQuery { Id = inactiveAirGap.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    //[Fact]
    //public async Task Handle_VerifyCompleteMapping_When_ValidId()
    //{
    //    // Arrange
    //    var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
    //    var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

    //    _mockCyberAirGapRepository
    // .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
    // .ReturnsAsync(existingAirGap);
    //    // Act
    //    var result = await _handler.Handle(query, CancellationToken.None);

    //    // Assert - Verify all properties are mapped
    //    result.ShouldNotBeNull();
    //}

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

    }

    [Fact]
    public async Task Handle_GetDetailForMultipleAirGaps_When_ValidIds()
    {
        // Arrange
        var airGaps = _cyberAirGapFixture.CyberAirGaps.Take(3).ToList();

        // Act & Assert
        foreach (var airGap in airGaps)
        {
            var query = new GetCyberAirGapDetailQuery { Id = airGap.ReferenceId };
            var result = await _handler.Handle(query, CancellationToken.None);

            result.ShouldNotBeNull();
            result.Id.ShouldBe(airGap.ReferenceId);
            result.Name.ShouldBe(airGap.Name);
            result.Description.ShouldBe(airGap.Description);
        }
    }

    
    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_ValidId()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    
    //[Fact]
    //public async Task Handle_PreserveJsonProperties_When_ValidId()
    //{
    //    // Arrange
    //    var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
    //    var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

    //    _mockCyberAirGapRepository
    // .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
    // .ReturnsAsync(existingAirGap);
    //    // Act
    //    var result = await _handler.Handle(query, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
        
    //    // Verify JSON structure is preserved
    //    if (!string.IsNullOrEmpty(result.Source))
    //    {
    //        result.Source.ShouldStartWith("{");
    //        result.Source.ShouldEndWith("}");
    //    }
        
    //    if (!string.IsNullOrEmpty(result.Target))
    //    {
    //        result.Target.ShouldStartWith("{");
    //        result.Target.ShouldEndWith("}");
    //    }
    //}

    
    //[Fact]
    //public async Task Handle_PreservePortNumber_When_ValidId()
    //{
    //    // Arrange
    //    var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
    //    var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

    //    _mockCyberAirGapRepository
    // .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
    // .ReturnsAsync(existingAirGap);
    //    // Act
    //    var result = await _handler.Handle(query, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //}

    /// <summary>
    /// Test: Component information is correctly mapped
    /// Expected: Source and target component details are preserved
    /// </summary>
    [Fact]
    public async Task Handle_PreserveComponentInformation_When_ValidId()
    {
        // Arrange
        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
        var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    //[Fact]
    //public async Task Handle_PreserveWorkflowInformation_When_ValidId()
    //{
    //    // Arrange
    //    var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
    //    var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

    //    _mockCyberAirGapRepository
    // .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
    // .ReturnsAsync(existingAirGap);
    //    // Act
    //    var result = await _handler.Handle(query, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //}

    
    //[Fact]
    //public async Task Handle_PreserveSiteInformation_When_ValidId()
    //{
    //        // Arrange
    //        var existingAirGap = _cyberAirGapFixture.CyberAirGaps.First();
    //        Assert.False(string.IsNullOrEmpty(existingAirGap.ReferenceId), "ReferenceId must not be null or empty for test setup.");

    //        var query = new GetCyberAirGapDetailQuery { Id = existingAirGap.ReferenceId };

    //        _mockCyberAirGapRepository
    //            .Setup(x => x.GetByReferenceIdAsync(existingAirGap.ReferenceId))
    //            .ReturnsAsync(existingAirGap);

    //        // Act
    //        var result = await _handler.Handle(query, CancellationToken.None);

    //        // Assert
    //        result.ShouldNotBeNull();
    //}
}
