﻿using ContinuityPatrol.Application.Features.GlobalVariable.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Events;

public class CreateGlobalVariableEventTests : IClassFixture<GlobalVariableFixture>, IClassFixture<UserActivityFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly GlobalVariableCreatedEventHandler _handler;

    public CreateGlobalVariableEventTests(GlobalVariableFixture globalVariableFixture, UserActivityFixture userActivityFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");

        var mockGlobalVariableEventLogger = new Mock<ILogger<GlobalVariableCreatedEventHandler>>();

        _mockUserActivityRepository = GlobalVariableRepositoryMocks.CreateGlobalVariableEventRepository(_userActivityFixture.UserActivities);

        _handler = new GlobalVariableCreatedEventHandler(mockLoggedInUserService.Object, mockGlobalVariableEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateGlobalVariableEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_globalVariableFixture.GlobalVariableCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_globalVariableFixture.GlobalVariableCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}