//using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.UpdateStatus;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;
//using ContinuityPatrol.Domain.Entities;

//namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Events;

//public class AirGapStatusUpdatedEventTests : IClassFixture<CyberAirGapStatusFixture>
//{
//    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
//    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
//    private readonly Mock<ILoggedInUserService> _mockUserService;
//    private readonly Mock<ILogger<AirGapStatusUpdatedEventHandler>> _mockLogger;
//    private readonly AirGapStatusUpdatedEventHandler _handler;

//    public AirGapStatusUpdatedEventTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
//    {
//        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
//        _mockUserActivityRepository = CyberAirGapStatusRepositoryMocks.CreateUserActivityRepository();
//        _mockUserService = CyberAirGapStatusRepositoryMocks.CreateUserService();
//        _mockLogger = new Mock<ILogger<AirGapStatusUpdatedEventHandler>>();

//        _handler = new AirGapStatusUpdatedEventHandler(
//            _mockUserActivityRepository.Object,
//            _mockUserService.Object,
//            _mockLogger.Object);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event with valid data
//    /// Expected: Successfully creates user activity and logs information
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_ValidData()
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = "Enterprise Air Gap Status System",
//            status = "Active"
//        };

//        UserActivity capturedActivity = null;

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivity = activity)
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        await _handler.Handle(statusUpdatedEvent, CancellationToken.None);

//        // Assert
//        capturedActivity.ShouldNotBeNull();
//        capturedActivity.UserId.ShouldBe("TestUser123");
//        capturedActivity.LoginName.ShouldBe("TestUser123");
//        capturedActivity.CompanyId.ShouldBe("TestCompany123");
//        capturedActivity.RequestUrl.ShouldBe("/api/test");
//        capturedActivity.HostAddress.ShouldBe("127.0.0.1");
//        capturedActivity.Entity.ShouldBe("CyberAirGapStatus");
//        capturedActivity.Action.ShouldBe("Update CyberAirGapStatus");
//        capturedActivity.ActivityType.ShouldBe("Update");
//        capturedActivity.ActivityDetails.ShouldContain("Enterprise Air Gap Status System");
//        capturedActivity.ActivityDetails.ShouldContain("status has been updated To Active successfully");

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//        _mockLogger.Verify(
//            x => x.Log(
//                LogLevel.Information,
//                It.IsAny<EventId>(),
//                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Enterprise Air Gap Status System") &&
//                                            v.ToString().Contains("status has been updated To Active successfully")),
//                It.IsAny<Exception>(),
//                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//            Times.Once);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event with null air gap name
//    /// Expected: Handles null air gap name gracefully
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_NullAirGapName()
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = null,
//            status = "Warning"
//        };

//        UserActivity capturedActivity = null;

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivity = activity)
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        await _handler.Handle(statusUpdatedEvent, CancellationToken.None);

//        // Assert
//        capturedActivity.ShouldNotBeNull();
//        capturedActivity.ActivityDetails.ShouldContain("status has been updated To Warning successfully");

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//        _mockLogger.Verify(
//            x => x.Log(
//                LogLevel.Information,
//                It.IsAny<EventId>(),
//                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("status has been updated To Warning successfully")),
//                It.IsAny<Exception>(),
//                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//            Times.Once);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event with null status
//    /// Expected: Handles null status gracefully
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_NullStatus()
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = "Test Air Gap Status",
//            status = null
//        };

//        UserActivity capturedActivity = null;

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivity = activity)
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        await _handler.Handle(statusUpdatedEvent, CancellationToken.None);

//        // Assert
//        capturedActivity.ShouldNotBeNull();
//        capturedActivity.ActivityDetails.ShouldContain("Test Air Gap Status");
//        capturedActivity.ActivityDetails.ShouldContain("status has been updated To  successfully");

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event with different status values
//    /// Expected: Handles various status values correctly
//    /// </summary>
//    [Theory]
//    [InlineData("Active", "System is running normally")]
//    [InlineData("Warning", "System has minor issues")]
//    [InlineData("Error", "System has critical errors")]
//    [InlineData("Maintenance", "System is under maintenance")]
//    [InlineData("Disabled", "System is temporarily disabled")]
//    [InlineData("Unknown", "System status is unknown")]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_DifferentStatusValues(string status, string description)
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = $"Test Air Gap Status - {description}",
//            status = status
//        };

//        UserActivity capturedActivity = null;

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivity = activity)
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        await _handler.Handle(statusUpdatedEvent, CancellationToken.None);

//        // Assert
//        capturedActivity.ShouldNotBeNull();
//        capturedActivity.ActivityDetails.ShouldContain($"Test Air Gap Status - {description}");
//        capturedActivity.ActivityDetails.ShouldContain($"status has been updated To {status} successfully");

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//        _mockLogger.Verify(
//            x => x.Log(
//                LogLevel.Information,
//                It.IsAny<EventId>(),
//                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"status has been updated To {status} successfully")),
//                It.IsAny<Exception>(),
//                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//            Times.Once);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event with special characters
//    /// Expected: Handles special characters in air gap name and status correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_SpecialCharacters()
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = "Special Characters & <script>alert('xss')</script> 🔄💻📊 状态更新",
//            status = "Active & Running 🚀"
//        };

//        UserActivity capturedActivity = null;

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivity = activity)
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        await _handler.Handle(statusUpdatedEvent, CancellationToken.None);

//        // Assert
//        capturedActivity.ShouldNotBeNull();
//        capturedActivity.ActivityDetails.ShouldContain("Special Characters");
//        capturedActivity.ActivityDetails.ShouldContain("🔄💻📊");
//        capturedActivity.ActivityDetails.ShouldContain("状态更新");
//        capturedActivity.ActivityDetails.ShouldContain("Active & Running 🚀");
//        capturedActivity.ActivityDetails.ShouldContain("status has been updated To Active & Running 🚀 successfully");

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//        _mockLogger.Verify(
//            x => x.Log(
//                LogLevel.Information,
//                It.IsAny<EventId>(),
//                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Special Characters") &&
//                                            v.ToString().Contains("🔄💻📊") &&
//                                            v.ToString().Contains("状态更新") &&
//                                            v.ToString().Contains("Active & Running 🚀")),
//                It.IsAny<Exception>(),
//                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//            Times.Once);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event with cancellation token
//    /// Expected: Respects cancellation and throws OperationCanceledException
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_CancellationRequested()
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = "Cancelled Status Update Test",
//            status = "Active"
//        };
//        var cancellationToken = new CancellationToken(true);

//        // Act & Assert
//        await Should.ThrowAsync<OperationCanceledException>(
//            async () => await _handler.Handle(statusUpdatedEvent, cancellationToken));

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Never);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event when repository fails
//    /// Expected: Throws exception but still logs
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_RepositoryFails()
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = "Repository Failure Status Update Test",
//            status = "Error"
//        };

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

//        // Act & Assert
//        var exception = await Should.ThrowAsync<InvalidOperationException>(
//            async () => await _handler.Handle(statusUpdatedEvent, CancellationToken.None));

//        exception.Message.ShouldBe("Database connection failed");
//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event with different user contexts
//    /// Expected: Uses correct user information from service
//    /// </summary>
//    [Theory]
//    [InlineData("StatusUser001", "<EMAIL>", "Company001", "*************", "/api/v1/cyberairgapstatus/updatestatus")]
//    [InlineData("StatusUser002", "<EMAIL>", "Company002", "*********", "/api/v2/cyberairgapstatus/updatestatus")]
//    [InlineData("StatusAdmin", "<EMAIL>", "AdminCompany", "***********", "/admin/cyberairgapstatus/updatestatus")]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_DifferentUserContexts(
//        string userId, string loginName, string companyId, string ipAddress, string requestUrl)
//    {
//        // Arrange
//        var mockUserService = new Mock<ILoggedInUserService>();
//        mockUserService.Setup(x => x.UserId).Returns(userId);
//        mockUserService.Setup(x => x.LoginName).Returns(loginName);
//        mockUserService.Setup(x => x.CompanyId).Returns(companyId);
//        mockUserService.Setup(x => x.IpAddress).Returns(ipAddress);
//        mockUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);

//        var handler = new AirGapStatusUpdatedEventHandler(
//            _mockUserActivityRepository.Object,
//            mockUserService.Object,
//            _mockLogger.Object);

//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = "User Context Status Update Test",
//            status = "Active"
//        };

//        UserActivity capturedActivity = null;

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivity = activity)
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        await handler.Handle(statusUpdatedEvent, CancellationToken.None);

//        // Assert
//        capturedActivity.ShouldNotBeNull();
//        capturedActivity.UserId.ShouldBe(userId);
//        capturedActivity.LoginName.ShouldBe(loginName);
//        capturedActivity.CompanyId.ShouldBe(companyId);
//        capturedActivity.HostAddress.ShouldBe(ipAddress);
//        capturedActivity.RequestUrl.ShouldBe(requestUrl);

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//    }

//    /// <summary>
//    /// Test: Handle multiple air gap status updated events
//    /// Expected: Handles multiple events correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_MultipleEvents()
//    {
//        // Arrange
//        var events = new[]
//        {
//            new AirGapStatusUpdatedEvent { AirGap = "Air Gap Status 1", status = "Active" },
//            new AirGapStatusUpdatedEvent { AirGap = "Air Gap Status 2", status = "Warning" },
//            new AirGapStatusUpdatedEvent { AirGap = "Air Gap Status 3", status = "Error" }
//        };

//        var capturedActivities = new List<UserActivity>();

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivities.Add(activity))
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        foreach (var evt in events)
//        {
//            await _handler.Handle(evt, CancellationToken.None);
//        }

//        // Assert
//        capturedActivities.Count.ShouldBe(3);
//        capturedActivities[0].ActivityDetails.ShouldContain("Air Gap Status 1");
//        capturedActivities[0].ActivityDetails.ShouldContain("updated To Active successfully");
//        capturedActivities[1].ActivityDetails.ShouldContain("Air Gap Status 2");
//        capturedActivities[1].ActivityDetails.ShouldContain("updated To Warning successfully");
//        capturedActivities[2].ActivityDetails.ShouldContain("Air Gap Status 3");
//        capturedActivities[2].ActivityDetails.ShouldContain("updated To Error successfully");

//        capturedActivities.ShouldAllBe(a => a.Entity == "CyberAirGapStatus");
//        capturedActivities.ShouldAllBe(a => a.Action == "Update CyberAirGapStatus");
//        capturedActivities.ShouldAllBe(a => a.ActivityType == "Update");

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Exactly(3));
//        _mockLogger.Verify(
//            x => x.Log(
//                LogLevel.Information,
//                It.IsAny<EventId>(),
//                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("status has been updated To") && v.ToString().Contains("successfully")),
//                It.IsAny<Exception>(),
//                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
//            Times.Exactly(3));
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event activity details validation
//    /// Expected: Activity details contain all required information
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_ValidatingActivityDetails()
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = "Validation Status Update Test Air Gap",
//            status = "Maintenance"
//        };

//        UserActivity capturedActivity = null;

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivity = activity)
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        await _handler.Handle(statusUpdatedEvent, CancellationToken.None);

//        // Assert
//        capturedActivity.ShouldNotBeNull();

//        // Validate activity details format
//        capturedActivity.ActivityDetails.ShouldNotBeNullOrEmpty();
//        capturedActivity.ActivityDetails.ShouldContain("CyberAirGapStatus");
//        capturedActivity.ActivityDetails.ShouldContain("Validation Status Update Test Air Gap");
//        capturedActivity.ActivityDetails.ShouldContain("status has been updated To Maintenance successfully");

//        // Validate activity structure
//        capturedActivity.Entity.ShouldBe("CyberAirGapStatus");
//        capturedActivity.Action.ShouldBe("Update CyberAirGapStatus");
//        capturedActivity.ActivityType.ShouldBe("Update");

//        // Validate user information is captured
//        capturedActivity.UserId.ShouldNotBeNullOrEmpty();
//        capturedActivity.LoginName.ShouldNotBeNullOrEmpty();

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event concurrent operations
//    /// Expected: Handles concurrent status update events correctly
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_ConcurrentOperations()
//    {
//        // Arrange
//        var events = Enumerable.Range(1, 10).Select(i => new AirGapStatusUpdatedEvent
//        {
//            AirGap = $"Concurrent Status Update Test {i}",
//            status = i % 2 == 0 ? "Active" : "Warning"
//        }).ToArray();

//        var capturedActivities = new List<UserActivity>();
//        var lockObject = new object();

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity =>
//            {
//                lock (lockObject)
//                {
//                    capturedActivities.Add(activity);
//                }
//            })
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
//        await Task.WhenAll(tasks);

//        // Assert
//        capturedActivities.Count.ShouldBe(10);
//        capturedActivities.ShouldAllBe(a => a.Entity == "CyberAirGapStatus");
//        capturedActivities.ShouldAllBe(a => a.Action == "Update CyberAirGapStatus");
//        capturedActivities.ShouldAllBe(a => a.ActivityType == "Update");
//        capturedActivities.ShouldAllBe(a => a.ActivityDetails.Contains("status has been updated To"));
//        capturedActivities.ShouldAllBe(a => a.ActivityDetails.Contains("successfully"));

//        for (int i = 1; i <= 10; i++)
//        {
//            capturedActivities.ShouldContain(a => a.ActivityDetails.Contains($"Concurrent Status Update Test {i}"));
//        }

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Exactly(10));
//    }

//    /// <summary>
//    /// Test: Handle air gap status updated event with empty strings
//    /// Expected: Handles empty strings gracefully
//    /// </summary>
//    [Fact]
//    public async Task Handle_AirGapStatusUpdatedEvent_When_EmptyStrings()
//    {
//        // Arrange
//        var statusUpdatedEvent = new AirGapStatusUpdatedEvent
//        {
//            AirGap = string.Empty,
//            status = string.Empty
//        };

//        UserActivity capturedActivity = null;

//        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<UserActivity>()))
//            .Callback<UserActivity>(activity => capturedActivity = activity)
//            .ReturnsAsync((UserActivity activity) => activity);

//        // Act
//        await _handler.Handle(statusUpdatedEvent, CancellationToken.None);

//        // Assert
//        capturedActivity.ShouldNotBeNull();
//        capturedActivity.ActivityDetails.ShouldContain("status has been updated To  successfully");
//        capturedActivity.Entity.ShouldBe("CyberAirGapStatus");
//        capturedActivity.Action.ShouldBe("Update CyberAirGapStatus");
//        capturedActivity.ActivityType.ShouldBe("Update");

//        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<UserActivity>()), Times.Once);
//    }
//}
