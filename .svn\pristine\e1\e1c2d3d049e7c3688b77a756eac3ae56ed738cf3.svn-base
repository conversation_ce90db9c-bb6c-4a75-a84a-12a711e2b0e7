﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Commands
{
    public class CreateWorkflowDrCalenderTest : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly WorkflowDrCalenderFixture _workflowDrCalenderFixture;

        private readonly Mock<IWorkflowDrCalenderRepository> _mockWorkflowDrcalenderRepository;

        private readonly CreateWorkflowDrCalenderCommandHandler _handler;


        public CreateWorkflowDrCalenderTest(WorkflowDrCalenderFixture workflowDrCalenderFixture)
        {
            _workflowDrCalenderFixture = workflowDrCalenderFixture;

            var mockPublisher = new Mock<IPublisher>();

            _mockWorkflowDrcalenderRepository = WorkflowDrCalenderRepositoryMocks.CreateWorkflowDrcalenderRepository(_workflowDrCalenderFixture.WorkflowDrCalenderInfos);

            _handler = new CreateWorkflowDrCalenderCommandHandler(_workflowDrCalenderFixture.Mapper, _mockWorkflowDrcalenderRepository.Object, mockPublisher.Object);

        }

        [Fact]
        public async Task Handle_Should_Increase_Count_When_AddValid_WorkflowTemp()
        {
            var initialCount = _workflowDrCalenderFixture.WorkflowDrCalenderInfos.Count;
            await _handler.Handle(_workflowDrCalenderFixture.CreateWorkflowDrCalenderCommand, CancellationToken.None);

            var allCategories = await _mockWorkflowDrcalenderRepository.Object.ListAllAsync();

            allCategories.Count.ShouldBe(initialCount + 1);
        }
        [Fact]
        public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
        {
            _workflowDrCalenderFixture.CreateWorkflowDrCalenderCommand.ProfileName = "TestDrCalender";
            await _handler.Handle(_workflowDrCalenderFixture.CreateWorkflowDrCalenderCommand, CancellationToken.None);

            _mockWorkflowDrcalenderRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowDrCalender>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_SuccessfulWorkflowTempResponse_When_AddValidWorkflowValues()
        {
            _workflowDrCalenderFixture.CreateWorkflowDrCalenderCommand.ProfileName = "Testdr";
            var result = await _handler.Handle(_workflowDrCalenderFixture.CreateWorkflowDrCalenderCommand, CancellationToken.None);

            result.ShouldBeOfType(typeof(CreateWorkflowDrCalenderResponse));

            result.Id.ShouldBeGreaterThan(0.ToString());

            result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
        }
    }
}
