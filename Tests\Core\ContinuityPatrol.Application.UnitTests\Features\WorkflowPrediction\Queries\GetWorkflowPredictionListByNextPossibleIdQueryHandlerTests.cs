﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetNextPossibleId;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Queries;

public class GetWorkflowPredictionListByNextPossibleIdQueryHandlerTests
{
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IWorkflowPredictionRepository> _mockWorkflowPredictionRepository;
    private readonly GetWorkflowPredictionListByNextPossibleIdQueryHandler _handler;

    public GetWorkflowPredictionListByNextPossibleIdQueryHandlerTests()
    {
        _mockMapper = new Mock<IMapper>();
        _mockWorkflowPredictionRepository = new Mock<IWorkflowPredictionRepository>();
        _handler = new GetWorkflowPredictionListByNextPossibleIdQueryHandler(
            _mockMapper.Object,
            _mockWorkflowPredictionRepository.Object
        );
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenNoPredictionsExist()
    {
        var query = new GetWorkflowPredictionListByNextPossibleIdQuery
        {
            NextPossibleId = "non-matching-id"
        };

        var workflowPredictions = new List<Domain.Entities.WorkflowPrediction>();

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId))
            .ReturnsAsync(workflowPredictions);

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions))
            .Returns(new List<WorkflowPredictionListByNextPossibleIdVm>());

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Empty(result);
        _mockWorkflowPredictionRepository.Verify(repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions), Times.Once);
    }
    [Fact]
    public async Task Handle_ReturnsMappedPredictions_WhenPredictionsExist()
    {
        // Arrange
        var query = new GetWorkflowPredictionListByNextPossibleIdQuery
        {
            NextPossibleId = "next-id-1"
        };

        var testDate = DateTime.UtcNow;
        var workflowPredictionEntity = new Domain.Entities.WorkflowPrediction
        {
            ReferenceId = "prediction-123",
            ActionId = "action-456",
            ActionName = "Test Action",
            Count = 5,
            NextPossibleId = "next-id-1",
            NextPossibleActionName = "Next Action",
            NodeId = "node-789",
            LastModifiedDate = testDate
        };

        var workflowPredictions = new List<Domain.Entities.WorkflowPrediction> { workflowPredictionEntity };

        var expectedMappedVm = new WorkflowPredictionListByNextPossibleIdVm
        {
            Id = workflowPredictionEntity.ReferenceId,
            ActionId = workflowPredictionEntity.ActionId,
            ActionName = workflowPredictionEntity.ActionName,
            Count = workflowPredictionEntity.Count,
            NextPossibleId = workflowPredictionEntity.NextPossibleId,
            NextPossibleActionName = workflowPredictionEntity.NextPossibleActionName,
            NodeId = workflowPredictionEntity.NodeId
        };

        var mappedPredictions = new List<WorkflowPredictionListByNextPossibleIdVm> { expectedMappedVm };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId))
            .ReturnsAsync(workflowPredictions);

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions))
            .Returns(mappedPredictions);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var actualVm = result.First();
        Assert.Equal(expectedMappedVm.Id, actualVm.Id);
        Assert.Equal(expectedMappedVm.ActionId, actualVm.ActionId);
        Assert.Equal(expectedMappedVm.ActionName, actualVm.ActionName);
        Assert.Equal(expectedMappedVm.Count, actualVm.Count);
        Assert.Equal(expectedMappedVm.NextPossibleId, actualVm.NextPossibleId);
        Assert.Equal(expectedMappedVm.NextPossibleActionName, actualVm.NextPossibleActionName);
        Assert.Equal(expectedMappedVm.NodeId, actualVm.NodeId);

        _mockWorkflowPredictionRepository.Verify(
            repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId),
            Times.Once);

        _mockMapper.Verify(
            mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions),
            Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldHandleNullProperties_WhenMappingToViewModel()
    {
        // Arrange
        var query = new GetWorkflowPredictionListByNextPossibleIdQuery
        {
            NextPossibleId = "next-id-1"
        };

        var workflowPredictionEntity = new Domain.Entities.WorkflowPrediction
        {
            Id = 0,
            ActionId = null,
            ActionName = null,
            Count = 0,
            NextPossibleId = null,
            NextPossibleActionName = null,
            NodeId = null
        };

        var workflowPredictions = new List<Domain.Entities.WorkflowPrediction> { workflowPredictionEntity };

        var expectedMappedVm = new WorkflowPredictionListByNextPossibleIdVm
        {
            Id = null,
            ActionId = null,
            ActionName = null,
            Count = 0,
            NextPossibleId = null,
            NextPossibleActionName = null,
            NodeId = null
        };

        var mappedPredictions = new List<WorkflowPredictionListByNextPossibleIdVm> { expectedMappedVm };

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId))
            .ReturnsAsync(workflowPredictions);

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions))
            .Returns(mappedPredictions);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var actualVm = result.First();
        Assert.Null(actualVm.Id);
        Assert.Null(actualVm.ActionId);
        Assert.Null(actualVm.ActionName);
        Assert.Equal(0, actualVm.Count);
        Assert.Null(actualVm.NextPossibleId);
        Assert.Null(actualVm.NextPossibleActionName);
        Assert.Null(actualVm.NodeId);
    }
}