﻿namespace ContinuityPatrol.Shared.Core.Constants;

public static class EventCodes
{
    public static class Account
    {
        public const string AppStart = "APP-START";
        public const string LoginSuccess = "AUTH-001";
        public const string LoginFailure = "AUTH-002";
        public const string Logout = "AUTH-003";
        public const string ForgotPassword = "AUTH-004";
        public const string ChangePassword = "AUTH-005";
        public const string GetDomains = "AUTH-006";
        public const string GetADGroups = "AUTH-007";
        public const string HashPassword = "AUTH-008";
        public const string ClearSession = "AUTH-009";
    }


    #region Admin
    public static class AccessManager
    {
        public const string CreateOrUpdate = "AM-A001";
        public const string Delete = "AM-A002";
        public const string List = "AM-A003";
        public const string GetRoleDetails = "AM-A004";
        public const string GetUserByRole = "AM-A005";
        public const string GetUserInfraByUser = "AM-A005";

    }

    public static class Archive
    {
        public const string CreateOrUpdate = "Arc-A001";
        public const string Delete = "Arc-A002";
        public const string List = "Arc -A003";
        public const string GetPagination = "Arc-A004";
        public const string GetPaginated = "Arc-A005";
        public const string IsArchiveNameExist = "Arc-A006";
        public const string IsTableNameExist = "Arc-A007";
    }

    public static class BackupData
    {
        public const string CreateOrUpdate = "BKP-A001";
        public const string Delete = "BKP-A002";
        public const string List = "BKP -A003";
        public const string GetList = "BKP-A004";
        public const string GetBackupConfig = "BKP-A005";
        public const string GetPagination = "BKP-A006";
        public const string ExecuteBackUpCommand = "BKP-A007";
        public const string CheckWindowsService = "BKP-A008";
    }

    public static class ComponentType
    {
        public const string CreateOrUpdate = "CMPT-A001";
        public const string Delete = "CMPT-A002";
        public const string List = "CMPT -A003";
        public const string GetComponentTypeListByName = "CMPT-A004";
        public const string ServerListbyName = "CMPT-A005";
        public const string DatabaseList = "CMPT-A006";
        public const string ReplicationList = "CMPT-A007";
        public const string SingleSignOnList = "CMPT-A008";
        public const string ServerList = "CMPT-A009";
        public const string ComponentTypeNameExist = "CMPT-A010";
        public const string GetPagination = "CMPT-A011";
    }
    public static class ConfigurePage
    {
        public const string CreateOrUpdate = "CONFPG-A001";
        public const string Delete = "CONFPG-A002";
        public const string List = "CONFPG -A003";
        public const string GetPageBuilderList = "CONFPG-A004";
        public const string GetByReferenceId = "CONFPG-A005";
        public const string GetMonitorTypeByInfraObject = "CONFPG-A006";
        public const string ReplicationList = "CONFPG-A007";
        public const string SingleSignOnList = "CONFPG-A008";
        public const string ServerList = "CONFPG-A009";
        public const string ComponentTypeNameExist = "CONFPG-A010";
    }
    public static class ConfigureWidget
    {
        public const string CreateOrUpdate = "CONFWGT-A001";
        public const string Delete = "CONFWGT-A002";
        public const string List = "CONFWGT -A003";
        public const string GetPageWidgetList = "CONFWGT-A004";
        public const string GetAllTableAccesses = "CONFWGT-A005";
        public const string DatasetDetails = "CONFWGT-A006";
        public const string DecryptDetails = "CONFWGT-A007";
        public const string GetMonitorServiceStatusByIdAndType = "CONFWGT-A008";

    }
    public static class DataSet
    {
        public const string CreateOrUpdate = "DSET-A001";
        public const string Delete = "DSET-A002";
        public const string List = "DSET -A003";
        public const string GetPagination = "DSET-A004";
        public const string RunQuery = "DSET-A005";
        public const string DataSetNameExist = "DSET-A006";
        public const string GetDbDetail = "DSET-A007";
        public const string GetColumnNamesBySchemaNameAndTableName = "DSET-A008";

    }
    public static class FormBuilder
    {
        public const string CreateOrUpdate = "FB-A001";
        public const string Delete = "FB-A002";
        public const string List = "FB -A003";
        public const string GetFormPagination = "FB-A004";
        public const string GetNames = "FB-A005";
        public const string IsPublish = "FB-A006";
        public const string GetFormById = "FB-A007";
        public const string FormExport = "FB-A008";
        public const string FormImport = "FB-A009";
        public const string FormDataEncrypt = "FB-A010";
        public const string FormDataDecrypt = "FB-A011";
        public const string GetForms = "FB-A012";
        public const string FormNameExist = "FB-A013";

    }

    public static class FormHistory
    {
        public const string CreateOrUpdate = "FH-A001";
        public const string Delete = "FH-A002";
        public const string List = "FH -A003";
        public const string GetFormHistoryByFormId = "FH-A004";

    }
    public static class FormMapping
    {
        public const string CreateOrUpdate = "FM-A001";
        public const string Delete = "FM-A002";
        public const string List = "FM -A003";
        public const string GetPagination = "FM-A004";
        public const string GetFormMappingListByName = "FM-A005";
        public const string ReplicationList = "FM-A006";
        public const string ServerList = "FM-A007";
        public const string DatabaseList = "FM-A008";
        public const string SingleSignOnList = "FM-A009";
        public const string NodeList = "FM-A010";
        public const string FormMappingNameExist = "FM-A011";
        public const string GetFormMappingByFormId = "FM-A011";

    }

    public static class FormType
    {
        public const string CreateOrUpdate = "FT-A001";
        public const string Delete = "FT-A002";
        public const string List = "FT -A003";
        public const string GetPagination = "FT-A004";
        public const string FormTypeNameExist = "FT-A005";
        public const string GetFormTypeNames = "FT-A006";
    }
    public static class GlobalSettings
    {
        public const string CreateOrUpdate = "GS-A001";
        public const string Delete = "GS-A002";
        public const string List = "GS -A003";
        public const string GetPagination = "GS-A004";
        public const string GetGlobalSettingList = "GS-A005";

    }
    public static class GroupNodePolicy
    {
        public const string CreateOrUpdate = "GNP-A001";
        public const string Delete = "GNP-A002";
        public const string List = "GNP -A003";
        public const string GetPagination = "GNP-A004";
        public const string IsGroupPolicyNameExist = "GNP-A005";
        public const string GetLoadBalancerList = "GNP-A006";
        public const string GetGroupPolicyByType = "GNP-A007";

    }
    public static class InfraReplicationMapping
    {
        public const string CreateOrUpdate = "IRM-A001";
        public const string Delete = "IRM-A002";
        public const string List = "IRM-A003";
        public const string GetPagination = "IRM-A004";
        public const string GetReplicationComponentType = "IRM-A005";
        public const string GetDatabaseComponentType = "IRM-A006";
        public const string GetReplicationMasterByInfraMasterName = "IRM-A007";

    }
    public static class ITViewBuilder
    {
        public const string CreateOrUpdate = "ITVB-A001";
        public const string Delete = "ITVB-A002";
        public const string List = "ITVB-A003";
        public const string GetPagination = "ITVB-A004";
        public const string GetDashboardViewListByInfraObjectId = "ITVB-A004";

    }
    public static class LicenseManager
    {
        public const string CreateOrUpdate = "LM-A001";
        public const string Delete = "LM-A002";
        public const string List = "LM -A003";
        public const string GetPagination = "LM-A004";
        public const string BaseLicenseCreateOrUpdate = "LM-A005";
        public const string DerivedLicenseCreateOrUpdate = "LM-A006";
        public const string DeleteDerived = "LM-A007";
        public const string LoadPartialView = "LM-A008";
        public const string GetLicenseManagerByPoNumber = "LM-A009";
        public const string GetList = "LM-A010";
        public const string GetLicenseManagerCount = "LM-A011";
        public const string GetLicensesNames = "LM-A012";
        public const string GetLicensesNamesWithCount = "LM-A012";
        public const string GetExpiredLicensesList = "LM-A013";
        public const string GetListById = "LM-A014";
        public const string GetEntityList = "LM-A015";
        public const string GetTypeList = "LM-A016";
        public const string DeleteDecommission = "LM-A017";
        public const string GetDerivedList = "LM-A018";
        public const string GetDecommissionList = "LM-A019";
        public const string DecommissionAuthentication = "LM-A020";
        public const string ReplaceLicenseDetails = "LM-A021";
        public const string GetLicenseExpiresByCompanyId = "LM-A022";
        public const string UpdateLicenseState = "LM-A023";
        public const string LicenseLanding = "LM-A024";
        public const string LicenseExpiredLanding = "LM-A025";
        public const string GetLicenseGeneratorKeyById = "LM-A026";
    }

    public static class LoadBalancer
    {
        public const string CreateOrUpdate = "LB-A001";
        public const string Delete = "LB-A002";
        public const string List = "LB-A003";
        public const string Pagination = "LB-A004";
        public const string IsExist = "LB-A005";
        public const string IsIpAndPortExist = "LB-A006";
        public const string IsDefault = "LB-A007";
        public const string TestConfiguration = "LB-A008";
        public const string StateMonitoring = "LB-A009";
        public const string LoadStateMonitoring = "LB-A010";
        public const string UpdateNodeStatus = "LB-A011";
    }
    public static class OperationType
    {
        public const string CreateOrUpdate = "OT-A001";
        public const string Delete = "OT-A002";
        public const string List = "OT-A003";
        public const string Pagination = "OT-A004";
        public const string IsExist = "OT-A005";
    }
    public static class ServerType
    {
        public const string CreateOrUpdate = "ST-A001";
        public const string Delete = "ST-A002";
        public const string List = "ST-A003";
        public const string Pagination = "ST-A004";
        public const string IsExist = "ST-A005";
        public const string GetServerTypeList = "ST-A006";
        public const string ServerSubTypeCreateOrUpdate = "ST-A007";
        public const string ServerSubTypeDelete = "ST-A008";
        public const string IsServerSubTypeExist = "ST-A009";
    }
    public static class Settings
    {
        public const string CreateOrUpdate = "S-A001";
        public const string List = "S-A003";
        public const string GetList = "S-A004";
        public const string SmtpConfiguration = "S-A0045";
       
    }

    public static class SmsConfiguration
    {
        public const string CreateOrUpdate = "SMS-A001";
        public const string GetList = "SMS-A004";
       
    }

    public static class SmtpConfiguration
    {
        public const string CreateOrUpdate = "SMTP-A001";
        public const string GetSmtpList = "SMTP-A004";
        public const string SendTestMail = "SMTP-A004";
        public const string DecryptPassword = "SMTP-A004";
     

    }
    public static class SolutionMapping
    {
        public const string CreateOrUpdate = "SM-A001";
        public const string Delete = "SM-A002";
        public const string List = "SM-A003";
        public const string Pagination = "SM-A004";
        public const string GetPageWidgetList = "SM-A005";
        public const string GetPageBuilderList = "SM-A006";
        public const string GetTypeByDatabaseIdAndReplicationMasterId = "SM-A007";
    }
    public static class TableAccess
    {
        public const string CreateOrUpdate = "TA-A001";
        public const string List = "TA-A003";
        public const string DataSetList = "TA-A004";
        public const string GetTableNamesBySchemaName = "TA-A005";
        public const string GetTableAccessList = "TA-A006";
        public const string TableList = "TA-A007";
    }
    public static class User
    {
        public const string CreateOrUpdate = "U-A001";
        public const string Delete = "U-A002";
        public const string List = "U-A003";
        public const string Pagination = "U-A004";
        public const string IsExist = "U-A005";
        public const string GetDomains = "U-A006";
        public const string GetDomainUsers = "U-A007";
        public const string GetDomainGroups = "U-A008";
        public const string GetUserLoginName = "U-A009";
        public const string GetUserRoleList = "U-A010";
        public const string GetAllInfraObjectList = "U-A011";
        public const string SiteAdminLanding = "U-A012";
        public const string Lock = "U-A013";
        public const string Unlock = "U-A014";
        public const string LockLogin = "U-A015";
        public const string UserActivityProfile = "U-A016";
        public const string UserProfiles = "U-A017";
        public const string UpdateUserProfileImage = "U-A018";
        public const string UserProfileDelete = "U-A019";
        public const string ChangePasswordView = "U-A020";
        public const string ChangePassword = "U-A021";
        public const string HashPassword = "U-A022";
        public const string HashPasswordForEmail = "U-A023";
        public const string DecryptPassword = "U-A024";
        public const string IsExceedUnlockAttempt = "U-A025";
        public const string ResetPassword = "U-A026";
        public const string IsNewPasswordInLastFive = "U-A027";
        public const string GetCompanyById = "U-A028";
    }
    public static class AdminUserGroup
    {
        public const string CreateOrUpdate = "UG-A001";
        public const string Delete = "UG-A002";
        public const string List = "UG-A003";
        public const string Pagination = "UG-A004";
        public const string IsExist = "UG-A005";
        public const string GetUserNames = "UG-A006";
        public const string RouteToPostView = "UG-A007";
    }
    public static class UserRole
    {
        public const string CreateOrUpdate = "UR-A001";
        public const string Delete = "UR-A002";
        public const string List = "UR-A003";
        public const string Pagination = "UR-A004";
        public const string IsExist = "UR-A005";
        public const string RouteToPostView = "UR-A006";
       
    }

    public static class WorkflowAction
    {
        public const string CreateOrUpdate = "WA-A001";
        public const string Delete = "WA-A002";
        public const string List = "WA-A003";
        public const string IsExist = "WA-A004";
        public const string SaveAsCreateOrUpdate = "WA-A005";
        public const string LockCreateOrUpdate = "WA-A006";
        public const string LockStatusUpdate = "WA-A007";
        public const string WorkflowActionList = "WA-A008";
        public const string CryptoEncryptPassword = "WA-A009";
        public const string CryptoDecryptPassword = "WA-A010";
        public const string ImportWorkflowAction = "WA-A011";
        public const string WorkflowActionCompareJson = "WA-A012";
        public const string WorkflowActionDataList = "WA-A013";
        public const string ActionCreateOrUpdate = "WA-A014";
        public const string ActionDelete = "WA-A015";
        public const string ValidateCpActionScript = "WA-A016";
    }
    public static class WorkflowCategory
    {
        public const string CreateOrUpdate = "WC-A001";
        public const string Delete = "WC-A002";
        public const string List = "WC-A003";
        public const string WorkflowCategoryList = "WC-A004";
        public const string RouteToPostView = "WC-A005";
    }
    #endregion
    #region Dashboard

    public static class Analytic
    {
        public const string GetDrill = "AN-DB004";
        public const string GetComponentFailure = "AN-DB005";
        public const string GetOperationalAvailability = "AN-DB006";
        public const string GetWorkflow = "AN-DB007";
        public const string GetSlaBreach = "AN-DB008";
        public const string GetOperationalHealthSummary = "AN-DB09";
    }
    public static class CustomDashboard
    {
        public const string List = "CD-DB003";
        public const string GetByReferenceId = "CD-DB004";
    }
    public static class ItResiliency
    {
        public const string UpdateInfraObjectState = "IR-D003";
        public const string List = "IR-DB004";
        public const string GetByInfraObjectId = "IR-DB005";
        public const string GetByBusinessServiceId = "IR-DB006";
        public const string GetLogByInfraObjectId = "IR-DB007";
        public const string GetInfraObjectDetails = "IR-DB008";
        public const string GetBusinessServiceList = "IR-DB009";
        public const string GetAllInfraSummaries = "IR-DB010";
        public const string GetMonitoringDetails = "IR-DB011";
        public const string RescheduleJob = "IR-DB012";
     
    }
    public static class ResiliencyMapping
    {
        public const string List = "RM-DB003";
        public const string GetDrCalendarDrillEvents = "RM-DB004";
        public const string GetBusinessService = "RM-DB005";
        public const string GetImpact = "RM-DB006";
        public const string GetImpactDetailCount = "RM-DB007";
        public const string GetBusinessServiceTopology = "RM-DB008";
        public const string GetSites = "RM-DB009";
        public const string GetBusinessServiceAvailability = "RM-DB010";
        public const string GetDrReadyStatus = "RM-DB011";
        public const string GetBusinessServiceDrReady = "RM-DB012";
        public const string GetDcMappingSiteDetails = "RM-DB013";
        public const string GetVerifiedWorkflow = "RM-DB014";
        public const string GetBreachDetails = "RM-DB015";
        public const string GetLastDrillDetails = "RM-DB016";
        public const string GetEntitiesEvent = "RM-DB017";
        public const string GetCyberSecurityList = "RM-DB018";
        public const string GetFailedDrillList = "RM-DB019";
        public const string GetTotalSiteDetails = "RM-DB020";
    }

    public static class ServiceAvailability
    {
        public const string List = "SA-DB003";
        public const string BusinessServiceOverview = "SA-DB004";
        public const string GetImpactDetail = "SA-DB005";
        public const string GetBusinessServiceTreeView = "SA-DB006";
        public const string BusinessServiceReport = "SA-DB007";
        public const string GetRto = "SA-DB008";
        public const string ImpactAvailability = "SA-DB009";
        public const string DataLag = "SA-DB010";
        public const string GetSiteProperties = "SA-DB011";
        public const string GetImpactDetailCount = "SA-DB012"; 
        public const string ResiliencyReadinessReport = "SA-DB013"; 
        public const string GetDrDrillDetails = "SA-DB014";
        public const string GetWorkflowOperation = "SA-DB015";
        public const string GetDynamicDashboard = "SA-DB016";
        public const string GetCustomDashboardNames = "SA-DB017";
        public const string GetDynamicSubDashboard = "SA-DB018";
        public const string DrillOverview = "SA-DB019";
    }
    #endregion

    #region Drift

    public static class DriftDashboard
    {
        public const string CreateOrUpdate = "DD-D001";
        public const string UpdateDriftSnap = "DD-D003";
        public const string List = "DD-D004";
        public const string GetDriftTree = "DD-D005";
        public const string GetDriftManagementStatus = "DD-D006";
        public const string GetDriftOperationSummary = "DD-D007";
        public const string GetDriftDashboardResource = "DD-D008";
        public const string GetDriftResourceSummary = "DD-D009";
        public const string GetDriftCategory = "DD-D010";
        public const string GetConflictOverView = "DD-D011"; 
        public const string GetDriftManagementMonitorStatus = "DD-D012";
        public const string GetDriftReport = "DD-D013"; 
        public const string GetDriftReportByDates = "DD-D014"; 
        public const string GetCompanyLogo = "DD-D015";
    }

    public static class DriftManagement
    {
        public const string CreateOrUpdate = "DM-D001";
        public const string Delete = "DM-D002";
        public const string List = "DM-D003";
        public const string Pagination = "DM-D004";
        public const string IsExist = "DM-D005";
        public const string UpdateJobState = "DM-D006";
        public const string Reset = "DM-D007";
        public const string GetDriftProfile = "DM-D008"; 
        public const string SolutionDetails = "DM-D009";
    }
    public static class DriftParameter
    {
        public const string CreateOrUpdate = "DPAR-D001";
        public const string Delete = "DPAR-D002";
        public const string List = "DPAR-D003";
        public const string Pagination = "DPAR-D004";
        public const string IsExist = "DPAR-D005";
        public const string CplValidation = "DPAR-D006";
        public const string DriftCategoryCreateOrUpdate = "DPAR-D007";
        public const string DriftCategoryDelete = "DPAR-D008";
        public const string DriftCategory = "DPAR-D009";
        public const string IsCategory = "DPAR-D010";
        public const string DriftImpactTypeCreateOrUpdate = "DPAR-D011";
        public const string DeleteImpactType = "DPAR-D012";
        public const string DriftImpactType = "DPAR-D013";
        public const string IsImpactType = "DPAR-D014";
       
    }
    public static class DriftProfile
    {
        public const string CreateOrUpdate = "DPRO-D001";
        public const string Delete = "DPRO-D002";
        public const string List = "DPRO-D003";
        public const string Pagination = "DPRO-D004";
        public const string IsExist = "DPRO-D005";
        public const string FormTypeName = "DPRO-D006";
        public const string ComponentTypeList = "DPRO-D007";
        public const string DriftCategoryList = "DPRO-D008";
    }
    #endregion

    #region ITAutomation
    public static class UserPrivileges
    {
        public const string CreateOrUpdate = "UP-IT001";
        public const string Delete = "UP-IT002";
        public const string List = "UP-IT003";
        public const string GetByReferenceId = "UP-IT004";
        public const string GetUserName = "UP-IT005";
        public const string GetUserRoleList = "UP-IT006";
        public const string GetWorkFlowList = "UP-IT007";
        public const string GetProfileList = "UP-IT008";
        public const string GetPaginatedWorkflowPermissionList = "UP-IT009";
        public const string GetAll = "UP-IT010";

    }

    public static class WorkflowConfiguration
    {
        public const string CreateOrUpdate = "WC-IT001";
        public const string Delete = "WC-IT002";
        public const string GetInfraObjectList = "WC-IT003";
        public const string GetInfraObjectListByReplicationTypeId = "WC-IT004";
        public const string GetWorkflowActionType = "WC-IT005";
        public const string GetWorkflowList = "WC-IT006";
        public const string GetWorkFlowActionsById = "WC-IT007";
        public const string GetServerByRoleTypeAndServerType = "WC-IT008";
        public const string GetInfraList = "WC-IT009";
        public const string GetDatabaseList = "WC-IT010";
        public const string GetAirGapList = "WC-IT011";
        public const string WorkFlowDataEncrypt = "WC-IT012";
        public const string WorkFlowDataDecrypt = "WC-IT013";
        public const string CheckInfraObjectAttachByWorkflowId = "WC-IT014";
        public const string CheckProfileAttachedByWorkflowId = "WC-IT015";
        public const string GetComponentTypeByDatabaseList = "WC-IT016";
        public const string GetDatabaseByServerId = "WC-IT017";
        public const string GetWorkflowInfraObjectByWorkflowId = "WC-IT018";
        public const string GetTemplateList = "WC-IT019";
        public const string GetWorkflowActionByNodeId = "WC-IT020";
        public const string GetTypeByDatabaseIdAndReplicationMasterId = "WC-IT021";
        public const string GetReplicationMasterByInfraMasterName = "WC-IT022";
        public const string GetReplicationNames = "WC-IT023";
        public const string GetWorkflowById = "WC-IT024";
        public const string GetInfraObjectById = "WC-IT025";
        public const string GetWorkflowPrediction = "WC-IT026";
        public const string AttachInfraObject = "WC-IT027";
        public const string CreateWorkflowExecutionTemp = "WC-IT028";
        public const string CreateWorkflowOperationGroup = "WC-IT029";
        public const string UpdateWorkflowLock = "WC-IT030";
        public const string UpdateWorkflowVerify = "WC-IT031";
        public const string UpdateWorkflowPublish = "WC-IT032";
        public const string DetachInfraObject = "WC-IT033";
        public const string CreateOrUpdateTemplate = "WC-IT034";
        public const string WorkflowSaveAs = "WC-IT035";
        public const string WorkFlowNameExist = "WC-IT036";
        public const string GetTemplateByReplicationTypeId = "WC-IT037";
        public const string GetTemplateByTypes = "WC-IT038";
        public const string TemplateNameExist = "WC-IT039";
        public const string GetWorkflowHistoryByWorkflowId = "WC-IT040";
        public const string GetTemplateByInfraObjectId = "WC-IT041";
        public const string TemplateValidation = "WC-IT042";
        public const string GetServerByServerIPAddress = "WC-IT043";
        public const string GetActionByActionType = "WC-IT044";
        public const string GetRunBookReport = "WC-IT045";
        public const string CplScriptToCPSharp = "WC-IT046";
    }

    public static class WorkflowExecution
    {
        public const string CreateOrUpdate = "WE-IT001";
        public const string Delete = "WE-IT002";
        public const string LoadUsers = "WE-IT003";
        public const string LoadProfleList = "WE-IT004";
        public const string LoadRunningProfilesByUserId = "WE-IT005";
        public const string GetProfileInfo = "WE-IT006";
        public const string GetServerDateTime = "WE-IT007";
        public const string LoadRunningProfiles = "WE-IT008";
        public const string CurrentExecutionList = "WE-IT009";
        public const string CheckWindowsService = "WE-IT010";
        public const string GetWorkflowProfileInfoByProfileId = "WE-IT011";
        public const string GetInfraObjectById = "WE-IT012";
        public const string UpdateInfraObjectState = "WE-IT013";
        public const string GetWorkflowActionResultByGroupId = "WE-IT014";
        public const string TimeLineView = "WE-IT015";
        public const string GetWorkflowById = "WE-IT016";
        public const string GetWorkflowExecutionLogByGroupId = "WE-IT017";
        public const string LoadWorkflowOperation = "WE-IT018";
        public const string UpdateCompleteStatus = "WE-IT019";
        public const string UpdateConditions = "WE-IT020";
        public const string UpdateFailedConditions = "WE-IT021";
        public const string VerifyProfile = "WE-IT022";
        public const string GetLogDataByGroupId = "WE-IT023";
        public const string CreateWorkflowOperationGroup = "WE-IT024";
        public const string GetWorkflowProfileInfoList = "WE-IT025";
        public const string GetWorkflowProfileInfo = "WE-IT026";
        public const string GetActionRunningCount = "WE-IT027";
        public const string getActionDetails = "WE-IT028";
        public const string GetSnapList = "WE-IT029";
        public const string CreateWorkflowExecutionTemp = "WE-IT030";
        public const string UpdateWorkflowExecutionTemp = "WE-IT031";
        public const string DeleteWorkflowExecutionTemp = "WE-IT032";
        public const string UpdateWorkflowLogStatus = "WE-IT033";
    }

    public static class WorkflowExecutionTemp
    {
        public const string CreateOrUpdate = "WET-IT001";
        public const string Delete = "WET-IT002";
        public const string GetWorkflowExecutionTempByGroupId = "WET-IT003";
    }

    public static class WorkflowInfraObject
    {
        public const string CreateOrUpdate = "WI-IT001";
        public const string Delete = "WI-IT002";
        public const string GetWorkflowByInfraObjectIdAndActionType = "WI-IT003";
    }

    public static class WorkflowList
    {
        public const string CreateOrUpdate = "WL-IT001";
        public const string Delete = "WL-IT002";
        public const string GetPagination = "WL-IT003";
        public const string UpdateWorkflowLock = "WL-IT004";
        public const string UpdateWorkflowPublish = "WL-IT005";
        public const string AuthenticationWorkflow = "WL-IT006";
        public const string GetWorkflowNames = "WL-IT007";
        public const string GetWorkflowById = "WL-IT008";

    }

    public static class WorkflowProfileManagement
    {
        public const string CreateOrUpdate = "WPM-IT001";
        public const string Delete = "WPM-IT002";
        public const string IsWorkflowProfileNameExist = "WPM-IT003";
        public const string CreateWorkflowProfileInfo = "WPM-IT004";
        public const string UpdateWorkflowProfileInfo = "WPM-IT005";
        public const string GetBusinessFunctionByBusinessServiceId = "WPM-IT006";
        public const string GetInfraObjectByBusinessFunctionId = "WPM-IT007";
        public const string GetWorkflowInfraObjectByInfraObjectId = "WPM-IT008";
        public const string GetWorkflowProfileInfoByProfileId = "WPM-IT009";
        public const string DeleteWorkflowProfileInfo = "WPM-IT010";
        public const string WorkflowProfileInfo = "WPM-IT011";
        public const string GetWorkflowProfileById = "WPM-IT012";
        public const string ProfileChangePassword = "WPM-IT013";
        public const string IsWorkflowProfilePasswordExist = "WPM-IT014";
    }

    public static class WorkflowScheduleExecutionHistory
    {
        public const string CreateOrUpdate = "WSEH-IT001";
        public const string Delete = "WSEH-IT002";
        public const string GetPagination = "WSEH-IT003";
        public const string DownloadReport = "WSEH-IT004";
    }

    public static class WorkflowTemplate
    {
        public const string CreateOrUpdate = "WT-IT001";
        public const string Delete = "WT-IT002";
        public const string GetTemplateList = "WT-IT003";
        public const string TemplateDataEncrypt = "WT-IT004";
        public const string TemplateDataDecrypt = "WT-IT005";
        public const string GetTemplateById = "WT-IT006";
        public const string TemplateNameExist = "WT-IT007";
    }

    #endregion
    #region Report

    public static class PreBuildReport
    {
        public const string List = "PBR-R003";
        public const string GetInfraObject = "PBR-R004";
        public const string LicenseUtilizationReport = "PBR-R005";
        public const string GetDataLagStatusReport = "PBR-R006";
        public const string GetInfraObjectList = "PBR-R007";
        public const string GetUserNames = "PBR-R008";
        public const string GetUserActivityDetails = "PBR-R009";
        public const string GetRtoReport = "PBR-R010";
        public const string GetLicensePoNumber = "PBR-R011";
        public const string ChildLicensePoNumber = "PBR-R012";
        public const string GetChildPoNumberByParentId = "PBR-R013";
        public const string GetBusinessServiceName = "PBR-R014";
        public const string GetDrReadyBusinessServiceName = "PBR-R015";
        public const string GetWorkflow = "PBR-R016";
        public const string GetAllWorkflow = "PBR-R017";
        public const string GetDrReadyStatus = "PBR-R018";
        public const string GetDrReadyExecution = "PBR-R019";
        public const string GetInfraObjectSummary = "PBR-R020";
        public const string GetRpoSlaDeviationReport = "PBR-R021";
        public const string GetInfraObjectByBusinessServiceId = "PBR-R022";
        public const string GetRpoSLAReport = "PBR-R023";
        public const string UserReportGeneratedCount = "PBR-R024";
        public const string GetBusinessServiceSummaryReport = "PBR-R025";
        public const string LoadReport = "PBR-R026";
        public const string AllReportDownload = "PBR-R027";
        public const string GetServerIpAddress = "PBR-R028";
        public const string GetAirGapReport = "PBR-R029";
        public const string GetAirGapList = "PBR-R030";
        public const string GetDriftReport = "PBR-R031";
        public const string GetDriftReportInfraName = "PBR-R032";
        public const string GetDriftStatus = "PBR-R033";
        public const string GetInfraObjectIdBulkImport = "PBR-R034";
        public const string Download_BulkImport_Report = "PBR-R035";
        public const string GetCyberSnapsList = "PBR-R036";
        public const string GetCyberSnapsBySnapId = "PBR-R037";
        public const string GetAirGapLists = "PBR-R038";
        public const string GetResiliencySchedulerLogReportList = "PBR-R039";
        public const string GetCyberResiliencyScheduleLogReportList = "PBR-R040";
        public const string GetCompanyLogo = "PBR-R041";

    }

    public static class ReportScheduler
    {
        public const string CreateOrUpdate = "RS-R001";
        public const string Delete = "RS-R002";
        public const string List = "RS-R003";
        public const string Pagination="RS-R004";
        public const string IsExist = "RS-R005";
        public const string GetReportScheduleById = "RS-R006";
        public const string GetReportScheduleExecutionById = "RS-R007";
        public const string GetUserGroup = "RS-R008";
        public const string GetUsers = "RS-R009";
        public const string GetMonitorTypeByInfraObject = "RS-R010";
        public const string GetInfraObjectList = "RS-R011";
        public const string GetBusinessFunction = "RS-R012";
        public const string GetInfraBusinessFunction = "RS-R013";
        public const string GetInfraJob = "RS-R014";
    }
    public static class ViewReport
    {
        public const string List = "VR-R003";
     
    }
    #endregion


    #region Configuration

    public static class BiaRules
    {
        public const string CreateOrUpdate = "BIA-C001";
        public const string Delete = "BIA-C002";
        public const string List = "BIA-C003";
        public const string BiaImpactList = "BIA-C004";
        public const string BiaRulesByEntityIdAndType = "BIA-C005";
        public const string BusinessServiceList = "BIA-C006";
        public const string BusinessFunctionList = "BIA-C007";
        public const string InfraObjectByBusinessServiceId = "BIA-C008";
        public const string GetInfraObjectById = "BIA-C009";
        public const string BiaBusinessServiceTreeViewListByBusinessServiceId = "BIA-C010";
    }

    public static class BulkDatabaseCredential
    {
        public const string UpdateBulkPassword = "BDC-C001";
        public const string Delete = "BDC-C002";
        public const string List = "BDC-C003";
        public const string GetDatabase = "BDC-C004";
        public const string GetDatabaseByUserName = "BDC-C005";
    }

    public static class BulkImport
    {
        public const string SaveBulkImport = "BI-C001";
        public const string UpdateBulkImportOperation = "BI-C002";
        public const string UpdateBulkImportGroup = "BI-C003";
        public const string UpdateBulkImportOperationGroup = "BI-C003";
        public const string RollBackBulkImportAction = "BI-C004";
        public const string UpdateBulkImportActionResult = "BI-C005";
        public const string BulkImportValidation = "BI-C006";
        public const string GetSiteByName = "BI-C007";
        public const string GetBulkImportActionResult = "BI-C008";
        public const string GetBulkImportOperationGroup = "BI-C009";
        public const string GetBulkImportOperationGroupByOperationId = "BI-C010";
        public const string GetBulkImportOperation = "BI-C011";
        public const string GetBulkImportOperationsrunningStatus = "BI-C012";
        public const string DownloadFile = "BI-C013";
        public const string Save_DatabaseRecord = "BI-C014";
        public const string Save_ServerRecord = "BI-C015";
        public const string download_TemplateDatabase = "BI-C016";
        public const string Download_Template = "BI-C017";
        public const string UploadFileDatabase = "BI-C018";
        public const string UploadFile = "BI-C019";
        public const string List = "BI-C020";
    }

    public static class BulkImportInput
    {
        public const string List = "BII-C004";
        public const string Download_Template = "BII-C001";
    }

    public static class BulkServerCredential
    {
        public const string List = "BSC-C004";
        public const string UpdateBulkPassword = "BSC-C002";
        public const string GetServerByUserName = "BSC-C005";
    }

    public static class Company
    {
        public const string CreateOrUpdate = "COM-C001";
        public const string Delete = "COM-C002";
        public const string List = "COM-C003";
        public const string GetByReferenceId = "COM-C004";
        public const string IsCompanyNameExist = "COM-C005";
        public const string IsCompanyDisplayNameExist = "COM-C006";
        public const string GetCompanies = "COM-C007";
        public const string GetPagination = "COM-C008";
    }

    public static class Database
    {
        public const string CreateOrUpdate = "DB-C001";
        public const string Delete = "DB-C002";
        public const string List = "DB-C003";
        public const string GetByReferenceId = "DB-C004";
        public const string SaveAs = "DB-C005";
        public const string SaveAll = "DB-C006";
        public const string UpdateVersion = "DB-C007";
        public const string IsDatabaseNameExist = "DB-C008";
        public const string GetDatabaseNames = "DB-C009";
        public const string GetPagination = "DB-C010";
        public const string TestConnection = "DB-C011";
        public const string LoadReport = "DB-C012";
        public const string GetDatabaseNamesForSaveAs = "DB-C013";
        public const string GetDataBaseList = "DB-C014";
    }

    public static class DataSyncProperties
    {
        public const string CreateOrUpdate = "DSP-C001";
        public const string Delete = "DSP-C002";
        public const string List = "DSP-C003";
        public const string IsDataSyncNameExist = "DSP-C004";
        public const string GetPaginated = "DSP-C005";
    }


    public static class DrCalendar
    {
        public const string CreateOrUpdate = "DRC-C001";
        public const string Delete = "DRC-C002";
        public const string List = "DRC-C003";
        public const string IsActivityNameExist = "DRC-C004";
        public const string GetPaginated = "DRC-C005";
        public const string GetUserList = "DRC-C006";
        public const string DownloadFile = "DRC-C007";
        public const string GetBusinessService = "DRC-C008";
        public const string GetProfileNamesByBusinessServiceId = "DRC-C009";
    }

    public static class FiaCost
    {
        public const string CreateOrUpdate = "FIAC-C001";
        public const string Delete = "FIAC-C002";
        public const string List = "FIAC-C003";
        public const string GetPagination = "FIAC-C004";
        public const string GetFiaTemplateList = "FIAC-C005";
    }

    public static class FiaTemplate
    {
        public const string CreateOrUpdate = "FIT-C001";
        public const string Delete = "FIT-C002";
        public const string List = "FIT-C003";
        public const string GetByReferenceId = "FIT-C004";
        public const string IsFiaTemplateNameExist = "FIT-C005";
        public const string GetPagination = "FIT-C006";
        public const string GetTimeIntervalMasterList = "FIT-C007";
        public const string GetImpactMasterList = "FIT-C008";
        public const string GetImpactTypeMasterList = "FIT-C009";
        public const string GetList = "FIT-C010";
        public const string IsTemplateNameExist = "FIT-C011";
        public const string ImpactMasterNameExist = "FIT-C012";
        public const string ImpactTypeMasterNameExist = "FIT-C013";
        public const string TimeIntervalMasterDelete = "FIT-C014";
        public const string ImpactTypeMasterDelete = "FIT-C015";
        public const string ImpactMasterDelete = "FIT-C016";
        public const string FiaTemplateDelete = "FIT-C017";
    }

    public static class HacmpCluster
    {
        public const string CreateOrUpdate = "HAC-C001";
        public const string Delete = "HAC-C002";
        public const string List = "HAC-C003";
        public const string GetByReferenceId = "HAC-C004";
        public const string IsClusterNameExist = "HAC-C005";
        public const string GetPagination = "HAC-C006";
    }

    public static class InfraObject
    {
        public const string CreateOrUpdate = "INFRA-C001";
        public const string Delete = "INFRA-C002";
        public const string List = "INFRA-C003";
        public const string GetByReferenceId = "INFRA-C004";
        public const string IsInfraObjectNameExist = "INFRA-C005";
        public const string GetPagination = "INFRA-C006";
        public const string GetBusinessFunctions = "INFRA-C007";
        public const string GetInfraObjectNames = "INFRA-C008";
        public const string GetReplicationMasterByInfraMasterName = "INFRA-C009";
        public const string GetServerRoleTypeAndServerType = "INFRA-C010";
        public const string GetDatabase = "INFRA-C011";
        public const string GetReplicationList = "INFRA-C012";
        public const string GetInfraObjectByBusinessServiceId = "INFRA-C013";
        public const string GetServersByInfraObjectId = "INFRA-C014";
        public const string GetDatabaseListByName = "INFRA-C015";
        public const string GetTypeByDatabaseIdAndReplicationMasterId = "INFRA-C016";
        public const string SitePropertiesByBusinessService = "INFRA-C017";
        public const string GetSiteTypeDetails = "INFRA-C018";
        public const string GetVeritasClusters = "INFRA-C019";
        public const string GetHACMPClusters = "INFRA-C020";

    }

    public static class Node
    {
        public const string CreateOrUpdate = "Node-C001";
        public const string Delete = "Node-C002";
        public const string List = "Node-C003";
        public const string GetByReferenceId = "Node-C004";
        public const string IsNodeNameExist = "Node-C005";
        public const string GetPagination = "Node-C006";
        public const string GetNodeNames = "Node-C007";
    }

    public static class OperationalFunction
    {
        public const string CreateOrUpdate = "OF-C001";
        public const string Delete = "OF-C002";
        public const string List = "OF-C003";
        public const string GetByReferenceId = "OF-C004";
        public const string IsBusinessFunctionNameExist = "OF-C005";
        public const string GetPagination = "OF-C006";
        public const string GetNodeNames = "OF-C007";
    }

    public static class OperationalService
    {
        public const string CreateOrUpdate = "OS-C001";
        public const string Delete = "OS-C002";
        public const string List = "OS-C003";
        public const string GetByReferenceId = "OS-C004";
        public const string IsBusinessServiceNameExist = "OS-C005";
        public const string GetPagination = "OS-C006";
        public const string GetList = "OS-C007";
        public const string GetSiteByTypeAndCompanyId = "OS-C008";
        public const string GetBusinessServiceNames = "OS-C009";
    }

    public static class Replication
    {
        public const string CreateOrUpdate = "REP-C001";
        public const string Delete = "REP-C002";
        public const string List = "REP-C003";
        public const string GetByReferenceId = "REP-C004";
        public const string IsReplicationNameExist = "REP-C005";
        public const string GetPagination = "REP-C006";
        public const string GetList = "REP-C007";
        public const string DeleteSubTypes = "REP-C008";
        public const string GetReplicationById = "REP-C009";
        public const string GetReplicationNames = "REP-C0010";
        public const string SaveAsReplication = "REP-C0011";
        public const string SaveAllReplication = "REP-C0012";
        public const string ReplicationDataSyncProperties = "REP-C0013";
        public const string ReplicationRSyncOptions = "REP-C0014";
        public const string ReplicationRoboCopyOptions = "REP-C0015";
    }

    public static class RoboCopyOptions
    {
        public const string CreateOrUpdate = "RCO-C001";
        public const string Delete = "RCO-C002";
        public const string List = "RCO-C003";
        public const string GetByReferenceId = "RCO-C004";
        public const string IsRoboCopyNameExist = "RCO-C005";
        public const string GetPagination = "RCO-C006";
    }

    public static class RSyncOptions
    {
        public const string CreateOrUpdate = "RSO-C001";
        public const string Delete = "RSO-C002";
        public const string List = "RSO-C003";
        public const string GetByReferenceId = "RSO-C004";
        public const string IsRsyncNameExist = "RSO-C005";
        public const string GetPagination = "RSO-C006";
    }

    public static class Server
    {
        public const string CreateOrUpdate = "SER-C001";
        public const string Delete = "SER-C002";
        public const string List = "SER-C003";
        public const string GetByReferenceId = "SER-C004";
        public const string IsServerNameExist = "SER-C005";
        public const string GetPagination = "SER-C006";
        public const string GetServerNames = "SER-C007";
        public const string GetServerRole = "SER-C008";
        public const string GetServerType = "SER-C009";
        public const string GetServerListData = "SER-C010";
        public const string DatabaseServerNameList = "SER-C011";
        public const string GetSiteNames = "SER-C012";
        public const string GetServerList = "SER-C013";
        public const string UpdateServerFormVersion = "SER-C014";
        public const string ServerDataEncrypt = "SER-C015";
        public const string ServerDataDecrypt = "SER-C016";
        public const string ServerTestConnection = "SER-C017";
        public const string HashPassword = "SER-C018";
        public const string HashPasswordDecrypt = "SER-C019";
        public const string GetServerNamesForSaveAs = "SER-C020";
        public const string SaveAllServer = "SER-C021";
        public const string SaveAsServer = "SER-C022";
        public const string LoadReport = "SER-C023";
    }

    public static class ServerLog
    {
        public const string CreateOrUpdate = "SERL-C001";
        public const string Delete = "SERL-C002";
        public const string List = "SERL-C003";
        public const string GetByReferenceId = "SERL-C004";
        public const string IsServerLogNameExist = "SERL-C005";
        public const string GetPagination = "SERL-C006";
    }

    public static class ServerLogHistory
    {
        public const string CreateOrUpdate = "SERLH-C001";
        public const string Delete = "SERLH-C002";
        public const string List = "SERLH-C003";
        public const string GetByReferenceId = "SERLH-C004";
        public const string IsServerLogNameExist = "SERLH-C005";
        public const string GetPagination = "SERLH-C006";
        public const string GetServerLogList = "SERLH-C007";
        public const string GetFolderContents = "SERLH-C008";
        public const string Download = "SERLH-C009";
        public const string GetLogFileContents = "SERLH-C010";
        public const string GetSharedFolderPath = "SERLH-C011";
    }


    public static class SingleSignOn
    {
        public const string CreateOrUpdate = "SSO-C001";
        public const string Delete = "SSO-C002";
        public const string List = "SSO-C003";
        public const string GetByReferenceId = "SSO-C004";
        public const string IsSingleSignOnNameExist = "SSO-C005";
        public const string GetPagination = "SSO-C006";
        public const string GetSingleSignOnList = "SSO-C007";
        public const string GetSingleSignOnByType = "SSO-C008";
    }

    public static class Site
    {
        public const string CreateOrUpdate = "Site-C001";
        public const string Delete = "Site-C002";
        public const string List = "Site-C003";
        public const string GetByReferenceId = "Site-C004";
        public const string IsSiteNameExist = "Site-C005";
        public const string GetPagination = "Site-C006";
        public const string GetSiteByTypeAndCompanyId = "Site-C007";
        public const string GetComponentDetails = "Site-C008";
    }

    public static class SiteLocation
    {
        public const string CreateOrUpdate = "SL-C001";
        public const string Delete = "SL-C002";
        public const string List = "SL-C003";
        public const string GetByReferenceId = "SL-C004";
        public const string IsSiteLocationExist = "SL-C005";
        public const string GetPagination = "SL-C006";
    }

    public static class SiteType
    {
        public const string CreateOrUpdate = "ST-C001";
        public const string Delete = "ST-C002";
        public const string List = "ST-C003";
        public const string GetByReferenceId = "ST-C004";
        public const string IsSiteTypeExist = "ST-C005";
        public const string GetPagination = "ST-C006";
        public const string GetSiteTypeList = "ST-C007";
        public const string GetComponentDetails = "ST-C008";
    }

    public static class TeamMaster
    {
        public const string CreateOrUpdate = "TM-C001";
        public const string Delete = "TM-C002";
        public const string List = "TM-C003";
        public const string GetByReferenceId = "TM-C004";
        public const string IsTeamNameAlreadyExist = "TM-C005";
        public const string GetPagination = "TM-C006";
        public const string UpdateTeamList = "TM-C007";
    }

    public static class UserGroup
    {
        public const string CreateOrUpdate = "UG-C001";
        public const string Delete = "UG-C002";
        public const string List = "UG-C003";
        public const string GetByReferenceId = "UG-C004";
        public const string IsGroupNameExist = "UG-C005";
        public const string GetPagination = "UG-C006";
        public const string GetUserNames = "UG-C007";
    }

    public static class VeritasCluster
    {
        public const string CreateOrUpdate = "VC-C001";
        public const string Delete = "VC-C002";
        public const string List = "VC-C003";
        public const string GetByReferenceId = "VC-C004";
        public const string IsVeritasClusterNameExist = "VC-C005";
        public const string GetPagination = "VC-C006";
        public const string GetServerList = "VC-C007";
    }

    #endregion

    #region ResiliencyReadiness

    public static class ResiliencyDashboard
    {
        public const string List = "RD-RD003";
        public const string GetReadinessDetails = "RD-RD004";
        public const string GetBusinessServiceDrReadyByBusinessServiceId = "RD-RD005";
        public const string ImpactDetails = "RD-RD006";

    }

    public static class ManageResiliencyReadiness
    {
        public const string CreateOrUpdate = "MR-RD-001";
        public const string Delete = "MR-RD-002";
        public const string List = "MR-RD-003";
        public const string Pagination = "MR-RD-004";
        public const string ResetManageResilienceReadinessStatus = "MR-RD-005";
        public const string UpdateManageResilienceReadinessState = "MR-RD-006";
        public const string GetWorkflowNameByInfraId = "MR-RD-007";
       
    }
    #endregion

    #region Monitor

    public static class Monitor
    {
        public const string GetMonitorServiceStatusByIdAndType = "MON-MO004";
        public const string GetRPPagination = "MON-MO005";
        public const string GetRPConsistencyById = "MON-MO006";
        public const string GetDiscover = "MON-MO007";
        public const string GetCGHealthStatus = "MON-MO008";
        public const string GetPMaxStorageById = "MON-MO009";
        public const string GetFastMonitorList = "MON-MO010";
        public const string GetMonitorServiceByInfraObjectId = "MON-MO011";
        public const string GetInfraObjectDetailsById = "MON-MO012";
    }

    #endregion

    #region Alert

    public static class AlertDashboard
    {
        public const string List = "AD-AL003";
        public const string Pagination = "AD-AL004";
        public const string ListWeek = "AD-AL004";
        public const string GetFilterDate = "AD-AL005";
        public const string LastAlertCount = "AD-AL006";
        public const string GetEscalationMatrixList = "AD-AL007";
        public const string GetEscalationMatrixLevelByMatrixId = "AD-AL008";
        public const string GetEscalationMatrixOwnerByMatrixId = "AD-AL009";
        public const string LoadReport = "AD-AL010";
        public const string UpdateAlertMaster = "AD-AL011";
        public const string GetAlertDetailsByAlertId = "AD-AL012";
    }
    public static class ManageAlert
    {
        public const string CreateOrUpdate = "MA-AL001";
        public const string Delete = "MA-AL002";
        public const string List = "MA-AL003";
        public const string Pagination = "MA-AL004";
        public const string LoadReport = "MA-AL005";
        public const string IsAlertIdExist = "MA-AL006";
        public const string IsAlertNameExist = "MA-AL007";
    }
    #endregion
}