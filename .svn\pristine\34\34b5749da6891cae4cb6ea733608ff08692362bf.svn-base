﻿using ContinuityPatrol.Application.Features.Archive.Commands.Create;
using ContinuityPatrol.Application.Features.Archive.Commands.Update;
using ContinuityPatrol.Application.Features.Archive.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Archive.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;


namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class ArchiveController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<ArchiveController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public ArchiveController(IPublisher publisher, ILogger<ArchiveController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    [EventCode(EventCodes.Archive.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Archive");
        try
        {
           await _publisher.Publish(new ArchivePaginatedEvent());

            _logger.LogDebug("List method completed successfully in Archive, returning view.");

            return View();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on archive page while processing the list request", ex);

            return View();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Archive.GetPagination)]
    public async Task<JsonResult> GetPagination(GetTableAccessPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in Archive");

        try
        {
            _logger.LogDebug("Successfully retrieved tableaccess paginated list in archive");

            return Json(await _dataProvider.TableAccess.GetTableAccessPaginatedList(query));

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on archive page while processing the tableaccess pagination request.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Archive.GetPaginated)]
    public async Task<JsonResult> GetPaginated(GetArchivePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginated method in Archive");
        try
        {
            _logger.LogDebug("Successfully retrieved paginated list for archive");

            return Json(await _dataProvider.Archive.GetPaginatedArchives(query));

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on archive page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.Archive.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(CreateArchiveCommand createArchiveCommand, UpdateArchiveCommand updateArchiveCommand)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Archive");

        var userId = Request.Form["id"].ToString();

        try
        {
            if (userId.IsNullOrWhiteSpace())
            {
                var createCommand = _mapper.Map<CreateArchiveCommand>(createArchiveCommand);

                _logger.LogDebug($"Creating Archive '{createCommand.ArchiveProfileName}'.");

                var response = await _dataProvider.Archive.CreateAsync(createCommand);

                TempData.NotifySuccess(response.Message);
            }
            else
            {
                var updateCommand = _mapper.Map<UpdateArchiveCommand>(updateArchiveCommand);

                _logger.LogDebug($"Updating archive '{createArchiveCommand.ArchiveProfileName}'");

                var response = await _dataProvider.Archive.UpdateAsync(updateCommand);

                TempData.NotifySuccess(response.Message);
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in archive, returning view.");

            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation exception occurred on archive page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on archive page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [EventCode(EventCodes.Archive.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Archive");

        try
        {
            var result = await _dataProvider.Archive.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in archive");

            TempData.NotifySuccess(result.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on archive.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }

    }
    [HttpGet]
    [EventCode(EventCodes.Archive.IsArchiveNameExist)]
    public async Task<bool> IsArchiveNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsArchiveNameExist method in Archive");

        try
        {
            var nameExist = await _dataProvider.Archive.IsArchiveNameExist(name, id);

            _logger.LogDebug($"Returning result for IsArchiveNameExist as {nameExist} on archive");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on archive page while checking if archive name exists for : {name}.", ex);

            return false;
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Archive.IsTableNameExist)]
    public async Task<bool> IsTableNameExist(string tableName, string id)
    {
        _logger.LogDebug("Entering IsTableNameExist method in Archive");

        try
        {
            var isTableNameExist = await _dataProvider.Archive.IsTableNameExist(tableName, id);

            _logger.LogDebug($"Returning result for IsTableNameExist as {isTableNameExist} on archive");

            return isTableNameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on archive page while checking if table name exists for : {tableName}.", ex);

            return false;
        }
    }
}
