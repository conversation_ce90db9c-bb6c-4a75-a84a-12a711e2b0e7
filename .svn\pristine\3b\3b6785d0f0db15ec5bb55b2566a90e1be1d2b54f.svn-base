﻿// QUnit test file for Job Management functionality
QUnit.config.autostart = false;

// Mock URLs used in AJAX calls
window.monitoringJobUrl = {
    createOrUpdate: "Manage/JobManagement/CreateOrUpdate",
    nameExistUrl: "Manage/JobManagement/IsJobNameExist",
    resetMonitoringJob: "Manage/JobManagement/ResetJobStatus",
    updateJobState: "Manage/JobManagement/UpdateJobState",
    deleteUrl: "Manage/JobManagement/Delete"
};

// Wait for DOM to be ready
$(document).ready(function () {
    // Setup test DOM in qunit-fixture
    $('#qunit-fixture').html(`
        <div id="tblJobManagement_wrapper">
            <table id="tblJobManagement">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAllJobs" /></th>
                        <th>Name</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr data-job-id="job1">
                        <td><input type="checkbox" class="jobCheckbox" /></td>
                        <td>Test Job 1</td>
                        <td>Active</td>
                        <td>
                            <button class="jobEditButton" data-job="${btoa(JSON.stringify({ id: 'job1', name: 'Test Job 1', state: 'Active', solutionTypeId: 'repl1', groupPolicyId: 'policy1', infraObjectId: 'infra1', templateId: 'template1', executionPolicyId: '1', cronExpression: '0 0 12 * * ?', scheduleTime: '12:00:00', scheduleType: 'Daily' }))}">Edit</button>
                            <button class="jobResetButton" data-job="${btoa(JSON.stringify({ id: 'job1', name: 'Test Job 1', state: 'Active' }))}">Reset</button>
                            <button class="jobDeleteButton" data-job-id="job1" data-job-name="Test Job 1">Delete</button>
                        </td>
                    </tr>
                    <tr data-job-id="job2">
                        <td><input type="checkbox" class="jobCheckbox" /></td>
                        <td>Test Job 2</td>
                        <td>Inactive</td>
                        <td>
                            <button class="jobEditButton" data-job="${btoa(JSON.stringify({ id: 'job2', name: 'Test Job 2', state: 'Inactive', solutionTypeId: 'repl2', groupPolicyId: 'policy2', infraObjectId: 'infra2', templateId: 'template2', executionPolicyId: '2', cronExpression: '0 0 * * * ?', scheduleTime: '00:00:00', scheduleType: 'Hourly' }))}">Edit</button>
                            <button class="jobResetButton" data-job="${btoa(JSON.stringify({ id: 'job2', name: 'Test Job 2', state: 'Inactive' }))}">Reset</button>
                            <button class="jobDeleteButton" data-job-id="job2" data-job-name="Test Job 2">Delete</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <button id="CreteButton">Create</button>
        <div id="Activebtn">Activate</div>
        <div id="Inactivebtn">Deactivate</div>
        <div id="CreateModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <form id="jobForm">
                    <input type="hidden" id="textJobId" name="Id" />
                    <div>
                        <label for="textJobName">Job Name</label>
                        <input type="text" id="textJobName" name="Name" />
                        <span id="Name-error"></span>
                    </div>
                    <div>
                        <label for="selectSolutionType">Solution Type</label>
                        <select id="selectSolutionType" name="SolutionTypeId">
                            <option value="">Select Solution Type</option>
                            <option value="repl1">Replication Type 1</option>
                            <option value="repl2">Replication Type 2</option>
                        </select>
                        <span id="SolutionType-error"></span>
                    </div>
                    <div>
                        <label for="selectGroupPolicy">Group Policy</label>
                        <select id="selectGroupPolicy" name="GroupPolicyId">
                            <option value="">Select Group Policy</option>
                            <option value="policy1">Policy 1</option>
                            <option value="policy2">Policy 2</option>
                        </select>
                        <span id="GroupPolicy-error"></span>
                    </div>
                    <div>
                        <label for="selectInfraObjectName">Infra Object</label>
                        <select id="selectInfraObjectName" name="InfraObjectId">
                            <option value="">Select Infra Object</option>
                            <option value="infra1">Infra 1</option>
                            <option value="infra2">Infra 2</option>
                        </select>
                        <span id="InfraObjectName-error"></span>
                    </div>
                    <div>
                        <label for="selectTemplateName">Template</label>
                        <select id="selectTemplateName" name="TemplateId">
                            <option value="">Select Template</option>
                            <option value="template1">Template 1</option>
                            <option value="template2">Template 2</option>
                        </select>
                        <span id="TemplateName-error"></span>
                    </div>
                    <div>
                        <label for="selectExecutionPolicy">Execution Policy</label>
                        <select id="selectExecutionPolicy" name="ExecutionPolicyId">
                            <option value="">Select Execution Policy</option>
                            <option value="1">Policy 1</option>
                            <option value="2">Policy 2</option>
                        </select>
                        <span id="ExecutionPolicy-error"></span>
                    </div>
                    <div>
                        <label>State</label>
                        <input type="radio" id="textStateActive" name="State" value="Active" />
                        <label for="textStateActive">Active</label>
                        <input type="radio" id="textStateInactive" name="State" value="Inactive" />
                        <label for="textStateInactive">Inactive</label>
                        <span id="state-error"></span>
                    </div>
                    <div id="datetimeCronlist">
                        <ul class="nav nav-tabs">
                            <li class="nav-item">
                                <a class="nav-link active" id="nav-Minutes-tab" data-toggle="tab" href="#nav-Minutes">Minutes</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="nav-Hourly-tab" data-toggle="tab" href="#nav-Hourly">Hourly</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="nav-Daily-tab" data-toggle="tab" href="#nav-Daily">Daily</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="nav-Weekly-tab" data-toggle="tab" href="#nav-Weekly">Weekly</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="nav-Monthly-tab" data-toggle="tab" href="#nav-Monthly">Monthly</a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane fade show active" id="nav-Minutes">
                                <input type="text" id="txtMins" value="5" />
                                <span id="CronMin-error"></span>
                            </div>
                            <div class="tab-pane fade" id="nav-Hourly">
                                <input type="text" id="txtHours" value="1" />
                                <input type="text" id="txtMinutes" value="0" />
                            </div>
                            <div class="tab-pane fade" id="nav-Daily">
                                <input type="text" id="txtDailyTime" value="12:00" />
                            </div>
                            <div class="tab-pane fade" id="nav-Weekly">
                                <select id="txtWeekDay">
                                    <option value="1">Monday</option>
                                    <option value="2">Tuesday</option>
                                </select>
                                <input type="text" id="txtWeeklyTime" value="12:00" />
                            </div>
                            <div class="tab-pane fade" id="nav-Monthly">
                                <input type="text" id="txtMonthDay" value="1" />
                                <input type="text" id="txtMonthlyTime" value="12:00" />
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="textCronExpression" name="CronExpression" />
                    <input type="hidden" id="textScheduleTime" name="ScheduleTime" />
                    <input type="hidden" id="cronexpresstype" name="ScheduleType" value="Minutes" />
                    <input type="hidden" id="textStatus" name="Status" value="Pending" />
                    <button type="button" id="SaveFunction">Save</button>
                    <button type="button" id="CancelFunction">Cancel</button>
                </form>
            </div>
        </div>
        <div id="deleteModal" class="modal">
            <div class="modal-content">
                <p>Are you sure you want to delete this job?</p>
                <input type="hidden" id="textDeleteId" />
                <span id="deleteData"></span>
                <button id="confirmDeleteBtn">Yes</button>
                <button id="cancelDeleteBtn">No</button>
            </div>
        </div>
    `);

    // Mock utility functions
    window.notificationAlert = function (type, message) {
        window._lastAlert = { type: type, msg: message };
        console.log(`${type} alert: ${message}`);
    };

    window.errorNotification = function (error) {
        window._lastError = error;
        console.log(`Error: ${error}`);
    };

    window.ClearJobErrorElements = function () {
        $('.field-validation-error').text('').removeClass('field-validation-error');
    };

    window.populateModalFields = function (jobData) {
        const data = JSON.parse(atob(jobData));
        $('#textJobId').val(data.id);
        $('#textJobName').val(data.name);
        $('#selectSolutionType').val(data.solutionTypeId);
        $('#selectGroupPolicy').val(data.groupPolicyId);
        $('#selectInfraObjectName').val(data.infraObjectId);
        $('#selectTemplateName').val(data.templateId);
        $('#selectExecutionPolicy').val(data.executionPolicyId);

        if (data.state === 'Active') {
            $('#textStateActive').prop('checked', true);
        } else {
            $('#textStateInactive').prop('checked', true);
        }

        $('#textCronExpression').val(data.cronExpression);
        $('#textScheduleTime').val(data.scheduleTime);
        $('#cronexpresstype').val(data.scheduleType);

        // Set the appropriate tab active based on schedule type
        $(`#nav-${data.scheduleType}-tab`).tab('show');
    };

    window.validateJobName = function (name, id, url) {
        return new Promise((resolve) => {
            $('#Name-error').text('').removeClass('field-validation-error');

            if (!name) {
                $('#Name-error').text('Job name is required').addClass('field-validation-error');
                resolve(false);
                return;
            }

            if (/[<>'"&]/.test(name)) {
                $('#Name-error').text('Special characters not allowed').addClass('field-validation-error');
                resolve(false);
                return;
            }

            // Check if name exists
            if (name === "Existing Job" && id !== "job-existing") {
                $('#Name-error').text('Job name already exists').addClass('field-validation-error');
                resolve(false);
                return;
            }

            resolve(true);
        });
    };

    window.validateJobDropDown = function (value, errorMessage, errorElement) {
        errorElement.text('').removeClass('field-validation-error');

        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }

        return true;
    };

    window.ValidateRadioButton = function (errorElement) {
        errorElement.text('').removeClass('field-validation-error');

        if (!$('input[name="State"]:checked').length) {
            errorElement.text('Please select state').addClass('field-validation-error');
            return false;
        }

        return true;
    };

    window.CronValidation = function () {
        const activeTab = $('.nav-link.active').attr('id');
        let isValid = true;

        switch (activeTab) {
            case 'nav-Minutes-tab':
                if (!$('#txtMins').val()) {
                    $('#CronMin-error').text('Minutes required').addClass('field-validation-error');
                    isValid = false;
                }
                break;
            case 'nav-Hourly-tab':
                // Add validation for hourly if needed
                break;
            case 'nav-Daily-tab':
                // Add validation for daily if needed
                break;
            case 'nav-Weekly-tab':
                // Add validation for weekly if needed
                break;
            case 'nav-Monthly-tab':
                // Add validation for monthly if needed
                break;
        }

        return isValid;
    };

    window.JobCronExpression = function () {
        const scheduleType = $('#cronexpresstype').val();
        let cronExpression = '';
        let scheduleTime = '';

        switch (scheduleType) {
            case 'Minutes':
                const mins = $('#txtMins').val() || '5';
                cronExpression = `0 */${mins} * * * ?`;
                scheduleTime = `Every ${mins} minute(s)`;
                break;
            case 'Hourly':
                const hours = $('#txtHours').val() || '1';
                const minutes = $('#txtMinutes').val() || '0';
                cronExpression = `0 ${minutes} */${hours} * * ?`;
                scheduleTime = `Every ${hours} hour(s) at ${minutes} minute(s)`;
                break;
            case 'Daily':
                const dailyTime = $('#txtDailyTime').val() || '12:00';
                const [dailyHour, dailyMinute] = dailyTime.split(':');
                cronExpression = `0 ${dailyMinute} ${dailyHour} * * ?`;
                scheduleTime = `Daily at ${dailyTime}`;
                break;
            case 'Weekly':
                const weekDay = $('#txtWeekDay').val() || '1';
                const weeklyTime = $('#txtWeeklyTime').val() || '12:00';
                const [weeklyHour, weeklyMinute] = weeklyTime.split(':');
                cronExpression = `0 ${weeklyMinute} ${weeklyHour} ? * ${weekDay}`;
                scheduleTime = `Weekly on day ${weekDay} at ${weeklyTime}`;
                break;
            case 'Monthly':
                const monthDay = $('#txtMonthDay').val() || '1';
                const monthlyTime = $('#txtMonthlyTime').val() || '12:00';
                const [monthlyHour, monthlyMinute] = monthlyTime.split(':');
                cronExpression = `0 ${monthlyMinute} ${monthlyHour} ${monthDay} * ?`;
                scheduleTime = `Monthly on day ${monthDay} at ${monthlyTime}`;
                break;
        }

        return {
            CronExpression: cronExpression,
            listcron: scheduleTime
        };
    };

    // Mock AJAX calls
    sinon.stub($, "ajax").callsFake(function (options) {
        console.log("Mock AJAX called with:", options.url, options.data);
        const deferred = $.Deferred();

        if (options.url.includes("resetMonitoringJob")) {
            deferred.resolve({
                success: true,
                data: { message: "Job reset successfully" }
            });
        } else if (options.url.includes("updateJobState")) {
            deferred.resolve({
                success: true,
                data: { message: "Job state updated" }
            });
        } else if (options.url.includes("CreateOrUpdate")) {
            deferred.resolve({
                success: true,
                data: { message: "Job saved successfully" }
            });
        } else if (options.url.includes("Delete")) {
            deferred.resolve({
                success: true,
                data: { message: "Job deleted successfully" }
            });
        } else if (options.url.includes("IsJobNameExist")) {
            // Return true if name exists, false otherwise
            const nameExists = options.data.name === "Existing Job";
            deferred.resolve({
                success: true,
                data: nameExists
            });
        } else {
            deferred.resolve({
                success: true,
                data: { message: "Operation successful" }
            });
        }

        if (options.success) {
            options.success(deferred.resolve());
        }

        return deferred.promise();
    });

    // Mock $.get specifically
    sinon.stub($, "get").callsFake(function (url, data, callback) {
        console.log("Mock $.get called with:", url);

        let responseData = { success: true, data: false };

        if (url.includes("GetGroupPolicies")) {
            responseData.data = [
                { id: "policy1", name: "Policy 1" },
                { id: "policy2", name: "Policy 2" }
            ];
        } else if (url.includes("GetSolutionTypeByPolicy")) {
            responseData.data = [
                { id: "repl1", name: "Replication Type 1" },
                { id: "repl2", name: "Replication Type 2" }
            ];
        } else if (url.includes("GetInfraObjectListByReplicationTypeId")) {
            responseData.data = [
                { id: "infra1", name: "Infra 1" },
                { id: "infra2", name: "Infra 2" }
            ];
        } else if (url.includes("GetTemplateByReplicationTypeId")) {
            responseData.data = [
                { id: "template1", name: "Template 1" },
                { id: "template2", name: "Template 2" }
            ];
        }

        if (typeof callback === 'function') {
            callback(responseData);
        }

        return {
            done: function (callback) {
                callback(responseData);
                return this;
            },
            fail: function (callback) {
                return this;
            }
        };
    });

    // Mock modal functions
    $.fn.modal = function (action) {
        if (action === 'show') {
            this.addClass('show');
        } else if (action === 'hide') {
            this.removeClass('show');
        }
        return this;
    };

    // Mock tab functions
    $.fn.tab = function (action) {
        if (action === 'show') {
            const target = $(this).attr('href');
            $('.tab-pane').removeClass('show active');
            $(target).addClass('show active');
            $('.nav-link').removeClass('active');
            $(this).addClass('active');
        }
        return this;
    };

    // ============================================================================
    // MODULE 1: VALIDATION FUNCTIONS TESTS
    // ============================================================================
    QUnit.module('Job Management - Validation Functions', {
        beforeEach: function () {
            // Reset form values
            $("#jobForm")[0].reset();
            $('.field-validation-error').text('').removeClass('field-validation-error');
        },
        afterEach: function () {
            // Clean up after each test
            $.ajax.resetHistory();
            $.get.resetHistory();
        }
    });

    // Test job name validation
    QUnit.test("validateJobName - empty value", async function (assert) {
        const done = assert.async();

        let result = await validateJobName("", "", monitoringJobUrl.nameExistUrl);
        assert.notOk(result, "Empty name should be invalid");
        assert.ok($('#Name-error').hasClass('field-validation-error'), "Error class should be added for empty name");
        assert.equal($('#Name-error').text(), "Job name is required", "Correct error message for empty name");

        done();
    });

    QUnit.test("validateJobName - special characters", async function (assert) {
        const done = assert.async();

        let result = await validateJobName("Test<Job>", "", monitoringJobUrl.nameExistUrl);
        assert.notOk(result, "Name with special characters should be invalid");
        assert.ok($('#Name-error').hasClass('field-validation-error'), "Error class should be added for special characters");
        assert.equal($('#Name-error').text(), "Special characters not allowed", "Correct error message for special characters");

        done();
    });

    QUnit.test("validateJobName - existing name", async function (assert) {
        const done = assert.async();

        let result = await validateJobName("Existing Job", "", monitoringJobUrl.nameExistUrl);
        assert.notOk(result, "Existing name should be invalid");
        assert.ok($('#Name-error').hasClass('field-validation-error'), "Error class should be added for existing name");
        assert.equal($('#Name-error').text(), "Job name already exists", "Correct error message for existing name");

        done();
    });

    QUnit.test("validateJobName - valid name", async function (assert) {
        const done = assert.async();

        let result = await validateJobName("Valid Job Name", "", monitoringJobUrl.nameExistUrl);
        assert.ok(result, "Valid name should be valid");
        assert.notOk($('#Name-error').hasClass('field-validation-error'), "Error class should be removed for valid name");
        assert.equal($('#Name-error').text(), "", "No error message for valid name");

        done();
    });

    QUnit.test("validateJobName - same name for same job", async function (assert) {
        const done = assert.async();

        let result = await validateJobName("Existing Job", "job-existing", monitoringJobUrl.nameExistUrl);
        assert.ok(result, "Same name for same job should be valid");
        assert.notOk($('#Name-error').hasClass('field-validation-error'), "Error class should be removed for same name");

        done();
    });

    // Test dropdown validation
    QUnit.test("validateJobDropDown - empty value", function (assert) {
        let result = validateJobDropDown("", "Select solution type", $('#SolutionType-error'));
        assert.notOk(result, "Empty dropdown should be invalid");
        assert.ok($('#SolutionType-error').hasClass('field-validation-error'), "Error class should be added for empty dropdown");
        assert.equal($('#SolutionType-error').text(), "Select solution type", "Correct error message for empty dropdown");
    });

    QUnit.test("validateJobDropDown - valid value", function (assert) {
        let result = validateJobDropDown("value", "Select solution type", $('#SolutionType-error'));
        assert.ok(result, "Valid dropdown should be valid");
        assert.notOk($('#SolutionType-error').hasClass('field-validation-error'), "Error class should be removed for valid dropdown");
        assert.equal($('#SolutionType-error').text(), "", "No error message for valid dropdown");
    });

    QUnit.test("validateJobDropDown - all dropdowns", function (assert) {
        // Test all dropdowns
        let result = validateJobDropDown("", "Select group policy", $('#GroupPolicy-error'));
        assert.notOk(result, "Empty group policy should be invalid");
        assert.equal($('#GroupPolicy-error').text(), "Select group policy", "Correct error message for group policy");

        result = validateJobDropDown("", "Select infra object", $('#InfraObjectName-error'));
        assert.notOk(result, "Empty infra object should be invalid");
        assert.equal($('#InfraObjectName-error').text(), "Select infra object", "Correct error message for infra object");

        result = validateJobDropDown("", "Select template", $('#TemplateName-error'));
        assert.notOk(result, "Empty template should be invalid");
        assert.equal($('#TemplateName-error').text(), "Select template", "Correct error message for template");

        result = validateJobDropDown("", "Select execution policy", $('#ExecutionPolicy-error'));
        assert.notOk(result, "Empty execution policy should be invalid");
        assert.equal($('#ExecutionPolicy-error').text(), "Select execution policy", "Correct error message for execution policy");
    });

    // Test radio button validation
    QUnit.test("ValidateRadioButton - no selection", function (assert) {
        $('input[name="State"]').prop('checked', false);
        let result = ValidateRadioButton($('#state-error'));
        assert.notOk(result, "No selection should be invalid");
        assert.ok($('#state-error').hasClass('field-validation-error'), "Error class should be added for no selection");
        assert.equal($('#state-error').text(), "Please select state", "Correct error message for no selection");
    });

    QUnit.test("ValidateRadioButton - with selection", function (assert) {
        $('#textStateActive').prop('checked', true);
        let result = ValidateRadioButton($('#state-error'));
        assert.ok(result, "Selection should be valid");
        assert.notOk($('#state-error').hasClass('field-validation-error'), "Error class should be removed for selection");
        assert.equal($('#state-error').text(), "", "No error message for selection");
    });

    // Test cron expression validation
    QUnit.test("CronValidation - Minutes tab", function (assert) {
        $('#nav-Minutes-tab').tab('show');

        $('#txtMins').val('5');
        let result = CronValidation();
        assert.ok(result, "Valid minutes should be valid");
        assert.notOk($('#CronMin-error').hasClass('field-validation-error'), "Error class should be removed for valid minutes");

        $('#txtMins').val('');
        result = CronValidation();
        assert.notOk(result, "Empty minutes should be invalid");
        assert.ok($('#CronMin-error').hasClass('field-validation-error'), "Error class should be added for empty minutes");
        assert.equal($('#CronMin-error').text(), "Minutes required", "Correct error message for empty minutes");
    });

    QUnit.test("JobCronExpression - all schedule types", function (assert) {
        // Test Minutes
        $('#cronexpresstype').val('Minutes');
        $('#txtMins').val('5');
        let result = JobCronExpression();
        assert.equal(result.CronExpression, "0 */5 * * * ?", "Correct cron expression for minutes");
        assert.equal(result.listcron, "Every 5 minute(s)", "Correct schedule time for minutes");

        // Test Hourly
        $('#cronexpresstype').val('Hourly');
        $('#txtHours').val('1');
        $('#txtMinutes').val('0');
        result = JobCronExpression();
        assert.equal(result.CronExpression, "0 0 */1 * * ?", "Correct cron expression for hourly");
        assert.equal(result.listcron, "Every 1 hour(s) at 0 minute(s)", "Correct schedule time for hourly");

        // Test Daily
        $('#cronexpresstype').val('Daily');
        $('#txtDailyTime').val('12:00');
        result = JobCronExpression();
        assert.equal(result.CronExpression, "0 00 12 * * ?", "Correct cron expression for daily");
        assert.equal(result.listcron, "Daily at 12:00", "Correct schedule time for daily");

        // Test Weekly
        $('#cronexpresstype').val('Weekly');
        $('#txtWeekDay').val('1');
        $('#txtWeeklyTime').val('12:00');
        result = JobCronExpression();
        assert.equal(result.CronExpression, "0 00 12 ? * 1", "Correct cron expression for weekly");
        assert.equal(result.listcron, "Weekly on day 1 at 12:00", "Correct schedule time for weekly");

        // Test Monthly
        $('#cronexpresstype').val('Monthly');
        $('#txtMonthDay').val('1');
        $('#txtMonthlyTime').val('12:00');
        result = JobCronExpression();
        assert.equal(result.CronExpression, "0 00 12 1 * ?", "Correct cron expression for monthly");
        assert.equal(result.listcron, "Monthly on day 1 at 12:00", "Correct schedule time for monthly");
    });

    // ============================================================================
    // MODULE 2: UI INTERACTION TESTS
    // ============================================================================
    QUnit.module('Job Management - UI Interactions', {
        beforeEach: function () {
            // Reset form values
            $("#jobForm")[0].reset();
            $('.field-validation-error').text('').removeClass('field-validation-error');
            $('#CreateModal').removeClass('show');
            $('#deleteModal').removeClass('show');
        },
        afterEach: function () {
            // Clean up after each test
            $.ajax.resetHistory();
            $.get.resetHistory();
        }
    });

    // Test create button click
    QUnit.test("Create Button Click", function (assert) {
        // Trigger create button click
        $('#CreteButton').trigger('click');        
        assert.equal($('#textJobId').val(), '', "Job ID should be empty");
        assert.equal($('#textJobName').val(), '', "Job name should be empty");
        assert.equal($('#selectSolutionType').val(), '', "Solution type should be empty");
        assert.equal($('#selectGroupPolicy').val(), '', "Group policy should be empty");
        assert.equal($('#selectInfraObjectName').val(), '', "Infra object should be empty");
        assert.equal($('#selectTemplateName').val(), '', "Template should be empty");
        assert.equal($('#selectExecutionPolicy').val(), '', "Execution policy should be empty");
        assert.equal($('#textStateActive').prop('checked'), false, "Active state should not be checked");
        assert.equal($('#textStateInactive').prop('checked'), false, "Inactive state should not be checked");
        assert.equal($('#cronexpresstype').val(), 'Minutes', "Schedule type should default to Minutes");
        assert.equal($('#txtMins').val(), '5', "Minutes should default to 5");       
    });
    // ============================================================================
    // MODULE 3: SAVE/EDIT/UPDATE FUNCTIONALITY TESTS
    // ============================================================================
    QUnit.module('Job Management - Save/Edit/Update Functionality', {
        beforeEach: function () {
            // Reset form values
            $("#jobForm")[0].reset();
            $('.field-validation-error').text('').removeClass('field-validation-error');
            $('#CreateModal').removeClass('show');
            $('#deleteModal').removeClass('show');
            $.ajax.resetHistory();
            $.get.resetHistory();

            // Mock populateModalFields function
            window.populateModalFields = function (jobData) {
                $('#CreateModal').addClass('show');
                const data = JSON.parse(atob(jobData));
                $('#textJobId').val(data.id);
                $('#textJobName').val(data.name);
                $('#selectSolutionType').val(data.solutionTypeId);
                $('#selectGroupPolicy').val(data.groupPolicyId);
                $('#selectInfraObjectName').val(data.infraObjectId);
                $('#selectTemplateName').val(data.templateId);
                $('#selectExecutionPolicy').val(data.executionPolicyId);

                if (data.state === 'Active') {
                    $('#textStateActive').prop('checked', true);
                } else {
                    $('#textStateInactive').prop('checked', true);
                }

                // Set the appropriate tab active based on schedule type
                $(`#nav-${data.scheduleType}-tab`).tab('show');

                // Change button text to "Update"
                $('#SaveFunction').text("Update");
            };
        },
        afterEach: function () {
            // Clean up after each test
            $.ajax.resetHistory();
            $.get.resetHistory();
            delete window.populateModalFields;
        }
    });

   
    // Test save with invalid data
    QUnit.test("Save Job - Invalid Data Shows Errors", async function (assert) {
        const done = assert.async();

        // Click create button to reset form
        $('#CreteButton').trigger('click');

        // Leave required fields empty
        $('#textJobName').val('');
        $('#selectSolutionType').val('').trigger('change');
        $('#selectExecutionPolicy').val('').trigger('change');

        // Trigger save
        $('#SaveFunction').trigger('click');

        // Allow time for validation
        setTimeout(() => {
            // Check that AJAX was NOT called
            assert.notOk($.ajax.called, "AJAX should not be called with invalid data");
            done();
        }, 100);
    });
    // Start QUnit
    QUnit.start();
});