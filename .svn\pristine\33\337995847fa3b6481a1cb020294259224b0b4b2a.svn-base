using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Delete;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetByFormTypeId;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetFormTypeCategoryByName;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetList;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class FormTypeCategoryFixture : IDisposable
{
    public CreateFormTypeCategoryCommand CreateFormTypeCategoryCommand { get; }
    public CreateFormTypeCategoryResponse CreateFormTypeCategoryResponse { get; }
    public UpdateFormTypeCategoryCommand UpdateFormTypeCategoryCommand { get; }
    public UpdateFormTypeCategoryResponse UpdateFormTypeCategoryResponse { get; }
    public DeleteFormTypeCategoryCommand DeleteFormTypeCategoryCommand { get; }
    public DeleteFormTypeCategoryResponse DeleteFormTypeCategoryResponse { get; }
    public GetFormTypeCategoryDetailQuery GetFormTypeCategoryDetailQuery { get; }
    public FormTypeCategoryDetailVm FormTypeCategoryDetailVm { get; }
    public GetFormTypeCategoryByFormTypeIdQuery GetFormTypeCategoryByFormTypeIdQuery { get; }
    public FormTypeCategoryByFormTypeIdVm FormTypeCategoryByFormTypeIdVm { get; }
    public GetFormTypeCategoryListQuery GetFormTypeCategoryListQuery { get; }
    public List<FormTypeCategoryListVm> FormTypeCategoryListVm { get; }
    public GetFormTypeCategoryNameQuery GetFormTypeCategoryNameQuery { get; }
    public List<FormTypeCategoryNameVm> FormTypeCategoryNameVm { get; }
    public GetFormTypeCategoryNameUniqueQuery GetFormTypeCategoryNameUniqueQuery { get; }
    public GetFormTypeCategoryPaginatedListQuery GetFormTypeCategoryPaginatedListQuery { get; }
    public PaginatedResult<FormTypeCategoryListVm> FormTypeCategoryPaginatedResult { get; }
    public GetFormTypeCategoryByNameQuery GetFormTypeCategoryByNameQuery { get; }
    public List<FormTypeCategoryByNameVm> FormTypeCategoryByNameVm { get; }

    public FormTypeCategoryFixture()
    {
        CreateFormTypeCategoryCommand = AutoFormTypeCategoryFixture.Create<CreateFormTypeCategoryCommand>();
        CreateFormTypeCategoryResponse = AutoFormTypeCategoryFixture.Create<CreateFormTypeCategoryResponse>();
        UpdateFormTypeCategoryCommand = AutoFormTypeCategoryFixture.Create<UpdateFormTypeCategoryCommand>();
        UpdateFormTypeCategoryResponse = AutoFormTypeCategoryFixture.Create<UpdateFormTypeCategoryResponse>();
        DeleteFormTypeCategoryCommand = AutoFormTypeCategoryFixture.Create<DeleteFormTypeCategoryCommand>();
        DeleteFormTypeCategoryResponse = AutoFormTypeCategoryFixture.Create<DeleteFormTypeCategoryResponse>();
        GetFormTypeCategoryDetailQuery = AutoFormTypeCategoryFixture.Create<GetFormTypeCategoryDetailQuery>();
        FormTypeCategoryDetailVm = AutoFormTypeCategoryFixture.Create<FormTypeCategoryDetailVm>();
        GetFormTypeCategoryByFormTypeIdQuery = AutoFormTypeCategoryFixture.Create<GetFormTypeCategoryByFormTypeIdQuery>();
        FormTypeCategoryByFormTypeIdVm = AutoFormTypeCategoryFixture.Create<FormTypeCategoryByFormTypeIdVm>();
        GetFormTypeCategoryListQuery = AutoFormTypeCategoryFixture.Create<GetFormTypeCategoryListQuery>();
        FormTypeCategoryListVm = AutoFormTypeCategoryFixture.CreateMany<FormTypeCategoryListVm>(3).ToList();
        GetFormTypeCategoryNameQuery = AutoFormTypeCategoryFixture.Create<GetFormTypeCategoryNameQuery>();
        FormTypeCategoryNameVm = AutoFormTypeCategoryFixture.CreateMany<FormTypeCategoryNameVm>(3).ToList();
        GetFormTypeCategoryNameUniqueQuery = AutoFormTypeCategoryFixture.Create<GetFormTypeCategoryNameUniqueQuery>();
        GetFormTypeCategoryPaginatedListQuery = AutoFormTypeCategoryFixture.Create<GetFormTypeCategoryPaginatedListQuery>();
        FormTypeCategoryPaginatedResult = AutoFormTypeCategoryFixture.Create<PaginatedResult<FormTypeCategoryListVm>>();
        GetFormTypeCategoryByNameQuery = AutoFormTypeCategoryFixture.Create<GetFormTypeCategoryByNameQuery>();
        FormTypeCategoryByNameVm = AutoFormTypeCategoryFixture.CreateMany<FormTypeCategoryByNameVm>(3).ToList();
    }

    public Fixture AutoFormTypeCategoryFixture
    {
        get
        {
            var fixture = new Fixture();

            // Configure fixture for enterprise form type category scenarios
            fixture.Customize<CreateFormTypeCategoryCommand>(c => c
                .With(x => x.Name, "Enterprise Risk Management Category")
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.FormName, "Enterprise Risk Assessment Form")
                .With(x => x.FormTypeId, Guid.NewGuid().ToString())
                .With(x => x.FormTypeName, "Enterprise Risk Management")
                .With(x => x.Logo, "risk-management-logo.png")
                .With(x => x.Version, "1.0")
                .With(x => x.Properties, "{\"category\":\"risk\",\"priority\":\"high\",\"department\":\"Risk Management\"}")
                .With(x => x.FormVersion, "1.0"));

            fixture.Customize<UpdateFormTypeCategoryCommand>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Updated Enterprise Risk Management Category")
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.FormName, "Updated Enterprise Risk Assessment Form")
                .With(x => x.FormTypeId, Guid.NewGuid().ToString())
                .With(x => x.FormTypeName, "Updated Enterprise Risk Management")
                .With(x => x.Logo, "updated-risk-management-logo.png")
                .With(x => x.Version, "1.1")
                .With(x => x.Properties, "{\"category\":\"risk\",\"priority\":\"critical\",\"department\":\"Risk Management\"}")
                .With(x => x.FormVersion, "1.1"));

            fixture.Customize<DeleteFormTypeCategoryCommand>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString()));

            fixture.Customize<CreateFormTypeCategoryResponse>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Success, true)
                .With(x => x.Message, "Form type category created successfully"));

            fixture.Customize<UpdateFormTypeCategoryResponse>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Success, true)
                .With(x => x.Message, "Form type category updated successfully"));

            fixture.Customize<DeleteFormTypeCategoryResponse>(c => c
                .With(x => x.Success, true)
                .With(x => x.Message, "Form type category deleted successfully")
                .With(x => x.IsActive, false));

            fixture.Customize<FormTypeCategoryDetailVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Compliance Category")
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.FormName, "Enterprise Compliance Form")
                .With(x => x.FormTypeId, Guid.NewGuid().ToString())
                .With(x => x.FormTypeName, "Enterprise Compliance Forms")
                .With(x => x.Logo, "compliance-logo.png")
                .With(x => x.Version, "1.0")
                .With(x => x.Properties, "{\"category\":\"compliance\",\"priority\":\"high\",\"department\":\"Compliance\"}")
                .With(x => x.FormVersion, "1.0"));

            fixture.Customize<FormTypeCategoryByFormTypeIdVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Security Category")
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.FormName, "Enterprise Security Form")
                .With(x => x.FormTypeId, Guid.NewGuid().ToString())
                .With(x => x.FormTypeName, "Enterprise Security Forms")
                .With(x => x.Logo, "security-logo.png")
                .With(x => x.Version, "1.0")
                .With(x => x.Properties, "{\"category\":\"security\",\"priority\":\"critical\",\"department\":\"Security\"}")
                .With(x => x.FormVersion, "1.0"));

            fixture.Customize<FormTypeCategoryListVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Business Continuity Category")
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.FormName, "Enterprise Business Continuity Form")
                .With(x => x.FormTypeId, Guid.NewGuid().ToString())
                .With(x => x.FormTypeName, "Enterprise Business Continuity Forms")
                .With(x => x.Logo, "business-continuity-logo.png")
                .With(x => x.Version, "1.0")
                .With(x => x.Properties, "{\"category\":\"business-continuity\",\"priority\":\"high\",\"department\":\"Business Continuity\"}")
                .With(x => x.IsMapped, true)
                .With(x => x.FormVersion, "1.0"));

            fixture.Customize<FormTypeCategoryNameVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Security Category"));

            fixture.Customize<FormTypeCategoryByNameVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Audit Category")
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.FormName, "Enterprise Audit Form")
                .With(x => x.FormTypeId, Guid.NewGuid().ToString())
                .With(x => x.FormTypeName, "Enterprise Audit Forms")
                .With(x => x.Logo, "audit-logo.png")
                .With(x => x.Version, "1.0")
                .With(x => x.Properties, "{\"category\":\"audit\",\"priority\":\"medium\",\"department\":\"Audit\"}")
                .With(x => x.FormVersion, "1.0"));

            fixture.Customize<GetFormTypeCategoryDetailQuery>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetFormTypeCategoryByFormTypeIdQuery>(c => c
                .With(x => x.FormTypeId, Guid.NewGuid().ToString())
                .With(x => x.Version, "1.0"));

            fixture.Customize<GetFormTypeCategoryNameUniqueQuery>(c => c
                .With(x => x.Name, "Enterprise Category")
                .With(x => x.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetFormTypeCategoryPaginatedListQuery>(c => c
                .With(x => x.PageNumber, 1)
                .With(x => x.PageSize, 10));

            fixture.Customize<GetFormTypeCategoryByNameQuery>(c => c
                .With(x => x.Name, "Enterprise Category"));

            fixture.Customize<PaginatedResult<FormTypeCategoryListVm>>(c => c
                .With(x => x.Succeeded, true)
                .With(x => x.PageSize, 10)
                .With(x => x.CurrentPage, 1));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
