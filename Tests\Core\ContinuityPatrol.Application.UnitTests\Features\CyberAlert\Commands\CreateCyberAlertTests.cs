using ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Commands;

public class CreateCyberAlertTests : IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<CreateCyberAlertCommandHandler>> _mockLogger;
    private readonly CreateCyberAlertCommandHandler _handler;

    private readonly Mock<IMapper> _mapper;
    private readonly Mock<IPublisher> _publisher;
    public CreateCyberAlertTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);
        _mockUserActivityRepository = CyberRepositoryMocks.CreateUserActivityRepository(_cyberAlertFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<CreateCyberAlertCommandHandler>>();


        // Setup default user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");
        _mapper = new Mock<IMapper>();
        _publisher = new Mock<IPublisher>();
        _handler = new CreateCyberAlertCommandHandler(_mapper.Object , _mockCyberAlertRepository.Object , _publisher.Object );
    }

    [Fact]
    public async Task Handle_CreateCyberAlert_When_ValidCommand()
    {
        // Arrange
        var command = new CreateCyberAlertCommand
        {
            Severity = "High",
            Type = "Intrusion",
            JobName = "TestJob"
        };

        //Domain.Entities.CyberAlert createdEntity = null;
        _mapper.Setup(m => m.Map<Domain.Entities.CyberAlert>(It.IsAny<CreateCyberAlertCommand>()))
    .Returns((CreateCyberAlertCommand cmd) => new Domain.Entities.CyberAlert
    {
        Severity = cmd.Severity,
        Type = cmd.Type,
        JobName = cmd.JobName,
        IsActive = true
    });

        Domain.Entities.CyberAlert? createdEntity = null;
        _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity =>
            {
                if (entity == null)
                    throw new ArgumentNullException(nameof(entity), "Entity passed to AddAsync is null. Check your mapper setup.");
                createdEntity = entity;
                createdEntity.ReferenceId = Guid.NewGuid().ToString();
            })
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity =>
            {
                createdEntity = entity;
                createdEntity.ReferenceId = Guid.NewGuid().ToString(); // Ensure ReferenceId is set
            })
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        _mockCyberAlertRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateEntityWithCorrectProperties_When_ValidCommand()
    {
        // Arrange
        var command = new CreateCyberAlertCommand
        {
            Type = "Malware"
        };

        _mapper.Setup(m => m.Map<Domain.Entities.CyberAlert>(It.IsAny<CreateCyberAlertCommand>()))
   .Returns((CreateCyberAlertCommand cmd) => new Domain.Entities.CyberAlert
   {
      
       Type = cmd.Type
   });

        Domain.Entities.CyberAlert? createdEntity = null;
        _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity =>
            {
                if (entity == null)
                    throw new ArgumentNullException(nameof(entity), "Entity passed to AddAsync is null. Check your mapper setup.");
                createdEntity = entity;
                createdEntity.ReferenceId = Guid.NewGuid().ToString();
            })
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);


        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        createdEntity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var command = new CreateCyberAlertCommand
        {
            Severity = "Medium"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        
    }

    [Fact]
    public async Task Handle_ProcessMultipleCommands_When_ValidCommands()
    {
        // Arrange
        var commands = new[]
        {
            new CreateCyberAlertCommand
            {
                Severity = "Low"
            },
            new CreateCyberAlertCommand
            {
                Severity = "Medium"
            },
            new CreateCyberAlertCommand
            {
                Severity = "High"
            }
        };

        _mapper.Setup(m => m.Map<Domain.Entities.CyberAlert>(It.IsAny<CreateCyberAlertCommand>()))
  .Returns((CreateCyberAlertCommand cmd) => new Domain.Entities.CyberAlert
  {

      Severity = cmd.Type
  });

        Domain.Entities.CyberAlert? createdEntity = null;
        _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity =>
            {
                if (entity == null)
                    throw new ArgumentNullException(nameof(entity), "Entity passed to AddAsync is null. Check your mapper setup.");
                createdEntity = entity;
                createdEntity.ReferenceId = Guid.NewGuid().ToString();
            })
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);


        // Act
        foreach (var command in commands)
        {
            var result = await _handler.Handle(command, CancellationToken.None);
            result.Success.ShouldBeTrue();
        }

        // Assert
        _mockCyberAlertRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()), Times.Exactly(3));
    }

    
    [Fact]
    public async Task Handle_HandleDifferentSeverityLevels_When_ValidCommands()
    {
        // Arrange
        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };

        foreach (var severity in severityLevels)
        {
            var command = new CreateCyberAlertCommand
            {
                Severity = severity
            };

            _mapper.Setup(m => m.Map<Domain.Entities.CyberAlert>(It.IsAny<CreateCyberAlertCommand>()))
   .Returns((CreateCyberAlertCommand cmd) => new Domain.Entities.CyberAlert
   {

       Severity = cmd.Severity
   });

            Domain.Entities.CyberAlert? createdEntity = null;
            _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
                .Callback<Domain.Entities.CyberAlert>(entity =>
                {
                    if (entity == null)
                        throw new ArgumentNullException(nameof(entity), "Entity passed to AddAsync is null. Check your mapper setup.");
                    createdEntity = entity;
                    createdEntity.ReferenceId = Guid.NewGuid().ToString();
                })
                .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Success.ShouldBeTrue();
            createdEntity.ShouldNotBeNull();
            createdEntity.Severity.ShouldBe(severity);
        }
    }

    [Fact]
    public async Task Handle_HandleDifferentAlertTypes_When_ValidCommands()
    {
        // Arrange
        var alertTypes = new[] { "Intrusion", "Malware", "DDoS", "Phishing", "Vulnerability" };

        foreach (var alertType in alertTypes)
        {
            var command = new CreateCyberAlertCommand
            {
                Type = alertType,
                Severity = "Medium"
            };

            _mapper.Setup(m => m.Map<Domain.Entities.CyberAlert>(It.IsAny<CreateCyberAlertCommand>()))
   .Returns((CreateCyberAlertCommand cmd) => new Domain.Entities.CyberAlert
   {

       Severity = cmd.Severity
   });

            Domain.Entities.CyberAlert? createdEntity = null;
            _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
                .Callback<Domain.Entities.CyberAlert>(entity =>
                {
                    if (entity == null)
                        throw new ArgumentNullException(nameof(entity), "Entity passed to AddAsync is null. Check your mapper setup.");
                    createdEntity = entity;
                    createdEntity.ReferenceId = Guid.NewGuid().ToString();
                })
                .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Success.ShouldBeTrue();
            createdEntity.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Handle_HandleComplexAlertData_When_ValidCommand()
    {
        // Arrange
        var complexAlertData = @"{
            ""source_ip"": ""*************"",
            ""destination_ip"": ""*********"",
            ""port"": 443,
            ""protocol"": ""HTTPS"",
            ""payload_size"": 1024,
            ""attack_vector"": ""SQL Injection"",
            ""confidence_score"": 0.95,
            ""metadata"": {
                ""user_agent"": ""Mozilla/5.0"",
                ""request_method"": ""POST"",
                ""response_code"": 200
            }
        }";

        var command = new CreateCyberAlertCommand
        {
            Severity = "High"
        };

        _mapper.Setup(m => m.Map<Domain.Entities.CyberAlert>(It.IsAny<CreateCyberAlertCommand>()))
   .Returns((CreateCyberAlertCommand cmd) => new Domain.Entities.CyberAlert
   {

       Severity = cmd.Severity
   });

        Domain.Entities.CyberAlert? createdEntity = null;
        _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity =>
            {
                if (entity == null)
                    throw new ArgumentNullException(nameof(entity), "Entity passed to AddAsync is null. Check your mapper setup.");
                createdEntity = entity;
                createdEntity.ReferenceId = Guid.NewGuid().ToString();
            })
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.ShouldBeTrue();
        createdEntity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CreateBasicEntity_When_CommandWithMinimalProperties()
    {
        // Arrange
        var command = new CreateCyberAlertCommand
        {
            Severity = "Medium"
        };

        _mapper.Setup(m => m.Map<Domain.Entities.CyberAlert>(It.IsAny<CreateCyberAlertCommand>()))
   .Returns((CreateCyberAlertCommand cmd) => new Domain.Entities.CyberAlert
   {

       Severity = cmd.Severity
   });

        Domain.Entities.CyberAlert? createdEntity = null;
        _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity =>
            {
                if (entity == null)
                    throw new ArgumentNullException(nameof(entity), "Entity passed to AddAsync is null. Check your mapper setup.");
                createdEntity = entity;
                createdEntity.ReferenceId = Guid.NewGuid().ToString();
            })
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        createdEntity.ShouldNotBeNull();
        createdEntity.Severity.ShouldBe("Medium");
        createdEntity.IsActive.ShouldBeTrue();
        result.Success.ShouldBeTrue();
    }
    [Fact]
    public async Task Handle_HandleSpecialCharacters_When_CommandWithSpecialChars()
    {
        // Arrange
        var command = new CreateCyberAlertCommand
        {
            Severity = "High"
        };

        _mapper.Setup(m => m.Map<Domain.Entities.CyberAlert>(It.IsAny<CreateCyberAlertCommand>()))
   .Returns((CreateCyberAlertCommand cmd) => new Domain.Entities.CyberAlert
   {

       Severity = cmd.Severity
   });

        Domain.Entities.CyberAlert? createdEntity = null;
        _mockCyberAlertRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAlert>()))
            .Callback<Domain.Entities.CyberAlert>(entity =>
            {
                if (entity == null)
                    throw new ArgumentNullException(nameof(entity), "Entity passed to AddAsync is null. Check your mapper setup.");
                createdEntity = entity;
                createdEntity.ReferenceId = Guid.NewGuid().ToString();
            })
            .ReturnsAsync((Domain.Entities.CyberAlert entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        createdEntity.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
    }
}
