﻿using ContinuityPatrol.Application.Features.Incident.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Queries;

public class GetIncidentDetailQueryHandlerTests : IClassFixture<IncidentFixture>
{
    private readonly IncidentFixture _incidentFixture;
    private readonly Mock<IIncidentRepository> _mockIncidentRepository;
    private readonly GetIncidentDetailQueryHandler _handler;

    public GetIncidentDetailQueryHandlerTests(IncidentFixture incidentFixture)
    {
        _incidentFixture = incidentFixture;

        _mockIncidentRepository = IncidentRepositoryMocks.GetIncidentRepository(_incidentFixture.Incidents);

        _handler = new GetIncidentDetailQueryHandler(_mockIncidentRepository.Object, _incidentFixture.Mapper);

        _incidentFixture.Incidents[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
    }

    [Fact]
    public async Task Handle_Return_IncidentDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetIncidentDetailQuery { Id = _incidentFixture.Incidents[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<GetIncidentDetailVm>();
        result.Id.ShouldNotBeNullOrEmpty();
        result.IncidentName.ShouldNotBeEmpty();
        result.IncidentNumber.ShouldNotBeEmpty();
        result.Description.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidIncidentId()
    {
        var invalidId = Guid.NewGuid().ToString();

        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetIncidentDetailQuery { Id = invalidId }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("Incident");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        await _handler.Handle(new GetIncidentDetailQuery { Id = _incidentFixture.Incidents[0].ReferenceId }, CancellationToken.None);

        _mockIncidentRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_MappedIncidentDetails_When_ValidId()
    {
        var incident = _incidentFixture.Incidents[0];
        incident.ReferenceId = Guid.NewGuid().ToString();

        var result = await _handler.Handle(new GetIncidentDetailQuery { Id = incident.ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<GetIncidentDetailVm>();
        result.Id.ShouldBe(incident.ReferenceId);
        result.IncidentName.ShouldBe(incident.IncidentName);
        result.IncidentNumber.ShouldBe(incident.IncidentNumber);
        result.Description.ShouldBe(incident.Description);
        result.AlertId.ShouldBe(incident.AlertId);
        result.WorkflowId.ShouldBe(incident.WorkflowId);
        result.WorkflowName.ShouldBe(incident.WorkflowName);
        result.BusinessServiceId.ShouldBe(incident.BusinessServiceId);
        result.BusinessServiceName.ShouldBe(incident.BusinessServiceName);
        result.BusinessFunctionId.ShouldBe(incident.BusinessFunctionId);
        result.BusinessFunctionName.ShouldBe(incident.BusinessFunctionName);
        result.InfraObjectId.ShouldBe(incident.InfraObjectId);
        result.InfraObjectName.ShouldBe(incident.InfraObjectName);
    }
}