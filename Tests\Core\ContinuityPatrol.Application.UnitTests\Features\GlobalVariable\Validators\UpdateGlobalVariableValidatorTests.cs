using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Validators;

public class UpdateGlobalVariableValidatorTests : IClassFixture<GlobalVariableFixture>
{
    private readonly Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;

    private readonly GlobalVariableFixture _globalVariableFixture;

    public UpdateGlobalVariableValidatorTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        var globalVariables = new Fixture().Create<List<Domain.Entities.GlobalVariable>>();

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.UpdateGlobalVariableRepository(globalVariables);
    }

    [Theory]
    [AutoGlobalVariableData]
    public async Task Verify_Create_ValidCommand_ShouldPass(UpdateGlobalVariableCommand updateGlobalVariableCommand)
    {
        var validator = new UpdateGlobalVariableCommandValidator(_mockGlobalVariableRepository.Object);

        updateGlobalVariableCommand.VariableName = "TestVariable";
        updateGlobalVariableCommand.VariableValue = "TestValue";
        updateGlobalVariableCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateGlobalVariableCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeTrue();
    }

}
