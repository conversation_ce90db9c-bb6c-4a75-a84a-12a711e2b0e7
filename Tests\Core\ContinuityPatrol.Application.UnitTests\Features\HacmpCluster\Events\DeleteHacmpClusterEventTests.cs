﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.HacmpCluster.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Events;

public class DeleteHacmpClusterEventTests : IClassFixture<HacmpClusterFixture>, IClassFixture<UserActivityFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly HacmpClusterDeletedEventHandler _handler;

    public DeleteHacmpClusterEventTests(HacmpClusterFixture hacmpClusterFixture, UserActivityFixture userActivityFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockHacmpClusterEventLogger = new Mock<ILogger<HacmpClusterDeletedEventHandler>>();

        _mockUserActivityRepository = HacmpClusterRepositoryMocks.CreateHacmpClusterEventRepository(_userActivityFixture.UserActivities);

        _handler = new HacmpClusterDeletedEventHandler(mockLoggedInUserService.Object, mockHacmpClusterEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateCompanyEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_hacmpClusterFixture.HacmpClusterDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_hacmpClusterFixture.HacmpClusterDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}