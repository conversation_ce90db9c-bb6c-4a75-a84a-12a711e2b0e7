﻿using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalSetting.Queries;

public class GetGlobalSettingNameUniqueQueryHandlerTests : IClassFixture<GlobalSettingFixture>
{
    private readonly GlobalSettingFixture _globalSettingFixture;
    private Mock<IGlobalSettingRepository> _mockGlobalSettingRepository;
    private readonly GetGlobalSettingNameUniqueQueryHandler _handler;

    public GetGlobalSettingNameUniqueQueryHandlerTests(GlobalSettingFixture globalSettingFixture)
    {
        _globalSettingFixture = globalSettingFixture;

        _mockGlobalSettingRepository = GlobalSettingRepositoryMocks.GetGlobalSettingNameUniqueRepository(_globalSettingFixture.GlobalSettings);

        _handler = new GetGlobalSettingNameUniqueQueryHandler(_mockGlobalSettingRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_GlobalSettingKey_Exist()
    {
        _globalSettingFixture.GlobalSettings[0].GlobalSettingKey = "TestKey";
        _globalSettingFixture.GlobalSettings[0].IsActive = true;

        var result = await _handler.Handle(new GetGlobalSettingNameUniqueQuery { GlobalSettingKey = _globalSettingFixture.GlobalSettings[0].GlobalSettingKey, Id = _globalSettingFixture.GlobalSettings[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_GlobalSettingKeyAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetGlobalSettingNameUniqueQuery { GlobalSettingKey = "NonExistentKey", Id = "1" }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_GlobalSettingKey_NotMatch()
    {
        var result = await _handler.Handle(new GetGlobalSettingNameUniqueQuery { GlobalSettingKey = "NonExistentKey", Id = "0" }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsGlobalSettingKeyExist_OneTime()
    {
        await _handler.Handle(new GetGlobalSettingNameUniqueQuery(), CancellationToken.None);

        _mockGlobalSettingRepository.Verify(x => x.IsGlobalSettingKeyExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_When_NoRecords()
    {
        _mockGlobalSettingRepository = GlobalSettingRepositoryMocks.GetGlobalSettingEmptyRepository();

        var handler = new GetGlobalSettingNameUniqueQueryHandler(_mockGlobalSettingRepository.Object);
        var result = await handler.Handle(new GetGlobalSettingNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_Return_True_When_GlobalSettingKeyExists_WithValidId()
    {
        // Arrange
        var testGlobalSetting = _globalSettingFixture.GlobalSettings[0];
        testGlobalSetting.GlobalSettingKey = "UniqueTestKey";
        testGlobalSetting.ReferenceId = "test-reference-id";

        // Act
        var result = await _handler.Handle(new GetGlobalSettingNameUniqueQuery
        {
            GlobalSettingKey = "UniqueTestKey",
            Id = "test-reference-id"
        }, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_When_GlobalSettingKeyExists_WithDifferentId()
    {
        // Arrange
        var testGlobalSetting = _globalSettingFixture.GlobalSettings[0];
        testGlobalSetting.GlobalSettingKey = "ExistingKey";
        testGlobalSetting.ReferenceId = "original-id";

        // Act
        var result = await _handler.Handle(new GetGlobalSettingNameUniqueQuery
        {
            GlobalSettingKey = "ExistingKey",
            Id = "different-id"
        }, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_When_GlobalSettingKeyDoesNotExist()
    {
        // Act
        var result = await _handler.Handle(new GetGlobalSettingNameUniqueQuery
        {
            GlobalSettingKey = "NonExistentKey",
            Id = "any-id"
        }, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_Correct_Result_When_MultipleGlobalSettings()
    {
        // Arrange - Ensure we have multiple global settings
        if (_globalSettingFixture.GlobalSettings.Count > 1)
        {
            _globalSettingFixture.GlobalSettings[0].GlobalSettingKey = "FirstKey";
            _globalSettingFixture.GlobalSettings[0].ReferenceId = "first-id";
            _globalSettingFixture.GlobalSettings[1].GlobalSettingKey = "SecondKey";
            _globalSettingFixture.GlobalSettings[1].ReferenceId = "second-id";

            // Act - Test first global setting
            var result1 = await _handler.Handle(new GetGlobalSettingNameUniqueQuery
            {
                GlobalSettingKey = "FirstKey",
                Id = "first-id"
            }, CancellationToken.None);

            // Act - Test second global setting
            var result2 = await _handler.Handle(new GetGlobalSettingNameUniqueQuery
            {
                GlobalSettingKey = "SecondKey",
                Id = "second-id"
            }, CancellationToken.None);

            // Assert
            result1.ShouldBeTrue();
            result2.ShouldBeTrue();
        }
    }

    [Fact]
    public async Task Handle_Return_False_When_EmptyGlobalSettingKey()
    {
        // Act
        var result = await _handler.Handle(new GetGlobalSettingNameUniqueQuery
        {
            GlobalSettingKey = string.Empty,
            Id = "any-id"
        }, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_When_NullGlobalSettingKey()
    {
        // Act
        var result = await _handler.Handle(new GetGlobalSettingNameUniqueQuery
        {
            GlobalSettingKey = null,
            Id = "any-id"
        }, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Verify_RepositoryMethod_CalledWithCorrectParameters()
    {
        // Arrange
        var testKey = "TestGlobalSettingKey";
        var testId = "test-id";

        // Act
        await _handler.Handle(new GetGlobalSettingNameUniqueQuery
        {
            GlobalSettingKey = testKey,
            Id = testId
        }, CancellationToken.None);

        // Assert
        _mockGlobalSettingRepository.Verify(x => x.IsGlobalSettingKeyExist(testKey, testId), Times.Once);
    }

    [Fact]
    public void GetGlobalSettingNameUniqueQuery_CanBeInstantiated()
    {
        var query = new GetGlobalSettingNameUniqueQuery();
        query.ShouldNotBeNull();
    }

    [Fact]
    public void GetGlobalSettingNameUniqueQuery_Properties_CanBeSet()
    {
        // Arrange & Act
        var query = new GetGlobalSettingNameUniqueQuery
        {
            GlobalSettingKey = "TestKey",
            Id = "test-id"
        };

        // Assert
        query.GlobalSettingKey.ShouldBe("TestKey");
        query.Id.ShouldBe("test-id");
    }
}