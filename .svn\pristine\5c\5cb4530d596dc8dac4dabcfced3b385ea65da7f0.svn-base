using ContinuityPatrol.Application.Features.CyberComponent.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

/// <summary>
/// Fixture class for CyberComponent module testing
/// Provides test data setup and AutoFixture configuration for comprehensive unit testing
/// Purpose: CyberComponent manages individual cyber security components and their configurations
/// </summary>
public class CyberComponentFixture : IDisposable
{
    public List<CyberComponent> CyberComponents { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateCyberComponentCommand CreateCyberComponentCommand { get; set; }
    public UpdateCyberComponentCommand UpdateCyberComponentCommand { get; set; }
    public DeleteCyberComponentCommand DeleteCyberComponentCommand { get; set; }
    //public ActivateCyberComponentCommand ActivateCyberComponentCommand { get; set; }
    //public DeactivateCyberComponentCommand DeactivateCyberComponentCommand { get; set; }
    public IMapper Mapper { get; set; }

    public CyberComponentFixture()
    {
        // Initialize manual test data with known values for reliable testing
        CyberComponents = new List<CyberComponent>
        {
            new CyberComponent
            {
                ReferenceId = Guid.NewGuid().ToString(),
               
                Name = "Test Cyber Component 01",
                Description = "Test cyber security component for unit testing",
                Type = "Firewall",
               
                Status = "Active",
                
                SiteId = Guid.NewGuid().ToString(),
                SiteName = "Test Site 01",
               
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalComponents = AutoCyberComponentFixture.CreateMany<CyberComponent>(2).ToList();
            CyberComponents.AddRange(additionalComponents);
            
            UserActivities = AutoCyberComponentFixture.CreateMany<UserActivity>(3).ToList();
            CreateCyberComponentCommand = AutoCyberComponentFixture.Create<CreateCyberComponentCommand>();
            UpdateCyberComponentCommand = AutoCyberComponentFixture.Create<UpdateCyberComponentCommand>();
            DeleteCyberComponentCommand = AutoCyberComponentFixture.Create<DeleteCyberComponentCommand>();
            //ActivateCyberComponentCommand = AutoCyberComponentFixture.Create<ActivateCyberComponentCommand>();
            //DeactivateCyberComponentCommand = AutoCyberComponentFixture.Create<DeactivateCyberComponentCommand>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateCyberComponentCommand = new CreateCyberComponentCommand();
            UpdateCyberComponentCommand = new UpdateCyberComponentCommand();
            DeleteCyberComponentCommand = new DeleteCyberComponentCommand();
            //ActivateCyberComponentCommand = new ActivateCyberComponentCommand();
            //DeactivateCyberComponentCommand = new DeactivateCyberComponentCommand();
        }

        // Configure AutoMapper for CyberComponent mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CyberComponentProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    /// <summary>
    /// AutoFixture configuration for CyberComponent entities and commands
    /// Handles circular references and provides realistic test data
    /// </summary>
    public Fixture AutoCyberComponentFixture
    {
        get
        {
            var fixture = new Fixture();
            
            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations for commands
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateCyberComponentCommand>(p => p.Name, 100));
            fixture.Customize<CreateCyberComponentCommand>(c => c
                //.With(b => b.ComponentId, () => Guid.NewGuid().ToString())
                //.With(b => b.Name, () => $"Test Cyber Component {fixture.Create<int>():000}")
                //.With(b => b.Description, () => $"Test cyber security component description {fixture.Create<int>()}")
                //.With(b => b.Type, () => new[] { "Firewall", "IDS", "IPS", "Antivirus", "SIEM", "VPN" }[fixture.Create<int>() % 6])
                //.With(b => b.Category, () => new[] { "Network Security", "Endpoint Security", "Application Security", "Data Security" }[fixture.Create<int>() % 4])
                //.With(b => b.Version, () => $"{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}")
                //.With(b => b.Vendor, () => $"Vendor {fixture.Create<int>():00}")
                //.With(b => b.Model, () => $"Model-{fixture.Create<int>():000}")
                //.With(b => b.SerialNumber, () => $"SN{fixture.Create<int>():000000000}")
                //.With(b => b.IpAddress, () => $"192.168.1.{fixture.Create<int>() % 255}")
                //.With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                //.With(b => b.Protocol, () => new[] { "HTTP", "HTTPS", "TCP", "UDP", "SSH", "SNMP" }[fixture.Create<int>() % 6])
                //.With(b => b.Status, "Active")
                //.With(b => b.Health, "Good")
                //.With(b => b.Configuration, () => $"{{\"rules\":{fixture.Create<int>() % 1000},\"policies\":{fixture.Create<int>() % 100},\"enabled\":true}}")
                //.With(b => b.Metadata, () => $"{{\"location\":\"DataCenter{fixture.Create<int>() % 10}\",\"rack\":\"R{fixture.Create<int>():00}\",\"slot\":\"S{fixture.Create<int>():00}\"}}")
                //.With(b => b.GroupId, () => Guid.NewGuid().ToString())
                //.With(b => b.GroupName, () => $"Security Group {fixture.Create<int>():00}")
                .With(b => b.SiteId, () => Guid.NewGuid().ToString())
                .With(b => b.SiteName, () => $"Test Site {fixture.Create<int>():00}"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateCyberComponentCommand>(p => p.Name, 100));
            fixture.Customize<UpdateCyberComponentCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.Name, () => $"Updated Cyber Component {fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Updated cyber security component description {fixture.Create<int>()}")
                //.With(b => b.Version, () => $"{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}")
                //.With(b => b.Status, () => new[] { "Active", "Inactive", "Maintenance", "Error" }[fixture.Create<int>() % 4])
                //.With(b => b.Health, () => new[] { "Good", "Warning", "Critical", "Unknown" }[fixture.Create<int>() % 4])
                );

            fixture.Customize<DeleteCyberComponentCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            //fixture.Customize<ActivateCyberComponentCommand>(c => c
            //    .With(b => b.Id, () => Guid.NewGuid().ToString()));

            //fixture.Customize<DeactivateCyberComponentCommand>(c => c
            //    .With(b => b.Id, () => Guid.NewGuid().ToString()));

            // CyberComponent entity customizations
            fixture.Customize<CyberComponent>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                //.With(b => b.ComponentId, () => Guid.NewGuid().ToString())
                .With(b => b.Name, () => $"Test Cyber Component {fixture.Create<int>():000}")
                .With(b => b.Description, () => $"Test cyber security component description {fixture.Create<int>()}")
                .With(b => b.Type, () => new[] { "Firewall", "IDS", "IPS", "Antivirus", "SIEM", "VPN" }[fixture.Create<int>() % 6])
                //.With(b => b.Category, () => new[] { "Network Security", "Endpoint Security", "Application Security", "Data Security" }[fixture.Create<int>() % 4])
                //.With(b => b.Version, () => $"{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}")
                //.With(b => b.Vendor, () => $"Vendor {fixture.Create<int>():00}")
                //.With(b => b.Model, () => $"Model-{fixture.Create<int>():000}")
                //.With(b => b.SerialNumber, () => $"SN{fixture.Create<int>():000000000}")
                //.With(b => b.IpAddress, () => $"192.168.1.{fixture.Create<int>() % 255}")
                //.With(b => b.Port, () => fixture.Create<int>() % 65535 + 1024)
                //.With(b => b.Protocol, () => new[] { "HTTP", "HTTPS", "TCP", "UDP", "SSH", "SNMP" }[fixture.Create<int>() % 6])
                //.With(b => b.Status, () => new[] { "Active", "Inactive", "Maintenance", "Error" }[fixture.Create<int>() % 4])
                //.With(b => b.Health, () => new[] { "Good", "Warning", "Critical", "Unknown" }[fixture.Create<int>() % 4])
                //.With(b => b.Configuration, () => $"{{\"rules\":{fixture.Create<int>() % 1000},\"policies\":{fixture.Create<int>() % 100},\"enabled\":true}}")
                //.With(b => b.Metadata, () => $"{{\"location\":\"DataCenter{fixture.Create<int>() % 10}\",\"rack\":\"R{fixture.Create<int>():00}\",\"slot\":\"S{fixture.Create<int>():00}\"}}")
                //.With(b => b.GroupId, () => Guid.NewGuid().ToString())
                //.With(b => b.GroupName, () => $"Security Group {fixture.Create<int>():00}")
                //.With(b => b.SiteId, () => Guid.NewGuid().ToString())
                //.With(b => b.SiteName, () => $"Test Site {fixture.Create<int>():00}")
                //.With(b => b.LastHeartbeat, () => DateTime.UtcNow.AddMinutes(-fixture.Create<int>() % 60))
                //.With(b => b.NextMaintenance, () => DateTime.UtcNow.AddDays(fixture.Create<int>() % 90))
                );

            // UserActivity customization for CyberComponent
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "CyberComponent")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test cyber component activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
