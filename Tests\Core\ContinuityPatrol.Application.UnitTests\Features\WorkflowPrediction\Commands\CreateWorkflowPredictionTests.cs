﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Commands;

public class CreateWorkflowPredictionTests
{
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IWorkflowPredictionRepository> _mockWorkflowPredictionRepository;
    private readonly CreateWorkflowPredictionCommandHandler _handler;

    public CreateWorkflowPredictionTests()
    {
        _mockMapper = new Mock<IMapper>();
        _mockWorkflowPredictionRepository = new Mock<IWorkflowPredictionRepository>();

        _handler = new CreateWorkflowPredictionCommandHandler(
            _mockMapper.Object,
            _mockWorkflowPredictionRepository.Object
        );
    }

    [Fact]
    public async Task Handle_ReturnsCorrectResponse_WhenWorkflowPredictionCreatedSuccessfully()
    {
        var actionId = Guid.NewGuid().ToString();
        var nextPossibleId = Guid.NewGuid().ToString();
        var nodeId = Guid.NewGuid().ToString();
        var command = new CreateWorkflowPredictionCommand
        {
            ActionId = actionId,
            ActionName = "Test Action",
            Count = 10,
            NextPossibleId = nextPossibleId,
            NextPossibleActionName = "Next Test Action",
            NodeId = nodeId
        };

       
        var referenceId = Guid.NewGuid().ToString();

        var mappedWorkflowPrediction = new Domain.Entities.WorkflowPrediction
        {
            ReferenceId = referenceId,
            ActionId = actionId,
            ActionName = "Test Action",
            Count = 10,
            NextPossibleId = nextPossibleId,
            NextPossibleActionName = "Next Test Action",
            NodeId = nodeId
        };
        _mockMapper
            .Setup(m => m.Map<Domain.Entities.WorkflowPrediction>(command))
            .Returns(mappedWorkflowPrediction);

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.AddAsync(mappedWorkflowPrediction))
            .ReturnsAsync(mappedWorkflowPrediction);

        var expectedMessage = $" WorkflowPrediction '{actionId}' has been created successfully";

        var result = await _handler.Handle(command, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(referenceId, result.WorkflowPredictionId);
        Assert.Equal(expectedMessage, result.Message);

        Assert.Equal(mappedWorkflowPrediction.ActionId, command.ActionId);
        Assert.Equal(mappedWorkflowPrediction.ActionName, command.ActionName);
        Assert.Equal(mappedWorkflowPrediction.Count, command.Count);
        Assert.Equal(mappedWorkflowPrediction.NextPossibleActionName, command.NextPossibleActionName);
        Assert.Equal(mappedWorkflowPrediction.NextPossibleId, command.NextPossibleId);
        Assert.Equal(mappedWorkflowPrediction.NodeId, command.NodeId);
   

        _mockMapper.Verify(m => m.Map<Domain.Entities.WorkflowPrediction>(command), Times.Once);
        _mockWorkflowPredictionRepository.Verify(repo => repo.AddAsync(mappedWorkflowPrediction), Times.Once);
    }

    [Fact]
    public async Task Handle_CallsRepositoryAndMapperMethodsCorrectly()
    {
        var command = new CreateWorkflowPredictionCommand();
        var mappedWorkflowPrediction = new Domain.Entities.WorkflowPrediction
        {
            ActionId = Guid.NewGuid().ToString(),
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockMapper
            .Setup(m => m.Map<Domain.Entities.WorkflowPrediction>(command))
            .Returns(mappedWorkflowPrediction);

        _mockWorkflowPredictionRepository
            .Setup(repo => repo.AddAsync(mappedWorkflowPrediction))
            .ReturnsAsync(mappedWorkflowPrediction);

        await _handler.Handle(command, CancellationToken.None);

        _mockMapper.Verify(m => m.Map<Domain.Entities.WorkflowPrediction>(command), Times.Once);
        _mockWorkflowPredictionRepository.Verify(repo => repo.AddAsync(mappedWorkflowPrediction), Times.Once);
    }
}