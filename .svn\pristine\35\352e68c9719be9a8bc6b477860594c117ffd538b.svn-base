﻿using ContinuityPatrol.Application.Features.Workflow.Commands.Lock;
using ContinuityPatrol.Application.Features.Workflow.Commands.Publish;
using ContinuityPatrol.Application.Features.Workflow.Events.PaginatedView;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.WorkflowProfileAuthentication;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Web.Areas.ITAutomation.Controllers;

[Area("ITAutomation")]
public class WorkflowListController : BaseController
{
    private readonly ILogger<WorkflowListController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;
    public WorkflowListController(ILogger<WorkflowListController> logger, IMapper mapper, IDataProvider dataProvider, IPublisher publisher)
    {
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _publisher = publisher;
    }
    public async Task<IActionResult> List()
    {
       await _publisher.Publish(new WorkflowPaginatedEvent());
        _logger.LogDebug("Entering List method in Workflow List");

        return View();
    }


    [HttpGet]
    [EventCode(EventCodes.WorkflowList.GetPagination)]
    public async Task<JsonResult> GetPagination(GetWorkflowProfileInfoPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in Workflow List");

        try
        {
           var result = await _dataProvider.WorkflowProfileInfo.GetPaginatedWorkflowProfileInfos(query);

           _logger.LogDebug("Successfully retrieved workflow paginated list on workflow list page");

            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on workflow list page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }


    [EventCode(EventCodes.WorkflowList.UpdateWorkflowLock)]
    public async Task<IActionResult> UpdateWorkflowLock(UpdateWorkflowLockCommand lockWorkFlow)
    {
        _logger.LogDebug("Entering update workflow lock method in workflow list");

        try
        {
            var lockWorkflow = _mapper.Map<UpdateWorkflowLockCommand>(lockWorkFlow);

            _logger.LogDebug($"Updating workflow lock by id '{lockWorkFlow.Id}'");

            var result = await _dataProvider.Workflow.UpdateWorkflowLock(lockWorkflow);

            _logger.LogDebug("UpdateWorkflowLock operation completed successfully in workflow list, returning view.");

            return Json(new {Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on workflow list page while processing the request for update workflow lock.", ex);

            return ex.GetJsonException();
        }
       
    }

    [EventCode(EventCodes.WorkflowList.UpdateWorkflowPublish)]
    public async Task<IActionResult> UpdateWorkflowPublish(UpdateWorkflowPublishCommand publishWorkFlow)
    {
        _logger.LogDebug("Entering update workflow publish method in workflow list");

        try
        {
            var publishWorkflow = _mapper.Map<UpdateWorkflowPublishCommand>(publishWorkFlow);

            _logger.LogDebug($"Updating workflow publish by id '{publishWorkFlow.Id}'");

            var result = await _dataProvider.Workflow.UpdateWorkflowPublish(publishWorkflow);

            _logger.LogDebug("Update workflow publish operation completed successfully in workflow list, returning view.");

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on workflow list page while processing the request for update workflow publish.", ex);

            return ex.GetJsonException();
        }
    }

    [EventCode(EventCodes.WorkflowList.AuthenticationWorkflow)]
    public async Task<IActionResult> AuthenticationWorkflow(WorkflowProfileAuthenticationCommand workflowProfileAuthentication)
    {
        _logger.LogDebug("Entering Authentication Workflow method in workflow list");

        try
        {
            var authentication = _mapper.Map<WorkflowProfileAuthenticationCommand>(workflowProfileAuthentication);

            _logger.LogDebug($"Authentication workflow by loginName '{workflowProfileAuthentication.LoginName}'");

            var result = await _dataProvider.WorkflowProfile.WorkflowProfileAuthentication(authentication);

            _logger.LogDebug("Authentication Workflow operation completed successfully in workflow list, returning view.");

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on workflow list page while processing the request for Authentication Workflow.", ex);

            return ex.GetJsonException();
        }
    }

    //For FormBuilder.
    [EventCode(EventCodes.WorkflowList.GetWorkflowNames)]
    public async Task<IActionResult> GetWorkflowNames()
    {
        _logger.LogDebug("Entering GetWorkflowNames method in workflow list");

        try
        {
            var workflowNameVms = await _dataProvider.Workflow.GetWorkflowNames();

            _logger.LogDebug("Successfully retrieved workflow names in workflow list");

            return Json(new { Success = true, data = workflowNameVms });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on workflow list page while retrieving workflow names.", ex);

            return ex.GetJsonException();
        }
    }

    [EventCode(EventCodes.WorkflowList.GetWorkflowById)]
    public async Task<JsonResult> GetWorkflowById(string workflowId)
    {
        _logger.LogDebug("Entering GetWorkflowById method in workflow list");

        try
        {
            var workflowDetailVm = await _dataProvider.Workflow.GetByReferenceId(workflowId);

            _logger.LogDebug($"Successfully retrieved workflow details for Id :{workflowId} in workflow list page");

            return Json(new { Success = true, data = workflowDetailVm });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on workflow list page while retrieving the workflow details by id.", ex);

            return ex.GetJsonException();
        }       
    }
}