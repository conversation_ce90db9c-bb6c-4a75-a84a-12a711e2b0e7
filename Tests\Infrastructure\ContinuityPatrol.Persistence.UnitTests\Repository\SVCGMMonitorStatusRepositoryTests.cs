using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SVCGMMonitorStatusRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SVCGMMonitorStatusRepository _repository;
    private readonly SVCGMMonitorStatusFixture _fixture;

    public SVCGMMonitorStatusRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _repository = new SVCGMMonitorStatusRepository(_dbContext);
        _fixture = new SVCGMMonitorStatusFixture();
    }

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnMatchingStatuses_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();

        var status1 = _fixture.CreateSVCGMMonitorStatus(
            type: "SVCGM_REPLICATION",
            infraObjectId: "INFRA_001",
            infraObjectName: "Test SVCGM 1",
            workflowId: "WF_001",
            workflowName: "SVCGM Workflow 1",
            isActive: true
        );
        var status2 = _fixture.CreateSVCGMMonitorStatus(
            type: "SVCGM_REPLICATION",
            infraObjectId: "INFRA_002",
            infraObjectName: "Test SVCGM 2",
            workflowId: "WF_002",
            workflowName: "SVCGM Workflow 2",
            isActive: true
        );
        var status3 = _fixture.CreateSVCGMMonitorStatus(
            type: "SVCGM_BACKUP",
            infraObjectId: "INFRA_003",
            infraObjectName: "Test SVCGM 3",
            workflowId: "WF_003",
            workflowName: "SVCGM Workflow 3",
            isActive: true
        );

        await _repository.AddAsync(status1);
        await _repository.AddAsync(status2);
        await _repository.AddAsync(status3);

        // Act
        var result = await _repository.GetDetailByType("SVCGM_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, status => Assert.Equal("SVCGM_REPLICATION", status.Type));
        Assert.Contains(result, status => status.InfraObjectName == "Test SVCGM 1");
        Assert.Contains(result, status => status.InfraObjectName == "Test SVCGM 2");
        Assert.DoesNotContain(result, status => status.Type == "SVCGM_BACKUP");
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var status = _fixture.CreateSVCGMMonitorStatus(type: "SVCGM_REPLICATION", isActive: true);
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetDetailByType("NONEXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldOnlyReturnActiveStatuses()
    {
        // Arrange
        await ClearDatabase();

        var activeStatus = _fixture.CreateSVCGMMonitorStatus(
            type: "SVCGM_REPLICATION",
            infraObjectName: "Active SVCGM",
            isActive: true
        );
        var inactiveStatus = _fixture.CreateSVCGMMonitorStatus(
            type: "SVCGM_REPLICATION",
            infraObjectName: "Inactive SVCGM",
            isActive: false
        );

await _dbContext.SVcgmMonitorStatuses.AddRangeAsync(activeStatus, inactiveStatus);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetDetailByType("SVCGM_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active SVCGM", result[0].InfraObjectName);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var status = _fixture.CreateSVCGMMonitorStatus(
            type: "SVCGM_REPLICATION",
            infraObjectId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            infraObjectName: "Test SVCGM Object",
            workflowId: "WF_001",
            workflowName: "Test Workflow",
            properties: "{\"rpo\": \"15\", \"status\": \"running\"}",
            configuredRPO: "15",
            dataLagValue: "5",
            threshold: "10",
            isActive: true
        );

        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetDetailByType("SVCGM_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultStatus = result[0];
        
        Assert.Equal("SVCGM_REPLICATION", resultStatus.Type);
        Assert.Equal("8f6aaef6-2b08-45d1-8915-c194439f4c27", resultStatus.InfraObjectId);
        Assert.Equal("Test SVCGM Object", resultStatus.InfraObjectName);
        Assert.Equal("WF_001", resultStatus.WorkflowId);
        Assert.Equal("Test Workflow", resultStatus.WorkflowName);
        Assert.Equal("{\"rpo\": \"15\", \"status\": \"running\"}", resultStatus.Properties);
        Assert.Equal("15", resultStatus.ConfiguredRPO);
        Assert.Equal("5", resultStatus.DataLagValue);
        Assert.Equal("10", resultStatus.Threshold);
        Assert.True(resultStatus.IsActive);
        Assert.NotNull(resultStatus.ReferenceId);
        Assert.True(resultStatus.Id > 0);
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var status = _fixture.CreateSVCGMMonitorStatus(type: "SVCGM_REPLICATION", isActive: true);
        await _repository.AddAsync(status);

        // Act
        var result1 = await _repository.GetDetailByType("SVCGM_REPLICATION");
        var result2 = await _repository.GetDetailByType("svcgm_replication");
        var result3 = await _repository.GetDetailByType("SVCGM_Replication");

        // Assert
        Assert.NotNull(result1);
        Assert.Single(result1);
        
        Assert.NotNull(result2);
        Assert.Empty(result2);
        
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    #endregion

    #region GetSVCGMMonitorStatusByInfraObjectId Tests

    [Fact]
    public async Task GetSVCGMMonitorStatusByInfraObjectId_ShouldReturnStatus_WhenInfraObjectIdExists()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";
        var status1 = _fixture.CreateSVCGMMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "Test SVCGM 1",
            type: "SVCGM_REPLICATION",
            workflowId: "WF_001",
            workflowName: "Test Workflow 1",
            isActive: true
        );
        var status2 = _fixture.CreateSVCGMMonitorStatus(
            infraObjectId: "abea4151-6ee0-4f6b-9fc0-2fa5c961d956",
            infraObjectName: "Test SVCGM 2",
            type: "SVCGM_REPLICATION",
            workflowId: "WF_002",
            workflowName: "Test Workflow 2",
            isActive: true
        );

        await _repository.AddAsync(status1);
        await _repository.AddAsync(status2);

        // Act
        var result = await _repository.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal("Test SVCGM 1", result.InfraObjectName);
        Assert.Equal("SVCGM_REPLICATION", result.Type);
        Assert.Equal("WF_001", result.WorkflowId);
        Assert.Equal("Test Workflow 1", result.WorkflowName);
    }

    [Fact]
    public async Task GetSVCGMMonitorStatusByInfraObjectId_ShouldReturnNull_WhenInfraObjectIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var status = _fixture.CreateSVCGMMonitorStatus(infraObjectId: "8f6aaef6-2b08-45d1-8915-c194439f4c27", isActive: true);
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetSVCGMMonitorStatusByInfraObjectId("abea4151-6ee0-4f6b-9fc0-2fa5c961d956");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetSVCGMMonitorStatusByInfraObjectId_ShouldReturnFirstMatch_WhenMultipleStatusesExist()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";
        var status1 = _fixture.CreateSVCGMMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "First SVCGM",
            type: "SVCGM_REPLICATION",
            isActive: true
        );
        var status2 = _fixture.CreateSVCGMMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "Second SVCGM",
            type: "SVCGM_BACKUP",
            isActive: true
        );

        await _repository.AddAsync(status1);
        await _repository.AddAsync(status2);

        // Act
        var result = await _repository.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return the first one found (database order dependent)
        Assert.True(result.InfraObjectName == "First SVCGM" || result.InfraObjectName == "Second SVCGM");
    }

    [Fact]
    public async Task GetSVCGMMonitorStatusByInfraObjectId_ShouldReturnInactiveStatus()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";
        var inactiveStatus = _fixture.CreateSVCGMMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "Inactive SVCGM",
            type: "SVCGM_REPLICATION",
            isActive: false
        );


await _dbContext.SVcgmMonitorStatuses.AddRangeAsync( inactiveStatus);
        _dbContext.SaveChanges(); 
        // Act
        var result = await _repository.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal("Inactive SVCGM", result.InfraObjectName);
        Assert.False(result.IsActive);
    }

    //[Fact]
    //public async Task GetSVCGMMonitorStatusByInfraObjectId_ShouldThrowException_WhenInfraObjectIdIsInvalid()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentException>(() => 
    //        _repository.GetSVCGMMonitorStatusByInfraObjectId("invalid-guid"));
    //}

    //[Fact]
    //public async Task GetSVCGMMonitorStatusByInfraObjectId_ShouldThrowException_WhenInfraObjectIdIsEmpty()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentException>(() => 
    //        _repository.GetSVCGMMonitorStatusByInfraObjectId(""));
    //}

    //[Fact]
    //public async Task GetSVCGMMonitorStatusByInfraObjectId_ShouldThrowException_WhenInfraObjectIdIsNull()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentException>(() => 
    //        _repository.GetSVCGMMonitorStatusByInfraObjectId(null));
    //}

    [Fact]
    public async Task GetSVCGMMonitorStatusByInfraObjectId_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";
        var status = _fixture.CreateSVCGMMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "Complete SVCGM Object",
            type: "SVCGM_REPLICATION",
            workflowId: "WF_001",
            workflowName: "Complete Workflow",
            properties: "{\"rpo\": \"30\", \"status\": \"healthy\", \"lastCheck\": \"2024-01-01T12:00:00Z\"}",
            configuredRPO: "30",
            dataLagValue: "8",
            threshold: "15",
            isActive: true
        );

        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal("Complete SVCGM Object", result.InfraObjectName);
        Assert.Equal("SVCGM_REPLICATION", result.Type);
        Assert.Equal("WF_001", result.WorkflowId);
        Assert.Equal("Complete Workflow", result.WorkflowName);
        Assert.Equal("{\"rpo\": \"30\", \"status\": \"healthy\", \"lastCheck\": \"2024-01-01T12:00:00Z\"}", result.Properties);
        Assert.Equal("30", result.ConfiguredRPO);
        Assert.Equal("8", result.DataLagValue);
        Assert.Equal("15", result.Threshold);
        Assert.True(result.IsActive);
        Assert.NotNull(result.ReferenceId);
        Assert.True(result.Id > 0);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SVcgmMonitorStatuses.RemoveRange(_dbContext.SVcgmMonitorStatuses);
        await _dbContext.SaveChangesAsync();
    }
}
