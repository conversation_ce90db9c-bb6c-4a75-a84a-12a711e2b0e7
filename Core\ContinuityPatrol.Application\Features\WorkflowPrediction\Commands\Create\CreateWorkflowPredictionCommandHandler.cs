﻿namespace ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Create;

public class
    CreateWorkflowPredictionCommandHandler : IRequestHandler<CreateWorkflowPredictionCommand,
        CreateWorkflowPredictionResponse>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowPredictionRepository _workflowPredictionRepository;

    public CreateWorkflowPredictionCommandHandler(IMapper mapper,
        IWorkflowPredictionRepository workflowPredictionRepository)
    {
        _mapper = mapper;
        _workflowPredictionRepository = workflowPredictionRepository;
    }

    public async Task<CreateWorkflowPredictionResponse> Handle(CreateWorkflowPredictionCommand request,
        CancellationToken cancellationToken)
    {
        var workflowPrediction = _mapper.Map<Domain.Entities.WorkflowPrediction>(request);

        workflowPrediction = await _workflowPredictionRepository.AddAsync(workflowPrediction);

        var response = new CreateWorkflowPredictionResponse
        {
            Message = Message.Create(nameof(Domain.Entities.WorkflowPrediction), workflowPrediction.ActionId),

            WorkflowPredictionId = workflowPrediction.ReferenceId
        };

        return response;
    }
}