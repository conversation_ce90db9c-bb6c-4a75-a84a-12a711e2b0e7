﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowProfile.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Commands;

public class DeleteWorkflowProfileTests : IClassFixture<WorkflowProfileFixture> , IClassFixture<WorkflowProfileInfoFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;

    private readonly Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;

    private readonly Mock<IPublisher> _mockPublisher;

    private readonly DeleteWorkflowProfileCommandHandler _handler;

    public DeleteWorkflowProfileTests(WorkflowProfileFixture workflowProfileFixture, WorkflowProfileInfoFixture workflowProfileInfoFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;

        var workflowProfileInfoNewFixture = workflowProfileInfoFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockWorkflowProfileRepository = new Mock<IWorkflowProfileRepository>();

        var mockWorkflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        _handler = new DeleteWorkflowProfileCommandHandler(
            _mockWorkflowProfileRepository.Object,
            _mockPublisher.Object,
            mockWorkflowProfileInfoRepository.Object
        );
        _workflowProfileFixture.WorkflowProfiles[0].ReferenceId = "880dd804-3426-473e-8a1e-aa9baa6395d4";
        workflowProfileInfoNewFixture.WorkflowProfileInfos[0].ProfileId = "880dd804-3426-473e-8a1e-aa9baa6395d4";

        _mockWorkflowProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync(_workflowProfileFixture.WorkflowProfiles[0]);

        mockWorkflowProfileInfoRepository.Setup(repo => repo.GetWorkflowProfileInfoByProfileIdAsync(It.IsAny<string>())).ReturnsAsync(new List<Domain.Entities.WorkflowProfileInfo>());
    }

    [Fact]
    public async Task Handle_UpdateGetWorkflowProfileByIdAsyncIsActiveFalse_When_WorkflowProfileDeleted()
    {
        var workflowProfile = _workflowProfileFixture.WorkflowProfiles[0];

        var result = await _handler.Handle(new DeleteWorkflowProfileCommand
        {
            Id = workflowProfile.ReferenceId
        }, CancellationToken.None);

        result.ShouldNotBeNull();

        result.Success.ShouldBeTrue();

        _mockWorkflowProfileRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.WorkflowProfile>(p => p.ReferenceId == workflowProfile.ReferenceId && !p.IsActive)), Times.Once);

        var updatedProfile = await _mockWorkflowProfileRepository.Object.GetByReferenceIdAsync(workflowProfile.ReferenceId);

        updatedProfile.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_SuccessfulWorkflowProfileResponse_When_WorkflowProfileDeleted()
    {
        var command = new DeleteWorkflowProfileCommand { Id = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId };

        var result = await _handler.Handle(command, CancellationToken.None);

        result.ShouldBeOfType<DeleteWorkflowProfileResponse>();

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);

        _mockWorkflowProfileRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.WorkflowProfile>()), Times.Once);

        _mockPublisher.Verify(p => p.Publish(It.IsAny<WorkflowProfileDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Return_IsActive_False_When_DeleteReferenceIdAsync_WorkflowProfile()
    {
        var command = new DeleteWorkflowProfileCommand { Id = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId };

        await _handler.Handle(command, CancellationToken.None);

        var workflowProfile = await _mockWorkflowProfileRepository.Object.GetByReferenceIdAsync(_workflowProfileFixture.WorkflowProfiles[0].ReferenceId);

        workflowProfile.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        var command = new DeleteWorkflowProfileCommand { Id = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId };

        await _handler.Handle(command, CancellationToken.None);

        _mockWorkflowProfileRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockWorkflowProfileRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowProfile>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowException_When_WorkflowProfileIsInUse()
    {
        // Arrange
        var workflowProfileId = Guid.NewGuid().ToString();
        var command = new DeleteWorkflowProfileCommand { Id = workflowProfileId };

        var workflowProfile = new Domain.Entities.WorkflowProfile
        {
            ReferenceId = workflowProfileId,
            Name = "Profile In Use"
        };

        var workflowProfileInfoList = new List<Domain.Entities.WorkflowProfileInfo>
        {
            new()
            {
                ProfileId = workflowProfileId,
                WorkflowId = "workflow-123"
            }
        };

        _mockWorkflowProfileRepository
            .Setup(repo => repo.GetByReferenceIdAsync(workflowProfileId))
            .ReturnsAsync(workflowProfile);

        var mockWorkflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        mockWorkflowProfileInfoRepository
            .Setup(repo => repo.GetWorkflowProfileInfoByProfileIdAsync(workflowProfile.ReferenceId))
            .ReturnsAsync(workflowProfileInfoList);

        var handler = new DeleteWorkflowProfileCommandHandler(
            _mockWorkflowProfileRepository.Object,
            _mockPublisher.Object,
            mockWorkflowProfileInfoRepository.Object
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(() =>
            handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe("The workflow Profile is currently in use.");
    }

}