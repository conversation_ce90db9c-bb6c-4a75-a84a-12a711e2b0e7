using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class PostgresMonitorLogsRepositoryTests : IClassFixture<PostgresMonitorLogsFixture>, IDisposable
{
    private readonly PostgresMonitorLogsFixture _postgresMonitorLogsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly PostgresMonitorLogsRepository _repository;

    public PostgresMonitorLogsRepositoryTests(PostgresMonitorLogsFixture postgresMonitorLogsFixture)
    {
        _postgresMonitorLogsFixture = postgresMonitorLogsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = ConfigurationRepositoryMocks.GetConnectionString();
        _repository = new PostgresMonitorLogsRepository(_dbContext, _mockConfiguration.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.PostgresMonitorLogs.RemoveRange(_dbContext.PostgresMonitorLogs);
        await _dbContext.SaveChangesAsync();
    }

    #region Constructor Tests

    [Fact]
    public void Constructor_ShouldCreateInstance_WhenValidParametersProvided()
    {
        // Arrange & Act
        var repository = new PostgresMonitorLogsRepository(_dbContext, _mockConfiguration.Object);

        // Assert
        Assert.NotNull(repository);
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnLogsWithSpecificType()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "TestType";
        var targetLogs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType(targetType, 3);
        var otherLogs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("OtherType", 2);
        
        await _repository.AddRangeAsync(targetLogs);
        await _repository.AddRangeAsync(otherLogs);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, log => Assert.Equal(targetType, log.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("ExistingType", 2);
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDetailByType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }
  
    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_TypeIsWhitespace()
    {
        // Act & Assert
        var result= await _repository.GetDetailByType("   ");
         Assert.NotNull(result);
        Assert.Empty(result);
    }

   

    [Fact]
    public async Task GetDetailByType_ShouldHandleLargeDataset()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "LargeDatasetType";
        var logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType(targetType, 100);
        
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.Count);
        Assert.All(result, log => Assert.Equal(targetType, log.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnCompleteEntityData()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "CompleteDataType";
        var log = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties(
            type: targetType,
            infraObjectId: "test-infra-id",
            infraObjectName: "test-infra-name",
            workflowId: "test-workflow-id",
            workflowName: "test-workflow-name");
        
        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        
        var returnedLog = result.First();
        Assert.Equal(targetType, returnedLog.Type);
        Assert.Equal("test-infra-id", returnedLog.InfraObjectId);
        Assert.Equal("test-infra-name", returnedLog.InfraObjectName);
        Assert.Equal("test-workflow-id", returnedLog.WorkflowId);
        Assert.Equal("test-workflow-name", returnedLog.WorkflowName);
        Assert.NotNull(returnedLog.Properties);
        Assert.NotNull(returnedLog.ConfiguredRPO);
        Assert.NotNull(returnedLog.DataLagValue);
        Assert.NotNull(returnedLog.Threshold);
    }

    #endregion

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddPostgresMonitorLog_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var log = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties();

        // Act
        var result = await _repository.AddAsync(log);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(log.Type, result.Type);
        Assert.Equal(log.InfraObjectId, result.InfraObjectId);
        
        var savedEntity = await _repository.GetByReferenceIdAsync(result.ReferenceId);
        Assert.NotNull(savedEntity);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdatePostgresMonitorLog_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var log = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties();
        await _repository.AddAsync(log);

        // Modify the entity
        log.Type = "UpdatedType";
        log.InfraObjectName = "UpdatedInfraObjectName";

        // Act
        var result = await _repository.UpdateAsync(log);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedType", result.Type);
        Assert.Equal("UpdatedInfraObjectName", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeletePostgresMonitorLog_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var log = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties();
        await _repository.AddAsync(log);

        // Act
        var result = await _repository.DeleteAsync(log);

        // Assert
        Assert.NotNull(result);
        
        // Verify entity is deleted
        var deletedEntity = await _dbContext.PostgresMonitorLogs.FindAsync(log.Id);
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultiplePostgresMonitorLogs()
    {
        // Arrange
        await ClearDatabase();
        var logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("BatchType", 3);

        // Act
        var result = await _repository.AddRangeAsync(logs);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());
        
        var allLogs = await _repository.ListAllAsync();
        Assert.Equal(3, allLogs.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllPostgresMonitorLogs()
    {
        // Arrange
        await ClearDatabase();
        var logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("ListAllType", 4);
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnPostgresMonitorLog_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();
        var log = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties();
        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetByReferenceIdAsync(log.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(log.ReferenceId, result.ReferenceId);
        Assert.Equal(log.Type, result.Type);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        await ClearDatabase();
        var logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("PaginatedType", 15);
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, null, "Type", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(15, result.TotalCount);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnSecondPage()
    {
        // Arrange
        await ClearDatabase();
        var logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("PaginatedType", 15);
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.PaginatedListAllAsync(2, 10, null, "Type", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Data.Count);
        Assert.Equal(15, result.TotalCount);
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleComplexTypeFiltering()
    {
        // Arrange
        await ClearDatabase();
        var type1Logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("Type1", 3);
        var type2Logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("Type2", 2);
        var type3Logs = _postgresMonitorLogsFixture.CreatePostgresMonitorLogsWithSameType("Type3", 4);

        await _repository.AddRangeAsync(type1Logs);
        await _repository.AddRangeAsync(type2Logs);
        await _repository.AddRangeAsync(type3Logs);

        // Act & Assert
        var type1Results = await _repository.GetDetailByType("Type1");
        Assert.Equal(3, type1Results.Count);
        Assert.All(type1Results, log => Assert.Equal("Type1", log.Type));

        var type2Results = await _repository.GetDetailByType("Type2");
        Assert.Equal(2, type2Results.Count);
        Assert.All(type2Results, log => Assert.Equal("Type2", log.Type));

        var type3Results = await _repository.GetDetailByType("Type3");
        Assert.Equal(4, type3Results.Count);
        Assert.All(type3Results, log => Assert.Equal("Type3", log.Type));

        var allResults = await _repository.ListAllAsync();
        Assert.Equal(9, allResults.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var log = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties(type: $"ConcurrentType_{i}");
            tasks.Add(_repository.AddAsync(log));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.Equal(10, allLogs.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.NotNull(allLogs);
        Assert.Empty(allLogs);

        var typeResults = await _repository.GetDetailByType("NonExistentType");
        Assert.NotNull(typeResults);
        Assert.Empty(typeResults);

        var paginatedResults = await _repository.PaginatedListAllAsync(1, 10, null, "Type", "asc");
        Assert.NotNull(paginatedResults);
        Assert.Empty(paginatedResults.Data);
        Assert.Equal(0, paginatedResults.TotalCount);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInType()
    {
        // Arrange
        await ClearDatabase();
        var specialTypes = new[]
        {
            "Type_With_Underscores",
            "Type-With-Dashes",
            "Type With Spaces",
            "Type.With.Dots",
            "Type@With@Symbols"
        };

        var logs = new List<PostgresMonitorLogs>();
        foreach (var type in specialTypes)
        {
            logs.Add(_postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties(type: type));
        }

        await _repository.AddRangeAsync(logs);

        // Act & Assert
        foreach (var type in specialTypes)
        {
            var results = await _repository.GetDetailByType(type);
            Assert.Single(results);
            Assert.Equal(type, results.First().Type);
        }
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity()
    {
        // Arrange
        await ClearDatabase();
        var originalLog = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties(
            type: "IntegrityTest",
            infraObjectId: "original-infra-id",
            infraObjectName: "original-infra-name");

        // Act
        var addedLog = await _repository.AddAsync(originalLog);

        // Verify data integrity after add
        Assert.Equal(originalLog.Type, addedLog.Type);
        Assert.Equal(originalLog.InfraObjectId, addedLog.InfraObjectId);
        Assert.Equal(originalLog.InfraObjectName, addedLog.InfraObjectName);

        // Update and verify integrity
        addedLog.Type = "UpdatedIntegrityTest";
        addedLog.InfraObjectName = "updated-infra-name";

        var updatedLog = await _repository.UpdateAsync(addedLog);
        Assert.Equal("UpdatedIntegrityTest", updatedLog.Type);
        Assert.Equal("updated-infra-name", updatedLog.InfraObjectName);
        Assert.Equal(originalLog.InfraObjectId, updatedLog.InfraObjectId); // Should remain unchanged

        // Verify through GetDetailByType
        var typeResults = await _repository.GetDetailByType("UpdatedIntegrityTest");
        Assert.Single(typeResults);
        Assert.Equal("updated-infra-name", typeResults.First().InfraObjectName);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleCaseSensitivity()
    {
        // Arrange
        await ClearDatabase();
        var log1 = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties(type: "CaseSensitiveType");
        var log2 = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties(type: "casesensitivetype");
        var log3 = _postgresMonitorLogsFixture.CreatePostgresMonitorLogWithProperties(type: "CASESENSITIVETYPE");

        await _repository.AddAsync(log1);
        await _repository.AddAsync(log2);
        await _repository.AddAsync(log3);

        // Act & Assert
        var exactMatchResults = await _repository.GetDetailByType("CaseSensitiveType");
        Assert.Single(exactMatchResults);
        Assert.Equal("CaseSensitiveType", exactMatchResults.First().Type);

        var lowerCaseResults = await _repository.GetDetailByType("casesensitivetype");
        Assert.Single(lowerCaseResults);
        Assert.Equal("casesensitivetype", lowerCaseResults.First().Type);

        var upperCaseResults = await _repository.GetDetailByType("CASESENSITIVETYPE");
        Assert.Single(upperCaseResults);
        Assert.Equal("CASESENSITIVETYPE", upperCaseResults.First().Type);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullAndEmptyProperties()
    {
        // Arrange
        await ClearDatabase();
        var log = new PostgresMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "NullPropertiesTest",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "TestInfraObject",
            WorkflowId = null, // Null property
            WorkflowName = "", // Empty property
            Properties = null,
            ConfiguredRPO = "",
            DataLagValue = null,
            Threshold = "",
            IsActive = true,
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            CreatedBy = "TestUser",
            LastModifiedBy = "TestUser"
        };

        // Act
        var addedLog = await _repository.AddAsync(log);

        // Assert
        Assert.NotNull(addedLog);
        Assert.Equal("NullPropertiesTest", addedLog.Type);
        Assert.Null(addedLog.WorkflowId);
        Assert.Equal("", addedLog.WorkflowName);
        Assert.Null(addedLog.Properties);

        var typeResults = await _repository.GetDetailByType("NullPropertiesTest");
        Assert.Single(typeResults);
        var retrievedLog = typeResults.First();
        Assert.Null(retrievedLog.WorkflowId);
        Assert.Equal("", retrievedLog.WorkflowName);
    }

    #endregion
}
