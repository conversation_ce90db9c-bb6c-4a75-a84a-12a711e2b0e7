﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetFormTypeCategoryByName;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormTypeCategory.Queries;

public class GetFormTypeCategoryByNameQueryHandlerTests : IClassFixture<FormTypeCategoryFixture>
{
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;
    private readonly IMapper _mapper;
    private readonly GetFormTypeCategoryByNameQueryHandler _handler;

    public GetFormTypeCategoryByNameQueryHandlerTests(FormTypeCategoryFixture formTypeCategoryFixture)
    {
        _formTypeCategoryFixture = formTypeCategoryFixture;
        _mapper = _formTypeCategoryFixture.Mapper;

        _formTypeCategoryFixture.FormTypeCategories[0].ReferenceId = Guid.NewGuid().ToString();
        _formTypeCategoryFixture.FormTypeCategories[0].Name = "Test FormTypeCategory";

        var mockFormTypeCategoryRepository = FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories);
        _handler = new GetFormTypeCategoryByNameQueryHandler(mockFormTypeCategoryRepository.Object, _mapper);
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_FormTypeCategory_When_Valid_Name_Provided(GetFormTypeCategoryByNameQuery query)
    {
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        query.Name = formTypeCategory.Name;

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldNotBeNull();
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_Success_Response_When_FormTypeCategory_Found(GetFormTypeCategoryByNameQuery query)
    {
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        query.Name = formTypeCategory.Name;

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldNotBeNull();
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Return_Mapped_FormTypeCategoryVm(GetFormTypeCategoryByNameQuery query)
    {
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        query.Name = formTypeCategory.Name;

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldNotBeNull();
    }

    [Theory]
    [AutoFormTypeCategoryData]
    public async Task Handle_Should_Map_All_FormTypeCategoryByNameVm_Properties_Correctly(GetFormTypeCategoryByNameQuery query)
    {
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        query.Name = formTypeCategory.Name;

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<FormTypeCategoryByNameVm>>();

        if (result.Any())
        {
            var firstResult = result.First();

            firstResult.ShouldNotBeNull();
            var id = firstResult.Id;
            var name = firstResult.Name;
            var formId = firstResult.FormId;
            var formName = firstResult.FormName;
            var formTypeId = firstResult.FormTypeId;
            var formTypeName = firstResult.FormTypeName;
            var logo = firstResult.Logo;
            var version = firstResult.Version;
            var properties = firstResult.Properties;
            var formVersion = firstResult.FormVersion;

            id.ShouldNotBeNull();
            name.ShouldNotBeNull();
        }

        var testVm = new FormTypeCategoryByNameVm();
        testVm.Id = "test-id";
        testVm.Name = "test-name";
        testVm.FormId = "test-form-id";
        testVm.FormName = "test-form-name";
        testVm.FormTypeId = "test-form-type-id";
        testVm.FormTypeName = "test-form-type-name";
        testVm.Logo = "test-logo";
        testVm.Version = "test-version";
        testVm.Properties = "test-properties";
        testVm.FormVersion = "test-form-version";

        testVm.Id.ShouldBe("test-id");
        testVm.Name.ShouldBe("test-name");
        testVm.FormId.ShouldBe("test-form-id");
        testVm.FormName.ShouldBe("test-form-name");
        testVm.FormTypeId.ShouldBe("test-form-type-id");
        testVm.FormTypeName.ShouldBe("test-form-type-name");
        testVm.Logo.ShouldBe("test-logo");
        testVm.Version.ShouldBe("test-version");
        testVm.Properties.ShouldBe("test-properties");
        testVm.FormVersion.ShouldBe("test-form-version");
    }
}