﻿using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;
using ContinuityPatrol.Shared.Core.Wrapper;

public class GetCyberAirGapPaginatedListQueryHandlerTests
{
    [Fact]
    public async Task Handle_ReturnsMappedPaginatedResult_WhenCalledWithValidQuery()
    {
        // Arrange
        var mockRepo = new Mock<ICyberAirGapRepository>();
        var mockMapper = new Mock<IMapper>();

        var query = new GetCyberAirGapPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var paginatedResult = PaginatedResult<CyberAirGap>.Success(
            new List<CyberAirGap> { new CyberAirGap { Name = "Test" } }, 1, 1, 10);

        var mappedResult = PaginatedResult<CyberAirGapListVm>.Success(
            new List<CyberAirGapListVm> { new CyberAirGapListVm { Name = "Test" } }, 1, 1, 10);

        mockRepo.Setup(r => r.PaginatedListAllAsync(
            query.PageNumber, query.PageSize, It.IsAny<CyberAirGapFilterSpecification>(), query.SortColumn, query.SortOrder))
            .ReturnsAsync(paginatedResult);

        mockMapper.Setup(m => m.Map<PaginatedResult<CyberAirGapListVm>>(paginatedResult))
            .Returns(mappedResult);

        var handler = new GetCyberAirGapPaginatedListQueryHandler(mockMapper.Object, mockRepo.Object);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        Assert.Equal("Test", result.Data[0].Name);
        mockRepo.Verify(r => r.PaginatedListAllAsync(
            query.PageNumber, query.PageSize, It.IsAny<CyberAirGapFilterSpecification>(), query.SortColumn, query.SortOrder), Times.Once);
        mockMapper.Verify(m => m.Map<PaginatedResult<CyberAirGapListVm>>(paginatedResult), Times.Once);
    }
}
