﻿using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Web.Areas.Dashboard.Controllers;

[Area("Dashboard")]
public class CustomDashboardController : BaseController
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IDataProvider _dataProvider;
    
    public CustomDashboardController(ILoggedInUserService loggedInUserService, IDataProvider dataProvider)
    {
        _loggedInUserService = loggedInUserService;
        _dataProvider = dataProvider;
    }
    [EventCode(EventCodes.CustomDashboard.List)]
    public async Task<IActionResult>List(string dashboardSubId = null)
    {
        if (dashboardSubId is null)
        {
            var userId = _loggedInUserService.UserId;

            var userRole = _loggedInUserService.Role;

            var dashboard = await _dataProvider.DynamicDashboardMap.GetDefaultDashboardByUserId(userId)
                            ?? await _dataProvider.DynamicDashboardMap.GetDefaultDashboardByRoleId(userRole);


            if (dashboard is null)
            {
                return RedirectToAction("List", "ServiceAvailability", new { Area = "Dashboard" });
            }

            var subDashboard = await _dataProvider.DynamicSubDashboard.GetByReferenceId(dashboard.DashBoardSubId);

            if (subDashboard is null)
            {
                return RedirectToAction("List", "ServiceAvailability", new { Area = "Dashboard" });
            }

           // return await DynamicDashboardList(dashboard.DashBoardSubId);

            return View(subDashboard);

        }
        else
        {
            var subDashboard = await _dataProvider.DynamicSubDashboard.GetByReferenceId(dashboardSubId);

            return View(subDashboard);
        }
        
    }
    [EventCode(EventCodes.CustomDashboard.GetByReferenceId)]
   public async Task<IActionResult> DynamicDashboardList(string dashboardSubId)
    {
        var subDashboard = await _dataProvider.DynamicSubDashboard.GetByReferenceId(dashboardSubId);

        return Json(subDashboard);
    }
}
