﻿using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactType.Events.Create;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactType.Commands
{
    public class CreateFiaImpactTypeCommandHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IFiaImpactTypeRepository> _mockFiaImpactTypeRepository;
        private readonly CreateFiaImpactTypeCommandHandler _handler;

        private readonly List<Domain.Entities.FiaImpactType> _fiaImpactTypeStore;

        public CreateFiaImpactTypeCommandHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();

            // Pre-filled in-memory list for testing
            _fiaImpactTypeStore = new List<Domain.Entities.FiaImpactType>();

            // Use your own mock helper to generate the repository
            _mockFiaImpactTypeRepository = FiaImpactTypeRepositoryMocks.CreateFiaImpactTypeRepository(_fiaImpactTypeStore);

            _handler = new CreateFiaImpactTypeCommandHandler(
                _mockMapper.Object,
                _mockFiaImpactTypeRepository.Object,
                _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_Should_Return_Correct_Response_When_Command_Is_Valid()
        {
            // Arrange
            var command = new CreateFiaImpactTypeCommand { Name = "High Impact" };

            var mappedEntity = new Domain.Entities.FiaImpactType { Name = command.Name };

            _mockMapper.Setup(m => m.Map<Domain.Entities.FiaImpactType>(command)).Returns(mappedEntity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(string.IsNullOrWhiteSpace(result.Id));
            Assert.Contains(command.Name, result.Message);

            _mockMapper.Verify(m => m.Map<Domain.Entities.FiaImpactType>(command), Times.Once);
            _mockFiaImpactTypeRepository.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.FiaImpactType>()), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<FiaImpactTypeCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Publish_Event_With_Correct_Name()
        {
            // Arrange
            var command = new CreateFiaImpactTypeCommand { Name = "Critical" };
            var mapped = new Domain.Entities.FiaImpactType { Name = "Critical" };

            _mockMapper.Setup(m => m.Map<Domain.Entities.FiaImpactType>(command)).Returns(mapped);

            // Act
            await _handler.Handle(command, CancellationToken.None);

            // Assert
            _mockPublisher.Verify(p => p.Publish(
                It.Is<FiaImpactTypeCreatedEvent>(e => e.Name == "Critical"),
                It.IsAny<CancellationToken>()),
                Times.Once);
        }
    }
}
