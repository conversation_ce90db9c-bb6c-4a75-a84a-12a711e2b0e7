using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUp.Events.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUp.Events;

public class BackUpPaginatedEventTests : IClassFixture<BackUpFixture>
{
    private readonly BackUpFixture _backUpFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<BackUpPaginatedEventHandler>> _mockLogger;
    private readonly BackUpPaginatedEventHandler _handler;

    public BackUpPaginatedEventTests(BackUpFixture backUpFixture)
    {
        _backUpFixture = backUpFixture;
        _mockUserActivityRepository = BackUpRepositoryMocks.CreateUserActivityRepository(_backUpFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BackUpPaginatedEventHandler>>();

        // Setup default user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");
        _mockUserService.Setup(x => x.CompanyId).Returns("TestCompany123");
        _mockUserService.Setup(x => x.RequestedUrl).Returns("/api/backup/paginated");
        _mockUserService.Setup(x => x.IpAddress).Returns("*************");

        _handler = new BackUpPaginatedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_ProcessBackUpPaginatedEvent_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpPaginatedEvent();

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivityWithCorrectPaginatedProperties_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpPaginatedEvent();

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("View BackUp");
        createdActivity.ActivityType.ShouldBe("View");
        createdActivity.ActivityDetails.ShouldBe("Backup Data viewed");
        createdActivity.UserId.ShouldBe("TestUser123");
        createdActivity.LoginName.ShouldBe("TestUser123");
        createdActivity.CompanyId.ShouldBe("TestCompany123");
        createdActivity.RequestUrl.ShouldBe("/api/backup/paginated");
        createdActivity.HostAddress.ShouldBe("*************");
        createdActivity.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_ProcessMultiplePaginatedEvents_When_ValidEvents()
    {
        // Arrange
        var events = new[]
        {
            new BackUpPaginatedEvent(),
            new BackUpPaginatedEvent(),
            new BackUpPaginatedEvent()
        };

        // Act
        foreach (var eventItem in events)
        {
            await _handler.Handle(eventItem, CancellationToken.None);
        }

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(3));
    }

    /// <summary>
    /// Test: Event processing performance with rapid succession
    /// Expected: Multiple rapid paginated events are processed correctly
    /// </summary>
    [Fact]
    public async Task Handle_ProcessRapidPaginatedEvents_When_MultipleEventsInSuccession()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new BackUpPaginatedEvent()).ToList();

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(10));
    }

    [Fact]
    public async Task Handle_UseUserServiceForUserAndCompanyInfo_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpPaginatedEvent();

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.UserId.ShouldBe("TestUser123");
        createdActivity.LoginName.ShouldBe("TestUser123");
        createdActivity.CompanyId.ShouldBe("TestCompany123");
        createdActivity.RequestUrl.ShouldBe("/api/backup/paginated");
        createdActivity.HostAddress.ShouldBe("*************");
        
        // Verify user service was called
        _mockUserService.Verify(x => x.UserId, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.LoginName, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.CompanyId, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.RequestedUrl, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.IpAddress, Times.AtLeastOnce);
    }

    /// <summary>
    /// Test: Logger integration
    /// Expected: Logger is used to log event processing
    /// </summary>
    [Fact]
    public async Task Handle_LogEventProcessing_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpPaginatedEvent();

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
          _mockLogger.ShouldNotBeNull();
    }

    /// <summary>
    /// Test: Event with different user contexts
    /// Expected: Different user contexts are handled correctly
    /// </summary>
    [Fact]
    public async Task Handle_HandleDifferentUserContexts_When_ValidEvents()
    {
        // Arrange
        var userContexts = new[]
        {
            new { UserId = "User1", LoginName = "User1", CompanyId = "Company1" },
            new { UserId = "User2", LoginName = "User2", CompanyId = "Company2" },
            new { UserId = "User3", LoginName = "User3", CompanyId = "Company3" }
        };

        foreach (var context in userContexts)
        {
            // Setup user service for this context
            _mockUserService.Setup(x => x.UserId).Returns(context.UserId);
            _mockUserService.Setup(x => x.LoginName).Returns(context.LoginName);
            _mockUserService.Setup(x => x.CompanyId).Returns(context.CompanyId);

            var backUpEvent = new BackUpPaginatedEvent();

            Domain.Entities.UserActivity createdActivity = null;
            _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
                .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

            // Act
            await _handler.Handle(backUpEvent, CancellationToken.None);

            // Assert
            createdActivity.ShouldNotBeNull();
            createdActivity.UserId.ShouldBe(context.UserId);
            createdActivity.LoginName.ShouldBe(context.LoginName);
            createdActivity.CompanyId.ShouldBe(context.CompanyId);
            createdActivity.ActivityDetails.ShouldBe("Backup Data viewed");
        }
    }

    /// <summary>
    /// Test: Event with null user service values
    /// Expected: Null user service values are handled gracefully
    /// </summary>
    [Fact]
    public async Task Handle_HandleNullUserServiceValues_When_ValidEvent()
    {
        // Arrange
        _mockUserService.Setup(x => x.UserId).Returns((string)null);
        _mockUserService.Setup(x => x.LoginName).Returns((string)null);
        _mockUserService.Setup(x => x.CompanyId).Returns((string)null);
        _mockUserService.Setup(x => x.RequestedUrl).Returns((string)null);
        _mockUserService.Setup(x => x.IpAddress).Returns((string)null);

        var backUpEvent = new BackUpPaginatedEvent();

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("View BackUp");
        createdActivity.ActivityType.ShouldBe("View");
        createdActivity.ActivityDetails.ShouldBe("Backup Data viewed");
        // Null values should be handled gracefully
    }

    /// <summary>
    /// Test: Event with empty string user service values
    /// Expected: Empty string user service values are handled gracefully
    /// </summary>
    [Fact]
    public async Task Handle_HandleEmptyUserServiceValues_When_ValidEvent()
    {
        // Arrange
        _mockUserService.Setup(x => x.UserId).Returns(string.Empty);
        _mockUserService.Setup(x => x.LoginName).Returns(string.Empty);
        _mockUserService.Setup(x => x.CompanyId).Returns(string.Empty);
        _mockUserService.Setup(x => x.RequestedUrl).Returns(string.Empty);
        _mockUserService.Setup(x => x.IpAddress).Returns(string.Empty);

        var backUpEvent = new BackUpPaginatedEvent();

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("View BackUp");
        createdActivity.ActivityType.ShouldBe("View");
        createdActivity.ActivityDetails.ShouldBe("Backup Data viewed");
        createdActivity.UserId.ShouldBe(string.Empty);
        createdActivity.LoginName.ShouldBe(string.Empty);
        createdActivity.CompanyId.ShouldBe(string.Empty);
    }

    /// <summary>
    /// Test: Event with concurrent processing
    /// Expected: Concurrent paginated events are processed correctly
    /// </summary>
    [Fact]
    public async Task Handle_ProcessConcurrentPaginatedEvents_When_MultipleEventsSimultaneously()
    {
        // Arrange
        var events = Enumerable.Range(1, 5).Select(i => new BackUpPaginatedEvent()).ToList();

        // Act
        var tasks = events.Select(evt => Task.Run(() => _handler.Handle(evt, CancellationToken.None)));
        await Task.WhenAll(tasks);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(5));
    }

    /// <summary>
    /// Test: Event activity type and entity validation
    /// Expected: Activity type and entity are correctly set for view operations
    /// </summary>
    [Fact]
    public async Task Handle_ValidateActivityTypeAndEntity_When_ValidEvent()
    {
        // Arrange
        var backUpEvent = new BackUpPaginatedEvent();

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.ActivityType.ShouldBe("View");
        createdActivity.Entity.ShouldBe("BackUp");
        createdActivity.Action.ShouldBe("View BackUp");
        createdActivity.ActivityDetails.ShouldBe("Backup Data viewed");
    }

    [Fact]
    public async Task Handle_HandleSpecialCharsInUserContext_When_ValidEvent()
    {
        // Arrange
        _mockUserService.Setup(x => x.UserId).Returns("User@123!<script>");
        _mockUserService.Setup(x => x.LoginName).Returns("Login&Name#Special");
        _mockUserService.Setup(x => x.CompanyId).Returns("Company$%^&*()");

        var backUpEvent = new BackUpPaginatedEvent();

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(backUpEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        createdActivity.UserId.ShouldBe("User@123!<script>");
        createdActivity.LoginName.ShouldBe("Login&Name#Special");
        createdActivity.CompanyId.ShouldBe("Company$%^&*()");
        createdActivity.ActivityDetails.ShouldBe("Backup Data viewed");
    }
}
