using ContinuityPatrol.Application.Features.Incident.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.IncidentModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Queries;

public class GetIncidentPaginatedListQueryHandlerTests : IClassFixture<IncidentFixture>
{
    private readonly GetIncidentPaginatedListQueryHandler _handler;
    private readonly Mock<IIncidentRepository> _mockIncidentRepository;
    private readonly IncidentFixture _incidentFixture;

    public GetIncidentPaginatedListQueryHandlerTests(IncidentFixture incidentFixture)
    {
        _incidentFixture = incidentFixture;

        _incidentFixture.Incidents[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _incidentFixture.Incidents[0].IncidentName = "Critical System Failure";
        _incidentFixture.Incidents[0].IncidentNumber = "INC-001";
        _incidentFixture.Incidents[0].Description = "Critical system failure incident";

        _incidentFixture.Incidents[1].IncidentName = "Network Outage";
        _incidentFixture.Incidents[1].IncidentNumber = "INC-002";
        _incidentFixture.Incidents[1].Description = "Network connectivity issues";

        _mockIncidentRepository = IncidentRepositoryMocks.GetPaginatedIncidentRepository(_incidentFixture.Incidents);

        _handler = new GetIncidentPaginatedListQueryHandler(_mockIncidentRepository.Object, _incidentFixture.Mapper);
    }

    [Fact]
    public void Should_Create_Query_With_Valid_Parameters()
    {
        // Act
        var query = new GetIncidentPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test",
            SortColumn = "IncidentName",
            SortOrder = "asc"
        };

        // Assert
        Assert.NotNull(query);
        Assert.Equal(1, query.PageNumber);
        Assert.Equal(10, query.PageSize);
        Assert.Equal("test", query.SearchString);
        Assert.Equal("IncidentName", query.SortColumn);
        Assert.Equal("asc", query.SortOrder);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetIncidentPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<IncidentListVm>>();

        result.TotalCount.ShouldBe(_incidentFixture.Incidents.Count);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_PaginatedIncidents_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetIncidentPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Critical" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<IncidentListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<IncidentListVm>();

        result.Data[0].Id.ShouldNotBeNullOrEmpty();

        result.Data[0].IncidentName.ShouldBe("Critical System Failure");

        result.Data[0].IncidentNumber.ShouldBe("INC-001");

        result.Data[0].Description.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetIncidentPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<IncidentListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetIncidentPaginatedListQuery(), CancellationToken.None);

        _mockIncidentRepository.Verify(x => x.PaginatedListAllAsync(It.IsAny<int>(),
            It.IsAny<int>(), It.IsAny<IncidentFilterSpecification>(),
            It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_Valid_IncidentDetails()
    {
        var result = await _handler.Handle(new GetIncidentPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<IncidentListVm>>();

        result.Data[0].Id.ShouldBe(_incidentFixture.Incidents[0].ReferenceId);
        result.Data[0].IncidentName.ShouldBe(_incidentFixture.Incidents[0].IncidentName);
        result.Data[0].IncidentNumber.ShouldBe(_incidentFixture.Incidents[0].IncidentNumber);
        result.Data[0].Description.ShouldBe(_incidentFixture.Incidents[0].Description);
    }

    [Fact]
    public async Task Handle_Return_CorrectPaginationInfo()
    {
        var result = await _handler.Handle(new GetIncidentPaginatedListQuery { PageNumber = 1, PageSize = 2 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<IncidentListVm>>();

        result.CurrentPage.ShouldBe(1);
        result.PageSize.ShouldBe(2);
        result.HasNextPage.ShouldBe(_incidentFixture.Incidents.Count > 2);
        result.HasPreviousPage.ShouldBe(false);
    }
}