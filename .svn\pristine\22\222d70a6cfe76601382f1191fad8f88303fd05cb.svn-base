﻿using ContinuityPatrol.Application.Features.FiaInterval.Commands.Update;
using ContinuityPatrol.Application.Features.FiaInterval.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaInterval.Commands;

public class UpdateFiaIntervalTests : IClassFixture<FiaIntervalFixture>
{
    private readonly FiaIntervalFixture _fiaIntervalFixture;
    private readonly Mock<IFiaIntervalRepository> _mockFiaIntervalRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateFiaIntervalCommandHandler _handler;

    public UpdateFiaIntervalTests(FiaIntervalFixture fiaIntervalFixture)
    {
        _fiaIntervalFixture = fiaIntervalFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockFiaIntervalRepository = FiaIntervalRepositoryMocks.UpdateFiaIntervalRepository(_fiaIntervalFixture.FiaIntervals);

        _handler = new UpdateFiaIntervalCommandHandler(_fiaIntervalFixture.Mapper, _mockFiaIntervalRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_UpdateFiaIntervalResponse_When_FiaIntervalUpdated()
    {
        _fiaIntervalFixture.UpdateFiaIntervalCommand.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        var result = await _handler.Handle(_fiaIntervalFixture.UpdateFiaIntervalCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateFiaIntervalResponse));

        result.Id.ShouldNotBeNullOrEmpty();

        result.Message.ShouldContain("Time interval updated successfully");

        result.Success.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        _fiaIntervalFixture.UpdateFiaIntervalCommand.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        await _handler.Handle(_fiaIntervalFixture.UpdateFiaIntervalCommand, CancellationToken.None);

        _mockFiaIntervalRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _fiaIntervalFixture.UpdateFiaIntervalCommand.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        await _handler.Handle(_fiaIntervalFixture.UpdateFiaIntervalCommand, CancellationToken.None);

        _mockFiaIntervalRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.FiaInterval>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_FiaIntervalUpdated()
    {
        _fiaIntervalFixture.UpdateFiaIntervalCommand.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        await _handler.Handle(_fiaIntervalFixture.UpdateFiaIntervalCommand, CancellationToken.None);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<FiaIntervalUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_FiaIntervalNotFound()
    {
        var command = _fiaIntervalFixture.UpdateFiaIntervalCommand;
        command.Id = "non-existent-id";

        _mockFiaIntervalRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync((Domain.Entities.FiaInterval)null);

        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Return_CorrectMessage_When_FiaIntervalUpdated()
    {
        _fiaIntervalFixture.UpdateFiaIntervalCommand.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        var result = await _handler.Handle(_fiaIntervalFixture.UpdateFiaIntervalCommand, CancellationToken.None);

        result.Message.ShouldBe("Time interval updated successfully");
    }

    [Fact]
    public async Task Handle_Return_EntityReferenceId_When_FiaIntervalUpdated()
    {
        _fiaIntervalFixture.UpdateFiaIntervalCommand.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        var result = await _handler.Handle(_fiaIntervalFixture.UpdateFiaIntervalCommand, CancellationToken.None);

        result.Id.ShouldNotBeNullOrEmpty();
        result.Id.ShouldBe(_fiaIntervalFixture.UpdateFiaIntervalCommand.Id);
    }

    [Fact]
    public async Task Handle_PublishEvent_WithCorrectEventName()
    {
        _fiaIntervalFixture.UpdateFiaIntervalCommand.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        await _handler.Handle(_fiaIntervalFixture.UpdateFiaIntervalCommand, CancellationToken.None);

        _mockPublisher.Verify(x => x.Publish(It.Is<FiaIntervalUpdatedEvent>(
            evt => evt.Name == "Time interval"), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallMapper_ToMapCommandToEntity()
    {
        var command = _fiaIntervalFixture.UpdateFiaIntervalCommand;
        command.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        await _handler.Handle(command, CancellationToken.None);

        // Verify mapper was called to map command to existing entity
        _mockFiaIntervalRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.FiaInterval>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetSuccessResponse_When_FiaIntervalUpdated()
    {
        _fiaIntervalFixture.UpdateFiaIntervalCommand.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        var result = await _handler.Handle(_fiaIntervalFixture.UpdateFiaIntervalCommand, CancellationToken.None);

        result.Success.ShouldBe(true);
        result.Message.ShouldNotBeNullOrEmpty();
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_UpdateEntity_WithCorrectValues()
    {
        var command = _fiaIntervalFixture.UpdateFiaIntervalCommand;
        command.Id = _fiaIntervalFixture.FiaIntervals[0].ReferenceId;

        await _handler.Handle(command, CancellationToken.None);

        _mockFiaIntervalRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.FiaInterval>(
            entity => entity.ReferenceId == command.Id)), Times.Once);
    }
}