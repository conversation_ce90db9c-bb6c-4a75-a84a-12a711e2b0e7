{"Version": 1, "WorkspaceRootPath": "D:\\TestCase UI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\attributes\\autoglobalvariabledataattribute.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\attributes\\autoglobalvariabledataattribute.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\commands\\createglobalvariabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\commands\\createglobalvariabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\commands\\deleteglobalvariabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\commands\\deleteglobalvariabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\commands\\updateglobalvariabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\commands\\updateglobalvariabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\events\\createglobalvariableeventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\events\\createglobalvariableeventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\events\\deleteglobalvariableeventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\events\\deleteglobalvariableeventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\events\\updateglobalvariableeventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\events\\updateglobalvariableeventtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailbynamequeryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailbynamequeryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariablelistqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariablelistqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariablenameuniquequeryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariablenameuniquequeryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariablepaginatedlistqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariablepaginatedlistqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\createglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\createglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\updateglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\updateglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\fixtures\\globalvariablefixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\fixtures\\globalvariablefixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\mocks\\globalvariablerepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\mocks\\globalvariablerepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\queries\\getpaginatedlist\\getglobalvariablepaginatedlistqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\queries\\getpaginatedlist\\getglobalvariablepaginatedlistqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 255, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 8, "Title": "GetGlobalVariableDetailQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:32:25.775Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "AutoGlobalVariableDataAttribute.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Attributes\\AutoGlobalVariableDataAttribute.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Attributes\\AutoGlobalVariableDataAttribute.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Attributes\\AutoGlobalVariableDataAttribute.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Attributes\\AutoGlobalVariableDataAttribute.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:31:19.396Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CreateGlobalVariableTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\CreateGlobalVariableTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\CreateGlobalVariableTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\CreateGlobalVariableTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\CreateGlobalVariableTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:53.933Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DeleteGlobalVariableTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\DeleteGlobalVariableTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\DeleteGlobalVariableTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\DeleteGlobalVariableTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\DeleteGlobalVariableTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:51.196Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UpdateGlobalVariableTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\UpdateGlobalVariableTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\UpdateGlobalVariableTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\UpdateGlobalVariableTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Commands\\UpdateGlobalVariableTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:48.198Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "CreateGlobalVariableEventTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\CreateGlobalVariableEventTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\CreateGlobalVariableEventTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\CreateGlobalVariableEventTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\CreateGlobalVariableEventTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:43.642Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DeleteGlobalVariableEventTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\DeleteGlobalVariableEventTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\DeleteGlobalVariableEventTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\DeleteGlobalVariableEventTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\DeleteGlobalVariableEventTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:40.396Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "UpdateGlobalVariableEventTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\UpdateGlobalVariableEventTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\UpdateGlobalVariableEventTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\UpdateGlobalVariableEventTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Events\\UpdateGlobalVariableEventTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:37.013Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "GetGlobalVariableDetailByNameQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailByNameQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailByNameQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailByNameQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailByNameQueryHandlerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:31.905Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "GetGlobalVariableListQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableListQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableListQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableListQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableListQueryHandlerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:24.978Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "GetGlobalVariableNameUniqueQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableNameUniqueQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableNameUniqueQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableNameUniqueQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableNameUniqueQueryHandlerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:21.821Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "CreateGlobalVariableValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:12.358Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "UpdateGlobalVariableValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\UpdateGlobalVariableValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\UpdateGlobalVariableValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\UpdateGlobalVariableValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\UpdateGlobalVariableValidatorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:30:08.611Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "GlobalVariableFixture.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Fixtures\\GlobalVariableFixture.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Fixtures\\GlobalVariableFixture.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Fixtures\\GlobalVariableFixture.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Fixtures\\GlobalVariableFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:29:59.079Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "GlobalVariableRepositoryMocks.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\GlobalVariableRepositoryMocks.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\GlobalVariableRepositoryMocks.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\GlobalVariableRepositoryMocks.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\GlobalVariableRepositoryMocks.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:29:48.981Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}, {"$type": "Document", "DocumentIndex": 16, "Title": "GetGlobalVariablePaginatedListQueryHandler.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQueryHandler.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQueryHandler.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQueryHandler.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQueryHandler.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAQwAwAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:13:01.023Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "GetGlobalVariablePaginatedListQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariablePaginatedListQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariablePaginatedListQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariablePaginatedListQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariablePaginatedListQueryHandlerTests.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAAwEgAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:10:01.708Z", "EditorCaption": ""}]}, {"DockedHeight": 21, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}]}]}]}