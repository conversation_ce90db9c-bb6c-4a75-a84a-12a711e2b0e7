{"Version": 1, "WorkspaceRootPath": "D:\\TestCase UI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\commands\\update\\updateglobalvariablecommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\commands\\update\\updateglobalvariablecommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\commands\\create\\createglobalvariablecommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\commands\\create\\createglobalvariablecommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\commands\\create\\createglobalvariablecommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\commands\\create\\createglobalvariablecommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\createglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\createglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\commands\\create\\createglobalvariablecommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\commands\\create\\createglobalvariablecommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\queries\\getpaginatedlist\\getglobalvariablepaginatedlistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\queries\\getpaginatedlist\\getglobalvariablepaginatedlistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\updateglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\validators\\updateglobalvariablevalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\commands\\update\\updateglobalvariablecommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\commands\\update\\updateglobalvariablecommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\constants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\constants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\company\\validators\\createcompanyvalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\company\\validators\\createcompanyvalidatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\queries\\getpaginatedlist\\getglobalvariablepaginatedlistqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\queries\\getpaginatedlist\\getglobalvariablepaginatedlistqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariablepaginatedlistqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariablepaginatedlistqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\globalvariable\\queries\\getglobalvariabledetailqueryhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\globalvariable\\queries\\getlist\\getglobalvariablelistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalvariable\\queries\\getlist\\getglobalvariablelistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 209, "SelectedChildIndex": 12, "Children": [{"$type": "Document", "DocumentIndex": 12, "Title": "GetGlobalVariableDetailQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariableDetailQueryHandlerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:32:25.775Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CreateGlobalVariableCommandHandler.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommandHandler.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommandHandler.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommandHandler.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommandHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:06:55.619Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "Constants.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Constants.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Constants.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Constants.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Constants.cs", "ViewState": "AgIAAGkGAAAAAAAAAAAAADUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:46:51.074Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}, {"$type": "Document", "DocumentIndex": 7, "Title": "UpdateGlobalVariableCommand.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Update\\UpdateGlobalVariableCommand.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Update\\UpdateGlobalVariableCommand.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Update\\UpdateGlobalVariableCommand.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Update\\UpdateGlobalVariableCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:34:58.652Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "UpdateGlobalVariableValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\UpdateGlobalVariableValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\UpdateGlobalVariableValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\UpdateGlobalVariableValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\UpdateGlobalVariableValidatorTests.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAYwCcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:34:44.148Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "CreateGlobalVariableCommand.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommand.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommand.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommand.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:33:32.421Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "CreateCompanyValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Company\\Validators\\CreateCompanyValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Company\\Validators\\CreateCompanyValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Company\\Validators\\CreateCompanyValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Company\\Validators\\CreateCompanyValidatorTests.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:32:59.391Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CreateGlobalVariableValidatorTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Validators\\CreateGlobalVariableValidatorTests.cs", "ViewState": "AgIAABkAAAAAAAAAAAAYwCoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:32:44.94Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "UpdateGlobalVariableCommandValidator.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Update\\UpdateGlobalVariableCommandValidator.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Update\\UpdateGlobalVariableCommandValidator.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Update\\UpdateGlobalVariableCommandValidator.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Update\\UpdateGlobalVariableCommandValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:00:17.149Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CreateGlobalVariableCommandValidator.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommandValidator.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommandValidator.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommandValidator.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Commands\\Create\\CreateGlobalVariableCommandValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:59:47.849Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "GetGlobalVariablePaginatedListQuery.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQuery.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQuery.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQuery.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:59:30.597Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "GetGlobalVariableListQuery.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetList\\GetGlobalVariableListQuery.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetList\\GetGlobalVariableListQuery.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetList\\GetGlobalVariableListQuery.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetList\\GetGlobalVariableListQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:59:19.914Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "GetGlobalVariablePaginatedListQueryHandler.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQueryHandler.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQueryHandler.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQueryHandler.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalVariable\\Queries\\GetPaginatedList\\GetGlobalVariablePaginatedListQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:58:16.493Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "GetGlobalVariablePaginatedListQueryHandlerTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariablePaginatedListQueryHandlerTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariablePaginatedListQueryHandlerTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariablePaginatedListQueryHandlerTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\GlobalVariable\\Queries\\GetGlobalVariablePaginatedListQueryHandlerTests.cs", "ViewState": "AgIAADEAAAAAAAAAAAAowFIAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:57:39.461Z", "EditorCaption": ""}]}, {"DockedHeight": 67, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}]}]}]}