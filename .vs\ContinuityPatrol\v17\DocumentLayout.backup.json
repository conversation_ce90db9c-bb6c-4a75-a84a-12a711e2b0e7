{"Version": 1, "WorkspaceRootPath": "D:\\TestCase UI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\incident\\commands\\delete\\deleteincidentcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\incident\\commands\\delete\\deleteincidentcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|d:\\testcase ui\\tests\\core\\continuitypatrol.application.unittests\\features\\incident\\commands\\createincidenttests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\features\\incident\\commands\\createincidenttests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\incident\\commands\\create\\createincidentcommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\incident\\commands\\create\\createincidentcommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\testcase ui\\core\\continuitypatrol.application\\features\\incident\\commands\\update\\updateincidentcommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\incident\\commands\\update\\updateincidentcommandvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 219, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}]}, {"DockedHeight": 57, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "DeleteIncidentCommand.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Delete\\DeleteIncidentCommand.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Delete\\DeleteIncidentCommand.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Delete\\DeleteIncidentCommand.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Delete\\DeleteIncidentCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:37:04.477Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UpdateIncidentCommandValidator.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Update\\UpdateIncidentCommandValidator.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Update\\UpdateIncidentCommandValidator.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Update\\UpdateIncidentCommandValidator.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Update\\UpdateIncidentCommandValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:22:51.594Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CreateIncidentCommandValidator.cs", "DocumentMoniker": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Create\\CreateIncidentCommandValidator.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Create\\CreateIncidentCommandValidator.cs", "ToolTip": "D:\\TestCase UI\\Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Create\\CreateIncidentCommandValidator.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\Incident\\Commands\\Create\\CreateIncidentCommandValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:22:41.552Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CreateIncidentTests.cs", "DocumentMoniker": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Commands\\CreateIncidentTests.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Commands\\CreateIncidentTests.cs", "ToolTip": "D:\\TestCase UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Commands\\CreateIncidentTests.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Features\\Incident\\Commands\\CreateIncidentTests.cs", "ViewState": "AgIAABwAAAAAAAAAAAAgwCgAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:20:34.77Z", "EditorCaption": ""}]}]}]}