﻿using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using SmtpConfig = ContinuityPatrol.Domain.Entities.SmtpConfiguration;
using WorkflowOpGrp = ContinuityPatrol.Domain.Entities.WorkflowOperationGroup;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Commands
{
   public class WorkflowDrCalenderSendEmailTest 
    {       
        private readonly Mock<IDrCalenderRepository> _drCalenderRepository = new();
        private readonly Mock<ILogger<WorkflowDrCalenderSendEmailCommandHandler>> _logger = new();
        private readonly Mock<ISmtpConfigurationRepository> _smtpRepo = new();
        private readonly Mock<IWorkflowOperationGroupRepository> _workflowOpRepo = new();
        private readonly Mock<IWorkflowProfileInfoViewRepository> _workflowProfileRepo = new();
        private readonly Mock<IUserRepository> _userRepo = new();

        private WorkflowDrCalenderSendEmailCommandHandler _handler;

        public WorkflowDrCalenderSendEmailTest()
        {
            _handler = new WorkflowDrCalenderSendEmailCommandHandler(
                _drCalenderRepository.Object,
                _logger.Object,
                _smtpRepo.Object,
                _workflowOpRepo.Object,
                _workflowProfileRepo.Object,
                _userRepo.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnSuccessMessage_WhenEmailIsSent()
        {
            // Arrange
            var command = new WorkflowDrCalenderSendEmailCommand
            {
                DrCalenderId = Guid.NewGuid().ToString(),
                ProfileId = "P1",
                WOrkflowOperationId = "OP1",
                WorkflowIds = new List<string> { "W1" },
                ResponsibleUser = new List<UserInfoDetailVm>
            {
                new UserInfoDetailVm { Email = "<EMAIL>", UserName = "Test User" }
            },
                ActivityDetail = "DR Activity Detail"
            };

            _drCalenderRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync(new DrCalenderActivity
                {
                    CreatedBy = "user1",
                    ScheduledStartDate = DateTime.Now,
                    ScheduledEndDate = DateTime.Now.AddHours(1),
                    Location = "Chennai"
                });

            _smtpRepo.Setup(x => x.ListAllAsync()).ReturnsAsync(new List<SmtpConfig>
        {
            new SmtpConfig
            {
                SmtpHost = "smtp.test.com",
                Port = "587",
                UserName = "encryptedUser",
                Password = "encryptedPass",
                EnableSSL = true
            }
        });

            _workflowProfileRepo.Setup(x => x.GetWorkflowProfileInfoViewByProfileIdandWorkflowIds(It.IsAny<string>(), It.IsAny<List<string>>()))
                .ReturnsAsync(new List<WorkflowProfileInfoView>
                {
                new WorkflowProfileInfoView { WorkflowId = "W1", WorkflowName = "Workflow1", WorkflowType = "TypeA" }
                });

            _workflowOpRepo.Setup(x => x.GetWorkflowOperationGroupByWorkflowOperationIdAndWorkflowIds(It.IsAny<List<string>>(), It.IsAny<string>()))
                .ReturnsAsync(new List<WorkflowOpGrp>
                {
                new WorkflowOpGrp
                {
                    WorkflowId = "W1",
                    CreatedDate = DateTime.Now,
                    LastModifiedDate = DateTime.Now.AddMinutes(30),
                    Status = "Success"
                }
                });

            _userRepo.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync(new Domain.Entities.User { LoginName = "organizer" });

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            //Assert.Equal("Email sent successfully", result.Message);
            Assert.Equal("The value cannot be an empty string. (Parameter 'address')", result.Message);
        }

        [Fact]
        public async Task Handle_ShouldReturnErrorMessage_WhenExceptionOccurs()
        {
            // Arrange
            var command = new WorkflowDrCalenderSendEmailCommand
            {
                DrCalenderId = "invalid",
                WorkflowIds = new List<string> { "W1" },
                ResponsibleUser = new List<UserInfoDetailVm>()
            };

            _drCalenderRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("DB error", result.Message);
        }
    }
}
