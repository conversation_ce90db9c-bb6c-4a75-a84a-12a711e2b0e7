
function pageLayoutDesign(layoutName) {

    
    if (layoutName == "PB_layout15") {
        // $("#layoutDesignModal").modal("show")
        $(".cardCreation").addClass('card-show')
        $(".cardCreation").removeClass("d-none")
        // appendData += `
        //<div class="row h-100 g-3 layoutcontainer pageBuilderSetRowDesign" id="pageBuilderInfraId">
        //     <div class="col-12 d-grid">
        //       <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)" >
        //       </div>
        //     </div>
        // </div>
        //  <div class="text-end">
        //      <button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button>
        //           <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
        //     </div>
        // `
        //$('#PB_pageContent').append(appendData);
    }
    else {
        if (layoutName == "PB_layout1") {
            appendData = `
        <div class="row g-3  mt-0 pageBuilderSetRowDesign Card_Design_None Create-Dashboard" style="height: calc(100vh - 160px);  overflow-y:auto;" id="pageBuilderInfraId">
            <div class="col-5 mt-0 ">
                <div class="card border-dashed mb-0">
                    <div class="card-body pageBuilderSetDesign"  ondrop="drop(event)" ondragover="allowDrop(event)" style="height: calc(100vh - 162px); overflow-y: auto;"></div>
                </div>
            </div>
            <div class="col-7 d-grid mt-0 gap-3">
                 <div class="card border-dashed mb-0 h-150">
                     <div class="card-body pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)"></div>
                  </div>
                  <div class="card border-dashed mb-0 h-150">
                      <div class="card-body pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)"></div>
                  </div>
                  <div class="card border-dashed mb-0 h-150">
                       <div class="card-body pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)"></div>
                  </div>
            </div>
        </div>`}
        else if (layoutName == "PB_layout2") {
            appendData = `

    <div class="row g-2 mt-0 pageBuilderSetRowDesign" style="height: calc(50vh - 23px);" id="pageBuilderInfraId">
    <div class="col-6" >
     <div class="card Card_Design_None border-dashed mb-2">
                                               <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
  </div>
    <div class="col-6">
     <div class="card Card_Design_None border-dashed mb-2">
                                               <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
   </div>
   </div>
    <div class="row g-2" style="height: calc(60vh - 23px);">
    <div class="col-6">
    <div class="card Card_Design_None border-dashed mb-2">
                                              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                        </div>
        <div class="card Card_Design_None border-dashed mb-2">
                                                  <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                        </div>
                         </div>
     
       <div class="col-6">
    <div class="card Card_Design_None border-dashed mb-2">
                                              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                        </div></div></div>
      </div>
     
    `
        }
        else if (layoutName == "PB_layout3") {
            appendData = `

<div class="row  g-3 pageBuilderSetRowDesign" style="height: calc(100vh - 56px);" id="pageBuilderInfraId">
        <div class="col-6 d-grid">
            <div class="card Card_Design_None border-dashed">
                                                      <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
            </div>

        </div>
        <div class="col-6 d-grid">
          <div class="card Card_Design_None  border-dashed">
                                                    <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
          </div>
           <div class="card Card_Design_None  border-dashed">
                                                     <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
          </div>
        </div>
    </div>
    
    
    `
        }
        else if (layoutName == "PB_layout4") {
            appendData = `
        <div class="row g-3 pageBuilderSetRowDesign" style="height: calc(50vh - 23px);"  id="pageBuilderInfraId">
            <div class="col-5">
                <div class="card border-dashed">
                                                          <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
            <div class="col-7">
                <div class="card Card_Design_None border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
        </div>


        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-4 d-grid">
                <div class="card Card_Design_None border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card Card_Design_None border-dashed">
                   <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card Card_Design_None border-dashed ">
                            
                     <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">

            <div class="col-8 d-grid">
                <div class="card Card_Design_None border-dashed">
                   <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                    
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="row">
                 <div class="col-6">
                    <div class="card Card_Design_None border-dashed">
                        <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                    </div>
                     </div>
                     <div class="col-6">
                    <div class="card Card_Design_None border-dashed">
                        <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                    </div>
                     </div>
                     <div class="col-12">
                                     <div class="card Card_Design_None border-dashed">
                                     <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>

                </div>
                     </div>
                </div>

            </div>
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-6">
                <div class="card border-dashed"> 
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
            <div class="col-6">
                <div class="card border-dashed">
                    <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-6">
                <div class="card border-dashed">
                   <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>

            <div class="col-6">
            <div class="card border-dashed">
   <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
             </div>
            </div>

            <div class="col-12">
                <div class="card border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>

            <div class="col-12">
                <div class="card border-dashed">
                  <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
            <div class="col-4">
                <div class="card border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
             <div class="col-4">
                <div class="card border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
             <div class="col-4">
                <div class="card border-dashed">
                   <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>

        </div>
    `
        }
        else if (layoutName == "PB_layout5") {
            appendData = `
    <div class="row pageBuilderSetRowDesign" style="height: calc(100vh - 56px);"  id="pageBuilderInfraId">
            <div class="col-5">
             <div class="card border-dashed">
             <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
             </div>
            </div>
            <div class="col-7">
             <div class="card border-dashed">
             <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
             </div>
              <div class="card border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
              <div class="card border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div>
            <div class="col-12">
                <div class="card border-dashed>
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
        </div>
    `
        }
        else if (layoutName == "PB_layout6") {
            appendData = `
        <div class="row pageBuilderSetRowDesign" style="height: calc(100vh - 56px);"  id="pageBuilderInfraId">
            <div class="col-7 d-grid">
             <div class="card Card_Design_None mb-2 border-dashed">
             <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
            </div>
             </div>
           <div class="card Card_Design_None mb-2">
             <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
            </div>
             </div>
            </div>
            <div class="col-5 d-grid">
             <div class="card Card_Design_None mb-2 border-dashed">
             <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
             </div>
             </div>
              <div class="card Card_Design_None mb-2border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
            </div>
              </div>

            </div>
            <div class="col-xl-6 d-grid">
                <div class="card Card_Design_None mb-2 h-100 border-dashed" id="mssqlserver" style="display: none;">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
               </div>
                </div>
            </div>
        </div>
      
    `
        }
        else if (layoutName == "PB_layout7") {

            appendData = `
    <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-7">
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>

            </div>
            <div class="col-5">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-5">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>

              </div>
            </div><div class="col-7">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div>
        </div>
       
    `

        }
        else if (layoutName == "PB_layout8") {
            appendData = `
                    <div class="row g-2 pageBuilderSetRowDesign">
                        <div class="col-5">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-7">
                            <div class="card Card_Design_None border-dashed min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-8">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0" >
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                                          
                            </div>
                        <div class="col-4">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                                          <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                         <div class="col-4">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                                          <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                         <div class="col-4">
                            <div class="card border-dashed Card_Design_None min-hight-100 mb-0">
                                          <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                            </div>
                        </div>
                    </div>
                    
    `
        }
        else if (layoutName == "PB_layout9") {
            appendData = `
            <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-7">
                <div class="card  border-dashed">
                              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
            <div class="col-5">
              <div class="card  border-dashed">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-5">
              <div class="card  border-dashed">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-7">
              <div class="card  border-dashed">
                            <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div>
        </div>
       
    `
        }
        else if (layoutName == "PB_layout10") {
            appendData = `
    <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-7">
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>

            </div>
            <div class="col-5">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-5">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-7">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div>
        </div>
      
    `
        }
        else if (layoutName == "PB_layout11") {
            appendData = `<div class="row g-3 pageBuilderSetRowDesign" style="height: calc(100vh - 56px);" id="pageBuilderInfraId">
            <div class="col-4" >
                <div class="card  border-dashed">
                  <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
                <div class="card  border-dashed">
                    <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
          <div class="col-4" >
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
            <div class="col-4" >
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
               <div class="row h-100">
              <div class="col-6">

                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div></div>
                <div class="col-6">

                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div></div></div>
            </div>
        </div>
        
            `

        }
        else if (layoutName == "PB_layout12") {
            appendData = `
    <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-7">
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>

            </div>
            <div class="col-5">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-5">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-7">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div>
        </div>
        
    `
        }
        else if (layoutName == "PB_layout13" || layoutName == "PB_layout19" ) {
            appendData = `
    <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-7">
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>

            </div>
            <div class="col-5">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-5">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div><div class="col-7">
              <div class="card  border-dashed">
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
              </div>
            </div>
        </div>
      
    `
        }
        else if (layoutName == "PB_layout14") {
            appendData = `
    <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-3" >
                <div class="card  border-dashed">
                  <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
                <div class="card  border-dashed">
                    <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
          <div class="col-3" >
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
            <div class="col-6" >
                <div class="card  border-dashed pageBuilderSetDesign">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
                <div class="card  border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
        </div>
       
    `
        }
        else if (layoutName == "PB_layout16") {
            appendData = `
    <div class="row g-2 mt-0 mb-2 pageBuilderSetRowDesign">
            <div class="col-6" >
                <div class="card mb-0 border-dashed">
                  <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
               
            </div>
          <div class="col-6" >
                <div class="card mb-0 border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
               
            </div>
          
        </div>
        <div class="row g-2">
          <div class="col-6" >
                <div class="card mb-2 border-dashed">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
                <div class="card mb-0 border-dashed ">
                <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
            </div>
              <div class="col-6" >
              <div class="card mb-0 border-dashed "  >
              <div class="card-body pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
</div>
                </div>
              </div>
        </div>
       
    `
        }
        else if (layoutName == "PB_layout17") {
        }
        $('#PB_pageContent').append(appendData);
    }
}