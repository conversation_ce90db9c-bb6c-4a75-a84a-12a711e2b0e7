﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Queries;

public class GetHacmpClusterNameUniqueQueryHandlerTests : IClassFixture<HacmpClusterFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;
    private Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;
    private readonly GetHacmpClusterNameUniqueQueryHandler _handler;

    public GetHacmpClusterNameUniqueQueryHandlerTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.GetHacmpClusterNameUniqueRepository(_hacmpClusterFixture.HacmpClusters);

        _handler = new GetHacmpClusterNameUniqueQueryHandler(_mockHacmpClusterRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_HacmpClusterName_Exist()
    {
        _hacmpClusterFixture.HacmpClusters[0].Name = "TestCluster";
        _hacmpClusterFixture.HacmpClusters[0].IsActive = true;

        var result = await _handler.Handle(new GetHacmpClusterNameUniqueQuery { Name = _hacmpClusterFixture.HacmpClusters[0].Name, Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_HacmpClusterNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetHacmpClusterNameUniqueQuery { Name = "NonExistentCluster", Id = 1.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_HacmpClusterName_NotMatch()
    {
        var result = await _handler.Handle(new GetHacmpClusterNameUniqueQuery { Name = "AnotherCluster", Id = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsNameExist_OneTime()
    {
        await _handler.Handle(new GetHacmpClusterNameUniqueQuery(), CancellationToken.None);

        _mockHacmpClusterRepository.Verify(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.GetHacmpClusterEmptyRepository();
        var handler = new GetHacmpClusterNameUniqueQueryHandler(_mockHacmpClusterRepository.Object);

        var result = await handler.Handle(new GetHacmpClusterNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_Return_True_When_NameExistsWithSameId()
    {
        _hacmpClusterFixture.HacmpClusters[0].Name = "UniqueCluster";
        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = "test-id-123";

        var result = await _handler.Handle(new GetHacmpClusterNameUniqueQuery { Name = "UniqueCluster", Id = "test-id-123" }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_When_NameExistsWithDifferentId()
    {
        _hacmpClusterFixture.HacmpClusters[0].Name = "ExistingCluster";
        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = "existing-id";

        var result = await _handler.Handle(new GetHacmpClusterNameUniqueQuery { Name = "ExistingCluster", Id = "different-id" }, CancellationToken.None);

        result.ShouldBeFalse();
    }
}