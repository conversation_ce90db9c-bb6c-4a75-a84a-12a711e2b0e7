using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Queries;

public class GetGlobalVariableNameUniqueQueryHandlerTests : IClassFixture<GlobalVariableFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;
    private Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;
    private readonly GetGlobalVariableNameUniqueQueryHandler _handler;

    public GetGlobalVariableNameUniqueQueryHandlerTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.GetNameUniqueRepository(_globalVariableFixture.GlobalVariables);

        _handler = new GetGlobalVariableNameUniqueQueryHandler(_mockGlobalVariableRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_GlobalVariableName_Exist()
    {
        _globalVariableFixture.GlobalVariables[0].VariableName = "TestVariable";
        _globalVariableFixture.GlobalVariables[0].IsActive = true;

        var result = await _handler.Handle(new GetGlobalVariableNameUniqueQuery { Name = _globalVariableFixture.GlobalVariables[0].VariableName, Id = _globalVariableFixture.GlobalVariables[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_GlobalVariableNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetGlobalVariableNameUniqueQuery { Name = "NonExistentVariable", Id = 1.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_GlobalVariableName_NotMatch()
    {
        var result = await _handler.Handle(new GetGlobalVariableNameUniqueQuery { Name = "AnotherNonExistentVariable", Id = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsNameExist_OneTime()
    {
        await _handler.Handle(new GetGlobalVariableNameUniqueQuery(), CancellationToken.None);

        _mockGlobalVariableRepository.Verify(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_When_NoRecords()
    {
        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.GetGlobalVariableEmptyRepository();

        var handler = new GetGlobalVariableNameUniqueQueryHandler(_mockGlobalVariableRepository.Object);
        var result = await handler.Handle(new GetGlobalVariableNameUniqueQuery(), CancellationToken.None);

        result.ShouldBeFalse();
    }
}