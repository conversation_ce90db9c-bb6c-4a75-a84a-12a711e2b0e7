﻿$('#createpageModal, #widgetCreationCard').hide()
$(".contextMenu").css({ display: 'none' });
$('#backPage').hide()
/*$('.columnListGroup').hide()*/
let globalWidgetList = []
let InfraDetailsList;
let globalWidgetId=""
let buttonContent = ['table', 'chart']
let globalInfraId;
let noDataImageData = "<figure class='figure'><img src='../../img/isomatric/no_data_found.svg' class='Card_NoData_Img' style='width: 250px;margin-top:100px'><figcaption class='figure-caption text-center text-danger'>No Data Found</figcaption></figure > "
$(".x-axisgroup").hide()
$(".y-axisgroup").hide()
let hourData = {
    "PR_Active_DG_Enabled": "NA",
    "PR_Protection_mode": "MAXIMUM PERFORMANCE",
    "PR_Archive_Dest_Location": "USE_DB_RECOVERY_FILE_DEST",
    "PR_Transmit_mode": "NA",
    "PR_Recovery_mode": "NA",
    "PR_Affirm": "NA",
    "PR_Archiver": "NA",
    "PR_Archivelog_compression": "NA",
    "PR_Delay_mins": "0",
    "PR_Log_sequence": "84",
    "PR_Dg_broker_status": "DISABLED",
    "PR_Remote_login_passwordfile": "EXCLUSIVE",
    "PR_Standby_file_management": "AUTO",
    "PR_Transport_lag": "NA",
    "PR_Apply_lag": "NA",
    "PR_Apply_finish_time": "NA",
    "PR_Estimated_startup_time": "NA",
    "PR_Standby_redo_logs": "0",
    "PR_Force_logging": "YES",
    "PR_Switchover_status": "NOT ALLOWED",
    "PR_Log_archive_config": "",
    "PR_Fal_server": "TESTDBDR",
    "PR_Fal_client": "",
    "Archieve_Log_Genearion_Hourly": "{\r\n  \"Data\": [\r\n    {\r\n      \"Name\": \"12-APR-24\",\r\n      \"Data\": [\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"1\",\r\n        \"0\",\r\n        \"0\",\r\n        \"1\",\r\n        \"4\",\r\n        \"5\",\r\n        \"0\",\r\n        \"0\"\r\n      ]\r\n    },\r\n    {\r\n      \"Name\": \"13-APR-24\",\r\n      \"Data\": [\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"1\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\"\r\n      ]\r\n    },\r\n    {\r\n      \"Name\": \"14-APR-24\",\r\n      \"Data\": [\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"1\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"1\",\r\n        \"0\"\r\n      ]\r\n    },\r\n    {\r\n      \"Name\": \"15-APR-24\",\r\n      \"Data\": [\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"1\",\r\n        \"0\"\r\n      ]\r\n    },\r\n    {\r\n      \"Name\": \"16-APR-24\",\r\n      \"Data\": [\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"0\",\r\n        \"1\",\r\n        \"0\"\r\n      ]\r\n    }\r\n  ],\r\n  \"Hours\": [\r\n    \"00\",\r\n    \"01\",\r\n    \"02\",\r\n    \"03\",\r\n    \"04\",\r\n    \"05\",\r\n    \"06\",\r\n    \"07\",\r\n    \"08\",\r\n    \"09\",\r\n    \"10\",\r\n    \"11\",\r\n    \"12\",\r\n    \"13\",\r\n    \"14\",\r\n    \"15\",\r\n    \"16\",\r\n    \"17\",\r\n    \"18\",\r\n    \"19\",\r\n    \"20\",\r\n    \"21\",\r\n    \"22\",\r\n    \"23\"\r\n  ]\r\n}",
    "Archieve_Log_Genearion_Day": "{\r\n  \"DayData\": [\r\n    {\r\n      \"Day\": \"2024-04-17\",\r\n      \"Hour\": \"22\",\r\n      \"Size\": \"186.226074\"\r\n    }\r\n  ]\r\n}",
    "Archieve_Log_Genearion_Weekly": "{\r\n  \"WeekData\": [\r\n    {\r\n      \"Days\": \"11/04/2024\",\r\n      \"Size\": \"1.78\\r\"\r\n    },\r\n    {\r\n      \"Days\": \"12/04/2024\",\r\n      \"Size\": \"222.88\\r\"\r\n    },\r\n    {\r\n      \"Days\": \"13/04/2024\",\r\n      \"Size\": \"184.81\\r\"\r\n    },\r\n    {\r\n      \"Days\": \"14/04/2024\",\r\n      \"Size\": \"353.07\\r\"\r\n    },\r\n    {\r\n      \"Days\": \"15/04/2024\",\r\n      \"Size\": \"174.67\\r\"\r\n    },\r\n    {\r\n      \"Days\": \"16/04/2024\",\r\n      \"Size\": \"176.1\\r\"\r\n    },\r\n    {\r\n      \"Days\": \"17/04/2024\",\r\n      \"Size\": \"186.23\"\r\n    }\r\n  ]\r\n}",
    "PR_Archieve_Log_Last_TimeStamp": "2024-04-17 22:05:38",
    "DR_Archieve_Log_Last_TimeStamp": "2024-04-17 22:00:38",
    "PR_Dataguard_status": "NA",
    "PR_Recovery_Status": "NA",
    "PR_Asm_Details": null,
    "DR_Active_DG_Enabled": "NO",
    "DR_Protection_mode": "MAXIMUM PERFORMANCE",
    "DR_Archive_Dest_Location": "/home/<USER>/dbs/arch",
    "DR_Transmit_mode": "Async",
    "DRRecovery_mode": "IDLE",
    "DR_Affirm": "NO",
    "DR_Archiver": "LGWR",
    "DR_Archivelog_compression": "DISABLE",
    "DR_Delay_mins": "0",
    "DR_Log_sequence": "84",
    "DR_Dg_broker_status": "DISABLED",
    "DR_Remote_login_passwordfile": "EXCLUSIVE",
    "DR_Standby_file_management": "MANUAL",
    "DR_Transport_lag": "NA",
    "DR_Apply_lag": "NA",
    "DR_Apply_finish_time": "NA",
    "DR_Estimated_startup_time": "NA",
    "DR_Standby_redo_logs": "NA",
    "DR_Force_logging": "YES",
    "DR_Switchover_status": "TO STANDBY",
    "DR_Log_archive_config": "",
    "DR_Fal_server": "",
    "DR_Fal_client": "",
    "DR_Dataguard_status": "VALID",
    "DR_Recovery_Status": "NA",
    "DR_Asm_Details": null,
    "PR_Database_name": "TESTDB",
    "PR_Instancename": "TESTDB",
    "PR_Instanceid": "1",
    "PR_Instance_startuptime": "12-APR-2024 21:59:05",
    "PR_Database_createdtime": "21-NOV-2022 13:43:37",
    "PR_Database_version": "19.0.0.0.0",
    "PR_Database_role": "PHYSICAL STANDBY",
    "PR_Openmode": "MOUNTED",
    "PR_Database_incarnation": "2",
    "PR_DB_Reset_logschange": "1920977",
    "PR_Reset_logsmode": "REQUIRED",
    "PR_Control_filetype": "STANDBY",
    "PR_Control_filename": "/u01/app/oracle/oradata/TESTDB/control01.ctl\r\n/u01/app/oracle/recovery_area/TESTDB/control02.ctl\r\n\r\n",
    "PR_Currentscn": "5473654",
    "PR_Parameterfile": "/home/<USER>/dbs/spfileTESTDB.ora",
    "PR_Archive_mode": "ARCHIVELOG",
    "PR_Flashback_on": "NO",
    "PR_Platform_name": "Linux x86 64-bit",
    "PR_Dbsize": "2130",
    "PR_Db_create_file_dest": "",
    "PR_Db_file_name_convert": "",
    "PR_Db_create_online_log_dest1": "",
    "PR_Log_file_name_convert": "",
    "PR_Db_recovery_file_dest": "/u01/app/oracle/recovery_area",
    "PR_Db_recovery_file_dest_size": "15360",
    "PR_Db_flashback_retention_target": "1440",
    "PR_Services": "TESTDB",
    "DR_Database_name": "TESTDB",
    "DR_Instancename": "TESTDBDR",
    "DR_Instanceid": "1",
    "DR_Instance_startuptime": "12-APR-2024 19:56:54",
    "DR_Database_createdtime": "21-NOV-2022 13:43:37",
    "DR_Database_version": "19.0.0.0.0",
    "DR_Database_role": "PRIMARY",
    "DR_Openmode": "READ WRITE",
    "DR_Database_incarnation": "2",
    "DR_DB_Reset_logschange": "1920977",
    "DR_Reset_logsmode": "NOT ALLOWED",
    "DR_Control_filetype": "CURRENT",
    "DR_Control_filename": "/home/<USER>/dbs/cntrlTESTDBDR.dbf\r\n\r\n",
    "DR_Currentscn": "5601273",
    "DR_Parameterfile": "/home/<USER>/dbs/spfileTESTDBDR.ora",
    "DR_Archive_mode": "ARCHIVELOG",
    "DR_Flashback_on": "NO",
    "DR_Platform_name": "Linux x86 64-bit",
    "DR_Dbsize": "2160",
    "DR_Db_create_file_dest": "",
    "DR_Db_file_name_convert": "",
    "DR_Db_create_online_log_dest1": "",
    "DR_Log_file_name_convert": "",
    "DR_Db_recovery_file_dest": "",
    "DR_Db_recovery_file_dest_size": "15360",
    "DR_Db_flashback_retention_target": "1440",
    "DR_Services": "TESTDBDR",
    "PR_InstanceName": "TESTDB",
    "PR_InstanceId": "1",
    "PR_InstanceStartUpTime": "12-APR-2024 21:59:05",
    "PR_OpenMode": "MOUNTED",
    "PR_ControlfileName": "/u01/app/oracle/oradata/TESTDB/control01.ctl",
    "PR_ParameterFile": "/home/<USER>/dbs/spfileTESTDB.ora",
    "PR_IsClusterDatabase": "false",
    "DR_InstanceName": "TESTDBDR",
    "DR_InstanceId": "1",
    "DR_InstanceStartUpTime": "12-APR-2024 19:56:54",
    "DR_OpenMode": "READ WRITE",
    "DR_ControlfileName": "/home/<USER>/dbs/cntrlTESTDBDR.dbf",
    "DR_ParameterFile": "/home/<USER>/dbs/spfileTESTDBDR.ora",
    "DR_IsClusterDatabase": "false",
    "PR_TNSServiceName": "TESTDB",
    "DR_TNSServiceName": "TESTDBDR",
    "PR_CDB": "NO",
    "PR_Containers": "TESTDB",
    "PR_Pdbs": "NA",
    "DR_CDB": "NO",
    "DR_Containers": "TESTDB",
    "DR_Pdbs": "NA",
    "PR_LSN_Logs": "84",
    "DR_LSN_Logs": "84",
    "PR_Datalag": "NA",
    "Monitor_Type": "Oracle",
    "PRDatabaseType": "Oracle",
    "DRDatabaseType": "Oracle",
    "PR_Server_IpAddress": "************",
    "PR_Server_HostName": "oracle-pr",
    "DR_Server_IpAddress": "************",
    "DR_Server_HostName": "oracle-dr",
    "PR_Database": "TESTDB",
    "DR_Database": "TESTDBDR",
    "PR_Database_Name": "Oracle_PR_Database",
    "DR_Database_Name": "Oracle_DR_Database",
    "PR_Server_Status": "Up",
    "DR_Server_Status": "Up",
    "PR_Server_Name": "Oracle_PR_Server",
    "DR_Server_Name": "Oracle_DR_Server",
    "PR_Database_Sid": "TESTDB",
    "DR_Database_Sid": "TESTDBDR",
    "PR_Os_Type": "Linux",
    "DR_Os_Type": "Linux",
    "PR_Database_Status": "Up",
    "DR_Database_Status": "Up",
    "PR_Database_Version": "19c",
    "DR_Database_Version": "19c",
    "PR_Unique_Name": "TESTDB",
    "DR_Unique_Name": "TESTDBDR",
    "PR_Replication_Mode": "ASYNC",
    "DR_Replication_Mode": "ASYNC"
}


GetPageWidgetList()
GetMonitorTypeByInfraObject()
GetAllTableAccesses()

let html = `<div class="card-body pt-0 widgetCreationCardChart" id="widgetCreationCardChart">
                    </div>
                `

//let html = `<div class="card Card_Design_None widgetCreationCardChartdesign">
//                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
//                        <span class="text-truncate d-inline-block chartTitle" title="Archive Log Generation Hourly Last 24Hrs(Count)">
//                            Archive Log Generation
//                            Hourly Last 24Hrs(Count)
//                        </span>
//                    </div>
//                    <div class="card-body pt-0 widgetCreationCardChart" id="widgetCreationCardChart">
//                    </div>
//                </div>`

let html1 = `<div class="card Card_Design_None wrapper1ChartDesign">
                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
                        <span class="text-truncate d-inline-block wrapperTitle" title="Archive Log Generation Hourly Last 24Hrs(Count)">
                            Archive Log Generation
                            Hourly Last 24Hrs(Count)
                        </span>
                    </div>
                    <div class="card-body pt-0 wrapper1Chart" id="wrapper1Chart">
                    </div>
                </div>`

$('#backblankpage').on('click', function (e) {
    
    if (e.currentTarget.textContent == 'Create') {
       
        $("#TitleModal").modal("show")
        $(".btn_save").text('Save')
    }
    else {
        $(".hideshowwidget").show()
        $("#WidgetMainTitle").text('Widget')
        $('#backblankpage').text('Create')
        $('#homepageModal').show()
        $('#backPage').hide()
        $("#createpageModal").hide()
    }
    $('#widgetTitle').val("")
    $('#widgetTitleError').removeClass('field-validation-error').text("");
})



$("#search-widget").on("keyup", function () {

    let filter = $(this).val();
    $("#homepageModal .widgetList").each(function () {

        let $i = 0;
        $(this).find(".widgetName").each(function () {

            let splitText = $(this).text()
            if (splitText.search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }
        });
        if ($i > 0) {
            $(this).closest("#homepageModal .widgetList").show();
        } else {
            $(this).closest("#homepageModal .widgetList").hide();
        }

    });

})



document.getElementById("search-widget").addEventListener("search", function (event) {
    $("#homepageModal .widgetList").removeAttr("style")
});

$("#widgetBuilderApply").on('click',async function (e) {
    let name = $("#widgetTitle").val();
    let isName = await validateName(name);
    $('.selectDataset option[value="' + this.getAttribute("datasetName") + '"]').prop('selected', true).trigger('change');
    if (isName) {
        if (!this.getAttribute("titleStatus")) {

            $("#widgetCreationCard").addClass('d-none')
            $("#collapseExample").addClass('d-none')
        }
        $(".templateModal").hide()
        $(".hideshowwidget").hide()

        $('#backblankpage').text('Back')
        $('#homepageModal').hide()
        $('#backPage').show()
        $("#createpageModal").show()
        $("#WidgetMainTitle").text($("#widgetTitle").val())
        $("#TitleModal").modal("hide")
        $("#widgetBuilderApply").removeAttr("widgetId")
        $("#widgetBuilderApply").removeAttr("titleStatus")
    }
})

$("#flexCheckChecked").on("click", function () {
    
    let checkValue = $(this).prop("checked")
    checkValue ? $(".imagepicker").removeClass("Active") : $(".imagepicker:first").addClass("Active")
    checkValue ? $("#widgetImageApply").attr("iconclass","") : $("#widgetImageApply").attr("iconclass", $(".imagepicker:first").attr("icon"))  

})

$(document).on("click", "#columnAdd", function (e) {
    $('.ConditionSettingsModal').attr('data-bs-target', '#ConditionSettingsModal');
    let ColumnrandomNo = Math.floor((Math.random() * 1000000) + 1);
    // Add a new header column
    if (globaldaggData.toLowerCase() == 'customtable') {
        $('#tableCreation thead tr').append('<th ><span class="tablerowcolumn column_' + ColumnrandomNo + ' text-truncate" tableclass="tablerowcolumn" contenteditable="true" onclick="columnadd(this)"  >Column</span></th>');
    }
    else {
        $('#tableCreation thead tr').append('<th ><span class=" text-truncate" tableclass="tablerowcolumn" contenteditable="true" class="tablerowcolumn column_' + ColumnrandomNo + '" >Column</span></th>');
    }
    // Add a new cell to each row in the body
    $('#tableCreation tbody tr').each(function () {
        let rowcolumnrandomNo = Math.floor((Math.random() * 1000000) + 1);
        $(this).append('<td class="text-truncate"><i class="cp-images me-1 " icon="cp-images" color="select" id="cpimages_' + rowcolumnrandomNo + '" name="cpimages_' + rowcolumnrandomNo + '" ></i><span tableclass="tablerowcolumn" onclick="columnadd(this)" class="tablerowcolumn column_' + rowcolumnrandomNo + '" id="column_' + rowcolumnrandomNo + '">Column</span></td >');
    });
    // $(this).before('<th class="ComponentClass"><span contenteditable="true">Column</span></th>')
})


$(document).on("click", "#rowAdd", function (e) {
    $('.ConditionSettingsModal').attr('data-bs-target', '#ConditionSettingsModal');
    // Add a new header column
    // Add a new cell to each row in the body
    if ($('#tableCreation tbody tr').length == 0) {
        return false;
    }
    $('#tableCreation tbody tr').each(function (key, data) {
        if (key == 0) {
            var tableBody = document.getElementById('tableCreation').getElementsByTagName('tbody')[0];
            var newRow = document.createElement('tr');
         
            newRow.classList.add('text-truncate');
            newRow.classList.add('dragtr');
            for (let i = 0; i < data.children.length; i++) {
                let rowcolumnrandomNo = Math.floor((Math.random() * 1000000) + 1);
                var cell = document.createElement('td');

              
                cell.classList.add('text-truncate');
                if (i == 0) {
                    cell.classList.add('fw-semibold');
                    cell.innerHTML = '<i class="cp-images me-1 " icon="cp-images" color="select" id="cpimagess_' + rowcolumnrandomNo + '" onclick="iconBondle(this)" status="label" name="cpimages" ></i><span contenteditable="true" class="tablerowcolumn column_' + rowcolumnrandomNo + ' text-truncate"  tableclass="tablerowcolumn" id="row_' + rowcolumnrandomNo + '" class="tablerowcolumn" >Label</span>';
                }
                else {
                    cell.innerHTML = '<i class="cp-images me-1 " icon="cp-images" color="select" id="cpimages_' + rowcolumnrandomNo + '" name="cpimages_' + rowcolumnrandomNo + '" ></i> <span tableclass="tablerowcolumn" class="tablerowcolumn column_' + rowcolumnrandomNo + ' text-truncate" id="column_' + rowcolumnrandomNo + '" onclick="columnadd(this)" >Column</span>';
                }
                newRow.appendChild(cell);

            }
            tableBody.appendChild(newRow);
        }

    })

})


let widgetCreationCard = $("#widgetCreationCard")
widgetCreationCard.on("sortstart", function (event, ui) {
    let connectId
    if (ui.item[0].children.length === 1) {
        connectId = ui.item[0].children[0].id
    } else if (ui.item[0].children.length > 1) {
        connectId = ui.item[0].children[1].id
    }

    if (!connectId.includes("group")) {
        if ($("#" + connectId).parent().find('.cp-workflow-line').length > 0) {
            $("#" + connectId).parent().find(".cp-workflow-line").remove()
        }
    } else {
        $("#" + connectId).prev().remove()
    }
});


widgetCreationCard.on("sortstop", function (event, ui) {
    let connectId = '';
    if (ui.item[0].children.length === 1) {
        connectId = ui.item[0].children[0].id
    } else if (ui.item[0].children.length > 1) {
        connectId = ui.item[0].children[1].id
    }

    $("#" + connectId).before(connectImage)
    $(".workflowActions").removeClass('selected')
    let connectGroupId;
    if ($("#" + connectId).parents('.parentGroup').length > 0) {
        connectGroupId = $("#" + connectId).parents('.parentGroup')[0].id
        $("#" + connectGroupId + " .accordion-body .ui-sortable-handle").find('.cp-workflow-line').remove()
        $("#" + connectGroupId + " .accordion-body .ui-sortable-handle").prepend(connectImage);
        $("#" + connectGroupId + " .accordion-body .ui-sortable-handle").first().find('.cp-workflow-line').remove();
    }
    $(".checkSaveWorkflow").show()
    checkPositionChanged(connectId)
})


$("#widgetImageApply").attr("iconClass", $(".imagepicker.Active").attr("icon"))
$(".imagepicker").on('click',function (e) {
    
    $(".imagepicker").removeClass("Active")
    e.currentTarget.setAttribute("class", "imagepicker Active")
    let iconClass = $(".imagepicker.Active").attr("icon")
    $("#flexCheckChecked").prop("checked",false)
    $("#widgetImageApply").attr("iconClass", iconClass)
  
})



$("#widgetImageApply").on('click', function (e) {
    $("#iconModal").modal("hide")
    let name = e.currentTarget.getAttribute("iconname")
    let icon = e.currentTarget.getAttribute("iconclass")

    $(".add_modal_table tr").each(function () {
        if ($(this).find("[name='iconadd']").attr("icon_id") == iconid) {
            labelIconid=""
            setTimeout(() => {
                if ($("#iconadd_" + iconaddid.split("_")[1] + "").attr("class") == "cp-web") {
                    $("#iconadd_" + iconaddid.split("_")[1] + "").removeClass("cp-web")
                    $("#iconadd_" + iconaddid.split("_")[1] + "").addClass(icon+" me-1")
                } else {
                    $("#iconadd_" + iconaddid.split("_")[1] + "").removeClass($("#iconadd_" + iconaddid.split("_")[1] + "").attr("class"))
                    $("#iconadd_" + iconaddid.split("_")[1] + "").addClass(icon + " me-1")
                }

            }, 400)
        }
    })
    if (iconaddid == "commonIcon") {
        let color = $("#selectColor option:selected").val()
        $("#commonIcon").removeClass()
        $("#commonIcon").addClass(icon + " me-1 " + color)
    }

    if ($('.ConditionSettingsModal').attr('data-bs-target') == "#ConditionSettingsModal") {
        $("#ConditionSettingsModal").modal("show")
 
    } else {
        $("#ConditionSettingsModal").modal("hide")
      
    }


    $("#widgettable .dragtr").each(function () {
        debugger
        if ($(this).find("[name='cpimages']").attr("id") == labelIconid) {
            iconid=""
            if ($("#cpimagess_" + labelIconid.split("_")[1] + "").attr("class").trim() == "cp-images me-1") {
                $("#cpimagess_" + labelIconid.split("_")[1] + "").removeClass("cp-images")
                $("#cpimagess_" + labelIconid.split("_")[1] + "").addClass(icon+" me-1")
            } else {
                $("#cpimagess_" + labelIconid.split("_")[1] + "").removeClass($("#cpimagess_" + labelIconid.split("_")[1] + "").attr("class"))
                $("#cpimagess_" + labelIconid.split("_")[1] + "").addClass(icon)
            }
        }
    })
  

    let color = $("#selectColor option:selected").val()
    let oldIcon = e.currentTarget.getAttribute("oldicon")
    let oldColor = e.currentTarget.getAttribute("oldColor")
    $("." + name).attr("icon", icon)
    $("." + name).removeClass(oldIcon)
    $("." + name).removeClass(oldColor)
    $("." + name).attr("color", color)
    $("#flexCheckChecked").prop("checked") ?"":$("." + name).addClass(" me-1 " + icon + "")
    $("#flexCheckChecked").prop("checked") ? "" : $("." + name).addClass("" + color + "")
    //$("." + name).removeClass(oldIcon);
})

$('#rowMinus').on('click', function (e) {
    if ($('#tableCreation tbody tr').length != 1) {
        $('#tableCreation tbody tr:last').remove();
    }
})
$('#columnMinus').on('click', function (e) {
    
    if ($('#tableCreation thead tr th').length != 1) {
        $('#tableCreation thead tr').each(function () {
            $(this).find('th:last').remove();
        });
        $('#tableCreation tbody tr').each(function () {
            $(this).find('td:last').remove();
        });
    }
    
})
$("#tableTitle").on('keyup', function (e) {

    $(".widgettitle").text(e.target.value)

})
$("#componentTitle").on('keyup', function (e) {
    $(".ComponentClass").text(e.target.value)
})
let chartType;
$(".chartDetails").on('click', function (e) {


    $("#widgetCreationCard").empty();
    $(".chartDetails").removeClass('active')
    $(this).addClass("active")
    let widgetTitle = $(this).attr('widgetTitle')
    chartType = ""
    chartType = $(".chartDetails.active").attr("name")
    if ($(".selectDataset option:selected").val() != "") {
        InfraDetailsList.forEach((infra) => {
            if (infra.infraObjectId != "") {
                if (infra.infraObjectId == globalInfraId) {

                    let type = $(".chartDetails.active").attr("name")
                    let title = $(".chartDetails.active").attr("widgettitle")
                    chartDetailsFunction(type, title, infra.entityId, infra.monitorType)
                }
            }
        })

        chartDetailsFunction(e.target.attributes.name.value, widgetTitle)
    }
    else {
        chartDetailsFunction(e.target.attributes.name.value, widgetTitle)
    }
})


$('#ColumnName').on('change', function (e) {
    let columnArray = $("#ColumnName").val()
    $(".widgettable").empty()

    columnArray.forEach(function (data) {
        let html = ""
        html += '<tr><td class="text-truncate fw-semibold "><i class="text-secondary cp-ip-address me-1"></i>' + data + '</td><td class="text-truncate"><span id="PR_Server_HostName" title="sol-alwayson-1"><i class="text-success cp-fal-server me-1 fs-6"></i>NA</span></td><td class="text-truncate"><span id="DR_Server_HostName" title="sol-alwayson-2"><i class="text-success cp-fal-server me-1 fs-6"></i>NA</span></td></tr >'
        $(".widgettable").append(html)
    })

   
})

$(document).on('click',function () { //When you left-click
    $("#contextMenu").hide();
});

$(".widgetCreationCardmenu").on("contextmenu", function (event) {
    $("#btnEdit").removeClass('d-none')
    event.preventDefault();
    let splitId = event.target.id.split("_")
    if (splitId[0] == "row" || splitId[0] == "column") {
        $("#contextMenu").css({
            display: 'block',//show the menu
            top: event.pageY,//make the menu be where you click (y)
            left: event.pageX//make the menu be where you click (x)
        });
        $("#btnEdit").attr("rowcolumn", event.target.id)
        if (splitId[0] == "column") {
            $("#btnEdit").addClass('d-none')
            $("#btnDelete").attr("rowcolumn", event.target.id)
        }
       
    }
    else if (splitId[0] == "title"){
        $("#dropdown").modal("show")
    }

});


    

$(".btn_preview").on('click', function (e) {
    
    let customLable = $(".btn_preview").attr("customLable")
    if ($(".chartDetails.active").attr("name") == "pie" || $(".chartDetails.active").attr("name") == "bar" || $(".chartDetails.active").attr("name") == "line" || $(".chartDetails.active").attr("name") == "donut") {

        let xAxisValue = $("#xaxisvalue option:selected").text()
        let yAxisValue = $("#yaxisvalue option:selected").text()
        let x = []
        let y = []
        if (GlobalChartData) {
            GlobalChartData.forEach((data) => {
                x.push(data[xAxisValue])
                y.push(data[yAxisValue])

            })
            let xy = {
                x: x,
                y: y
            }
            ChartFunction($(".chartDetails.active").attr("name"), xy, "#widgetCreationCard", $("#WidgetMainTitle").text())
        }
        else {
            $(".preview-chart").empty()
            $("#previewZoom").modal("show")
            $(".preview-chart").html($("#widgetCreationCard").html())
        }
    }
    else if (customLable == "customTable") {


        let indexNo = 1;
        $("#widgettable").empty();



        GlobalChartData.forEach((data, index) => {

            let Html = ""
            Html += '<tr class="dragtr">'
            for (let tabledata in data) {
                $("#tableCreation thead th").each(function (column) {
                    if (this.textContent == tabledata) {
                        Html += '<td class="text-truncate fw-semibold"><span class="d-flex align-items-center text-truncate"><span contenteditable="true" class="tablerowcolumn text-truncate" tableclass="tablerowcolumn" id="row_1">' + data[tabledata] + '</span></span><div class="collapse mt-2" id="IconsCollapse"><div class="d-flex flex-wrap align-items-center"><div class="border p-2" role="button"><i class="cp-server"></i></div><div class="border p-2" role="button"><i class="cp-database"></i></div><div class="border p-2" role="button"><i class="cp-cloud"></i></div><div class="border p-2" role="button"><i class="cp-server-role"></i></div><div class="border p-2" role="button"><i class="cp-dataguard-status"></i></div><div class="border p-2" role="button"><i class="cp-virtualization"></i></div><div class="border p-2" role="button"><i class="cp-business-function"></i></div><div class="border p-2" role="button"><i class="cp-business-service"></i></div><div class="border p-2" role="button"><i class="cp-database-success"></i></div></div></div></td>'
                        // Html += '<td class="text-truncate fw-semibold " ><i class="cp-images me-1 cpimages_310827" icon="cp-images" color="select" name="cpimages_310827" onclick="iconBondle(this)"></i><span tableclass="tablerowcolumn" id="rowcolum_310827">' + data.BusinessServiceName + '</span></td > <td class="text-truncate fw-semibold "><i class="cp-images me-1 cpimages_407571" icon="cp-images" color="select" name="cpimages_407571" onclick="iconBondle(this)"></i><span tableclass="tablerowcolumn" id="rowcolum_407571">' + data.BusinessFunctionName + '</span></td>'
                        // Html += '<td class="text-truncate fw-semibold " ><i class="cp-images me-1 cpimages_310827" icon="cp-images" color="select" name="cpimages_310827" onclick="iconBondle(this)"></i><span tableclass="tablerowcolumn" id="rowcolum_310827">' + data.Name + '</span></td > <td class="text-truncate fw-semibold "><i class="cp-images me-1 cpimages_407571" icon="cp-images" color="select" name="cpimages_407571" onclick="iconBondle(this)"></i><span tableclass="tablerowcolumn" id="rowcolum_407571">' + data.State + '</span></td>'
                    }
                })
            }
            Html += '</tr>'
            indexNo = indexNo + 1
            $("#widgettable").append(Html)

        })


    }
    else {
        $(".preview-chart").empty()
        $("#previewZoom").modal("show")
        $(".preview-chart").html($("#widgetCreationCard").html())
        let scrolbarText = $("#scrolbarText").val()
        let scrolbarTextVH = $("#scrolbarTextVH").val()
        if (scrolbarText != "" && scrolbarText != undefined) {
            $(".tablePropertiesSet").attr("style", 'height:calc(' + scrolbarTextVH +'vh - ' + scrolbarText + 'px);overflow:auto')
        }

    }

})

$("#btnEdit").on('click',function (e) {
    
    $("#headingModal").modal("show")
    $("#widgetLabelApply").attr("rowcolumn", this.getAttribute("rowcolumn"))
    $(".contextMenu").css({ display: 'none' });
    
})

$("#btnDelete").on('click', function (e) {

    let findId = e.currentTarget.getAttribute("rowcolumn")
    $("#" + findId).parent().children().remove()
    $(".contextMenu").css({ display: 'none' });
})




$("#widgetLabelApply").on('click',function (e) {
    
    $("#" + this.getAttribute("rowcolumn")).text("")
    let labelName = $("#labelTitle").val()
    $("#" + this.getAttribute("rowcolumn")).text(labelName)
    $("#headingModal").modal("hide")
})


$(".btn_cancel").on('click',function (e) {
    $("#widgetCreationCard").empty()
    $("#widgetCreationCard").addClass('d-none')
    $("#collapseExample").addClass('d-none')

})

function iconCollection() {
    $("form.dropdown-menu").removeAttr("style")
    $("form.dropdown-menu").show()
}
$('#widgetTitle').on('keydown keyup', async function () {
    
    const value = $(this).val();
    let sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateName(value);
});


$(".addParameterSave").on("click", function (e) {

    e.preventDefault();
    let parameterValue = e.target.getAttribute("parameterStatus")
    let selectParameterVal = $("#selectParameter option:selected").val()
    let selectParameterText = $("#selectParameter option:selected").attr("id")
    $("." + parameterValue).attr("id", selectParameterVal)
    $("." + parameterValue).text(selectParameterText)
    $("#ParameterSettingsModal").modal("hide");

})


$("#search-inp").on('keyup',function () {

    var filter = $(this).val();
    let categoryFlagStatus = true
    $(".columnList .list-group-item").each(function () {
        
        var $i = 0;

            var splitText = $(this).text().split(" ")
            if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }

        if ($i > 0) {
            $(this).closest(".columnList .list-group-item").show();
            categoryFlagStatus = false
        } else {
            $(this).closest(".columnList .list-group-item").hide();
        }
    });
    //if (categoryFlagStatus) {
    //    $(".columnList").append("No Data Found");
    //}
    //else {
    //    $(".columnList").append("")
    //}
})



$("#search-widget").on('keyup', function () {

    var filter = $(this).val();
    let categoryFlagStatus = true
    $(".columnList .list-group-item").each(function () {

        var $i = 0;

        var splitText = $(this).text().split(" ")
        if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
            $i++;
        }

        if ($i > 0) {
            $(this).closest(".columnList .list-group-item").show();
            categoryFlagStatus = false
        } else {
            $(this).closest(".columnList .list-group-item").hide();
        }
    });
})


$(".selectDataset").on('change',function (e) {
    let tableName = $(".selectDataset option:selected").text()
    let schemeName = $(".selectDataset option:selected").attr("database")
    DatasetDetails(tableName, schemeName, e.target.getAttribute('selectType'))
})
let arr = []
$(".btn_save").on("click", async function (e) {
    arr = []
    let scrolbarText = $("#scrolbarText").val()
    let scrolbarTextVH = $("#scrolbarTextVH").val()
    $("#widgettable .dragtr").each(function () {
        $(this).find("td").each(function (index) {
            let cell = $(this).find(".tablerowcolumn");
            if (cell.length != 0) {
                let cellId = cell.attr("id");
                let list_data = cell.attr("listdata")
                let columId = cell.attr("class").split(" ")[1]
                let columnvalue = cell.attr("datasettext")
                if (cell.text() != "Label") {
                    let i = {
                        Id: cellId,
                        listData: list_data,
                        columnId: columId,
                        columnvalue: columnvalue,
                        columntext: cell.text()
                    }
                    arr.push(i)
                }
            }
        })
    })
    if (scrolbarText != "" && scrolbarText != undefined) {
        $(".tablePropertiesSet").attr("style", 'height:calc(' + scrolbarTextVH +'vh - ' + scrolbarText + 'px);overflow:auto')
    }

    console.log(arr)
    let name = $("#widgetTitle").val();
    let randomNum = Math.floor(Math.random() * 90000) + 10000;
    if (name == "") {
        return false;
    }
    let widgetHtml = $('#widgetCreationCard').children()[0];
    let data = {}
    var href = ''
    if (e.target.getAttribute('type') =="customDiagram")
    {
        href = "../../img/charts_img/pagebuilder-solution.svg"
    }
    else {

        var container = document.getElementById("widgetCreationCard"); /* full page */
        await html2canvas(container, { allowTaint: true }).then(function (canvas) {

            href = canvas.toDataURL();

        });
    }
    let commonId;
    if (e.target.textContent == 'Update') {
        data.id = e.target.getAttribute('widgetid')
        commonId = globalWidgetId
    }
    else {
        commonId = 'widget_' + randomNum
    }
    data.__RequestVerificationToken=gettoken()
    data.Name = name
    data.Properties = JSON.stringify({
        Listdata : JSON.stringify(arr),
        widgetId: commonId,
        type: e.target.getAttribute('type'),
        widgetDetails: widgetHtml.outerHTML,
        datasetId: $(".selectDataset option:selected").attr("id"),
        datasetName: $(".selectDataset option:selected").text(),
        datasetQuery: $(".selectDataset option:selected").attr("database"),
        datasetType: e.target.getAttribute('type'),
        chartType: chartType,
        xView: $("#xaxisvalue option:selected").text(),
        yview: $("#yaxisvalue option:selected").text(),
        title: $("#WidgetMainTitle").text(),
        hrefImage: href,
        InfraId: globalInfraId
        
    })
    $.ajax({
        type: "POST",
        url: RootUrl + 'Admin/ConfigureWidget/CreateOrUpdate',
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            
            if (result.success) {
                $('#homepageModal').show()
                $("#createpageModal").hide()
                $("#backblankpage").text("Create")
                $(".hideshowwidget").show()
                $('#alertClass').removeClass("info-toast")
                $('#alertClass').addClass("success-toast")
                $('#notificationAlertmessage').text(result.message)
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                $(".iconClass").removeClass("cp-exclamation")
                $(".iconClass").addClass("cp-check")
                $("#widgetTitle").val("")
                $("#componentTitle").val("")
                GetPageWidgetList()
            }

        }
    })

})



let labelIconid=""
function iconBondle(data) {
    iconid=""
    labelIconid = data.getAttribute("id")
    $("#iconModal").modal("show")
    $('.ConditionSettingsModal').attr('data-bs-target', '');
    $("#widgetImageApply").attr("iconName", data.getAttribute("name"))
    $("#widgetImageApply").attr("oldIcon", data.getAttribute("icon"))
    $("#widgetImageApply").attr("oldColor", data.getAttribute("color"))
    $("#widgetImageApply").removeAttr("status")
}


function ParameterModal(data) {

    debugger
    let randomNum = Math.floor(Math.random() * 90000) + 10000;
    $("#ParameterSettingsModal").modal("show");
    $("#selectParameter").empty()
    $(data).addClass("parameter" +randomNum)
    if (GlobalChartData) {
        $(".addParameterSave").attr("parameterStatus", "parameter" + randomNum)
        populateDropdowns(GlobalChartData, null, null, null, null)
    }
}


let dragText = '';
function dragg(e) {
    
    $("#widgetCreationCard").removeClass('d-none')
    $("#collapseExample").removeClass('d-none')
    dragText = $(e.srcElement).attr('name')
    e.dataTransfer.setData("text", $(e.srcElement).attr('name'));
    e.dataTransfer.setData("htmlElement", e.target.getAttribute("htmlPropertics"))
    e.dataTransfer.setData("name", e.target.getAttribute("name"))
    e.dataTransfer.setData("value", e.target.getAttribute("value"))
    e.dataTransfer.setData("id", e.target.textContent)
    $(".widget_btnDrag").removeAttr('style')
    $("." + $(e.srcElement).attr('name')).attr('style','background-color:#edf5ff')
    if (e.target.getAttribute("name") =="datasetList") {
        $("#widgetCreationCard").removeAttr("ondrop")
        $("#widgetCreationCard").removeAttr("ondragover")
        $("#tableCreation").attr("ondrop", "drop(event)")
        $("#tableCreation").attr("ondragover", "allowDrop(event)")
    }
    else {
        $("#widgetCreationCard").attr("ondrop", "drop(event)")
        $("#widgetCreationCard").attr("ondragover", "allowDrop(event)")
    }
}

function GetPageWidgetList() {

    $.ajax({
        type: "GET",
        url: RootUrl + 'Admin/ConfigureWidget/GetPageWidgetList',
        dataType: "json",
        traditional: true,
        success: function (result) {
            globalWidgetList=[]
            $("#homepageModal").empty()
            if (result.success) {
                if (result.message.length != 0) {
                    $("#homepageModal").removeClass('text-center')
                    result.message.forEach((data) => {

                        globalWidgetList.push(data)
                        let widgetHtml = JSON.parse(data.properties)
                        let html = "";
                        html += '<div class="col-2 p-1 widgetList">'
                        html += '<div class="card border mb-0 h-100">'
                        html += '<div class="dropdown d-flex justify-content-md-end" >'
                        html += '<i class="cp-horizontal-dots p-1 show" role="button" data-bs-toggle="dropdown" title="More" aria-expanded="true"></i>'
                        html += '<ul class="dropdown-menu" style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(184px, 26px);" data-popper-placement="bottom-start">'
                        html += '<li class="editActionList" id="' + data.id + '" type=' + widgetHtml.type + ' name=' + data.name + '   onclick = "editwidgetView(this,event)" > <a class="dropdown-item" href="#"><i class="cp-edit me-2" title="Edit"></i>Edit</a></li >'
                        html += '<li class="deleteActionList " id="' + data.id + '"   name=' + data.name + ' onclick="deletewidgetListView(this)" data-bs-toggle="modal" data-bs-target="#DeleteModal"><a class="dropdown-item" href="#"><i class="cp-Delete me-2" title="Delete"></i>Delete</a></li>'
                        html += '</ul >'
                        html += '</div >'
                        html += '<div class="card-body py-0">'
                        html += '<img src = "' + widgetHtml.hrefImage + '" class="w-100" style="height:170px; object-fit:scale-down; object-position: top; " />'
                        html += '</div>'
                        html += '<div class="card-footer widgetName list-title text-center" title="' + data.name + '">' + data.name + '</div>'
                        html += '</div>'
                        html += '</div>'
                        $("#homepageModal").append(html)
                    })
                }
               else {
                    $("#homepageModal").append(noDataImageData)
                    $("#homepageModal").addClass('text-center')
                }
            }
           
        }
    })
}

let editlist =[]
function editwidgetView(data) {
    editlist=[]
    GetAllTableAccesses()
    $("#TitleModal").modal("show")
    $(".hideshowwidget").hide()
    $('#backblankpage').text('Back')
    $("#widgetCreationCard").removeClass('d-none')
    $("#collapseExample").removeClass('d-none')
    $('#collapseExample').collapse('show')
    $('#widgetCreationCard').empty()
    $("#widgetBuilderApply").attr("titleStatus", "edit")
    $("#widgetBuilderApply").attr("widgetId", data.id)
    $(".selectDataset").attr("selectType", data.type)
    globaldaggData = data.type
    if (data.type == "customChart") {
        $(".chartListData").show()
        $(".tableDetails").hide()

    }
    else {
        if (data.type == 'customTable') {
            $(".rowGroup").hide()
        }
        else {
            $(".rowGroup").show()
        }
        $(".chartListData").hide()
        if (data.type == 'customTable' || data.type == 'customList') {
            $(".tableDetails").show()
        }
        else {
            $(".tableDetails").hide()
        }

    }
    $(".btn_save").text('Update')
    $(".btn_save").attr('widgetId', data.id)
    globalWidgetId = ""
    globalWidgetList.forEach((widget) => {
        if (data.id == widget.id) {
         
            let jsonpropertices = JSON.parse(widget.properties)
            if (jsonpropertices?.Listdata) {
                editlist = JSON.parse(jsonpropertices?.Listdata)
            }
          

            globalWidgetId = jsonpropertices.widgetId
            $('#widgetTitle').val(widget.name)
            $("#WidgetMainTitle").text(widget.name)
            $('#widgetCreationCard').append(jsonpropertices.widgetDetails)
            $("#widgetBuilderApply").attr("datasetName", jsonpropertices.datasetName)
            let infraId = $(".solutionDiagramData").attr("infraobject")
            let type = $(".solutionDiagramData").attr("type")
            if (infraId) {
                monitoringSolution(infraId, type, "Solution_Diagram")
                monitoringSolution(infraId, type, "wrapper1")
            }
        }
    })
   
}

function deletewidgetListView(data) {
    
    $("#deleteData").text(data.getAttribute("name"));
    var workflowWidgetId = data.id
    $('#textDeleteId').val(workflowWidgetId);
    $('#commonFormDelete').attr('action', '/Admin/ConfigureWidget/Delete')
}
function GetAllTableAccesses() {

    $.ajax({
        type: "GET",
        url: RootUrl + 'Admin/ConfigureWidget/GetAllTableAccesses',
        dataType: "json",
        traditional: true,
        success: function (result) {
            
            $(".selectDataset").empty()
            if (result.success) {
                result.message.forEach((data, key) => {

                    let option = "";
                    if (key == 0) {
                        option = '<option  value=""></option>'
                        $(".selectDataset").append(option)
                    }
                    option = $('<option></option>')
                        .attr('database', data.storedQuery)  // Set the `database` attribute with query string
                        .attr('id', data.id)                 // Set the `id` attribute
                        .attr('value', data.dataSetName)     // Set the `value` attribute
                        .text(data.dataSetName);

                    // Set the visible text

                    // html += '<option database=\"' + data.storedQuery + '\" id="' + data.id + '" value=' + data.dataSetName + '>' + data.dataSetName + '</option>'

                    $(".selectDataset").append(option)

                })

            }

        }
    })
}

let GlobalChartData;
let tablenamedata = ""

  async function  DatasetDetails(tableName,schemeName,widgetStaus) {
      tablenamedata = tableName
    await $.ajax({
        type: "GET",
        url: RootUrl + 'Admin/ConfigureWidget/DatasetDetails',
        dataType: "json",
        data: { data: schemeName },
        success: async  function (result) {
            if (widgetStaus != "customDiagram") {
                debugger
                if (result.success) {
                    let tableData = JSON.parse(result.message.tableValue);

                    if (widgetStaus == "customChart") {
                        globalInfraId = ""
                        if ($(".chartDetails.active").attr("name") == "pie" || $(".chartDetails.active").attr("name") == "bar" || $(".chartDetails.active").attr("name") == "line" || $(".chartDetails.active").attr("name") == "donut") {
                            GlobalChartData = ""
                            GlobalChartData = tableData
                            //ChartFunction(type, tableData, "widgetCreationCardChart")

                            $("#xaxisvalue").empty()
                            $("#yaxisvalue").empty()

                            for (let propertiesData in tableData[0]) {

                                $("#xaxisvalue").append('<option value="' + tableData[0][propertiesData] + '">' + propertiesData + '</option>')
                                $("#yaxisvalue").append('<option value="' + tableData[0][propertiesData] + '">' + propertiesData + '</option>')
                            }

                        }
                        else {

                            if (InfraDetailsList.length != 0) {
                                InfraDetailsList.forEach((infra) => {
                                    if (infra.infraObjectId !="") {
                                        if (tableData[0].InfraObjectId == infra.infraObjectId) {
                                            globalInfraId = tableData[0].InfraObjectId
                                            let type = $(".chartDetails.active").attr("name")
                                            let title = $(".chartDetails.active").attr("widgettitle")
                                            chartDetailsFunction(type, title, infra.entityId, infra.monitorType)
                                        }
                                    }
                                })
                            }
                        }
                    }
                    else {
                        $(".btn_preview").attr("customLable", widgetStaus)
                       /* $('.columnListGroup').show()*/
                        GlobalChartData = tableData 
                       // $(".pageBuilderInfraId").attr("infraObject", tableData[0][data])
                    }
                }
            }
            else {
                $("#selectInfraObject").empty();
                /*$('.columnListGroup').hide()*/
                if (result.success) {

                    let tableData = JSON.parse(result.message.tableValue)
                    tableData.sort(function (a, b) {
                        return b.LastModifiedDate - a.LastModifiedDate;
                    }
                    );

                    if (tableData.length != 0 && tableData) {
                        
                        tableData.forEach((data, index) => {
                            if (index == 0) {

                                $("#selectInfraObject").append('<option value="">Select InfraObject</option>')
                            }

                            $("#selectInfraObject").append('<option value="' + data.InfraObjectId + '">' + data.InfraObjectName +'</option>')
                            


                        })

                        $("#solutionCardModal").modal("show")
                  
                    }
                    

                }
            }
        }
    })
}
let editIndex = null;
let setId = ""
let iconid = ""
let iconaddid = ""
function iconadd(d) {
    iconid = $(d).attr("icon_id")
    iconaddid = $(d).attr("id")
    labelIconid = ""
}
async function columnadd(d) {
    if (GlobalChartData != undefined) {
        $('.ConditionSettingsModal').attr('data-bs-target', '#ConditionSettingsModal');
        let randomicon = Math.floor((Math.random() * 1000000) + 1);
    if (d) {
        $(".add_modal_table").empty();
        let updatelistdata= d.getAttribute("listdata")
        setId = $(d).attr("class");
        $("#selectCommonName").val($(d).attr("id")).change()
        $("#commonIcon").removeClass()
        $("#commonIcon").addClass($(d).prev().attr("class"))
        if (updatelistdata) {

            let jsonData = JSON.parse(updatelistdata)
            if (jsonData && jsonData.length != 0) {
                jsonData.forEach((data, index) => {
                    debugger
                    $("#selectCommonName").empty();
                    let randomNo = Math.floor((Math.random() * 1000000) + 1);
                    let updatehtml = `<tr class="clearcondition Add_delete"> <td></td> <td><label>Dataset :</label> <div class="input-group border border-light-subtle rounded-1" style="width:150px"><select class="form-select addselectDataset addselectDataset${randomNo}" data-placeholder="Select Dataset" aria-label="Default select example"></select> </div> </td> <td><label>Operators :</label> <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_operator condition_operator${randomNo}" aria-label="Default select example"> <option value=">=">>=</option> <option value=">">></option> <option value="<="><=</option> <option value="<"><</option> <option value="==">==</option> <option value="!=">!=</option> </select> </div> </td> <td><label>Status :</label>  <div class="input-group border border-light-subtle rounded-1"> <input type="text" class="form-control condition_time condition_time${randomNo}" placeholder="Enter status" style="width:80px"> </input> </div> </td> <td><label>Style :</label>  <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_style condition_style${randomNo}" aria-label="Default select example">  <option value="text-primary">primary</option><option value="text-secondary">secondary</option><option value="text-success">success</option><option value="text-danger">danger</option><option value="text-warning">warning</option><option value="text-info">info</option> </select> </div> </td> <td><label>View :</label>  <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_view condition_view${randomNo}" aria-label="Default select example"> <option value="Visible">Visible</option><option value="Not Visible">Not Visible</option> </select> </td><td><label>Icon :</label> <span class="input-group-text mt-1 "><i class="cp-web" name="iconadd"  icon_id="icon_${randomNo}" data-bs-toggle="modal" id="iconadd_${randomNo}" onclick="iconadd(this)" data-bs-target="#iconModal" role="button" aria-expanded="false" aria-controls="iconModal"></i></span> </div></td><td class="mt-3" onclick="delete_btn(this)"><i class="cp-Delete "></i></td> </tr>`;
                    $(".add_modal_table").append(updatehtml);

                    populateDropdowns(GlobalChartData, $(d).attr("id"), $(d).prev().attr("class"), "addselectDataset" + randomNo, data.dataSet);

                    $(".addselectDataset" + randomNo + "").val(data.dataSet)
                    $(".condition_operator" + randomNo + "").val(data.Operator)
                    $(".condition_time" + randomNo).val(data.Time)
                    $(".condition_style" + randomNo).val(data.Style)
                    $(".condition_view" + randomNo + "").val(data.View)
                    $("#iconadd_" + randomNo).removeClass()
                    $("#iconadd_" + randomNo).addClass(data.Icon)

                })
            }
            else {
                $("#selectCommonName").empty();
                populateDropdowns(GlobalChartData, $(d).attr("id"), $(d).prev().attr("class"), null, null);
            }
        }
        else {
            $("#selectCommonName").empty();
            populateDropdowns(GlobalChartData, $(d).attr("id"), $(d).prev().attr("class"), null, null)
            $("#selectCommonName").val($(d).attr("id")).change()
            $("#commonIcon").removeClass()
            $("#commonIcon").addClass($(d).prev().attr("class"))
        }
        }

    //let html = `<tr class="clearcondition Add_delete"> <td></td> <td><label>Dataset :</label> <div class="input-group border border-light-subtle rounded-1" style="width:150px"><select class="form-select addselectDataset" data-placeholder="Select Dataset" aria-label="Default select example"></select> </div> </td> <td><label>Operators :</label> <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_operator" aria-label="Default select example"> <option value=">=">>=</option> <option value=">">></option> <option value="<="><=</option> <option value="<"><</option> <option value="==">==</option> <option value="!=">!=</option> </select> </div> </td> <td><label>Status :</label>  <div class="input-group border border-light-subtle rounded-1"> <input type="text" class="form-control condition_time" placeholder="Enter status" style="width:80px"> </input> </div> </td> <td><label>Style :</label>  <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_style" aria-label="Default select example">  <option value="text-primary">primary</option><option value="text-secondary">secondary</option><option value="text-success">success</option><option value="text-danger">danger</option><option value="text-warning">warning</option><option value="text-info">info</option> </select> </div> </td> <td><label>View :</label>  <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_view" aria-label="Default select example"> <option value="Visible">Visible</option><option value="Not Visible">Not Visible</option> </select> </td><td><label>Icon :</label> <span class="input-group-text mt-1 "><i class="cp-web" name="iconadd"  icon_id="icon_${randomicon}" data-bs-toggle="modal" id="iconadd_${randomicon}" onclick="iconadd(this)" data-bs-target="#iconModal" role="button" aria-expanded="false" aria-controls="iconModal"></i></span> </div></td><td class="mt-3" onclick="delete_btn(this)"><i class="cp-Delete "></i></td> </tr>`;

    //$(".add_modal_table").append(html);

        
        $(".addselectDataset option").each(function () { $(this).siblings('[value="' + this.value + '"]').remove(); });

        $("#ConditionSettingsModal").modal("show");
    }
}
$(".add_condition_modal").on("click", function () {
    
    //if ($(".btn_save").text() == "Update") {
        $('.ConditionSettingsModal').attr('data-bs-target', '#ConditionSettingsModal');
        let randomicon = Math.floor((Math.random() * 1000000) + 1);
        let html = `<tr class="clearcondition Add_delete"> <td></td> <td><label>Dataset :</label> <div class="input-group border border-light-subtle rounded-1" style="width:150px"><select class="form-select addselectDataset" data-placeholder="Select Dataset" aria-label="Default select example"></select> </div> </td> <td><label>Operators :</label> <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_operator" aria-label="Default select example"> <option value=">=">>=</option> <option value=">">></option> <option value="<="><=</option> <option value="<"><</option> <option value="==">==</option> <option value="!=">!=</option> </select> </div> </td> <td><label>Status :</label>  <div class="input-group border border-light-subtle rounded-1"> <input type="text" class="form-control condition_time" placeholder="Enter status" style="width:80px"> </input> </div> </td> <td><label>Style :</label>  <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_style" aria-label="Default select example">  <option value="text-primary">primary</option><option value="text-secondary">secondary</option><option value="text-success">success</option><option value="text-danger">danger</option><option value="text-warning">warning</option><option value="text-info">info</option> </select> </div> </td> <td><label>View :</label>  <div class="input-group border border-light-subtle rounded-1"> <select class="form-select condition_view" aria-label="Default select example"> <option value="Visible">Visible</option><option value="Not Visible">Not Visible</option> </select> </td><td><label>Icon :</label> <span class="input-group-text mt-1 "><i class="cp-web"  icon_id="icon_${randomicon}" data-bs-toggle="modal" id="iconadd_${randomicon}" name="iconadd" onclick="iconadd(this)" data-bs-target="#iconModal" role="button" aria-expanded="false" aria-controls="iconModal"></i></span> </div></td><td class="mt-3" onclick="delete_btn(this)"><i class="cp-Delete "></i></td> </tr>`;

        $(".add_modal_table").append(html);

        populateDropdowns(GlobalChartData, null, null, null, null)

        $("#ConditionSettingsModal").modal("show");
   // } else {
    //   $("#selectCommonName").empty()
      //  columnadd()
    //}
})


async function populateDropdowns(GlobalChartData, colId, colicon,conditionDropdown,datasetValue) {
    debugger
    let dataObj = GlobalChartData[0];

    for (let key in dataObj) {
        if (key.toLowerCase() !== "properties") {
            let html = `<option title="${key}" id="${dataObj[key]}" value="${key}">${key}</option>`;
            $("#selectCommonName, .addselectDataset,#selectParameter").append(html);
        } else {
            $(".pageBuilderInfraId").attr("infraObject", dataObj.InfraObjectId);

            let properties;
            if (dataObj?.Properties.includes("{")) {
                properties = JSON.parse(dataObj.Properties);
            } else {
                let decryptedProperties = await DecryptPassword(dataObj.Properties);
                properties = JSON.parse(decryptedProperties);
            }

            recursivePropertyHandler(properties, conditionDropdown);
            if (datasetValue) {
                $("." + conditionDropdown).val(datasetValue)
            }
        }
    }

    let commonItems =[`<option title="--" id="--" value="--">--</option>`]

    commonItems.forEach((data) => {

        $("#selectCommonName").append(data)
    })
   


    if (colId) {
        $("#selectCommonName").val(colId).change()
        $("#commonIcon").removeClass()
        $("#commonIcon").addClass(colicon)
    }
}

let type;
function recursivePropertyHandler(properties, prefix = "") {
    debugger
    
    if (properties?.Type) {
        type = properties?.Type
    }
    for (let key in properties) {
        let value = properties[key];
        let displayName = prefix ? `${key}` : key;

        if (typeof value === "object" && value != null && value != undefined) {
            recursivePropertyHandler(value, displayName);
        } else {
            let html;
            if (type) {
                if (type.toLowerCase() == "pr") {
                    if (value) {
                        html = `<option title="${displayName}" id="${value}" value="${key}">${displayName}</option>`;
                    }
                    else {
                        html = `<option title="${displayName}" id="NA" value="${key}">${displayName}</option>`;
                    }
                }
                else {
                    if (value) {
                        html = `<option title="${displayName}" id="${value}" value="${type}_${key}">${type}_${displayName}</option>`;
                    }
                    else {
                         html = `<option title="${displayName}" id="NA" value="${type}_${key}">${type}_${displayName}</option>`;
                    }
                }
               
            }
            else {
                if (value) {
                    html = `<option title="${displayName}" id="${value}" value="${key}">${displayName}</option>`;
                }
                else {
                    html = `<option title="${displayName}" id="NA" value="${key}">${displayName}</option>`;
                }
            }
            $("#selectCommonName, .addselectDataset,#selectParameter").append(html);
       
          
        }
    }
}




function delete_btn(d) {
    $(d).closest("tr").remove();
}
$(".add_condition_cancel").on("click", function () {
    $(".addselectDataset,.condition_operator,.condition_time,.condition_style,.condition_view ").val("").trigger("change")
})
let arrdata = []
$(".add_condition_save").on("click", function () {
    $("#ConditionSettingsModal").modal("hide");

    let selectCommonName=$("#selectCommonName option:selected").val()
    arrdata=[]
    $(".add_modal_table tr").each(function () {
        let dataset = $(this).find(".addselectDataset option:selected").val();
        let datasetvalue = $(this).find(".addselectDataset option:selected").attr("id")
        let operator = $(this).find(".condition_operator option:selected").val();
        let time = $(this).find(".condition_time").val();
        let style = $(this).find(".condition_style option:selected").val();
        let view = $(this).find(".condition_view option:selected").val();
        let icon = $(this).find("[name='iconadd']").attr('class')

      let i = {
          dataSet: dataset,
          datasetvalue: datasetvalue,
            Operator: operator,
            Time: time,
            Style: style,
            View: view,
          Icon: icon,
          IconId: $(this).find("[name='iconadd']").attr('icon_id')
        };
        arrdata.push(i);
    

    });
    console.log(arrdata, setId)
    let storedata = JSON.stringify(arrdata)

        $("." + setId.split(" ")[1]).attr("listData", storedata)
        $("." + setId.split(" ")[1]).attr("column_id", selectCommonName)
    if (globaldaggData.toLowerCase() == 'customtable') {
        $("." + setId.split(" ")[1]).text($("#selectCommonName option:selected").text())
    }
    else {
        $("." + setId.split(" ")[1]).text($("#selectCommonName option:selected").attr("id"))
    }
       // $("." + setId.split(" ")[1]).attr("id", selectCommonName)
        $("." + setId.split(" ")[1]).attr("datasettext", selectCommonName)
        if ($(".add_condition_save").text() == "Save") {
            $("#cpimages_" + setId.split(" ")[1].split("_")[1]).removeAttr('class')
            $("#cpimages_" + setId.split(" ")[1].split("_")[1]).addClass($("#commonIcon").attr("class") + ' me-1')
        } else {
            $("#cpimages_" + setId.split(" ")[1].split("_")[1]).removeAttr('class');
            $("#cpimages_" + setId.split(" ")[1].split("_")[1]).addClass($("#commonIcon").attr("class") +' me-1');
        }

})
async function validateName(value) {
    
    const errorElement = $('#widgetTitleError');

    if (!value) {
        errorElement.text('Enter widget name')
            .addClass('field-validation-error');
        return false;
    }


    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxCompanylength(value),
        await secondChar(value),
    ];

    return await CommonValidation(errorElement, validationResults);
}


function allowDrop(e) {
    e.preventDefault();
}



function drop(e) {
    $(".btn_save").attr("type", e.dataTransfer.getData("name"))

    globaldaggData = e.dataTransfer.getData("name")
    $(".btn_preview").attr("customLable", globaldaggData)
    $(".selectDataset").attr("selectType", e.dataTransfer.getData("name"))
    if (e.dataTransfer.getData("name") != "") {
        if (e.dataTransfer.getData("name") != 'datasetList') {
            //$("#widgetTitle").val("")
            $("#componentTitle").val("")
            document.getElementById('widgetCreationCard').style.display = 'none';
            $("#widgetCreationCard").empty()
            $(".ChartSettings").hide()
            if (e.dataTransfer.getData("name") == "customTable" || e.dataTransfer.getData("name") == "customDiagram" || e.dataTransfer.getData("name") == "customList") {
                $(".chartListData").hide()
                $(".templateModal").hide()
                $(".divcomponent").show()
                defaultDesign(e.dataTransfer.getData("name"))
                if (e.dataTransfer.getData("name") == "customTable") {
                    $(".tableDetails").show()
                    $(".buttonName").text("Table")
                    $(".rowGroup").hide()
                    $(".monitorTypeListGroup").hide()
                    
                }
                else if (e.dataTransfer.getData("name") == "customList") {
                    $(".tableDetails").show()
                    $(".buttonName").text("List")
                    $(".rowGroup").show()
                    $(".monitorTypeListGroup").hide()
                }
                else if (e.dataTransfer.getData("name") == "customDiagram") {
                    $(".tableDetails").hide()
                    //$("#solutionCardModal").modal("show")
                    $(".monitorTypeListGroup").show()
                    
                }
                else {
                    $(".tableDetails").hide()
                    $(".monitorTypeListGroup").hide()
                }
            }
            else if (e.dataTransfer.getData("name") == "customCard") {
                $(".tableDetails").hide()
                $(".chartListData").hide()
                $("#CardModal").modal("show")
            }
            else if (e.dataTransfer.getData("name") == "customLabel") {
                $(".tableDetails").hide()
                defaultDesign(e.dataTransfer.getData("name"))
            }
            else if (e.dataTransfer.getData("name") == "customText") {
                $(".tableDetails").hide()
                defaultDesign(e.dataTransfer.getData("name"))
            } 
            else if (e.dataTransfer.getData("name") == "customButton") {
                $(".tableDetails").hide()
                defaultDesign(e.dataTransfer.getData("name"))
            } 
            else if (e.dataTransfer.getData("name") == "customDropdown") {
                $(".tableDetails").hide()
                defaultDesign(e.dataTransfer.getData("name"))
            } 
            else if (e.dataTransfer.getData("name") == "preBuildTable" || e.dataTransfer.getData("name") == "preBuildTablePluggable" || e.dataTransfer.getData("name") == "preBuildTableASM" || e.dataTransfer.getData("name") == "preBuildList" || e.dataTransfer.getData("name") == "preBuildDiagram" || e.dataTransfer.getData("name") == "preBuildTabs") {
                $(".chartListData").hide()
                $(".templateModal").hide()
                $(".divcomponent").show()
                $('#widgetCreationCard').append(e.dataTransfer.getData("htmlElement"))
                if (e.dataTransfer.getData("name") == "preBuildTable" || e.dataTransfer.getData("name") == "preBuildList") {
                    $(".tableDetails").show()
                }
                else {
                    $(".tableDetails").hide()
                }
            }
            else if (e.dataTransfer.getData("name") == "customChart") {
                $("#ArchiveLogHour").addClass("active")
                $(".tableDetails").hide()
                $(".chartListData").show()
                $(".templateModal").show()
                $(".divcomponent").hide()
                $(".ChartSettings").show()
                $("#widgetCreationCard").append(html)
                $("#wrapper1").append(html1)
                ChartFunction("ArchiveLogHour", hourData, "widgetCreationCardChart")
                ChartFunction("ArchiveLogHour", hourData, "wrapper1Chart") 
            }
            else {
                $(".chartListData").show()
                $(".templateModal").show()
                $(".divcomponent").hide()
                chartDetailsFunction("ArchiveLogHour", "Archive Log Generation Hourly Last 24Hrs(Count)")
              
            }

            $('#collapseExample').collapse('show')
        }
        else {
            if (e.target.getAttribute("tableclass") == "tablerowcolumn") {
                e.target.setAttribute("class", e.dataTransfer.getData("id") + " fw-semibold text-truncate")
                e.target.setAttribute("id", e.dataTransfer.getData("id"))
                let replaceData = e.dataTransfer.getData("value").replaceAll('\n', ' ')
                let replaceId = e.dataTransfer.getData("id").replaceAll('\n', ' ')

                if (replaceData == "" || replaceData == undefined || replaceData == null) {
                    e.target.innerText = "NA"
                }
                else {
                    if ($(".buttonName").text() != "Table") {
                        e.target.innerText = replaceData.trim()
                    }
                    else {
                        e.target.innerText = replaceId.trim()
                    }
                }
            }
        }
    }
}



$("#widgetCardApply").on("click",function () {
    
    let selectCardType = $("#selectCardType option:selected").val()
    defaultDesign(selectCardType)
    $("#CardModal").modal("hide")
})


$("#solutionCardApply").on("click",function () {

    $("#solutionCardModal").modal("hide")

    let selectsolutionType=$(".selectsolutionType option:selected").val()
    let infraId =$("#selectInfraObject option:selected").val()

    $(".solutionDiagramData").attr("Type", selectsolutionType)

    $(".solutionDiagramData").attr("infraobject", selectInfraObject)

    monitoringSolution(infraId, selectsolutionType, "Solution_Diagram")

    
})



function chartDetailsFunction(name, widgetTitle, monitorId, type) {
    
    if (widgetTitle != undefined) {
        $.ajax({
            type: "GET",
            url: RootUrl + 'Admin/ConfigureWidget/GetMonitorServiceStatusByIdAndType',
            dataType: "json",
            data: {
                monitorId: monitorId,
                type: type
            },
            async: false,
            success: function (result) {
                debugger
                if (result.success) {
                    let jsonString = result.data?.properties;
                    let data = JSON?.parse(jsonString);
                    $(".preview-chart").empty()
                    $("#widgetCreationCard").append(html)
                    $("#wrapper1").append(html1)
                    // oracleSolutionDiagram(data);
                    //Archieve 
                    $(".chartTitle").empty()
                    $(".wrapperTitle").empty()
                    $("#widgetCreationCardChart").empty()
                    $("#wrapper1Chart").empty()
                    $(".chartTitle").text(widgetTitle)
                    $(".wrapperTitle").text(widgetTitle)
                    ChartFunction(name, data, "widgetCreationCardChart")
                    //ChartFunction(name, data, "wrapper1Chart")
                }
            }
        })
    }
    else {
        if (globaldaggData == "customChart") {
            ChartFunction(name, 0, "#widgetCreationCard", $("#WidgetMainTitle").text())
        }
        else {
            ChartFunction(name, 0, "widgetCreationCardChart")
        }
        //ChartFunction(name, 0, "wrapper1Chart")
    }
} 
function GetMonitorTypeByInfraObject() {
    $.ajax({
        type: "GET",
        url: RootUrl + 'Admin/ConfigurePage/GetMonitorTypeByInfraObject',
        dataType: "json",
        traditional: true,
        success: function (result) {
            InfraDetailsList = result.data
        }
    })
}

function widgetLabel() {
    debugger
    let labelHeaderTitle = $("#labelHeaderTitle")
    let labelTL = $("#labelTL").val()
    labelHeaderTitle.text(labelTL)
    $("form.dropdown-menu").attr("style","display: none;")
}


