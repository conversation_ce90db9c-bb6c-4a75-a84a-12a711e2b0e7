using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaTemplate.Validators;

public class CreateFiaTemplateValidatorTests : IClassFixture<FiaTemplateFixture>
{
    private readonly Mock<IFiaTemplateRepository> _mockFiaTemplateRepository;
    private readonly FiaTemplateFixture _fiaTemplateFixture;
    private readonly CreateFiaTemplateCommandValidator _validator;

    public CreateFiaTemplateValidatorTests(FiaTemplateFixture fiaTemplateFixture)
    {
        _fiaTemplateFixture = fiaTemplateFixture;

        var fiaTemplates = new Fixture().Create<List<Domain.Entities.FiaTemplate>>();

        _mockFiaTemplateRepository = FiaTemplateRepositoryMocks.CreateFiaTemplateRepository(fiaTemplates);
        _validator = new CreateFiaTemplateCommandValidator(_mockFiaTemplateRepository.Object);
    }

    //Name

    [Theory]
    [AutoFiaTemplateData]
    public async Task Verify_Create_Name_WithEmpty(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Name = "";

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FiaTemplate.NameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFiaTemplateData]
    public async Task Verify_Create_Name_Isnull(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Name = null;

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FiaTemplate.NameNotEmpty, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoFiaTemplateData]
    public async Task Verify_Create_Name_MiniMumRange_Validator(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Name = "AB"; // Less than 3 characters

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FiaTemplate.NameRange, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFiaTemplateData]
    public async Task Verify_Create_Name_MaximumRange_Validator(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Name = new string('A', 101); // More than 100 characters

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FiaTemplate.NameRange, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFiaTemplateData]
    public async Task Verify_Create_Name_InvalidCharacters_Validator(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Name = "Invalid@Name#"; // Contains invalid characters

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FiaTemplate.NameValid, validateResult.Errors[0].ErrorMessage);
    }

    //Description

    [Theory]
    [AutoFiaTemplateData]
    public async Task Verify_Create_Description_WithEmpty(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Description = "";

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.True(validateResult.IsValid);
    }

    [Theory]
    [AutoFiaTemplateData]
    public async Task Verify_Create_Description_Isnull(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Description = null;

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.True(validateResult.IsValid);
    }

    [Theory]
    [AutoFiaTemplateData]
    public async Task Verify_Create_Description_MiniMumRange_Validator(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Description = "AB"; // Less than 3 characters

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.True(validateResult.IsValid);
    }

    // Note: Description validation is not implemented in the production validator, so no test needed

    // Note: Name uniqueness validation is not tested in Company feature either
    // The MustAsync name uniqueness validation is not covered by tests in the established pattern

    [Theory]
    [AutoFiaTemplateData]
    public async Task Should_Not_Have_Error_When_Valid_Command(CreateFiaTemplateCommand createFiaTemplateCommand)
    {
        createFiaTemplateCommand.Name = "Valid FIA Template Name";
        createFiaTemplateCommand.Description = "Valid description for FIA Template";

        var validateResult = await _validator.ValidateAsync(createFiaTemplateCommand, CancellationToken.None);
        Assert.Empty(validateResult.Errors);
    }
}
