using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Event.PaginatedView;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class GlobalSettingFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<GlobalSetting> GlobalSettings { get; set; }

    public List<GlobalSetting> InvalidGlobalSettings { get; set; }

    public CreateGlobalSettingCommand CreateGlobalSettingCommand { get; set; }

    public UpdateGlobalSettingCommand UpdateGlobalSettingCommand { get; set; }

    public GlobalSettingPaginatedViewEvent GlobalSettingPaginatedViewEvent { get; set; }

    public GlobalSettingFixture()
    {
        GlobalSettings = AddGlobalSettingBusinessLogic(AutoGlobalSettingFixture.Create<List<GlobalSetting>>());

        InvalidGlobalSettings = AddInvalidGlobalSettingBusinessLogic(AutoGlobalSettingFixture.Create<List<GlobalSetting>>());

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<GlobalSettingProfile>();
        });
        Mapper = configurationProvider.CreateMapper();

        CreateGlobalSettingCommand = AutoGlobalSettingFixture.Create<CreateGlobalSettingCommand>();

        UpdateGlobalSettingCommand = AutoGlobalSettingFixture.Create<UpdateGlobalSettingCommand>();

        GlobalSettingPaginatedViewEvent = AutoGlobalSettingFixture.Create<GlobalSettingPaginatedViewEvent>();
    }

    private List<GlobalSetting> AddGlobalSettingBusinessLogic(List<GlobalSetting> globalSettings)
    {
        var globalSettingList = new List<GlobalSetting>();

        foreach (var globalSetting in globalSettings)
        {
            globalSetting.IsActive = true;
            globalSetting.ReferenceId = Guid.NewGuid().ToString();
            globalSettingList.Add(globalSetting);
        }

        return globalSettingList;
    }

    private List<GlobalSetting> AddInvalidGlobalSettingBusinessLogic(List<GlobalSetting> globalSettings)
    {
        var invalidGlobalSettingList = new List<GlobalSetting>();

        foreach (var globalSetting in globalSettings)
        {
            globalSetting.IsActive = false; // Invalid - inactive
            globalSetting.ReferenceId = Guid.NewGuid().ToString();
            invalidGlobalSettingList.Add(globalSetting);
        }

        // Add some specific invalid scenarios
        if (invalidGlobalSettingList.Count > 0)
        {
            invalidGlobalSettingList[0].GlobalSettingKey = string.Empty; // Invalid - empty key
        }

        if (invalidGlobalSettingList.Count > 1)
        {
            invalidGlobalSettingList[1].GlobalSettingValue = string.Empty; // Invalid - empty value
        }

        return invalidGlobalSettingList;
    }

    public Fixture AutoGlobalSettingFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateGlobalSettingCommand>(p => p.GlobalSettingKey, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateGlobalSettingCommand>(p => p.GlobalSettingValue, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateGlobalSettingCommand>(p => p.LoginUserId, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateGlobalSettingCommand>(p => p.GlobalSettingKey, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateGlobalSettingCommand>(p => p.GlobalSettingValue, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateGlobalSettingCommand>(p => p.LoginUserId, 10));

            fixture.Customize<GlobalSetting>(c => c.With(b => b.IsActive, true));
            fixture.Customize<GlobalSetting>(c => c.With(b => b.ReferenceId, () => Guid.NewGuid().ToString()));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
