using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUp.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUp.Events;

public class BackUpCreatedEventTests : IClassFixture<BackUpFixture>
{
    private readonly BackUpFixture _backUpFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<BackUpCreatedEventHandler>> _mockLogger;
    private readonly BackUpCreatedEventHandler _handler;

    public BackUpCreatedEventTests(BackUpFixture backUpFixture)
    {
        _backUpFixture = backUpFixture;
        _mockUserActivityRepository = BackUpRepositoryMocks.CreateUserActivityRepository(_backUpFixture.UserActivities);
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BackUpCreatedEventHandler>>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/backup");
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new BackUpCreatedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_When_BackUpCreatedEventReceived()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_BackUpCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "ProductionDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == _mockLoggedInUserService.Object.UserId &&
            ua.LoginName == _mockLoggedInUserService.Object.LoginName &&
            ua.RequestUrl == _mockLoggedInUserService.Object.RequestedUrl &&
            ua.HostAddress == _mockLoggedInUserService.Object.IpAddress &&
            ua.Action == $"{ActivityType.Create} {Modules.BackUp}" &&
            ua.Entity == Modules.BackUp.ToString() &&
            ua.ActivityType == ActivityType.Create.ToString() &&
            ua.ActivityDetails == "BackUp 'ProductionDatabase' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_FullBackupCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "FullBackupDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUp 'FullBackupDatabase' created successfully." &&
            ua.Action == $"{ActivityType.Create} {Modules.BackUp}" &&
            ua.ActivityType == ActivityType.Create.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserInfo_When_BackUpCreated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var loginName = "BackupUser123";
        var requestUrl = "/api/v6/backup/create";
        var ipAddress = "*************";

        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns(loginName);
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns(ipAddress);

        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_BackUpCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "CustomBackupDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUp 'CustomBackupDatabase' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectEntityAndModule_When_BackUpCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.Entity == Modules.BackUp.ToString() &&
            ua.Action.Contains(Modules.BackUp.ToString()))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityType_When_BackUpCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Create.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCreatedByAndModifiedBy_When_BackUpCreated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);

        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.CreatedBy == userId &&
            ua.LastModifiedBy == userId)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetDefaultGuidWhenUserIdEmpty_When_BackUpCreated()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(string.Empty);

        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        
    }

    [Fact]
    public async Task Handle_SetDefaultGuidWhenUserIdNull_When_BackUpCreated()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.UserId).Returns((string)null);

        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        
    }

    [Fact]
    public async Task Handle_LogInformation_When_BackUpCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_BackUpCreatedWithNullName()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = null };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUp '' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyEventName_When_BackUpCreatedWithEmptyName()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = string.Empty };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUp '' created successfully.")), Times.Once);
    }


    [Fact]
    public async Task Handle_CallRepositoryOnce_When_EventHandled()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        _mockUserActivityRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_CreateEventWithComplexDatabaseName_When_BackUpCreated()
    {
        // Arrange
        var complexDatabaseName = "Complex_Production_Database_With_Special_Characters_123";
        var createdEvent = new BackUpCreatedEvent { Name = complexDatabaseName };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == $"BackUp '{complexDatabaseName}' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_VerifyEventType_When_BackUpCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldBeOfType<BackUpCreatedEvent>();
        createdEvent.ShouldBeAssignableTo<INotification>();
    }

    [Fact]
    public async Task Handle_CreateEventForDifferentialBackup_When_DifferentialBackupCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "DifferentialBackupDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUp 'DifferentialBackupDatabase' created successfully." &&
            ua.ActivityType == ActivityType.Create.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateEventForTransactionLogBackup_When_TransactionLogBackupCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "TransactionLogDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUp 'TransactionLogDatabase' created successfully." &&
            ua.Entity == Modules.BackUp.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateEventForScheduledBackup_When_ScheduledBackupCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "ScheduledBackupDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUp 'ScheduledBackupDatabase' created successfully." &&
            ua.Action == $"{ActivityType.Create} {Modules.BackUp}")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateEventForRemoteBackup_When_RemoteBackupCreated()
    {
        // Arrange
        var createdEvent = new BackUpCreatedEvent { Name = "RemoteBackupDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUp 'RemoteBackupDatabase' created successfully." &&
            ua.ActivityType == ActivityType.Create.ToString() &&
            ua.Entity == Modules.BackUp.ToString())), Times.Once);
    }
}
