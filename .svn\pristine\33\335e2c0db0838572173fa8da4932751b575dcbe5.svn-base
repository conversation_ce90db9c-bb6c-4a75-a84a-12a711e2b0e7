using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoGlobalSettingDataAttribute : AutoDataAttribute
{
    public AutoGlobalSettingDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            // First add the string truncation customizations
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateGlobalSettingCommand>(p => p.GlobalSettingKey, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateGlobalSettingCommand>(p => p.GlobalSettingKey, 10));

            // Then add the GUID customizations - these will override AutoFixture's default string generation
            fixture.Customize<CreateGlobalSettingCommand>(c => c.With(b => b.LoginUserId, () => Guid.NewGuid().ToString()));

            fixture.Customize<UpdateGlobalSettingCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.LoginUserId, () => Guid.NewGuid().ToString()));

            return fixture;
        })
    {

    }
}
