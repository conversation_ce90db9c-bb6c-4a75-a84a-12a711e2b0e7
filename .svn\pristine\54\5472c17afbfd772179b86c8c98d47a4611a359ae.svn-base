using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.FiaInterval.Commands.Create;
using ContinuityPatrol.Application.Features.FiaInterval.Commands.Delete;
using ContinuityPatrol.Application.Features.FiaInterval.Commands.Update;
using ContinuityPatrol.Application.Features.FiaInterval.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaInterval.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.FiaIntervalModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class FiaIntervalControllerTests
{
    private readonly FiaIntervalsController _controller;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly FiaIntervalFixture _fiaIntervalFixture;

    public FiaIntervalControllerTests()
    {
        _fiaIntervalFixture = new FiaIntervalFixture();
        var testBuilder = new ControllerTestBuilder<FiaIntervalsController>();
        _controller = testBuilder.CreateController(
            _ => new FiaIntervalsController(),
            out _mediatorMock);
    }

    #region GetFiaIntervals Tests

    [Fact]
    public async Task GetFiaIntervals_ReturnsListOfFiaIntervals()
    {
        // Arrange
        var expectedIntervals = _fiaIntervalFixture.FiaIntervalListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaIntervalListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedIntervals);

        // Act
        var result = await _controller.GetFiaIntervals();

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<FiaIntervalListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedIntervals = Assert.IsType<List<FiaIntervalListVm>>(okResult.Value);
        Assert.Equal(5, returnedIntervals.Count);
        Assert.All(returnedIntervals, interval => Assert.True(interval.MinTime > 0));
    }

    #endregion

    #region GetFiaIntervalById Tests

    [Fact]
    public async Task GetFiaIntervalById_WithValidId_ReturnsFiaIntervalDetail()
    {
        // Arrange
        var intervalId = Guid.NewGuid().ToString();
        var expectedInterval = _fiaIntervalFixture.FiaIntervalDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaIntervalDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedInterval);

        // Act
        var result = await _controller.GetFiaIntervalById(intervalId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<FiaIntervalDetailVm>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedInterval = Assert.IsType<FiaIntervalDetailVm>(okResult.Value);
        Assert.Equal(expectedInterval.MinTime, returnedInterval.MinTime);
        Assert.Equal(expectedInterval.MaxTime, returnedInterval.MaxTime);
    }

    #endregion

    #region GetPaginatedFiaIntervals Tests

    [Fact]
    public async Task GetPaginatedFiaIntervals_ReturnsPagedResults()
    {
        // Arrange
        var query = _fiaIntervalFixture.GetFiaIntervalPaginatedListQuery;
        var expectedResult = PaginatedResult<FiaIntervalListVm>.Success(
            _fiaIntervalFixture.FiaIntervalListVm, 
            _fiaIntervalFixture.FiaIntervalListVm.Count, 
            1, 
            10);

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedFiaIntervals(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<FiaIntervalListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResult = Assert.IsType<PaginatedResult<FiaIntervalListVm>>(okResult.Value);
        Assert.Equal(5, returnedResult.Data.Count);
        Assert.All(returnedResult.Data, interval => Assert.True(interval.MinTime > 0));
    }

    #endregion

    #region CreateFiaInterval Tests

    [Fact]
    public async Task CreateFiaInterval_WithValidCommand_ReturnsCreatedResponse()
    {
        // Arrange
        var command = _fiaIntervalFixture.CreateFiaIntervalCommand;
        var expectedResponse = _fiaIntervalFixture.CreateFiaIntervalResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateFiaInterval(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<CreateFiaIntervalResponse>>(result);
        var createdResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<CreateFiaIntervalResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.Equal(expectedResponse.Id, returnedResponse.Id);
    }

    #endregion

    #region UpdateFiaInterval Tests

    [Fact]
    public async Task UpdateFiaInterval_WithValidCommand_ReturnsUpdatedResponse()
    {
        // Arrange
        var command = _fiaIntervalFixture.UpdateFiaIntervalCommand;
        var expectedResponse = _fiaIntervalFixture.UpdateFiaIntervalResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateFiaInterval(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<UpdateFiaIntervalResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<UpdateFiaIntervalResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.Equal(expectedResponse.Id, returnedResponse.Id);
    }

    #endregion

    #region DeleteFiaInterval Tests

    [Fact]
    public async Task DeleteFiaInterval_WithValidId_ReturnsDeletedResponse()
    {
        // Arrange
        var intervalId = Guid.NewGuid().ToString();
        var expectedResponse = _fiaIntervalFixture.DeleteFiaIntervalResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteFiaIntervalCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteFiaInterval(intervalId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<DeleteFiaIntervalResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<DeleteFiaIntervalResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_CallsBaseMethod()
    {
        // Act & Assert - This method is inherited from base controller
        // We can't directly test the cache clearing, but we can ensure the method exists
        Assert.True(typeof(FiaIntervalsController).GetMethod("ClearDataCache") != null);
    }

    #endregion

    #region Additional Test Cases

    [Fact]
    public async Task GetFiaIntervals_WithNoData_ReturnsEmptyList()
    {
        // Arrange
        var emptyList = new List<FiaIntervalListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaIntervalListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetFiaIntervals();

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<FiaIntervalListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedIntervals = Assert.IsType<List<FiaIntervalListVm>>(okResult.Value);
        Assert.Empty(returnedIntervals);
    }

    [Fact]
    public async Task GetPaginatedFiaIntervals_WithSearchFilter_ReturnsFilteredResults()
    {
        // Arrange
        var query = _fiaIntervalFixture.GetFiaIntervalPaginatedListQuery;
        query.SearchString = "Hours";
        var filteredData = _fiaIntervalFixture.FiaIntervalListVm
            .Where(x => x.MinTimeUnit == 1).ToList(); // Hours = 1
        var filteredResult = PaginatedResult<FiaIntervalListVm>.Success(
            filteredData,
            filteredData.Count,
            1,
            10);

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedFiaIntervals(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<FiaIntervalListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResult = Assert.IsType<PaginatedResult<FiaIntervalListVm>>(okResult.Value);
        Assert.All(returnedResult.Data, interval => Assert.Equal(1, interval.MinTimeUnit));
    }

    [Fact]
    public async Task CreateFiaInterval_WithInvalidData_ThrowsException()
    {
        // Arrange
        var invalidCommand = new CreateFiaIntervalCommand
        {
            MinTime = -1, // Invalid negative value
            MaxTime = 0,  // Invalid zero value
            MinTimeUnit = 0,
            MaxTimeUnit = 0
        };

        _mediatorMock
            .Setup(m => m.Send(invalidCommand, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentException("Invalid time values"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateFiaInterval(invalidCommand));
    }

    [Fact]
    public async Task UpdateFiaInterval_WithNonExistentId_ThrowsException()
    {
        // Arrange
        var command = _fiaIntervalFixture.UpdateFiaIntervalCommand;
        command.Id = Guid.NewGuid().ToString(); // Non-existent ID

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new KeyNotFoundException("FiaInterval not found"));

        // Act & Assert
        await Assert.ThrowsAsync<KeyNotFoundException>(() => _controller.UpdateFiaInterval(command));
    }

    [Fact]
    public async Task DeleteFiaInterval_WithNonExistentId_ThrowsException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteFiaIntervalCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new KeyNotFoundException("FiaInterval not found"));

        // Act & Assert
        await Assert.ThrowsAsync<KeyNotFoundException>(() => _controller.DeleteFiaInterval(nonExistentId));
    }

    #endregion

    #region Time Unit and Validation Tests

    [Fact]
    public async Task CreateFiaInterval_WithHourlyInterval_ReturnsSuccess()
    {
        // Arrange
        var command = _fiaIntervalFixture.CreateFiaIntervalCommand;
        command.MinTime = 1;
        command.MaxTime = 8;
        command.MinTimeUnit = 1; // Hours
        command.MaxTimeUnit = 1; // Hours

        var expectedResponse = _fiaIntervalFixture.CreateFiaIntervalResponse;
        expectedResponse.Message = "Time interval for 1-8 hours created successfully";

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateFiaInterval(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<CreateFiaIntervalResponse>>(result);
        var createdResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<CreateFiaIntervalResponse>(createdResult.Value);
        Assert.Contains("hours", returnedResponse.Message.ToLower());
    }

    [Fact]
    public async Task GetFiaIntervals_WithDailyIntervals_ReturnsDailyIntervals()
    {
        // Arrange
        var dailyIntervals = _fiaIntervalFixture.FiaIntervalListVm
            .Where(x => x.MinTimeUnit == 2 && x.MaxTimeUnit == 2).ToList(); // Days = 2

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaIntervalListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(dailyIntervals);

        // Act
        var result = await _controller.GetFiaIntervals();

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<FiaIntervalListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedIntervals = Assert.IsType<List<FiaIntervalListVm>>(okResult.Value);
        Assert.All(returnedIntervals, interval =>
        {
            Assert.Equal(2, interval.MinTimeUnit);
            Assert.Equal(2, interval.MaxTimeUnit);
        });
    }

    [Fact]
    public async Task UpdateFiaInterval_WithTimeUnitChange_UpdatesSuccessfully()
    {
        // Arrange
        var command = _fiaIntervalFixture.UpdateFiaIntervalCommand;
        command.MinTime = 1;
        command.MaxTime = 7;
        command.MinTimeUnit = 2; // Days
        command.MaxTimeUnit = 2; // Days

        var expectedResponse = _fiaIntervalFixture.UpdateFiaIntervalResponse;
        expectedResponse.Message = "Time interval updated from hours to days successfully";

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateFiaInterval(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<UpdateFiaIntervalResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResponse = Assert.IsType<UpdateFiaIntervalResponse>(okResult.Value);
        Assert.Contains("days", returnedResponse.Message.ToLower());
    }

    [Fact]
    public async Task GetPaginatedFiaIntervals_WithTimeRangeFilter_ReturnsMatchingIntervals()
    {
        // Arrange
        var query = _fiaIntervalFixture.GetFiaIntervalPaginatedListQuery;
        query.SearchString = "24";
        var filteredData = _fiaIntervalFixture.FiaIntervalListVm
            .Where(x => x.MinTime <= 24 && x.MaxTime >= 24).ToList();
        var filteredResult = PaginatedResult<FiaIntervalListVm>.Success(
            filteredData,
            filteredData.Count,
            1,
            10);

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedFiaIntervals(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<FiaIntervalListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedResult = Assert.IsType<PaginatedResult<FiaIntervalListVm>>(okResult.Value);
        Assert.All(returnedResult.Data, interval =>
            Assert.True(interval.MinTime <= 24 && interval.MaxTime >= 24));
    }

    [Fact]
    public async Task CreateFiaInterval_WithInvalidTimeRange_ThrowsException()
    {
        // Arrange
        var command = _fiaIntervalFixture.CreateFiaIntervalCommand;
        command.MinTime = 10;
        command.MaxTime = 5; // Max less than Min - invalid

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentException("Maximum time must be greater than minimum time"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateFiaInterval(command));
    }

    [Fact]
    public async Task DeleteFiaInterval_WithActiveTemplateUsage_ThrowsException()
    {
        // Arrange
        var intervalId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteFiaIntervalCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Cannot delete time interval that is currently used in active FIA templates"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.DeleteFiaInterval(intervalId));
    }

    [Fact]
    public async Task GetFiaIntervalById_WithComplexTimeConfiguration_ReturnsDetailedInterval()
    {
        // Arrange
        var intervalId = Guid.NewGuid().ToString();
        var detailedInterval = _fiaIntervalFixture.FiaIntervalDetailVm;
        detailedInterval.MinTime = 4;
        detailedInterval.MaxTime = 72;
        detailedInterval.MinTimeUnit = 1; // Hours
        detailedInterval.MaxTimeUnit = 1; // Hours

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetFiaIntervalDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(detailedInterval);

        // Act
        var result = await _controller.GetFiaIntervalById(intervalId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<FiaIntervalDetailVm>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var returnedInterval = Assert.IsType<FiaIntervalDetailVm>(okResult.Value);
        Assert.Equal(4, returnedInterval.MinTime);
        Assert.Equal(72, returnedInterval.MaxTime);
        Assert.Equal(1, returnedInterval.MinTimeUnit);
        Assert.Equal(1, returnedInterval.MaxTimeUnit);
    }

    #endregion
}
