﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Events.UpdatePassword;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Events;

public class UpdatePasswordEventHandlerTests : IClassFixture<WorkflowProfileFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly UpdatePasswordEventHandler _handler;

    public UpdatePasswordEventHandlerTests(WorkflowProfileFixture workflowProfileFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowProfileEventLogger = new Mock<ILogger<UpdatePasswordEventHandler>>();

        _mockUserActivityRepository = WorkflowProfileRepositoryMocks.CreateWorkflowProfileEventRepository(_workflowProfileFixture.UserActivities);

        _handler = new UpdatePasswordEventHandler(mockLoggedInUserService.Object, mockWorkflowProfileEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateWorkflowProfileEventUpdated()
    {
        _workflowProfileFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowProfileFixture.UpdatePasswordEvent, CancellationToken.None);

        result.Equals(_workflowProfileFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowProfileFixture.UpdatePasswordEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdateWorkflowProfileEventUpdated()
    {
        _workflowProfileFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowProfileFixture.UpdatePasswordEvent, CancellationToken.None);

        result.Equals(_workflowProfileFixture.UserActivities[0].Id);

        result.Equals(_workflowProfileFixture.WorkflowProfileUpdatedEvent.ProfileName);

        await Task.CompletedTask;
    }

}