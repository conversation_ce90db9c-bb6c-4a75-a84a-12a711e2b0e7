﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Queries
{
   public class GetWorkflowDrCalenderDetailQueryHandlerTests : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly WorkflowDrCalenderFixture _workflowDrcalenderFixture;

        private readonly Mock<IWorkflowDrCalenderRepository> _mockWorkflowDrCalenderRepository;

        private readonly GetWorkflowDrCalenderDetailsQueryHandler _handler;

        public GetWorkflowDrCalenderDetailQueryHandlerTests(WorkflowDrCalenderFixture workflowDrCalenderFixture)
        {
            _workflowDrcalenderFixture = workflowDrCalenderFixture;

            _mockWorkflowDrCalenderRepository = WorkflowDrCalenderRepositoryMocks.GetworkflowDrcalenderRepository(_workflowDrcalenderFixture.WorkflowDrCalenderInfos);

            _handler = new GetWorkflowDrCalenderDetailsQueryHandler(_workflowDrcalenderFixture.Mapper, _mockWorkflowDrCalenderRepository.Object);
        }
        

        [Fact]
        public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowDrcalenderId()
        {
            var handler = new GetWorkflowDrCalenderDetailsQueryHandler(_workflowDrcalenderFixture.Mapper, _mockWorkflowDrCalenderRepository.Object);

            await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetWorkflowDrCalenderDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
        }

    }
}
