﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowProfileRepositoryMocks
{
    public static Mock<IWorkflowProfileRepository> CreateWorkflowProfileRepository(List<WorkflowProfile> workflowProfiles)
    {
        var workflowProfileRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfiles);

        workflowProfileRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowProfile>())).ReturnsAsync(
            (WorkflowProfile workflowProfile) =>
            {
                workflowProfile.Id = new Fixture().Create<int>();

                workflowProfile.ReferenceId = new Fixture().Create<Guid>().ToString();

                workflowProfiles.Add(workflowProfile);

                return workflowProfile;
            });

        return workflowProfileRepository;
    }

    public static Mock<IWorkflowProfileRepository> UpdateWorkflowProfileRepository(List<WorkflowProfile> workflowProfiles)
    {
        var workflowProfileRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfiles);

        workflowProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfiles.SingleOrDefault(x => x.ReferenceId == i));

        workflowProfileRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowProfile>())).ReturnsAsync((WorkflowProfile workflowProfile) =>
        {
            var index = workflowProfiles.FindIndex(item => item.Id == workflowProfile.Id);

            workflowProfiles[index] = workflowProfile;

            return workflowProfile;
        });

        return workflowProfileRepository;
    }

    public static Mock<IWorkflowProfileRepository> DeleteWorkflowProfileRepository(List<WorkflowProfile> workflowProfiles)
    {
        var workflowProfileRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfiles);

        workflowProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfiles.SingleOrDefault(x => x.ReferenceId == i));

        workflowProfileRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowProfile>())).ReturnsAsync((WorkflowProfile workflowProfile) =>
        {
            var index = workflowProfiles.FindIndex(item => item.Id == workflowProfile.Id);

            workflowProfile.IsActive = false;

            workflowProfiles[index] = workflowProfile;

            return workflowProfile;
        });

        return workflowProfileRepository;
    }
   
    public static Mock<IWorkflowProfileRepository> GetWorkflowProfileRepository(List<WorkflowProfile> workflowProfiles)
    {
        var workflowProfileRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfiles);

        workflowProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfiles.SingleOrDefault(x => x.ReferenceId == i));

        return workflowProfileRepository;
    }

    public static Mock<IWorkflowProfileRepository> GetByProfileIdAsync(List<WorkflowProfile> workflowProfiles)
    {
        var workflowProfileRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileRepository
            .Setup(repo => repo.GetByProfileIdAsync(It.IsAny<List<string>>()))
            .ReturnsAsync(workflowProfiles);

        return workflowProfileRepository;
    }


    public static Mock<IWorkflowProfileRepository> GetWorkflowProfileNamesRepository(List<WorkflowProfile> workflowProfiles)
    {
        var workflowProfileNamesRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileNamesRepository.Setup(repo => repo.GetWorkflowProfileNames()).ReturnsAsync(workflowProfiles);

        return workflowProfileNamesRepository;
    }

    public static Mock<IWorkflowProfileRepository> GetWorkflowProfileNameUniqueRepository(List<WorkflowProfile> workflowProfiles)
    {
        var workflowProfileNameUniqueRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileNameUniqueRepository.Setup(repo => repo.IsWorkflowProfileNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => workflowProfiles.Exists(x => x.Name == i && x.ReferenceId == j));

        return workflowProfileNameUniqueRepository;
    }

    public static Mock<IWorkflowProfileRepository> GetWorkflowProfileNamesEmptyRepository()
    {
        var workflowProfileEmptyRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileEmptyRepository.Setup(repo => repo.GetWorkflowProfileNames()).ReturnsAsync(new List<WorkflowProfile>());

        return workflowProfileEmptyRepository;
    }

    public static Mock<IWorkflowProfileRepository> GetWorkflowProfileEmptyRepository()
    {
        var workflowProfileEmptyRepository = new Mock<IWorkflowProfileRepository>();

        workflowProfileEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowProfile>());

        workflowProfileEmptyRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((WorkflowProfile)null!);

        return workflowProfileEmptyRepository;
    }



    public static Mock<IWorkflowProfileRepository> GetPaginatedWorkflowProfileRepository(List<WorkflowProfile> workflowProfile)
    {
        var workflowViewRepository = new Mock<IWorkflowProfileRepository>();

        workflowViewRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<WorkflowProfile>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<WorkflowProfile> spec, string sortColumn, string sortOrder) =>
            {
                var sortedCompanies = workflowProfile.AsQueryable();

                if (spec.Criteria != null)
                {
                    sortedCompanies = sortedCompanies.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    // Assuming Company has a Name property; replace logic as needed
                    sortedCompanies = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedCompanies.OrderByDescending(c => c.Name)
                        : sortedCompanies.OrderBy(c => c.Name);
                }

                var totalCount = sortedCompanies.Count();
                var paginated = sortedCompanies
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PaginatedResult<WorkflowProfile>.Success(paginated, totalCount, pageNumber, pageSize);
            });

        return workflowViewRepository;
    }



    //public static Mock<IWorkflowProfileRepository> GetPaginatedWorkflowProfileRepository(List<WorkflowProfile> workflowProfiles)
    //{
    //    var workflowProfileRepository = new Mock<IWorkflowProfileRepository>();

    //    var queryableWorkflowProfile = workflowProfiles.BuildMock();

    //    workflowProfileRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableWorkflowProfile);

    //    return workflowProfileRepository;
    //}

    //Events
    public static Mock<IUserActivityRepository> CreateWorkflowProfileEventRepository(List<UserActivity> userActivities)
    {
        var workflowProfileEventRepository = new Mock<IUserActivityRepository>();
       
        workflowProfileEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowProfileEventRepository;
    }
}
