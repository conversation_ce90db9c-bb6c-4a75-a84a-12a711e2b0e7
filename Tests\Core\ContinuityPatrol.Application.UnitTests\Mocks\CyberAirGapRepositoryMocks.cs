using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

/// <summary>
/// Repository mocks for CyberAirGap module testing
/// Provides comprehensive mock implementations for all repository operations
/// </summary>
public static class CyberAirGapRepositoryMocks
{
    /// <summary>
    /// Creates a mock ICyberAirGapRepository with comprehensive CRUD operations
    /// Includes proper filtering, validation, and error handling
    /// </summary>
    /// <param name="cyberAirGaps">List of CyberAirGap entities for testing</param>
    /// <returns>Configured mock repository</returns>
    public static Mock<ICyberAirGapRepository> CreateCyberAirGapRepository(List<CyberAirGap> cyberAirGaps)
    {
        var mockCyberAirGapRepository = new Mock<ICyberAirGapRepository>();

        // ListAllAsync - Returns only active entities
        mockCyberAirGapRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberAirGaps.Where(x => x.IsActive).ToList());

        // GetByReferenceIdAsync - Returns active entity by reference ID
        mockCyberAirGapRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberAirGaps.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        // IsNameExist - Checks for duplicate names excluding current entity
        mockCyberAirGapRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) => 
                cyberAirGaps.Any(x => x.Name == name && x.ReferenceId != id && x.IsActive));

        // GetAirGapBySiteId - Returns air gaps filtered by site ID
        mockCyberAirGapRepository.Setup(repo => repo.GetAirGapBySiteId(It.IsAny<string>()))
            .ReturnsAsync((string siteId) => 
                cyberAirGaps.Where(x => (x.SourceSiteId == siteId || x.TargetSiteId == siteId) && x.IsActive).ToList());

        // GetAirGapByServerId - Returns air gaps filtered by server ID (from JSON)
        mockCyberAirGapRepository.Setup(repo => repo.GetAirGapByServerId(It.IsAny<string>()))
            .ReturnsAsync((string serverId) => 
                cyberAirGaps.Where(x => (x.Source.Contains(serverId) || x.Target.Contains(serverId)) && x.IsActive).ToList());

        // GetAirGapByComponentId - Returns air gaps filtered by component ID
        mockCyberAirGapRepository.Setup(repo => repo.GetAirGapByComponentId(It.IsAny<string>()))
            .ReturnsAsync((string componentId) => 
                cyberAirGaps.Where(x => (x.SourceComponentId == componentId || x.TargetComponentId == componentId) && x.IsActive).ToList());

        // AddAsync - Adds new entity with generated ID and reference ID
        mockCyberAirGapRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAirGap>()))
            .ReturnsAsync((CyberAirGap cyberAirGap) =>
            {
                cyberAirGap.ReferenceId = Guid.NewGuid().ToString();
                cyberAirGap.Id = cyberAirGaps.Count + 1;
                cyberAirGap.IsActive = true;
                cyberAirGaps.Add(cyberAirGap);
                return cyberAirGap;
            });

        // UpdateAsync - Updates existing entity properties
        mockCyberAirGapRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAirGap>()))
            .Returns<CyberAirGap>(async (CyberAirGap cyberAirGap) =>
            {
                var existingCyberAirGap = cyberAirGaps.FirstOrDefault(x => x.ReferenceId == cyberAirGap.ReferenceId);
                if (existingCyberAirGap != null)
                {
                    existingCyberAirGap.Name = cyberAirGap.Name;
                    existingCyberAirGap.Description = cyberAirGap.Description;
                    existingCyberAirGap.SourceSiteId = cyberAirGap.SourceSiteId;
                    existingCyberAirGap.SourceSiteName = cyberAirGap.SourceSiteName;
                    existingCyberAirGap.TargetSiteId = cyberAirGap.TargetSiteId;
                    existingCyberAirGap.TargetSiteName = cyberAirGap.TargetSiteName;
                    existingCyberAirGap.Port = cyberAirGap.Port;
                    existingCyberAirGap.Source = cyberAirGap.Source;
                    existingCyberAirGap.Target = cyberAirGap.Target;
                    existingCyberAirGap.SourceComponentId = cyberAirGap.SourceComponentId;
                    existingCyberAirGap.SourceComponentName = cyberAirGap.SourceComponentName;
                    existingCyberAirGap.TargetComponentId = cyberAirGap.TargetComponentId;
                    existingCyberAirGap.TargetComponentName = cyberAirGap.TargetComponentName;
                    existingCyberAirGap.WorkflowStatus = cyberAirGap.WorkflowStatus;
                    existingCyberAirGap.EnableWorkflowId = cyberAirGap.EnableWorkflowId;
                    existingCyberAirGap.DisableWorkflowId = cyberAirGap.DisableWorkflowId;
                    existingCyberAirGap.IsActive = cyberAirGap.IsActive;
                    return existingCyberAirGap;
                }
                return cyberAirGap;
            });

        // DeleteAsync - Soft delete by setting IsActive to false
        mockCyberAirGapRepository.Setup(repo => repo.DeleteAsync(It.IsAny<CyberAirGap>()))
            .Returns<CyberAirGap>(async (CyberAirGap cyberAirGap) =>
            {
                var existingCyberAirGap = cyberAirGaps.FirstOrDefault(x => x.ReferenceId == cyberAirGap.ReferenceId);
                if (existingCyberAirGap != null)
                {
                    existingCyberAirGap.IsActive = false;
                }
                return cyberAirGap;
            });

        return mockCyberAirGapRepository;
    }

    /// <summary>
    /// Creates a mock ICyberAirGapStatusRepository for status-related operations
    /// </summary>
    /// <param name="cyberAirGapStatuses">List of CyberAirGapStatus entities for testing</param>
    /// <returns>Configured mock status repository</returns>
    public static Mock<ICyberAirGapStatusRepository> CreateCyberAirGapStatusRepository(List<CyberAirGapStatus> cyberAirGapStatuses)
    {
        var mockCyberAirGapStatusRepository = new Mock<ICyberAirGapStatusRepository>();

        mockCyberAirGapStatusRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(cyberAirGapStatuses.Where(x => x.IsActive).ToList());

        mockCyberAirGapStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => cyberAirGapStatuses.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        mockCyberAirGapStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<CyberAirGapStatus>()))
            .ReturnsAsync((CyberAirGapStatus cyberAirGapStatus) =>
            {
                cyberAirGapStatus.ReferenceId = Guid.NewGuid().ToString();
                cyberAirGapStatus.Id = cyberAirGapStatuses.Count + 1;
                cyberAirGapStatus.IsActive = true;
                cyberAirGapStatuses.Add(cyberAirGapStatus);
                return cyberAirGapStatus;
            });

        mockCyberAirGapStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CyberAirGapStatus>()))
            .Returns<CyberAirGapStatus>(async (CyberAirGapStatus cyberAirGapStatus) =>
            {
                var existingStatus = cyberAirGapStatuses.FirstOrDefault(x => x.ReferenceId == cyberAirGapStatus.ReferenceId);
                if (existingStatus != null)
                {
                    existingStatus.AirGapId = cyberAirGapStatus.AirGapId;
                    existingStatus.AirGapName = cyberAirGapStatus.AirGapName;
                    existingStatus.Description = cyberAirGapStatus.Description;
                    existingStatus.Source = cyberAirGapStatus.Source;
                    existingStatus.Target = cyberAirGapStatus.Target;
                    existingStatus.SourceSiteId = cyberAirGapStatus.SourceSiteId;
                    existingStatus.SourceSiteName = cyberAirGapStatus.SourceSiteName;
                    existingStatus.TargetSiteId = cyberAirGapStatus.TargetSiteId;
                    existingStatus.TargetSiteName = cyberAirGapStatus.TargetSiteName;
                    existingStatus.Port = cyberAirGapStatus.Port;
                    existingStatus.SourceComponentId = cyberAirGapStatus.SourceComponentId;
                    existingStatus.SourceComponentName = cyberAirGapStatus.SourceComponentName;
                    existingStatus.TargetComponentId = cyberAirGapStatus.TargetComponentId;
                    existingStatus.TargetComponentName = cyberAirGapStatus.TargetComponentName;
                    existingStatus.EnableWorkflowId = cyberAirGapStatus.EnableWorkflowId;
                    existingStatus.DisableWorkflowId = cyberAirGapStatus.DisableWorkflowId;
                    existingStatus.IsActive = cyberAirGapStatus.IsActive;
                    return existingStatus;
                }
                return cyberAirGapStatus;
            });

        return mockCyberAirGapStatusRepository;
    }

    /// <summary>
    /// Creates a mock IUserActivityRepository for activity logging
    /// </summary>
    /// <param name="userActivities">List of UserActivity entities for testing</param>
    /// <returns>Configured mock user activity repository</returns>
    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity userActivity) =>
            {
                userActivity.Id = userActivities.Count + 1;
                userActivity.ReferenceId = Guid.NewGuid().ToString();
                userActivity.IsActive = true;
                userActivities.Add(userActivity);
                return userActivity;
            });

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities.Where(x => x.IsActive).ToList());

        return mockUserActivityRepository;
    }
}
