﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowProfileInfoByProfileId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Queries;

public class GetWorkflowProfileInfoByProfileIdQueryHandlerTests : IClassFixture<WorkflowProfileFixture> ,IClassFixture<WorkflowProfileInfoViewFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;
    private readonly WorkflowProfileInfoViewFixture _workflowProfileInfoViewFixture;
    private readonly Mock<IWorkflowProfileInfoViewRepository> _mockWorkflowProfileInfoViewRepository;
    private readonly Mock<IWorkflowExecutionTempRepository> _mockWorkflowExecutionTempRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetWorkflowProfileInfoByProfileIdQueryHandler _handler;

    public GetWorkflowProfileInfoByProfileIdQueryHandlerTests(WorkflowProfileFixture workflowProfileFixture, WorkflowProfileInfoViewFixture workflowProfileInfoViewFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;
        _workflowProfileInfoViewFixture = workflowProfileInfoViewFixture;
        _mockWorkflowProfileInfoViewRepository = new Mock<IWorkflowProfileInfoViewRepository>();
        _mockWorkflowExecutionTempRepository = new Mock<IWorkflowExecutionTempRepository>();
        _mockMapper = new Mock<IMapper>();

        Mock<IWorkflowProfileRepository> mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetByProfileIdAsync(_workflowProfileFixture.WorkflowProfiles);

        _workflowProfileInfoViewFixture.WorkflowProfileInfoViews[0].ProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId;
        _workflowProfileInfoViewFixture.WorkflowProfileInfoViews[1].ProfileId = _workflowProfileFixture.WorkflowProfiles[1].ReferenceId;
        _workflowProfileInfoViewFixture.WorkflowProfileInfoViews[2].ProfileId = _workflowProfileFixture.WorkflowProfiles[2].ReferenceId;

        _handler = new GetWorkflowProfileInfoByProfileIdQueryHandler(mockWorkflowProfileRepository.Object, _mockMapper.Object, _mockWorkflowExecutionTempRepository.Object, _mockWorkflowProfileInfoViewRepository.Object);
        
    }

    [Fact]
    public async Task Handle_ReturnWorkflowProfileInfo_When_ValidProfileId()
    {

        _workflowProfileInfoViewFixture.WorkflowProfileInfoViews[0].ProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId;
        _workflowProfileInfoViewFixture.WorkflowProfileInfoViews[1].ProfileId = _workflowProfileFixture.WorkflowProfiles[1].ReferenceId;
        _workflowProfileInfoViewFixture.WorkflowProfileInfoViews[2].ProfileId = _workflowProfileFixture.WorkflowProfiles[2].ReferenceId;

        var expectedProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId;

        var workflowProfileNameVmList = new List<GetWorkflowProfileInfoByProfileIdVm>
        {
            new()
            {
                ProfileId = expectedProfileId,
                ProfileName = _workflowProfileFixture.WorkflowProfiles[0].Name
            }
        };

        _mockMapper
            .Setup(mapper => mapper.Map<List<GetWorkflowProfileInfoByProfileIdVm>>(_workflowProfileFixture.WorkflowProfiles))
            .Returns(workflowProfileNameVmList);

        var expectedViews = _workflowProfileInfoViewFixture.WorkflowProfileInfoViews.Where(x => x.ProfileId == expectedProfileId)
            .ToList();

        _mockWorkflowProfileInfoViewRepository
            .Setup(x => x.GetWorkflowProfileInfoByProfileIds(It.Is<List<string>>(ids => ids.Contains(expectedProfileId))))
            .ReturnsAsync(expectedViews);

        var workflowProfileInfoNameVmList = expectedViews.Select(x=> new WorkflowProfileInfoDto
        {
                ProfileId = x.ProfileId,
                WorkflowId = x.WorkflowId,
                WorkflowName = x.WorkflowName,
                CurrentActionId = x.CurrentActionId,
                CurrentActionName = x.CurrentActionName,
                Message = x.Message,
                ProgressStatus = x.ProgressStatus,
                ConditionalOperation = x.ConditionalOperation,
                WorkflowType = x.WorkflowType,
                ActionMode = x.ActionMode,
                IsLock = x.IsLock,
                IsParallel = x.IsParallel,
                IsRunning = x.IsRunning,
                InfraObjectId = x.InfraObjectId,
                InfraObjectName = x.InfraObjectName,
                BusinessFunctionId = x.BusinessFunctionId,
                BusinessFunctionName = x.BusinessFunctionName,
                BusinessServiceId = x.BusinessServiceId,
                BusinessServiceName = x.BusinessServiceName,
            }).ToList();

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowProfileInfoDto>>(expectedViews))
            .Returns(workflowProfileInfoNameVmList);


        var workflowId = workflowProfileInfoNameVmList[0].WorkflowId;

        var expectedTempViews = new List<(string WorkflowId, string Id, bool Exists)>
        {
            (workflowId, "TempExecutionId123", true)
        };


        _mockWorkflowExecutionTempRepository
            .Setup(x => x.GetFirstMatchedIdAndExistenceAsync(It.Is<List<string>>(ids => ids.Contains(workflowId))))
            .ReturnsAsync(expectedTempViews);


        var result = await _handler.Handle(new GetWorkflowProfileInfoByProfileIdQuery { ProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<List<GetWorkflowProfileInfoByProfileIdVm>>();

        result.Count.ShouldBe(1);

        result[0].ProfileId.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].ReferenceId);
        result[0].ProfileName.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Name);
    }


    [Fact]
    public async Task Handle_ShouldCoverElseCase_When_WorkflowIdIsNullOrWhiteSpace()
    {
        // Arrange
        var profile = _workflowProfileFixture.WorkflowProfiles[0];
        var expectedProfileId = profile.ReferenceId;

        var workflowProfileNameVmList = new List<GetWorkflowProfileInfoByProfileIdVm>
    {
        new()
        {
            ProfileId = expectedProfileId,
            ProfileName = profile.Name
        }
    };

        _mockMapper
            .Setup(mapper => mapper.Map<List<GetWorkflowProfileInfoByProfileIdVm>>(
                It.IsAny<List<Domain.Entities.WorkflowProfile>>()))
            .Returns(workflowProfileNameVmList);

        var expectedViews = new List<WorkflowProfileInfoView>
    {
        new()
        {
            ProfileId = expectedProfileId,
            WorkflowId = null, // Will trigger `return info;`
            WorkflowName = "NullWorkflow",
            CurrentActionId = "ActionId1",
            // Fill other necessary properties...
        }
    };

        _mockWorkflowProfileInfoViewRepository
            .Setup(x => x.GetWorkflowProfileInfoByProfileIds(It.Is<List<string>>(ids => ids.Contains(expectedProfileId))))
            .ReturnsAsync(expectedViews);

        var workflowProfileInfoDtos = new List<WorkflowProfileInfoDto>
    {
        new()
        {
            ProfileId = expectedProfileId,
            WorkflowId = null, // <<<<<<<<<<<<<< this triggers else
            WorkflowName = "NullWorkflow",
            // Fill other props...
        }
    };

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowProfileInfoDto>>(expectedViews))
            .Returns(workflowProfileInfoDtos);

        // Since WorkflowId is null, this won't be called but must be mocked
        _mockWorkflowExecutionTempRepository
            .Setup(x => x.GetFirstMatchedIdAndExistenceAsync(It.IsAny<List<string>>()))
            .ReturnsAsync(new List<(string WorkflowId, string Id, bool Exists)>());

        // Act
        var result = await _handler.Handle(new GetWorkflowProfileInfoByProfileIdQuery
        {
            ProfileId = expectedProfileId
        }, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);

        var workflowInfos = result[0].WorkflowProfileInfos;
        workflowInfos.Count.ShouldBe(1);
        var nullWorkflow = workflowInfos[0];

        nullWorkflow.WorkflowId.ShouldBeNull(); // Ensures it hit the else path
        nullWorkflow.CustomId.ShouldBeNull();   // Should not be set
        nullWorkflow.IsCustom.ShouldBeFalse();  // Should not be set
    }





    //[Fact]
    //public async Task Handle_ReturnEmptyList_When_NoRecords()
    //{
    //    //_mockWorkflowProfileInfoRepository = WorkflowProfileInfoRepositoryMocks.GetWorkflowProfileInfoEmptyRepository();

    //    //_workflowProfileFixture.WorkflowProfiles[0].ReferenceId =null;
    //    //_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId;

    //    //_workflowProfileFixture.WorkflowProfiles[0].Name = null;
    //    //_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileName = _workflowProfileFixture.WorkflowProfiles[0].Name;

    //    //_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileId =
    //    //    "d0b0e8ce-db44-47c3-b43d-7d3ab172a2d3,fee8efd7-8cab-415e-8aad-1ee8d3ca190b,b280b7c0-6e09-4d8f-b60a-d23b994ae226";

    //    var handler = new GetWorkflowProfileInfoByProfileIdQueryHandler(_mockWorkflowProfileRepository.Object, _mockWorkflowProfileInfoRepository.Object, _workflowProfileInfoFixture.Mapper);

    //    var result = await handler.Handle(new GetWorkflowProfileInfoByProfileIdQuery { ProfileId = "d0b0e8ce-db44-47c3-b43d-7d3ab172a2d3,fee8efd7-8cab-415e-8aad-1ee8d3ca190b,b280b7c0-6e09-4d8f-b60a-d23b994ae226" }, CancellationToken.None);

    //    result.Count.ShouldBe(1);
    //}

    //[Fact]
    //public async Task Handle_ShouldCall_GetByProfileIdAsync_Once()
    //{
    //    await _handler.Handle(new GetWorkflowProfileInfoByProfileIdQuery { ProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId }, CancellationToken.None);

    //    _mockWorkflowProfileRepository.Verify(x => x.GetByProfileIdAsync(It.IsAny<List<string>>()), Times.Once);
    //}

    //[Fact]
    //public async Task GetWorkflowProfileInfoByProfileIds()
    //{
    //    await _handler.Handle(new GetWorkflowProfileInfoByProfileIdQuery { ProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId }, CancellationToken.None);

    //    _mockWorkflowProfileInfoViewRepository.Verify(x => x.GetWorkflowProfileInfoByProfileIds(It.IsAny<List<string>>()), Times.Once);
    //}

}