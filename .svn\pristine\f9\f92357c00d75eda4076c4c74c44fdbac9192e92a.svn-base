﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactCategory.Events
{
    public class FiaImpactCategoryDeletedEventHandlerTests : IClassFixture<FiaImpactCategoryFixture>
    {
        private readonly FiaImpactCategoryFixture _fixture;
        private readonly Mock<ILogger<FiaImpactCategoryDeletedEventHandler>> _mockLogger;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly FiaImpactCategoryDeletedEventHandler _handler;
        private readonly List<Domain.Entities.UserActivity> _userActivities;

        public FiaImpactCategoryDeletedEventHandlerTests(FiaImpactCategoryFixture fixture)
        {
            _fixture = fixture;

            _mockLogger = new Mock<ILogger<FiaImpactCategoryDeletedEventHandler>>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _userActivities = new List<Domain.Entities.UserActivity>();

            _mockUserActivityRepository = FiaImpactCategoryRepositoryMocks.CreateFiaImpactCategoryEventRepository(_userActivities);

            // Mock user context
            _mockUserService.Setup(u => u.UserId).Returns("user-123");
            _mockUserService.Setup(u => u.LoginName).Returns("testuser");
            _mockUserService.Setup(u => u.RequestedUrl).Returns("/api/fia-impact-category/delete");
            _mockUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

            _handler = new FiaImpactCategoryDeletedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact(DisplayName = "Handle_Should_Log_And_SaveUserActivity_When_ValidEventReceived")]
        public async Task Handle_Should_Log_And_SaveUserActivity_When_ValidEventReceived()
        {
            // Arrange
            var deletedEvent = _fixture.FiaImpactCategoryDeletedEvent;

            // Act
            await _handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            Assert.Single(_userActivities);

            var activity = _userActivities.First();
            Assert.Equal("user-123", activity.UserId);
            Assert.Equal("testuser", activity.LoginName);
            Assert.Equal("/api/fia-impact-category/delete", activity.RequestUrl);
            Assert.Equal("127.0.0.1", activity.HostAddress);
            Assert.Equal("Delete FiaImpactCategory", activity.Action);
            Assert.Equal("FiaImpactCategory", activity.Entity);
            Assert.Equal("Delete", activity.ActivityType);
            Assert.Contains(deletedEvent.Name, activity.ActivityDetails);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            //_mockLogger.Verify(log => log.LogInformation($"FiaImpactCategory '{deletedEvent.Name}' deleted successfully."), Times.Once);
            _mockLogger.Verify(log => log.Log(LogLevel.Information, It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString() == $"FiaImpactCategory '{deletedEvent.Name}' deleted successfully."),
                It.IsAny<Exception>(), It.IsAny<Func<It.IsAnyType, Exception, string>>()!), Times.Once);
        }
    }
}
