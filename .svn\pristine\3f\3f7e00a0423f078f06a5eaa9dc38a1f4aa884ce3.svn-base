﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberJobWorkflowSchedulerRepository : BaseRepository<CyberJobWorkflowScheduler>, ICyberJobWorkflowSchedulerRepository
{
    private readonly ApplicationDbContext _dbContext;

    public CyberJobWorkflowSchedulerRepository(ApplicationDbContext dbContext): base(dbContext)
    {
        _dbContext = dbContext;
        
    }
    public async Task<CyberJobWorkflowScheduler> GetCyberJobWorkflowSchedulerByJobId(string jobId)
    {
        if (string.IsNullOrWhiteSpace(jobId))
            throw new ArgumentException("Job ID must be provided.", nameof(jobId));

        return await _dbContext.CyberJobWorkflowSchedulers
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.JobId == jobId);
    }
    public async Task<PaginatedResult<CyberJobWorkflowSchedulerListVm>> GetCyberJobWorkflowSchedulerPagination(int pageNumber, int pageSize, Specification<CyberJobWorkflowScheduler> specification, string startDate, string endDate)
    {
        var query = _dbContext.CyberJobWorkflowSchedulers.AsNoTracking().Active().Specify(specification);

        if (startDate.IsNotNullOrWhiteSpace() && endDate.IsNotNullOrWhiteSpace())
        {
            query = query.Where(x =>
                        (x.StartTime == DateTime.MinValue || x.StartTime.Date >= startDate.ToDateTime()) &&
                        (x.EndTime == DateTime.MinValue || x.EndTime.Date <= endDate.ToDateTime()));

        }
        var groupedData = await query.ToListAsync();

        var queryable = groupedData
             .GroupBy(x => x.JobId)
             .OrderByDescending(g => g.Key)
             .Select(g => new CyberJobWorkflowSchedulerListVm
             {
                 JobId = g.First().JobId,
                 Name = g.First().Name,
                 WorkflowName = g.First().WorkflowName,
                 StartTime = g.First().StartTime,
                 Status = $"{g.Count(x => x.Status != null && x.Status.ToLower() == "completed")}/{g.Count()}",
                 State = g.First().State,
                 EndTime = g.First().EndTime,
                 ConditionActionId = g.First().ConditionActionId,
                 CurrentActionName = g.First().CurrentActionName,
                 SuccessCount = $"{(g.Count(x => x.Status != null && x.Status.ToLower() == "completed") * 100 / g.Count())}%"
             }).OrderByDescending(x => x.Id).AsQueryable();

        var totalCount = queryable.Count();

        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        pageSize = pageSize > 0 ? pageSize : totalCount;

        var pagedData = queryable.ToList()
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return new PaginatedResult<CyberJobWorkflowSchedulerListVm>(
            succeeded: true,
            data: pagedData,
            count: totalCount,
            page: pageNumber,
            pageSize: pageSize
        );
    }
}
