﻿using ContinuityPatrol.Application.Features.GlobalVariable.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Events;

public class UpdateGlobalVariableEventTests : IClassFixture<GlobalVariableFixture>, IClassFixture<UserActivityFixture>
{
    private readonly GlobalVariableFixture _globalVariableFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly GlobalVariableUpdatedEventHandler _handler;

    public UpdateGlobalVariableEventTests(GlobalVariableFixture globalVariableFixture, UserActivityFixture userActivityFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockGlobalVariableEventLogger = new Mock<ILogger<GlobalVariableUpdatedEventHandler>>();

        _mockUserActivityRepository = GlobalVariableRepositoryMocks.CreateGlobalVariableEventRepository(_userActivityFixture.UserActivities);

        _handler = new GlobalVariableUpdatedEventHandler(mockLoggedInUserService.Object, mockGlobalVariableEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateGlobalVariableEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_globalVariableFixture.GlobalVariableUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_globalVariableFixture.GlobalVariableUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}