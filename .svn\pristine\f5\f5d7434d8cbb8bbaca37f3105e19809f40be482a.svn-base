﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowPermissionRepositoryMocks
{
    public static Mock<IWorkflowPermissionRepository> GetPaginatedWorkflowPermissionRepository(List<WorkflowPermission> workflowPermission)
    {
        var workflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();

        workflowPermissionRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<WorkflowPermission>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<WorkflowPermission> spec, string sortColumn, string sortOrder) =>
            {
                var sortedCompanies = workflowPermission.AsQueryable();

                if (spec.Criteria != null)
                {
                    sortedCompanies = sortedCompanies.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    // Assuming Company has a Name property; replace logic as needed
                    sortedCompanies = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedCompanies.OrderByDescending(c => c.AccessType)
                        : sortedCompanies.OrderBy(c => c.AccessType);
                }

                var totalCount = sortedCompanies.Count();
                var paginated = sortedCompanies
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PaginatedResult<WorkflowPermission>.Success(paginated, totalCount, pageNumber, pageSize);
            });
        return workflowPermissionRepository;
    }


    public static Mock<IWorkflowPermissionRepository> CreateWorkflowPermissionRepository(List<WorkflowPermission> workflowPermissions)
    {
        var workflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();

        workflowPermissionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowPermissions);

        workflowPermissionRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowPermission>())).ReturnsAsync(
            (WorkflowPermission workflowPermission) =>
            {
                workflowPermission.Id = new Fixture().Create<int>();

                workflowPermission.ReferenceId = new Fixture().Create<Guid>().ToString();

                workflowPermissions.Add(workflowPermission);

                return workflowPermission;
            });

        return workflowPermissionRepository;
    }

    public static Mock<IWorkflowPermissionRepository> UpdateWorkflowPermissionRepository(List<WorkflowPermission> workflowPermissions)
    {
        var workflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();

        workflowPermissionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowPermissions);

        workflowPermissionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowPermissions.SingleOrDefault(x => x.ReferenceId == i));

        workflowPermissionRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowPermission>())).ReturnsAsync((WorkflowPermission workflowPermission) =>
        {
            var index = workflowPermissions.FindIndex(item => item.ReferenceId == workflowPermission.ReferenceId);

            workflowPermissions[index] = workflowPermission;

            return workflowPermission;

        });
        return workflowPermissionRepository;
    }

    public static Mock<IWorkflowPermissionRepository> DeleteWorkflowPermissionRepository(List<WorkflowPermission> workflowPermissions)
    {
        var workflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();

        workflowPermissionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowPermissions);

        workflowPermissionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowPermissions.SingleOrDefault(x => x.ReferenceId == i));

        workflowPermissionRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowPermission>())).ReturnsAsync((WorkflowPermission workflowPermission) =>
        {
            var index = workflowPermissions.FindIndex(item => item.ReferenceId == workflowPermission.ReferenceId);

            workflowPermission.IsActive = false;

            workflowPermissions[index] = workflowPermission;

            return workflowPermission;
        });

        return workflowPermissionRepository;
    }
    public static Mock<IWorkflowPermissionRepository> GetWorkflowPermissionRepository(List<WorkflowPermission> workflowPermissions)
    {
        var workflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();

        workflowPermissionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowPermissions);

        //workflowRepository.Setup(repo => repo.GetWorkflowList()).ReturnsAsync(workflowPermissions);

        workflowPermissionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowPermissions.SingleOrDefault(x => x.ReferenceId == i));

        return workflowPermissionRepository;
    }

    public static Mock<IWorkflowPermissionRepository> GetWorkflowPermissionEmptyRepository()
    {
        var workflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();

        workflowPermissionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowPermission>());

        return workflowPermissionRepository;
    }

    //Events
    public static Mock<IUserActivityRepository> CreateWorkflowPermissionEventRepository(List<UserActivity> userActivities)
    {
        var mockSiteTypeEventRepository = new Mock<IUserActivityRepository>();

        mockSiteTypeEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockSiteTypeEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return mockSiteTypeEventRepository;
    }
}