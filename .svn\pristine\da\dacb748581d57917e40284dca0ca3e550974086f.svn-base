﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Commands
{
  public class UpdateWorkflowDrCalenderTest : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly Mock<IWorkflowDrCalenderRepository> _mockWorkflowDrCalenderRepository;

        private readonly WorkflowDrCalenderFixture _workflowDrcalenderFixture;

        private readonly UpdateWorkflowDrCalenderCommandHandler _handler;

        public UpdateWorkflowDrCalenderTest(WorkflowDrCalenderFixture workflowDrCalenderFixture)
        {
            _workflowDrcalenderFixture = workflowDrCalenderFixture;

            var mockPublisher = new Mock<IPublisher>();

            _mockWorkflowDrCalenderRepository = WorkflowDrCalenderRepositoryMocks.UpdateworkflowDrcalenderRepository(_workflowDrcalenderFixture.WorkflowDrCalenderInfos);

            _handler = new UpdateWorkflowDrCalenderCommandHandler(_workflowDrcalenderFixture.Mapper, _mockWorkflowDrCalenderRepository.Object, mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ValidWorkflowProfileInfo_UpdatedTo_WorkflowProfileInfoRepo()
        {
            _workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand.Id = _workflowDrcalenderFixture.WorkflowDrCalenderInfos[0].ReferenceId;

            var result = await _handler.Handle(_workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand, CancellationToken.None);

            var workflowProfileInfo = await _mockWorkflowDrCalenderRepository.Object.GetByReferenceIdAsync(result.Id);

            Assert.Equal(_workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand.ProfileName, workflowProfileInfo.ProfileName);
        }

        [Fact]
        public async Task Handle_Return_Valid_WorkflowProfileInfoResponse()
        {
            _workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand.Id = _workflowDrcalenderFixture.WorkflowDrCalenderInfos[0].ReferenceId;

            var result = await _handler.Handle(_workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand, CancellationToken.None);

            result.ShouldBeOfType(typeof(UpdateWorkflowDrCalenderResponse));

            result.Id.ShouldBe(_workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand.Id);

            var expectedMessage = $" WorkflowDrCalender '{_workflowDrcalenderFixture.WorkflowDrCalenderInfos[0].UserName}' has been updated successfully";

            result.Message.ShouldBe(expectedMessage);
        }

        [Fact]
        public async Task Handle_Throw_NotFoundException_When_Invalid_WorkflowProfileInfoId()
        {
            _workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand.Id = int.MaxValue.ToString();

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
        {
            _workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand.Id = _workflowDrcalenderFixture.WorkflowDrCalenderInfos[0].ReferenceId;

            await _handler.Handle(_workflowDrcalenderFixture.UpdateWorkflowDrcalenderCommand, CancellationToken.None);

            _mockWorkflowDrCalenderRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

            _mockWorkflowDrCalenderRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowDrCalender>()), Times.Once);
        }
    }
}
