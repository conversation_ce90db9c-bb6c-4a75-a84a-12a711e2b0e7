﻿@using ContinuityPatrol.Domain.ViewModels.PageBuilderModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/pagebuilder.css" rel="stylesheet" />
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />

<div class="page-content" style="background-color:white">
    <div class="card Card_Design_None">
        <div class="card-body" style="height: calc(100vh - 125px);overflow: auto;">

   
        <div class="row mx-0 g-0">
            <div class="col" >
                <div class="header px-3 py-2">
                    <h6 class="page_title"><i class="cp-platform-name"></i><span id="pageBuilderTitle">Page Builder</span></h6>
                    <div class="cardCreation d-none">
                        <div>
                            <div class="form-label fs-7">Card Creation</div>
                        </div>
                        <div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-name"></i></span>
                                <input class="form-control" id="pageHeight" type="number" placeholder="Enter Height" />
                            </div>
                            <span id="pageHeight_Error"></span>
                        </div>

                        <div>
                            <div class="input-group">
                               

                                <span class="input-group-text"><i class="cp-name"></i></span>
                                <input class="form-control" id="pageWidth" type="number" placeholder="Enter Width" />
                            </div>
                            <span id="pageWidth_error"></span>

                        </div>
                        <div>
                            <div class="text-center">
                                <button type="button" class="btn btn-primary btn-sm " id="PageAdd">Add</button>
                            </div>
                        </div>
                    </div>
                    <div class="btn-group gap-2 align-items-center d-none">
                        <span>Create Page Builder</span>
                        <button type="button" class="btn btn-outline-primary btn-sm rounded-2">Templates</button>
                        <div class="vr"></div>
                        <button type="button" class="btn btn-outline-primary btn-sm rounded-2">My Files</button>
                        <div class="vr"></div>
                        <button type="button" class="btn btn-outline-primary btn-sm rounded-2">Custom Dashboard</button>
                    </div>
                    <div class="btn-group gap-2 sideSearchButton">
                        <div class="input-group hideshowPage">
                            <input type="search" id="search-PageBuilder" class="form-control" placeholder="Search" autocomplete="off">
                        </div>
                        <button type="button" id="createLayoutBtn" class="btn btn-primary btn-sm rounded-2" data-bs-toggle="modal" data-bs-target="#LayoutModal"><i class="cp-add me-1"></i>Create</button>
                    </div>
                </div>
                <div class="p-3 pt-0">
                    <div class="Choose-Template" id="homepageModal">
                        <div class="card-title d-none">Choose template to built Page Builder</div>
                        <div class="row row-cols-xl-6 row-cols-xxl-5 pagebuilderlist g-2"></div>
                    </div>
                    <div class="row h-100" id="createpageModal">
                        <div class="col PB_pageLayoutContent" id="PB_pageContent">
                            <div class="d-flex justify-content-between border px-3 align-items-center">
                                <h5 class="page_title"><span id=""></span></h5>
                            </div>
                        </div>
                     

                    </div>
                </div>
            </div>
        </div>
        </div>
        <div class="card-footer text-end footerContainer">
           
                <button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button>
                <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
           
        </div>
    </div>
</div>




<!-- Layouts Modal -->
<div class="modal fade" id="LayoutModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Company Configuration"><i class="cp-grid"></i><span>Layouts</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-6 g-2">
                    <div class="col PB_layout" id="PB_layout15">
                        <div class="card border" data-bs-dismiss="modal" aria-label="Close" role="button">
                            <div class="card-body p-2 PB_layout" role="button">
                                <img src="~/img/layouts_img/grid-layouts/layout15.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">Blank</label>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout1">
                        <div class="card border" data-bs-dismiss="modal" aria-label="Close" role="button">
                            <div class="card-body p-2 PB_layout" role="button">
                                <img src="~/img/layouts_img/grid-layouts/layout1.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">MSSQL NLS</label>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout2" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2 ">
                                <img src="~/img/layouts_img/grid-layouts/layout11.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">MSSQL AlwaysOn</label>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout3" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2 ">
                                <img src="~/img/layouts_img/grid-layouts/layout6.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">Postgres</label>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout4" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/oracle_dataguard_11g.svg" class="w-100" />
                            </div>
                            <label style="text-align: center;">Oracle Dataguard</label>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout5" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout7.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">Mysql</label>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout6" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout3.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">
                                MongoDB
                            </label>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout7" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2" id="PB_layout7">
                                <img src="~/img/layouts_img/grid-layouts/layout7.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">
                                RSync
                            </label>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout8" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout8.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">
                                Oracle Rac
                            </label>
                            
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout9" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout9.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">
                                DataSync
                            </label>

                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout10" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout10.png" class="w-100" />
                            </div>
                            <label style="text-align: center;">
                                Open Shift
                            </label>

                           
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout11" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout2.png" class="w-100" />
                            </div>


                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout12" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout12.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout13" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout13.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout14" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/layout14.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout16" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/mssql_alwayson_101.svg" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout17" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/mssql_native_logshipping..svg" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col PB_layout" id="PB_layout18" data-bs-dismiss="modal" aria-label="Close" role="button">
                        <div class="card border">
                            <div class="card-body p-2">
                                <img src="~/img/layouts_img/grid-layouts/mysql_master_slave_native_replication.svg" class="w-100" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




@* Offcanvas Modal  *@
<div class="offcanvas offcanvas-start" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasScrolling" aria-labelledby="offcanvasScrollingLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="offcanvasScrollingLabel">Offcanvas with body scrolling</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <p>Try scrolling the rest of the page to see this option in action.</p>
    </div>
</div>

<!-- Title Modal -->
<div class="modal fade" id="TitleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-grid"></i><span>Page Title</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <div class="form-label">Page Title</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input class="form-control" id="pageTitle" type="text" placeholder="Enter Page Title" />
                        </div>
                        <span id="pageTitleError"></span>
                    </div>
                    <div class="form-group dropdownComponentType">
                        <label class="form-label">Type</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                            <select class="form-select-modal" id="selectComponentType" data-live-search="true" data-placeholder="Select Type">
                                <option value="">Select</option>
                                <option value="Mysql">Mysql</option>
                                <option value="MssqlNLS">Mssql</option>
                                <option value="mssqldbmirroring">Mssql DB Mirroring</option>
                                <option value="MssqlAlwaysOn">MssqlAlwaysOn</option>
                                <option value="Oracle">Oracle</option>
                                <option value="Postgres">Postgres</option>
                                <option value="MongoDB">MongoDB</option>
                                <option value="AzureStorageAccount">AzureStorageAccount</option>
                                <option value="RSyncAppReplication">RSyncAppReplication</option>  
                                <option value="DataSyncAppReplication">DataSyncAppReplication</option>
                                <option value="SybaseRSHADR">SybaseRSHADR</option>
                            </select>
                        </div>
                        <span id="componentTypeError"></span>
                    </div>
                    <div class="form-group dropdownComponentType">
                        <label class="form-label">Infraobject Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                            <select class="form-select-modal" id="selectInfraobjectName" data-live-search="true" data-placeholder="Select Infraobject Name">

                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="pageBuilderApply">Apply</button>
            </div>
        </div>
    </div>
</div>

<!-- Widget list Modal -->


<div class="modal fade" id="WidgetList" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-grid"></i><span>Page Widget List</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label class="form-label">Widget List</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                            <select class="form-select-modal" id="selectWidgetList" data-live-search="true" data-placeholder="Select Widget List">
                              
                            </select>
                        </div>

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="WidgetListApply">Apply</button>
            </div>
        </div>
    </div>
</div>




<!-- Title Modal -->
<div class="modal fade" id="TitleEditModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-grid"></i><span>Page Title</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <div class="form-label">Page Title</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input class="form-control" id="pageEditTitle" type="text" placeholder="Enter Page Title" />
                        </div>
                        <span id="pageTitleError"></span>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="pageBuilderEditApply">Apply</button>
            </div>
        </div>
    </div>
</div>


<div class="contextMenu dropdown" id="contextMenu">
    <ul class="UlContextBtn dropdown-menu dropdown-menu-lg-end fs-8" style="display:block;">

        <li id="btncopy">
            <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                <span><i class="cp-copy me-1 fs-7"></i>Copy</span>
            </a>
        </li>
        <li id="btnDelete">
            <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                <span><i class="cp-Delete me-1 fs-7"></i>Delete</span>
            </a>
        </li>

    </ul>
</div>


<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" model="new PageBuilderViewModel()" />
</div>

@section Scripts
{
    <partial name="_ValidationScriptsPartial" />
}
<link href="~/css/jquerythemes.css" rel="stylesheet" />
<script src="~/js/dashboardbuilder/customdashboardsort.js"></script>
<script src="~/js/PageBuilder/PageBuilder.js"></script>
<script src="~/js/PageBuilder/PageLayout.js"></script>
<script src="~/js/common/slide_toggle.js"></script>
<script src="~/lib/canvas/canvas.js"></script>
<script src="~/lib/jquery-ui/jquery-ui.min.js"></script>

