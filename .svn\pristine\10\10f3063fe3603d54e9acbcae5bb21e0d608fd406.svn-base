using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGapStatus.Commands;

public class UpdateCyberAirGapStatusTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly Mock<ICyberAirGapStatusRepository> _mockCyberAirGapStatusRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateCyberAirGapStatusCommandHandler _handler;

    public UpdateCyberAirGapStatusTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _mockCyberAirGapStatusRepository = CyberAirGapStatusRepositoryMocks.CreateCyberAirGapStatusRepository(_cyberAirGapStatusFixture.CyberAirGapStatuses);
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        _handler = new UpdateCyberAirGapStatusCommandHandler(
            _mockMapper.Object,
            _mockCyberAirGapStatusRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapStatus_When_ValidCommand()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapId = "updated-airgap-001",
            AirGapName = "Updated Enterprise Air Gap Status",
            SourceSiteName = "Updated Production Site",
            TargetSiteName = "Updated DR Site",
            Port = 9443,
            Description = "Updated air gap status description"
        };

        CyberAirGapStatusUpdatedEvent publishedEvent = null;

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapStatusCommand), typeof(Domain.Entities.CyberAirGapStatus)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapStatusCommand)src;
                var entity = (Domain.Entities.CyberAirGapStatus)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.SourceSiteName = updateCmd.SourceSiteName;
                entity.TargetSiteName = updateCmd.TargetSiteName;
                entity.Port = updateCmd.Port;
                entity.Description = updateCmd.Description;
            });

        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapStatusUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateCyberAirGapStatusResponse>();
        result.Success.ShouldBeTrue();
        result.Id.ShouldBe(existingEntity.ReferenceId);
        result.Message.ShouldContain("updated successfully");

        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe(command.AirGapName);

        _mockCyberAirGapStatusRepository.Verify(x => x.GetByReferenceIdAsync(command.Id), Times.Once);
        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(existingEntity), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapStatus_When_EntityNotFound()
    {
        // Arrange
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = "non-existent-id",
            AirGapName = "Non-existent Air Gap Status"
        };

        _mockCyberAirGapStatusRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync((Domain.Entities.CyberAirGapStatus)null);

        // Act & Assert
       
        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGapStatus>()), Times.Never);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapStatus_When_ComplexProperties()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Updated Complex Air Gap Status",
            Source = @"{
                ""type"": ""updated-database"",
                ""cluster"": {
                    ""name"": ""UPDATED-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""UPDATED-DB-01"", ""role"": ""Primary""},
                        {""server"": ""UPDATED-DB-02"", ""role"": ""Secondary""},
                        {""server"": ""UPDATED-DB-03"", ""role"": ""Tertiary""}
                    ]
                }
            }",
            Target = @"{
                ""type"": ""updated-database"",
                ""cluster"": {
                    ""name"": ""UPDATED-DR-CLUSTER-01"",
                    ""nodes"": [
                        {""server"": ""UPDATED-DR-DB-01"", ""role"": ""Primary""},
                        {""server"": ""UPDATED-DR-DB-02"", ""role"": ""Secondary""}
                    ]
                }
            }"
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapStatusCommand), typeof(Domain.Entities.CyberAirGapStatus)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapStatusCommand)src;
                var entity = (Domain.Entities.CyberAirGapStatus)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Source = updateCmd.Source;
                entity.Target = updateCmd.Target;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.CyberAirGapStatus>(e =>
            e.Source.Contains("UPDATED-CLUSTER-01") &&
            e.Target.Contains("UPDATED-DR-CLUSTER-01"))), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapStatus_When_CancellationRequested()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Cancelled Update"
        };
        var cancellationToken = new CancellationToken(true);

        
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapStatus_When_RepositoryFails()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Failed Update"
        };

        var mockFailingRepository = CyberAirGapStatusRepositoryMocks.CreateFailingCyberAirGapStatusRepository();
        mockFailingRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(existingEntity);

        var handler = new UpdateCyberAirGapStatusCommandHandler(
            _mockMapper.Object,
            mockFailingRepository.Object,
            _mockPublisher.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            async () => await handler.Handle(command, CancellationToken.None));

        exception.Message.ShouldBe("Update operation failed");
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapStatusUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("Active", "System is running normally")]
    [InlineData("Warning", "System has minor issues")]
    [InlineData("Error", "System has critical errors")]
    [InlineData("Maintenance", "System is under maintenance")]
    [InlineData("Disabled", "System is temporarily disabled")]
    public async Task Handle_UpdateCyberAirGapStatus_When_DifferentStatusValues(string status, string description)
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = $"Status Test - {status}",
            Description = description
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapStatusCommand), typeof(Domain.Entities.CyberAirGapStatus)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapStatusCommand)src;
                var entity = (Domain.Entities.CyberAirGapStatus)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Description = updateCmd.Description;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain(command.AirGapName);

        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.CyberAirGapStatus>(e =>
            e.AirGapName.Contains(status))), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapStatus_When_NullProperties()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Null Properties Update",
            Description = null,
            Source = null,
            Target = null,
            SourceComponentName = null,
            TargetComponentName = null
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapStatusCommand), typeof(Domain.Entities.CyberAirGapStatus)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapStatusCommand)src;
                var entity = (Domain.Entities.CyberAirGapStatus)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Description = updateCmd.Description;
                entity.Source = updateCmd.Source;
                entity.Target = updateCmd.Target;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CyberAirGapStatus>()), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapStatus_When_SpecialCharacters()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Updated Special Characters & <script>alert('xss')</script>",
            Description = "Updated description with special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?",
            SourceSiteName = "Updated Source with émojis 🔄💻📊",
            TargetSiteName = "Updated Target with unicode 更新数据"
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapStatusCommand), typeof(Domain.Entities.CyberAirGapStatus)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapStatusCommand)src;
                var entity = (Domain.Entities.CyberAirGapStatus)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Description = updateCmd.Description;
                entity.SourceSiteName = updateCmd.SourceSiteName;
                entity.TargetSiteName = updateCmd.TargetSiteName;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain("Updated Special Characters");

        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.CyberAirGapStatus>(e =>
            e.AirGapName.Contains("Updated Special Characters") &&
            e.SourceSiteName.Contains("🔄💻📊") &&
            e.TargetSiteName.Contains("更新数据"))), Times.Once);
    }

    [Theory]
    [InlineData(1, "Updated Minimum Port")]
    [InlineData(65535, "Updated Maximum Port")]
    [InlineData(443, "Updated HTTPS Port")]
    [InlineData(80, "Updated HTTP Port")]
    public async Task Handle_UpdateCyberAirGapStatus_When_BoundaryPortValues(int port, string description)
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = $"Updated Port Test {port}",
            Description = description,
            Port = port
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapStatusCommand), typeof(Domain.Entities.CyberAirGapStatus)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapStatusCommand)src;
                var entity = (Domain.Entities.CyberAirGapStatus)dest;
                entity.AirGapName = updateCmd.AirGapName;
                entity.Port = updateCmd.Port;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();

        _mockCyberAirGapStatusRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.CyberAirGapStatus>(e =>
            e.Port == port)), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateCyberAirGapStatus_When_ValidatingResponse()
    {
        // Arrange
        var existingEntity = _cyberAirGapStatusFixture.CyberAirGapStatuses.First();
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = existingEntity.ReferenceId,
            AirGapName = "Response Validation Test"
        };

        _mockMapper.Setup(x => x.Map(command, existingEntity, typeof(UpdateCyberAirGapStatusCommand), typeof(Domain.Entities.CyberAirGapStatus)))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var updateCmd = (UpdateCyberAirGapStatusCommand)src;
                var entity = (Domain.Entities.CyberAirGapStatus)dest;
                entity.AirGapName = updateCmd.AirGapName;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateCyberAirGapStatusResponse>();
        result.Success.ShouldBeTrue();
        result.Id.ShouldBe(existingEntity.ReferenceId);
        result.Message.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("CyberAirGapStatus");
        result.Message.ShouldContain("updated successfully");
        result.Message.ShouldContain(command.AirGapName);
    }
}
