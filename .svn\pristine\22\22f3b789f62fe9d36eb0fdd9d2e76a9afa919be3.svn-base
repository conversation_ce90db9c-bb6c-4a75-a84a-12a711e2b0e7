﻿//using ContinuityPatrol.Domain.Extensions;
//using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
//using ContinuityPatrol.Shared.Core.Wrapper;

//namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetManagedWorkflowProfileInfos;

//public class GetManagedWorkflowProfileInfosQueryHandler : IRequestHandler<GetManagedWorkflowProfileInfosQuery,
//    PaginatedResult<WorkflowProfileInfoListVm>>
//{
//    private readonly IMapper _mapper;
//    private readonly IUserRepository _userRepository;
//    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;

//    public GetManagedWorkflowProfileInfosQueryHandler(IMapper mapper,
//        IWorkflowProfileInfoRepository workflowProfileInfoRepository, IUserRepository userRepository)
//    {
//        _mapper = mapper;
//        _workflowProfileInfoRepository = workflowProfileInfoRepository;
//        _userRepository = userRepository;
//    }


//    public async Task<PaginatedResult<WorkflowProfileInfoListVm>> Handle(GetManagedWorkflowProfileInfosQuery request,
//        CancellationToken cancellationToken)
//    {
//        var result = _workflowProfileInfoRepository.GetPaginatedQuery();

//        var userDetails = _userRepository.GetUserNames();

//        var workflowProfileList1 = await result
//            .Select(m => _mapper.Map<WorkflowProfileInfoListVm>(m))
//            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

//        workflowProfileList1.Data.ForEach(tm =>
//            tm.CreatedBy = userDetails.Result.Where(x => x.ReferenceId == tm.CreatedBy).Select(x => x.LoginName)
//                .SingleOrDefault()
//        );

//        return workflowProfileList1;
//    }
//}