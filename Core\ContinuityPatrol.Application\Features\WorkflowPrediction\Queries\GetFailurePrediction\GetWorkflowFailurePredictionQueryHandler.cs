﻿namespace ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetFailurePrediction;

public class GetWorkflowFailurePredictionQueryHandler : IRequestHandler<GetWorkflowFailurePredictionQuery, List<WorkflowFailurePredictionListVm>>
{
    private readonly IWorkflowPredictionRepository _workflowPredictionRepository;
    public GetWorkflowFailurePredictionQueryHandler(
        IWorkflowPredictionRepository workflowPredictionRepository)
    {
        _workflowPredictionRepository = workflowPredictionRepository;
    }
    public async Task<List<WorkflowFailurePredictionListVm>> Handle(GetWorkflowFailurePredictionQuery request,
        CancellationToken cancellationToken)
    {
        var failureList = await _workflowPredictionRepository.GetWorkflowFailurePredictionById(request.WorkflowId);


        return failureList.Count > 0 ? failureList
            : new List<WorkflowFailurePredictionListVm>();
    }
}
