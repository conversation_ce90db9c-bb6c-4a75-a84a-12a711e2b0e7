using ContinuityPatrol.Application.Features.Incident.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Commands;

public class DeleteIncidentTests : IClassFixture<IncidentFixture>
{
    private readonly IncidentFixture _incidentFixture;
    private readonly Mock<IIncidentRepository> _mockIncidentRepository;
    private readonly DeleteIncidentCommandHandler _handler;

    public DeleteIncidentTests(IncidentFixture incidentFixture)
    {
        _incidentFixture = incidentFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockIncidentRepository = IncidentRepositoryMocks.DeleteIncidentRepository(_incidentFixture.Incidents);

        _handler = new DeleteIncidentCommandHandler(_mockIncidentRepository.Object, _incidentFixture.Mapper, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_IncidentDeleted()
    {
        var validGuid = Guid.NewGuid();

        _incidentFixture.Incidents[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteIncidentCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteIncidentResponse_When_IncidentDeleted()
    {
        var validGuid = Guid.NewGuid();

        _incidentFixture.Incidents[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteIncidentCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteIncidentCommandResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_IncidentDeleted_ByReferenceId()
    {
        var validGuid = Guid.NewGuid();

        _incidentFixture.Incidents[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteIncidentCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var incident = await _mockIncidentRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        incident.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidIncidentId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteIncidentCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _incidentFixture.Incidents[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteIncidentCommand { Id = _incidentFixture.Incidents[0].ReferenceId }, CancellationToken.None);

        _mockIncidentRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockIncidentRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Incident>()), Times.Once);
    }
}