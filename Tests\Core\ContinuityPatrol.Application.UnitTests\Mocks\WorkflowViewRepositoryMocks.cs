﻿using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowViewRepositoryMocks
{
    public static Mock<IWorkflowViewRepository> ListAllAsync(List<WorkflowView> workflowView)
    {
        var workflowProfileInfoViewRepository = new Mock<IWorkflowViewRepository>();

        workflowProfileInfoViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowView);
        return workflowProfileInfoViewRepository;
    }
    public static Mock<IWorkflowViewRepository> GetWorkflowViewEmptyRepository()
    {
        var mockWorkflowViewRepository = new Mock<IWorkflowViewRepository>();

        mockWorkflowViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowView>());

        return mockWorkflowViewRepository;
    }

    public static Mock<IWorkflowViewRepository> GetWorkflowByProfileIds(List<string> workflowView)
    {
        var workflowProfileInfoViewRepository = new Mock<IWorkflowViewRepository>();

        workflowProfileInfoViewRepository.Setup(repo => repo.GetWorkflowByProfileIds(It.IsAny<List<string>>())).ReturnsAsync(workflowView);
        return workflowProfileInfoViewRepository;
    }

    //
}