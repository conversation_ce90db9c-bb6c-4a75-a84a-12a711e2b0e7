using ContinuityPatrol.Application.Features.Incident.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Constants;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.Incident.Validators;

public class CreateIncidentValidatorTests : IClassFixture<IncidentFixture>
{
    private readonly Mock<IIncidentRepository> _mockIncidentRepository;

    private readonly IncidentFixture _incidentFixture;

    public CreateIncidentValidatorTests(IncidentFixture incidentFixture)
    {
        _incidentFixture = incidentFixture;

        var incidents = new Fixture().Create<List<Domain.Entities.Incident>>();

        _mockIncidentRepository = IncidentRepositoryMocks.CreateIncidentRepository(incidents);
    }

    //IncidentName

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_IncidentName_WithEmpty(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.IncidentName = "";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "IncidentName is required.");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_IncidentName_IsNull(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.IncidentName = null;

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "IncidentName is required.");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_IncidentName_MinimumRange_Validator(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.IncidentName = "AB";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "IncidentName should contain between 3 to 30 characters.");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_IncidentName_MaximumRange_Validator(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.IncidentName = "ThisIsAVeryLongIncidentNameThatExceedsTheMaximumAllowedLength";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "IncidentName should contain between 3 to 30 characters.");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_IncidentName_InvalidFormat_Validator(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.IncidentName = "123Invalid@Name";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "Please Enter Valid IncidentName");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_IncidentName_ValidFormat_ShouldPass(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.IncidentName = "ValidIncident";
        createIncidentCommand.BusinessFunctionName = "ValidFunction";
        createIncidentCommand.BusinessServiceName = "ValidService";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeTrue();
    }

    //BusinessFunctionName

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_BusinessFunctionName_WithEmpty(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.BusinessFunctionName = "";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "Select BusinessFunctionName.");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_BusinessFunctionName_IsNull(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.BusinessFunctionName = null;

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "Select BusinessFunctionName.");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_BusinessFunctionName_InvalidFormat_Validator(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.BusinessFunctionName = "123Invalid@Function";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "Please Enter Valid BusinessFunctionName.");
    }

    //BusinessServiceName

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_BusinessServiceName_WithEmpty(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.BusinessServiceName = "";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "Select BusinessServiceName.");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_BusinessServiceName_IsNull(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.BusinessServiceName = null;

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "Select BusinessServiceName.");
    }

    [Theory]
    [AutoIncidentData]
    public async Task Verify_Create_BusinessServiceName_InvalidFormat_Validator(CreateIncidentCommand createIncidentCommand)
    {
        var validator = new CreateIncidentCommandValidator(_mockIncidentRepository.Object);

        createIncidentCommand.BusinessServiceName = "123Invalid@Service";

        var validateResult = await validator.ValidateAsync(createIncidentCommand, CancellationToken.None);

        validateResult.IsValid.ShouldBeFalse();
        validateResult.Errors.ShouldContain(e => e.ErrorMessage == "Please Enter Valid BusinessServiceName.");
    }
}