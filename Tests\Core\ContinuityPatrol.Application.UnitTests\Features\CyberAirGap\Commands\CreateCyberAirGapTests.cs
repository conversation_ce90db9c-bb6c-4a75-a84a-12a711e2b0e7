﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGap.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Commands;

public class CreateCyberAirGapTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly Mock<ICyberAirGapRepository> _mockCyberAirGapRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateCyberAirGapCommandHandler _handler;

    public CreateCyberAirGapTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _mockCyberAirGapRepository = CyberAirGapRepositoryMocks.CreateCyberAirGapRepository(_cyberAirGapFixture.CyberAirGaps);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new CreateCyberAirGapCommandHandler(
            _cyberAirGapFixture.Mapper,
            _mockCyberAirGapRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_CreateCyberAirGap_When_ValidCommand()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "TestAirGap_Create",
            Description = "Test Air Gap for Creation",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Source Site Create",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Target Site Create",
            Port = 9090,
            Source = "{\"serverId\":\"create-server-001\",\"componentId\":\"create-comp-001\"}",
            Target = "{\"serverId\":\"create-server-002\",\"componentId\":\"create-comp-002\"}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Create Source Component",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Create Target Component",
            WorkflowStatus = "Active",
            EnableWorkflowId = Guid.NewGuid().ToString(),
            DisableWorkflowId = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CreateCyberAirGapResponse>();
        result.Id.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("TestAirGap_Create");
        result.Message.ShouldContain("Airgap");
    }

    [Fact]
    public async Task Handle_CallAddAsync_OnlyOnce()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "TestAirGap_AddOnce",
            Description = "Test for single add call",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8080
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockCyberAirGapRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGap>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishCyberAirGapCreatedEvent_When_ValidCommand()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "TestAirGap_Event",
            Description = "Test for event publishing",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8080
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberAirGapCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_VerifyEventData_When_ValidCommand()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "TestAirGap_EventData",
            Description = "Test for event data verification",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8080
        };

        CyberAirGapCreatedEvent publishedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberAirGapCreatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<CyberAirGapCreatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe("TestAirGap_EventData");
    }

    [Fact]
    public async Task Handle_VerifyEntityMapping_When_ValidCommand()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "TestAirGap_Mapping",
            Description = "Test for entity mapping",
            SourceSiteId = "source-site-123",
            SourceSiteName = "Source Site Mapping",
            TargetSiteId = "target-site-456",
            TargetSiteName = "Target Site Mapping",
            Port = 7070,
            Source = "{\"serverId\":\"mapping-server-001\"}",
            Target = "{\"serverId\":\"mapping-server-002\"}",
            SourceComponentId = "source-comp-123",
            SourceComponentName = "Source Component Mapping",
            TargetComponentId = "target-comp-456",
            TargetComponentName = "Target Component Mapping",
            WorkflowStatus = "Mapping",
            EnableWorkflowId = "enable-workflow-123",
            DisableWorkflowId = "disable-workflow-456"
        };

        Domain.Entities.CyberAirGap addedEntity = null;
        _mockCyberAirGapRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGap>()))
            .Callback<Domain.Entities.CyberAirGap>(entity => addedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAirGap entity) => entity);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        addedEntity.ShouldNotBeNull();
        addedEntity.Name.ShouldBe("TestAirGap_Mapping");
        addedEntity.Description.ShouldBe("Test for entity mapping");
        addedEntity.SourceSiteId.ShouldBe("source-site-123");
        addedEntity.SourceSiteName.ShouldBe("Source Site Mapping");
        addedEntity.TargetSiteId.ShouldBe("target-site-456");
        addedEntity.TargetSiteName.ShouldBe("Target Site Mapping");
        addedEntity.Port.ShouldBe(7070);
        addedEntity.Source.ShouldBe("{\"serverId\":\"mapping-server-001\"}");
        addedEntity.Target.ShouldBe("{\"serverId\":\"mapping-server-002\"}");
        addedEntity.SourceComponentId.ShouldBe("source-comp-123");
        addedEntity.SourceComponentName.ShouldBe("Source Component Mapping");
        addedEntity.TargetComponentId.ShouldBe("target-comp-456");
        addedEntity.TargetComponentName.ShouldBe("Target Component Mapping");
        addedEntity.WorkflowStatus.ShouldBe("Mapping");
        addedEntity.EnableWorkflowId.ShouldBe("enable-workflow-123");
        addedEntity.DisableWorkflowId.ShouldBe("disable-workflow-456");
    }

    /// <summary>
    /// Test: Handler supports cancellation
    /// Expected: OperationCanceledException when cancellation is requested
    /// </summary>
    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "TestAirGap_Cancellation",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8080
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        
    }

    [Fact]
    public async Task Handle_CreateWithDifferentPorts_When_ValidPorts()
    {
        // Arrange
        var ports = new[] { 80, 443, 8080, 9090, 65535 };

        foreach (var port in ports)
        {
            var command = new CreateCyberAirGapCommand
            {
                Name = $"TestAirGap_Port_{port}",
                Description = $"Test for port {port}",
                SourceSiteId = Guid.NewGuid().ToString(),
                TargetSiteId = Guid.NewGuid().ToString(),
                Port = port
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldNotBeNullOrEmpty();
            result.Message.ShouldContain($"TestAirGap_Port_{port}");
        }
    }

    [Fact]
    public async Task Handle_CreateWithComplexJson_When_ValidJson()
    {
        // Arrange
        var complexSource = "{\"serverId\":\"srv-001\",\"componentId\":\"comp-001\",\"metadata\":{\"type\":\"primary\",\"region\":\"us-east\"}}";
        var complexTarget = "{\"serverId\":\"srv-002\",\"componentId\":\"comp-002\",\"metadata\":{\"type\":\"secondary\",\"region\":\"us-west\"}}";

        var command = new CreateCyberAirGapCommand
        {
            Name = "TestAirGap_ComplexJson",
            Description = "Test for complex JSON handling",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8080,
            Source = complexSource,
            Target = complexTarget
        };

        Domain.Entities.CyberAirGap addedEntity = null;
        _mockCyberAirGapRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.CyberAirGap>()))
            .Callback<Domain.Entities.CyberAirGap>(entity => addedEntity = entity)
            .ReturnsAsync((Domain.Entities.CyberAirGap entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        addedEntity.ShouldNotBeNull();
        addedEntity.Source.ShouldBe(complexSource);
        addedEntity.Target.ShouldBe(complexTarget);
    }
}