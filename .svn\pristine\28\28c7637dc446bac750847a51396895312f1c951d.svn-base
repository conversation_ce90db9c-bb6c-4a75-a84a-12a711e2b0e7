﻿using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.HacmpCluster.Commands;

public class DeleteHacmpClusterTests : IClassFixture<HacmpClusterFixture>
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;
    private readonly Mock<IHacmpClusterRepository> _mockHacmpClusterRepository;
    private readonly DeleteHacmpClusterCommandHandler _handler;

    public DeleteHacmpClusterTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockHacmpClusterRepository = HacmpClusterRepositoryMocks.DeleteHacmpClusterRepository(_hacmpClusterFixture.HacmpClusters);

        _handler = new DeleteHacmpClusterCommandHandler(_mockHacmpClusterRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_HacmpClusterDeleted()
    {
        var validGuid = Guid.NewGuid();

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteHacmpClusterCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteHacmpClusterResponse_When_HacmpClusterDeleted()
    {
        var validGuid = Guid.NewGuid();

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteHacmpClusterCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteHacmpClusterResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_HacmpClusterDeleted_ByReferenceId()
    {
        var validGuid = Guid.NewGuid();

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteHacmpClusterCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var hacmpCluster = await _mockHacmpClusterRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        hacmpCluster.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidHacmpClusterId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteHacmpClusterCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteHacmpClusterCommand { Id = _hacmpClusterFixture.HacmpClusters[0].ReferenceId }, CancellationToken.None);

        _mockHacmpClusterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockHacmpClusterRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.HacmpCluster>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_CorrectMessage_When_HacmpClusterDeleted()
    {
        var validGuid = Guid.NewGuid();

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = validGuid.ToString();
        _hacmpClusterFixture.HacmpClusters[0].Name = "Test HACMP Cluster";

        var result = await _handler.Handle(new DeleteHacmpClusterCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.Message.ShouldContain("HACMP Cluster");
        result.Message.ShouldContain("Test HACMP Cluster");
        result.Message.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteHacmpClusterCommand { Id = validGuid.ToString() }, CancellationToken.None);

        _mockHacmpClusterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetIsActiveFalse_When_HacmpClusterDeleted()
    {
        var validGuid = Guid.NewGuid();

        _hacmpClusterFixture.HacmpClusters[0].ReferenceId = validGuid.ToString();
        _hacmpClusterFixture.HacmpClusters[0].IsActive = true;

        await _handler.Handle(new DeleteHacmpClusterCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var hacmpCluster = await _mockHacmpClusterRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        hacmpCluster.IsActive.ShouldBeFalse();
    }
}